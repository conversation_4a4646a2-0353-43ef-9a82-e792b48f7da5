# Guti Builder

An advanced, API-first page builder library for React applications. Build beautiful, responsive pages with a drag-and-drop interface, powered by JSON Schema validation and a flexible component system.

## ✨ Features

- **🎯 API-First Design** - JSON-based data model with full TypeScript support
- **🧩 Component-Based Architecture** - Extensible block system with built-in components
- **📱 Responsive by Default** - Mobile-first design with responsive controls
- **🎨 Theme System** - Comprehensive design token system for consistent styling
- **🔧 Framework Agnostic Core** - Can be adapted to other frameworks beyond React
- **📋 JSON Schema Validation** - Robust data validation and form generation
- **🚀 Performance Optimized** - Efficient rendering with minimal re-renders
- **🔌 Plugin System** - Easy to extend with custom blocks and functionality

## 🚀 Quick Start

```bash
npm install guti-builder
```

```tsx
import { GutiBuilder, createTheme } from 'guti-builder';
import 'guti-builder/dist/style.css';

const theme = createTheme({
  colors: {
    primary: '#3b82f6',
    secondary: '#64748b',
    // ... more theme options
  },
});

function App() {
  const [pageData, setPageData] = useState(null);

  return (
    <GutiBuilder
      theme={theme}
      pageData={pageData}
      onPageChange={setPageData}
    />
  );
}
```

## 📚 Documentation

- [**Getting Started**](./docs/guide/getting-started.md) - Installation and basic usage
- [**Architecture Overview**](./docs/guide/architecture.md) - Core concepts and design decisions
- [**Data Model**](./docs/guide/data-model.md) - Understanding the JSON-based data structure
- [**Built-in Blocks**](./docs/guide/built-in-blocks.md) - Overview of included components
- [**Custom Blocks**](./docs/guide/custom-blocks.md) - Creating your own components
- [**Theme System**](./docs/guide/theme-system.md) - Customizing appearance and design tokens
- [**API Reference**](./docs/api/README.md) - Complete API documentation
- [**Examples**](./docs/examples/README.md) - Code examples and use cases

## 🏗️ Built-in Blocks

Guti Builder comes with a comprehensive set of built-in blocks:

### Content Blocks
- **Text Block** - Rich text with inline editing
- **Heading Block** - H1-H6 headings with customizable levels
- **Paragraph Block** - Formatted paragraph text
- **Image Block** - Responsive images with lazy loading
- **Video Block** - Video player with YouTube/Vimeo embed support

### Layout Blocks
- **Container Block** - Flexible row/column layouts
- **Grid Block** - CSS Grid with responsive columns
- **Spacer Block** - Adjustable spacing between elements

## 🎨 Theme System

Guti Builder uses a comprehensive design token system:

```tsx
const customTheme = createTheme({
  colors: {
    primary: { 500: '#3b82f6', 600: '#2563eb' },
    secondary: { 500: '#64748b', 600: '#475569' },
    // ... full color scales
  },
  typography: {
    fontFamily: { sans: 'Inter, sans-serif' },
    fontSize: { base: '16px', lg: '18px' },
    // ... typography tokens
  },
  spacing: {
    sm: '8px',
    md: '16px',
    lg: '24px',
    // ... spacing scale
  },
});
```

## 🔧 Extensibility

Create custom blocks with full TypeScript support:

```tsx
import { BlockDefinition, BlockComponentProps } from 'guti-builder';

const customBlockDefinition: BlockDefinition = {
  type: 'custom-block',
  name: 'Custom Block',
  category: 'custom',
  schema: {
    type: 'object',
    properties: {
      title: { type: 'string' },
      description: { type: 'string' },
    },
  },
  defaultProps: {
    title: 'Default Title',
    description: 'Default Description',
  },
  component: CustomBlockComponent,
};

function CustomBlockComponent({ block, theme }: BlockComponentProps) {
  return (
    <div>
      <h3>{block.props.title}</h3>
      <p>{block.props.description}</p>
    </div>
  );
}
```

## 📦 Project Structure

```
guti-builder/
├── src/
│   ├── core/           # Core builder logic
│   ├── blocks/         # Built-in block components
│   ├── theme/          # Theme system and tokens
│   ├── schemas/        # JSON Schema definitions
│   ├── types/          # TypeScript type definitions
│   └── utils/          # Utility functions
├── docs/               # Documentation
├── example/            # Example application
└── tests/              # Test files
```

## 🛠️ Development

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Run tests
npm test

# Build library
npm run build:lib

# Build documentation
npm run docs:build
```

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](./CONTRIBUTING.md) for details.

## 📄 License

MIT License - see [LICENSE](./LICENSE) file for details.

## 🙏 Acknowledgments

- Built with React 19 and TypeScript
- Styled with Tailwind CSS v4
- Validated with JSON Schema
- Inspired by modern page builders and design systems
