import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import dts from 'vite-plugin-dts'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const isLib = mode === 'lib'
  
  return {
    plugins: [
      react(),
      ...(isLib ? [dts({ include: ['src'] })] : [])
    ],
    resolve: {
      alias: {
        '@': resolve(__dirname, './src'),
        '@/core': resolve(__dirname, './src/core'),
        '@/blocks': resolve(__dirname, './src/blocks'),
        '@/theme': resolve(__dirname, './src/theme'),
        '@/schemas': resolve(__dirname, './src/schemas'),
        '@/types': resolve(__dirname, './src/types'),
        '@/utils': resolve(__dirname, './src/utils'),
      },
    },
    ...(isLib ? {
      build: {
        lib: {
          entry: resolve(__dirname, 'src/index.ts'),
          name: 'GutiBuilder',
          formats: ['es', 'umd'],
          fileName: (format) => `index.${format === 'es' ? 'esm.js' : 'js'}`
        },
        rollupOptions: {
          external: ['react', 'react-dom'],
          output: {
            globals: {
              react: 'React',
              'react-dom': 'ReactDOM'
            }
          }
        }
      }
    } : {
      root: './example',
      build: {
        outDir: '../dist-example'
      }
    }),
    test: {
      globals: true,
      environment: 'jsdom',
      setupFiles: './src/test/setup.ts',
    }
  }
})
