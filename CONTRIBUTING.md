# Contributing to Guti Builder

Thank you for your interest in contributing to Guti Builder! This document provides guidelines and information for contributors.

## 🚀 Getting Started

### Prerequisites

- Node.js 18+ 
- npm, yarn, or pnpm
- Git

### Development Setup

1. **Fork and clone the repository**
   ```bash
   git clone https://github.com/yourusername/guti-builder.git
   cd guti-builder
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

4. **Run tests**
   ```bash
   npm test
   ```

## 📋 Development Guidelines

### Code Style

- **TypeScript**: All code must be written in TypeScript with strict mode enabled
- **ESLint**: Follow the configured ESLint rules
- **Prettier**: Code must be formatted with Prettier
- **Naming**: Use descriptive names for variables, functions, and components

### Commit Messages

Follow the [Conventional Commits](https://www.conventionalcommits.org/) specification:

```
type(scope): description

[optional body]

[optional footer]
```

**Types:**
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

**Examples:**
```
feat(blocks): add video block component
fix(theme): resolve dark mode color contrast issue
docs(api): update BlockDefinition interface documentation
```

### Branch Naming

- `feature/description` - New features
- `fix/description` - Bug fixes
- `docs/description` - Documentation updates
- `refactor/description` - Code refactoring

## 🧪 Testing

### Test Requirements

- All new features must include tests
- Bug fixes must include regression tests
- Maintain or improve test coverage
- Tests must pass before merging

### Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run specific test file
npm test -- BlockRenderer.test.tsx
```

### Test Structure

```typescript
import { render, screen } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import { ComponentName } from './ComponentName';

describe('ComponentName', () => {
  it('should render correctly', () => {
    render(<ComponentName />);
    expect(screen.getByRole('button')).toBeInTheDocument();
  });
});
```

## 📚 Documentation

### Documentation Requirements

- All public APIs must be documented
- Include JSDoc comments for functions and components
- Update relevant documentation files
- Provide examples for new features

### Documentation Structure

```typescript
/**
 * Brief description of the function/component
 * 
 * @param param1 - Description of parameter
 * @param param2 - Description of parameter
 * @returns Description of return value
 * 
 * @example
 * ```tsx
 * const result = myFunction('example');
 * ```
 */
```

## 🔧 Architecture Guidelines

### Component Structure

```typescript
// 1. Imports
import React from 'react';
import { ComponentProps } from './types';

// 2. Types/Interfaces
interface Props extends ComponentProps {
  // component-specific props
}

// 3. Component
export function ComponentName({ prop1, prop2 }: Props) {
  // hooks
  // handlers
  // render
}

// 4. Default export (if needed)
export default ComponentName;
```

### File Organization

- One component per file
- Co-locate related files (component, styles, tests)
- Use index files for clean imports
- Keep files focused and small

### State Management

- Use Zustand for global state
- Use React state for component-local state
- Prefer immutable updates with Immer
- Keep state minimal and normalized

## 🎨 Design Guidelines

### Theme System

- Use design tokens from the theme system
- Avoid hardcoded colors or spacing
- Support both light and dark modes
- Ensure accessibility compliance

### Responsive Design

- Mobile-first approach
- Use semantic breakpoints
- Test on multiple screen sizes
- Consider touch interactions

## 🐛 Bug Reports

### Before Submitting

1. Check existing issues
2. Reproduce the bug
3. Test with the latest version
4. Gather relevant information

### Bug Report Template

```markdown
**Describe the bug**
A clear description of the bug.

**To Reproduce**
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. See error

**Expected behavior**
What you expected to happen.

**Screenshots**
If applicable, add screenshots.

**Environment:**
- OS: [e.g. macOS, Windows, Linux]
- Browser: [e.g. Chrome, Firefox, Safari]
- Version: [e.g. 1.0.0]

**Additional context**
Any other context about the problem.
```

## 💡 Feature Requests

### Before Submitting

1. Check if the feature already exists
2. Consider if it fits the project scope
3. Think about implementation complexity
4. Gather community feedback

### Feature Request Template

```markdown
**Is your feature request related to a problem?**
A clear description of the problem.

**Describe the solution you'd like**
A clear description of what you want to happen.

**Describe alternatives you've considered**
Alternative solutions or features you've considered.

**Additional context**
Any other context, mockups, or examples.
```

## 🔄 Pull Request Process

### Before Submitting

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Update documentation
6. Run the full test suite
7. Commit with conventional commit messages

### Pull Request Template

```markdown
**Description**
Brief description of changes.

**Type of Change**
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

**Testing**
- [ ] Tests pass locally
- [ ] Added tests for new functionality
- [ ] Manual testing completed

**Checklist**
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] No breaking changes (or documented)
```

### Review Process

1. Automated checks must pass
2. Code review by maintainers
3. Address feedback
4. Final approval and merge

## 📦 Release Process

### Versioning

We follow [Semantic Versioning](https://semver.org/):

- **MAJOR**: Breaking changes
- **MINOR**: New features (backward compatible)
- **PATCH**: Bug fixes (backward compatible)

### Release Checklist

1. Update version in package.json
2. Update CHANGELOG.md
3. Create release notes
4. Tag the release
5. Publish to npm
6. Update documentation

## 🤝 Community Guidelines

### Code of Conduct

- Be respectful and inclusive
- Welcome newcomers
- Provide constructive feedback
- Focus on the issue, not the person
- Help others learn and grow

### Communication

- **GitHub Issues**: Bug reports and feature requests
- **GitHub Discussions**: General questions and ideas
- **Pull Requests**: Code contributions and reviews

## 🏆 Recognition

Contributors will be recognized in:

- README.md contributors section
- Release notes
- Project documentation
- Special contributor badges

## 📞 Getting Help

- **Documentation**: Check the docs/ directory
- **Examples**: Look at the example/ directory
- **Issues**: Search existing GitHub issues
- **Discussions**: Start a GitHub discussion

Thank you for contributing to Guti Builder! 🎉
