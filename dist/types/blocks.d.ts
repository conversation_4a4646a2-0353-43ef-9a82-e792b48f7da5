/**
 * Built-in block type definitions
 */
export interface TextBlockProps {
    content: string;
    tag?: 'span' | 'p' | 'div';
    className?: string;
}
export interface ImageBlockProps {
    src: string;
    alt: string;
    width?: number;
    height?: number;
    objectFit?: 'cover' | 'contain' | 'fill' | 'none' | 'scale-down';
    loading?: 'lazy' | 'eager';
    className?: string;
}
export interface VideoBlockProps {
    src?: string;
    poster?: string;
    width?: number;
    height?: number;
    autoplay?: boolean;
    loop?: boolean;
    muted?: boolean;
    controls?: boolean;
    embedUrl?: string;
    embedType?: 'youtube' | 'vimeo' | 'custom';
    className?: string;
}
export interface HeadingBlockProps {
    content: string;
    level: 1 | 2 | 3 | 4 | 5 | 6;
    className?: string;
}
export interface ParagraphBlockProps {
    content: string;
    className?: string;
}
export interface SpacerBlockProps {
    height: string;
    className?: string;
}
export interface ContainerBlockProps {
    direction?: 'row' | 'column';
    gap?: string;
    alignItems?: 'flex-start' | 'center' | 'flex-end' | 'stretch';
    justifyContent?: 'flex-start' | 'center' | 'flex-end' | 'space-between' | 'space-around' | 'space-evenly';
    wrap?: boolean;
    className?: string;
}
export interface GridBlockProps {
    columns: number;
    gap?: string;
    columnGap?: string;
    rowGap?: string;
    mobileColumns?: number;
    tabletColumns?: number;
    className?: string;
}
/**
 * Union type of all built-in block props
 */
export type BuiltInBlockProps = TextBlockProps | ImageBlockProps | VideoBlockProps | HeadingBlockProps | ParagraphBlockProps | SpacerBlockProps | ContainerBlockProps | GridBlockProps;
