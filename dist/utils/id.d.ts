/**
 * Generate a unique identifier with optional prefix
 * @param prefix - Optional prefix for the ID
 * @returns Unique identifier string
 */
export declare function generateId(prefix?: string): string;
/**
 * Generate a block ID
 * @returns Block ID with 'block' prefix
 */
export declare function generateBlockId(): string;
/**
 * Generate a page ID
 * @returns Page ID with 'page' prefix
 */
export declare function generatePageId(): string;
/**
 * Check if a string is a valid ID format
 * @param id - ID to validate
 * @returns True if valid ID format
 */
export declare function isValidId(id: string): boolean;
