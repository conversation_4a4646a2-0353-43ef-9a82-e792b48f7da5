import { BlockData } from '../types/core';

/**
 * Deep clone an object
 * @param obj - Object to clone
 * @returns Cloned object
 */
export declare function deepClone<T>(obj: T): T;
/**
 * Clone a block with new IDs for the block and all children
 * @param block - Block to clone
 * @returns Cloned block with new IDs
 */
export declare function cloneBlock(block: BlockData): BlockData;
/**
 * Clone multiple blocks
 * @param blocks - Array of blocks to clone
 * @returns Array of cloned blocks with new IDs
 */
export declare function cloneBlocks(blocks: BlockData[]): BlockData[];
