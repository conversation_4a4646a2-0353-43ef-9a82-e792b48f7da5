var Ra = Object.defineProperty;
var Ca = (e, t, r) => t in e ? Ra(e, t, { enumerable: !0, configurable: !0, writable: !0, value: r }) : e[t] = r;
var Xr = (e, t, r) => Ca(e, typeof t != "symbol" ? t + "" : t, r);
const Aa = "useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict";
let Ma = (e = 21) => {
  let t = "", r = crypto.getRandomValues(new Uint8Array(e |= 0));
  for (; e--; )
    t += Aa[r[e] & 63];
  return t;
};
function kn(e) {
  const t = Ma(10);
  return e ? `${e}_${t}` : t;
}
function za() {
  return kn("block");
}
function Xu() {
  return kn("page");
}
function Yu(e) {
  return /^[a-zA-Z0-9_-]+$/.test(e);
}
function Gt(e) {
  if (e === null || typeof e != "object")
    return e;
  if (e instanceof Date)
    return new Date(e.getTime());
  if (Array.isArray(e))
    return e.map((r) => Gt(r));
  const t = {};
  for (const r in e)
    Object.prototype.hasOwnProperty.call(e, r) && (t[r] = Gt(e[r]));
  return t;
}
function jn(e) {
  const t = Gt(e);
  return t.id = za(), t.children && (t.children = t.children.map((r) => jn(r))), t;
}
function Qu(e) {
  return e.map((t) => jn(t));
}
const ed = {
  type: "object",
  required: ["id", "title", "blocks", "createdAt", "updatedAt"],
  properties: {
    id: {
      type: "string",
      description: "Unique page identifier"
    },
    title: {
      type: "string",
      minLength: 1,
      maxLength: 200,
      description: "Page title",
      "x-ui": {
        component: "input",
        label: "Page Title",
        placeholder: "Enter page title"
      }
    },
    description: {
      type: "string",
      maxLength: 500,
      description: "Page description",
      "x-ui": {
        component: "textarea",
        label: "Page Description",
        placeholder: "Enter page description"
      }
    },
    meta: {
      type: "object",
      description: "Page metadata",
      additionalProperties: !0
    },
    blocks: {
      type: "array",
      description: "Root blocks in the page",
      items: {
        $ref: "#/definitions/blockData"
      }
    },
    theme: {
      type: "object",
      description: "Page-specific theme overrides",
      additionalProperties: !0
    },
    settings: {
      type: "object",
      description: "Page settings",
      properties: {
        layout: {
          type: "string",
          enum: ["full-width", "contained", "centered"],
          default: "contained",
          "x-ui": {
            component: "select",
            label: "Page Layout"
          }
        },
        background: {
          type: "string",
          description: "Page background color or image",
          "x-ui": {
            component: "color",
            label: "Background"
          }
        },
        className: {
          type: "string",
          description: "Custom CSS classes",
          "x-ui": {
            component: "input",
            label: "CSS Classes",
            placeholder: "custom-class another-class"
          }
        }
      }
    },
    createdAt: {
      type: "string",
      format: "date-time",
      description: "Page creation timestamp"
    },
    updatedAt: {
      type: "string",
      format: "date-time",
      description: "Page last update timestamp"
    }
  },
  definitions: {
    blockData: {
      type: "object",
      required: ["id", "type", "props"],
      properties: {
        id: {
          type: "string",
          description: "Unique block identifier"
        },
        type: {
          type: "string",
          description: "Block type identifier"
        },
        props: {
          type: "object",
          description: "Block properties/content",
          additionalProperties: !0
        },
        children: {
          type: "array",
          description: "Child blocks",
          items: {
            $ref: "#/definitions/blockData"
          }
        },
        style: {
          type: "object",
          description: "Block styling",
          additionalProperties: !0
        },
        settings: {
          type: "object",
          description: "Block settings",
          properties: {
            className: {
              type: "string"
            },
            responsive: {
              type: "object",
              properties: {
                mobile: { type: "boolean" },
                tablet: { type: "boolean" },
                desktop: { type: "boolean" }
              }
            },
            animation: {
              type: "object",
              properties: {
                type: { type: "string" },
                duration: { type: "number" },
                delay: { type: "number" }
              }
            }
          }
        }
      }
    }
  }
}, Da = {
  type: "object",
  required: ["content"],
  properties: {
    content: {
      type: "string",
      description: "Text content",
      "x-ui": {
        component: "rich-text",
        label: "Content",
        placeholder: "Enter text content"
      }
    },
    tag: {
      type: "string",
      enum: ["span", "p", "div"],
      default: "span",
      "x-ui": {
        component: "select",
        label: "HTML Tag"
      }
    },
    className: {
      type: "string",
      "x-ui": {
        component: "input",
        label: "CSS Classes",
        placeholder: "custom-class"
      }
    }
  }
}, Va = {
  type: "object",
  required: ["src", "alt"],
  properties: {
    src: {
      type: "string",
      format: "uri",
      description: "Image source URL",
      "x-ui": {
        component: "image",
        label: "Image"
      }
    },
    alt: {
      type: "string",
      description: "Alternative text",
      "x-ui": {
        component: "input",
        label: "Alt Text",
        placeholder: "Describe the image"
      }
    },
    width: {
      type: "number",
      minimum: 1,
      "x-ui": {
        component: "input",
        label: "Width",
        placeholder: "400"
      }
    },
    height: {
      type: "number",
      minimum: 1,
      "x-ui": {
        component: "input",
        label: "Height",
        placeholder: "300"
      }
    },
    objectFit: {
      type: "string",
      enum: ["cover", "contain", "fill", "none", "scale-down"],
      default: "cover",
      "x-ui": {
        component: "select",
        label: "Object Fit"
      }
    },
    loading: {
      type: "string",
      enum: ["lazy", "eager"],
      default: "lazy",
      "x-ui": {
        component: "select",
        label: "Loading"
      }
    },
    className: {
      type: "string",
      "x-ui": {
        component: "input",
        label: "CSS Classes"
      }
    }
  }
}, Fa = {
  type: "object",
  properties: {
    src: {
      type: "string",
      format: "uri",
      "x-ui": {
        component: "video",
        label: "Video File"
      }
    },
    poster: {
      type: "string",
      format: "uri",
      "x-ui": {
        component: "image",
        label: "Poster Image"
      }
    },
    width: {
      type: "number",
      minimum: 1,
      "x-ui": {
        component: "input",
        label: "Width"
      }
    },
    height: {
      type: "number",
      minimum: 1,
      "x-ui": {
        component: "input",
        label: "Height"
      }
    },
    autoplay: {
      type: "boolean",
      default: !1,
      "x-ui": {
        component: "toggle",
        label: "Autoplay"
      }
    },
    loop: {
      type: "boolean",
      default: !1,
      "x-ui": {
        component: "toggle",
        label: "Loop"
      }
    },
    muted: {
      type: "boolean",
      default: !1,
      "x-ui": {
        component: "toggle",
        label: "Muted"
      }
    },
    controls: {
      type: "boolean",
      default: !0,
      "x-ui": {
        component: "toggle",
        label: "Show Controls"
      }
    },
    embedUrl: {
      type: "string",
      format: "uri",
      "x-ui": {
        component: "input",
        label: "Embed URL",
        placeholder: "YouTube or Vimeo URL"
      }
    },
    embedType: {
      type: "string",
      enum: ["youtube", "vimeo", "custom"],
      "x-ui": {
        component: "select",
        label: "Embed Type"
      }
    },
    className: {
      type: "string",
      "x-ui": {
        component: "input",
        label: "CSS Classes"
      }
    }
  },
  oneOf: [
    { required: ["src"] },
    { required: ["embedUrl"] }
  ]
}, qa = {
  type: "object",
  required: ["content", "level"],
  properties: {
    content: {
      type: "string",
      minLength: 1,
      "x-ui": {
        component: "input",
        label: "Heading Text",
        placeholder: "Enter heading"
      }
    },
    level: {
      type: "integer",
      minimum: 1,
      maximum: 6,
      default: 1,
      "x-ui": {
        component: "select",
        label: "Heading Level"
      }
    },
    className: {
      type: "string",
      "x-ui": {
        component: "input",
        label: "CSS Classes"
      }
    }
  }
}, xa = {
  type: "object",
  required: ["content"],
  properties: {
    content: {
      type: "string",
      minLength: 1,
      "x-ui": {
        component: "rich-text",
        label: "Paragraph Content",
        placeholder: "Enter paragraph text"
      }
    },
    className: {
      type: "string",
      "x-ui": {
        component: "input",
        label: "CSS Classes"
      }
    }
  }
}, Ua = {
  type: "object",
  required: ["height"],
  properties: {
    height: {
      type: "string",
      pattern: "^\\d+(px|rem|em|vh|%)$",
      default: "20px",
      "x-ui": {
        component: "input",
        label: "Height",
        placeholder: "20px, 2rem, 5vh"
      }
    },
    className: {
      type: "string",
      "x-ui": {
        component: "input",
        label: "CSS Classes"
      }
    }
  }
}, La = {
  type: "object",
  properties: {
    direction: {
      type: "string",
      enum: ["row", "column"],
      default: "row",
      "x-ui": {
        component: "select",
        label: "Direction"
      }
    },
    gap: {
      type: "string",
      pattern: "^\\d+(px|rem|em)$",
      default: "16px",
      "x-ui": {
        component: "input",
        label: "Gap",
        placeholder: "16px, 1rem"
      }
    },
    alignItems: {
      type: "string",
      enum: ["flex-start", "center", "flex-end", "stretch"],
      default: "stretch",
      "x-ui": {
        component: "select",
        label: "Align Items"
      }
    },
    justifyContent: {
      type: "string",
      enum: ["flex-start", "center", "flex-end", "space-between", "space-around", "space-evenly"],
      default: "flex-start",
      "x-ui": {
        component: "select",
        label: "Justify Content"
      }
    },
    wrap: {
      type: "boolean",
      default: !1,
      "x-ui": {
        component: "toggle",
        label: "Wrap Items"
      }
    },
    className: {
      type: "string",
      "x-ui": {
        component: "input",
        label: "CSS Classes"
      }
    }
  }
}, Ka = {
  type: "object",
  required: ["columns"],
  properties: {
    columns: {
      type: "integer",
      minimum: 1,
      maximum: 12,
      default: 2,
      "x-ui": {
        component: "slider",
        label: "Columns"
      }
    },
    gap: {
      type: "string",
      pattern: "^\\d+(px|rem|em)$",
      default: "16px",
      "x-ui": {
        component: "input",
        label: "Gap"
      }
    },
    columnGap: {
      type: "string",
      pattern: "^\\d+(px|rem|em)$",
      "x-ui": {
        component: "input",
        label: "Column Gap"
      }
    },
    rowGap: {
      type: "string",
      pattern: "^\\d+(px|rem|em)$",
      "x-ui": {
        component: "input",
        label: "Row Gap"
      }
    },
    mobileColumns: {
      type: "integer",
      minimum: 1,
      maximum: 12,
      default: 1,
      "x-ui": {
        component: "slider",
        label: "Mobile Columns"
      }
    },
    tabletColumns: {
      type: "integer",
      minimum: 1,
      maximum: 12,
      "x-ui": {
        component: "slider",
        label: "Tablet Columns"
      }
    },
    className: {
      type: "string",
      "x-ui": {
        component: "input",
        label: "CSS Classes"
      }
    }
  }
}, ve = {
  type: "object",
  properties: {
    50: { type: "string", pattern: "^#[0-9a-fA-F]{6}$" },
    100: { type: "string", pattern: "^#[0-9a-fA-F]{6}$" },
    200: { type: "string", pattern: "^#[0-9a-fA-F]{6}$" },
    300: { type: "string", pattern: "^#[0-9a-fA-F]{6}$" },
    400: { type: "string", pattern: "^#[0-9a-fA-F]{6}$" },
    500: { type: "string", pattern: "^#[0-9a-fA-F]{6}$" },
    600: { type: "string", pattern: "^#[0-9a-fA-F]{6}$" },
    700: { type: "string", pattern: "^#[0-9a-fA-F]{6}$" },
    800: { type: "string", pattern: "^#[0-9a-fA-F]{6}$" },
    900: { type: "string", pattern: "^#[0-9a-fA-F]{6}$" },
    950: { type: "string", pattern: "^#[0-9a-fA-F]{6}$" }
  },
  required: ["500"],
  // Base color is required
  additionalProperties: !1
}, Ha = {
  type: "object",
  properties: {
    primary: ve,
    secondary: ve,
    accent: ve,
    gray: ve,
    success: ve,
    warning: ve,
    error: ve,
    info: ve,
    background: { type: "string", pattern: "^#[0-9a-fA-F]{6}$" },
    surface: { type: "string", pattern: "^#[0-9a-fA-F]{6}$" },
    overlay: { type: "string", pattern: "^#[0-9a-fA-F]{6}$" },
    text: { type: "string", pattern: "^#[0-9a-fA-F]{6}$" },
    textMuted: { type: "string", pattern: "^#[0-9a-fA-F]{6}$" },
    textInverse: { type: "string", pattern: "^#[0-9a-fA-F]{6}$" },
    border: { type: "string", pattern: "^#[0-9a-fA-F]{6}$" },
    borderMuted: { type: "string", pattern: "^#[0-9a-fA-F]{6}$" },
    borderStrong: { type: "string", pattern: "^#[0-9a-fA-F]{6}$" }
  },
  required: ["primary", "background", "text"],
  additionalProperties: !1
}, Ga = {
  type: "object",
  properties: {
    fontFamily: {
      type: "object",
      properties: {
        sans: { type: "string" },
        serif: { type: "string" },
        mono: { type: "string" }
      },
      required: ["sans"],
      additionalProperties: !0
    },
    fontSize: {
      type: "object",
      properties: {
        xs: { type: "string", pattern: "^\\d+(\\.\\d+)?(px|rem|em)$" },
        sm: { type: "string", pattern: "^\\d+(\\.\\d+)?(px|rem|em)$" },
        base: { type: "string", pattern: "^\\d+(\\.\\d+)?(px|rem|em)$" },
        lg: { type: "string", pattern: "^\\d+(\\.\\d+)?(px|rem|em)$" },
        xl: { type: "string", pattern: "^\\d+(\\.\\d+)?(px|rem|em)$" },
        "2xl": { type: "string", pattern: "^\\d+(\\.\\d+)?(px|rem|em)$" },
        "3xl": { type: "string", pattern: "^\\d+(\\.\\d+)?(px|rem|em)$" },
        "4xl": { type: "string", pattern: "^\\d+(\\.\\d+)?(px|rem|em)$" },
        "5xl": { type: "string", pattern: "^\\d+(\\.\\d+)?(px|rem|em)$" },
        "6xl": { type: "string", pattern: "^\\d+(\\.\\d+)?(px|rem|em)$" }
      },
      required: ["base"],
      additionalProperties: !0
    },
    fontWeight: {
      type: "object",
      properties: {
        thin: { type: "number", minimum: 100, maximum: 900 },
        light: { type: "number", minimum: 100, maximum: 900 },
        normal: { type: "number", minimum: 100, maximum: 900 },
        medium: { type: "number", minimum: 100, maximum: 900 },
        semibold: { type: "number", minimum: 100, maximum: 900 },
        bold: { type: "number", minimum: 100, maximum: 900 },
        extrabold: { type: "number", minimum: 100, maximum: 900 },
        black: { type: "number", minimum: 100, maximum: 900 }
      },
      required: ["normal"],
      additionalProperties: !0
    },
    lineHeight: {
      type: "object",
      properties: {
        none: { type: "number", minimum: 0.5, maximum: 3 },
        tight: { type: "number", minimum: 0.5, maximum: 3 },
        snug: { type: "number", minimum: 0.5, maximum: 3 },
        normal: { type: "number", minimum: 0.5, maximum: 3 },
        relaxed: { type: "number", minimum: 0.5, maximum: 3 },
        loose: { type: "number", minimum: 0.5, maximum: 3 }
      },
      required: ["normal"],
      additionalProperties: !0
    },
    letterSpacing: {
      type: "object",
      properties: {
        tighter: { type: "string", pattern: "^-?\\d+(\\.\\d+)?(px|em)$" },
        tight: { type: "string", pattern: "^-?\\d+(\\.\\d+)?(px|em)$" },
        normal: { type: "string", pattern: "^-?\\d+(\\.\\d+)?(px|em)$" },
        wide: { type: "string", pattern: "^-?\\d+(\\.\\d+)?(px|em)$" },
        wider: { type: "string", pattern: "^-?\\d+(\\.\\d+)?(px|em)$" },
        widest: { type: "string", pattern: "^-?\\d+(\\.\\d+)?(px|em)$" }
      },
      required: ["normal"],
      additionalProperties: !0
    }
  },
  required: ["fontFamily", "fontSize", "fontWeight", "lineHeight"],
  additionalProperties: !1
}, Ba = {
  type: "object",
  patternProperties: {
    "^(0|px|0\\.5|[1-9]\\d*(\\.5)?)$": {
      type: "string",
      pattern: "^\\d+(\\.\\d+)?(px|rem|em)$"
    }
  },
  additionalProperties: !1
}, td = {
  type: "object",
  properties: {
    colors: Ha,
    typography: Ga,
    spacing: Ba,
    borderRadius: {
      type: "object",
      properties: {
        none: { type: "string", pattern: "^\\d+(\\.\\d+)?(px|rem|em)$" },
        sm: { type: "string", pattern: "^\\d+(\\.\\d+)?(px|rem|em)$" },
        base: { type: "string", pattern: "^\\d+(\\.\\d+)?(px|rem|em)$" },
        md: { type: "string", pattern: "^\\d+(\\.\\d+)?(px|rem|em)$" },
        lg: { type: "string", pattern: "^\\d+(\\.\\d+)?(px|rem|em)$" },
        xl: { type: "string", pattern: "^\\d+(\\.\\d+)?(px|rem|em)$" },
        "2xl": { type: "string", pattern: "^\\d+(\\.\\d+)?(px|rem|em)$" },
        "3xl": { type: "string", pattern: "^\\d+(\\.\\d+)?(px|rem|em)$" },
        full: { type: "string", enum: ["9999px", "50%", "100%"] }
      },
      required: ["base"],
      additionalProperties: !0
    },
    shadows: {
      type: "object",
      properties: {
        sm: { type: "string" },
        base: { type: "string" },
        md: { type: "string" },
        lg: { type: "string" },
        xl: { type: "string" },
        "2xl": { type: "string" },
        inner: { type: "string" },
        none: { type: "string", enum: ["none"] }
      },
      required: ["base"],
      additionalProperties: !0
    },
    breakpoints: {
      type: "object",
      properties: {
        sm: { type: "string", pattern: "^\\d+px$" },
        md: { type: "string", pattern: "^\\d+px$" },
        lg: { type: "string", pattern: "^\\d+px$" },
        xl: { type: "string", pattern: "^\\d+px$" },
        "2xl": { type: "string", pattern: "^\\d+px$" }
      },
      required: ["sm", "md", "lg"],
      additionalProperties: !0
    },
    animations: {
      type: "object",
      properties: {
        duration: {
          type: "object",
          patternProperties: {
            "^\\d+$": { type: "string", pattern: "^\\d+ms$" }
          },
          additionalProperties: !1
        },
        easing: {
          type: "object",
          properties: {
            linear: { type: "string" },
            in: { type: "string" },
            out: { type: "string" },
            "in-out": { type: "string" }
          },
          required: ["linear"],
          additionalProperties: !0
        }
      },
      additionalProperties: !1
    }
  },
  required: ["colors", "typography", "spacing"],
  additionalProperties: !1
};
function Nn(e) {
  return e && e.__esModule && Object.prototype.hasOwnProperty.call(e, "default") ? e.default : e;
}
var Bt = { exports: {} }, On = {}, ce = {}, Fe = {}, rt = {}, z = {}, tt = {};
(function(e) {
  Object.defineProperty(e, "__esModule", { value: !0 }), e.regexpCode = e.getEsmExportName = e.getProperty = e.safeStringify = e.stringify = e.strConcat = e.addCodeArg = e.str = e._ = e.nil = e._Code = e.Name = e.IDENTIFIER = e._CodeOrName = void 0;
  class t {
  }
  e._CodeOrName = t, e.IDENTIFIER = /^[a-z$_][a-z$_0-9]*$/i;
  class r extends t {
    constructor($) {
      if (super(), !e.IDENTIFIER.test($))
        throw new Error("CodeGen: name must be a valid identifier");
      this.str = $;
    }
    toString() {
      return this.str;
    }
    emptyStr() {
      return !1;
    }
    get names() {
      return { [this.str]: 1 };
    }
  }
  e.Name = r;
  class n extends t {
    constructor($) {
      super(), this._items = typeof $ == "string" ? [$] : $;
    }
    toString() {
      return this.str;
    }
    emptyStr() {
      if (this._items.length > 1)
        return !1;
      const $ = this._items[0];
      return $ === "" || $ === '""';
    }
    get str() {
      var $;
      return ($ = this._str) !== null && $ !== void 0 ? $ : this._str = this._items.reduce((P, j) => `${P}${j}`, "");
    }
    get names() {
      var $;
      return ($ = this._names) !== null && $ !== void 0 ? $ : this._names = this._items.reduce((P, j) => (j instanceof r && (P[j.str] = (P[j.str] || 0) + 1), P), {});
    }
  }
  e._Code = n, e.nil = new n("");
  function a(p, ...$) {
    const P = [p[0]];
    let j = 0;
    for (; j < $.length; )
      c(P, $[j]), P.push(p[++j]);
    return new n(P);
  }
  e._ = a;
  const s = new n("+");
  function i(p, ...$) {
    const P = [b(p[0])];
    let j = 0;
    for (; j < $.length; )
      P.push(s), c(P, $[j]), P.push(s, b(p[++j]));
    return l(P), new n(P);
  }
  e.str = i;
  function c(p, $) {
    $ instanceof n ? p.push(...$._items) : $ instanceof r ? p.push($) : p.push(m($));
  }
  e.addCodeArg = c;
  function l(p) {
    let $ = 1;
    for (; $ < p.length - 1; ) {
      if (p[$] === s) {
        const P = u(p[$ - 1], p[$ + 1]);
        if (P !== void 0) {
          p.splice($ - 1, 3, P);
          continue;
        }
        p[$++] = "+";
      }
      $++;
    }
  }
  function u(p, $) {
    if ($ === '""')
      return p;
    if (p === '""')
      return $;
    if (typeof p == "string")
      return $ instanceof r || p[p.length - 1] !== '"' ? void 0 : typeof $ != "string" ? `${p.slice(0, -1)}${$}"` : $[0] === '"' ? p.slice(0, -1) + $.slice(1) : void 0;
    if (typeof $ == "string" && $[0] === '"' && !(p instanceof r))
      return `"${p}${$.slice(1)}`;
  }
  function f(p, $) {
    return $.emptyStr() ? p : p.emptyStr() ? $ : i`${p}${$}`;
  }
  e.strConcat = f;
  function m(p) {
    return typeof p == "number" || typeof p == "boolean" || p === null ? p : b(Array.isArray(p) ? p.join(",") : p);
  }
  function S(p) {
    return new n(b(p));
  }
  e.stringify = S;
  function b(p) {
    return JSON.stringify(p).replace(/\u2028/g, "\\u2028").replace(/\u2029/g, "\\u2029");
  }
  e.safeStringify = b;
  function _(p) {
    return typeof p == "string" && e.IDENTIFIER.test(p) ? new n(`.${p}`) : a`[${p}]`;
  }
  e.getProperty = _;
  function v(p) {
    if (typeof p == "string" && e.IDENTIFIER.test(p))
      return new n(`${p}`);
    throw new Error(`CodeGen: invalid export name: ${p}, use explicit $id name mapping`);
  }
  e.getEsmExportName = v;
  function y(p) {
    return new n(p.toString());
  }
  e.regexpCode = y;
})(tt);
var Jt = {};
(function(e) {
  Object.defineProperty(e, "__esModule", { value: !0 }), e.ValueScope = e.ValueScopeName = e.Scope = e.varKinds = e.UsedValueState = void 0;
  const t = tt;
  class r extends Error {
    constructor(u) {
      super(`CodeGen: "code" for ${u} not defined`), this.value = u.value;
    }
  }
  var n;
  (function(l) {
    l[l.Started = 0] = "Started", l[l.Completed = 1] = "Completed";
  })(n || (e.UsedValueState = n = {})), e.varKinds = {
    const: new t.Name("const"),
    let: new t.Name("let"),
    var: new t.Name("var")
  };
  class a {
    constructor({ prefixes: u, parent: f } = {}) {
      this._names = {}, this._prefixes = u, this._parent = f;
    }
    toName(u) {
      return u instanceof t.Name ? u : this.name(u);
    }
    name(u) {
      return new t.Name(this._newName(u));
    }
    _newName(u) {
      const f = this._names[u] || this._nameGroup(u);
      return `${u}${f.index++}`;
    }
    _nameGroup(u) {
      var f, m;
      if (!((m = (f = this._parent) === null || f === void 0 ? void 0 : f._prefixes) === null || m === void 0) && m.has(u) || this._prefixes && !this._prefixes.has(u))
        throw new Error(`CodeGen: prefix "${u}" is not allowed in this scope`);
      return this._names[u] = { prefix: u, index: 0 };
    }
  }
  e.Scope = a;
  class s extends t.Name {
    constructor(u, f) {
      super(f), this.prefix = u;
    }
    setValue(u, { property: f, itemIndex: m }) {
      this.value = u, this.scopePath = (0, t._)`.${new t.Name(f)}[${m}]`;
    }
  }
  e.ValueScopeName = s;
  const i = (0, t._)`\n`;
  class c extends a {
    constructor(u) {
      super(u), this._values = {}, this._scope = u.scope, this.opts = { ...u, _n: u.lines ? i : t.nil };
    }
    get() {
      return this._scope;
    }
    name(u) {
      return new s(u, this._newName(u));
    }
    value(u, f) {
      var m;
      if (f.ref === void 0)
        throw new Error("CodeGen: ref must be passed in value");
      const S = this.toName(u), { prefix: b } = S, _ = (m = f.key) !== null && m !== void 0 ? m : f.ref;
      let v = this._values[b];
      if (v) {
        const $ = v.get(_);
        if ($)
          return $;
      } else
        v = this._values[b] = /* @__PURE__ */ new Map();
      v.set(_, S);
      const y = this._scope[b] || (this._scope[b] = []), p = y.length;
      return y[p] = f.ref, S.setValue(f, { property: b, itemIndex: p }), S;
    }
    getValue(u, f) {
      const m = this._values[u];
      if (m)
        return m.get(f);
    }
    scopeRefs(u, f = this._values) {
      return this._reduceValues(f, (m) => {
        if (m.scopePath === void 0)
          throw new Error(`CodeGen: name "${m}" has no value`);
        return (0, t._)`${u}${m.scopePath}`;
      });
    }
    scopeCode(u = this._values, f, m) {
      return this._reduceValues(u, (S) => {
        if (S.value === void 0)
          throw new Error(`CodeGen: name "${S}" has no value`);
        return S.value.code;
      }, f, m);
    }
    _reduceValues(u, f, m = {}, S) {
      let b = t.nil;
      for (const _ in u) {
        const v = u[_];
        if (!v)
          continue;
        const y = m[_] = m[_] || /* @__PURE__ */ new Map();
        v.forEach((p) => {
          if (y.has(p))
            return;
          y.set(p, n.Started);
          let $ = f(p);
          if ($) {
            const P = this.opts.es5 ? e.varKinds.var : e.varKinds.const;
            b = (0, t._)`${b}${P} ${p} = ${$};${this.opts._n}`;
          } else if ($ = S == null ? void 0 : S(p))
            b = (0, t._)`${b}${$}${this.opts._n}`;
          else
            throw new r(p);
          y.set(p, n.Completed);
        });
      }
      return b;
    }
  }
  e.ValueScope = c;
})(Jt);
(function(e) {
  Object.defineProperty(e, "__esModule", { value: !0 }), e.or = e.and = e.not = e.CodeGen = e.operators = e.varKinds = e.ValueScopeName = e.ValueScope = e.Scope = e.Name = e.regexpCode = e.stringify = e.getProperty = e.nil = e.strConcat = e.str = e._ = void 0;
  const t = tt, r = Jt;
  var n = tt;
  Object.defineProperty(e, "_", { enumerable: !0, get: function() {
    return n._;
  } }), Object.defineProperty(e, "str", { enumerable: !0, get: function() {
    return n.str;
  } }), Object.defineProperty(e, "strConcat", { enumerable: !0, get: function() {
    return n.strConcat;
  } }), Object.defineProperty(e, "nil", { enumerable: !0, get: function() {
    return n.nil;
  } }), Object.defineProperty(e, "getProperty", { enumerable: !0, get: function() {
    return n.getProperty;
  } }), Object.defineProperty(e, "stringify", { enumerable: !0, get: function() {
    return n.stringify;
  } }), Object.defineProperty(e, "regexpCode", { enumerable: !0, get: function() {
    return n.regexpCode;
  } }), Object.defineProperty(e, "Name", { enumerable: !0, get: function() {
    return n.Name;
  } });
  var a = Jt;
  Object.defineProperty(e, "Scope", { enumerable: !0, get: function() {
    return a.Scope;
  } }), Object.defineProperty(e, "ValueScope", { enumerable: !0, get: function() {
    return a.ValueScope;
  } }), Object.defineProperty(e, "ValueScopeName", { enumerable: !0, get: function() {
    return a.ValueScopeName;
  } }), Object.defineProperty(e, "varKinds", { enumerable: !0, get: function() {
    return a.varKinds;
  } }), e.operators = {
    GT: new t._Code(">"),
    GTE: new t._Code(">="),
    LT: new t._Code("<"),
    LTE: new t._Code("<="),
    EQ: new t._Code("==="),
    NEQ: new t._Code("!=="),
    NOT: new t._Code("!"),
    OR: new t._Code("||"),
    AND: new t._Code("&&"),
    ADD: new t._Code("+")
  };
  class s {
    optimizeNodes() {
      return this;
    }
    optimizeNames(o, d) {
      return this;
    }
  }
  class i extends s {
    constructor(o, d, w) {
      super(), this.varKind = o, this.name = d, this.rhs = w;
    }
    render({ es5: o, _n: d }) {
      const w = o ? r.varKinds.var : this.varKind, I = this.rhs === void 0 ? "" : ` = ${this.rhs}`;
      return `${w} ${this.name}${I};` + d;
    }
    optimizeNames(o, d) {
      if (o[this.name.str])
        return this.rhs && (this.rhs = F(this.rhs, o, d)), this;
    }
    get names() {
      return this.rhs instanceof t._CodeOrName ? this.rhs.names : {};
    }
  }
  class c extends s {
    constructor(o, d, w) {
      super(), this.lhs = o, this.rhs = d, this.sideEffects = w;
    }
    render({ _n: o }) {
      return `${this.lhs} = ${this.rhs};` + o;
    }
    optimizeNames(o, d) {
      if (!(this.lhs instanceof t.Name && !o[this.lhs.str] && !this.sideEffects))
        return this.rhs = F(this.rhs, o, d), this;
    }
    get names() {
      const o = this.lhs instanceof t.Name ? {} : { ...this.lhs.names };
      return H(o, this.rhs);
    }
  }
  class l extends c {
    constructor(o, d, w, I) {
      super(o, w, I), this.op = d;
    }
    render({ _n: o }) {
      return `${this.lhs} ${this.op}= ${this.rhs};` + o;
    }
  }
  class u extends s {
    constructor(o) {
      super(), this.label = o, this.names = {};
    }
    render({ _n: o }) {
      return `${this.label}:` + o;
    }
  }
  class f extends s {
    constructor(o) {
      super(), this.label = o, this.names = {};
    }
    render({ _n: o }) {
      return `break${this.label ? ` ${this.label}` : ""};` + o;
    }
  }
  class m extends s {
    constructor(o) {
      super(), this.error = o;
    }
    render({ _n: o }) {
      return `throw ${this.error};` + o;
    }
    get names() {
      return this.error.names;
    }
  }
  class S extends s {
    constructor(o) {
      super(), this.code = o;
    }
    render({ _n: o }) {
      return `${this.code};` + o;
    }
    optimizeNodes() {
      return `${this.code}` ? this : void 0;
    }
    optimizeNames(o, d) {
      return this.code = F(this.code, o, d), this;
    }
    get names() {
      return this.code instanceof t._CodeOrName ? this.code.names : {};
    }
  }
  class b extends s {
    constructor(o = []) {
      super(), this.nodes = o;
    }
    render(o) {
      return this.nodes.reduce((d, w) => d + w.render(o), "");
    }
    optimizeNodes() {
      const { nodes: o } = this;
      let d = o.length;
      for (; d--; ) {
        const w = o[d].optimizeNodes();
        Array.isArray(w) ? o.splice(d, 1, ...w) : w ? o[d] = w : o.splice(d, 1);
      }
      return o.length > 0 ? this : void 0;
    }
    optimizeNames(o, d) {
      const { nodes: w } = this;
      let I = w.length;
      for (; I--; ) {
        const R = w[I];
        R.optimizeNames(o, d) || (Z(o, R.names), w.splice(I, 1));
      }
      return w.length > 0 ? this : void 0;
    }
    get names() {
      return this.nodes.reduce((o, d) => D(o, d.names), {});
    }
  }
  class _ extends b {
    render(o) {
      return "{" + o._n + super.render(o) + "}" + o._n;
    }
  }
  class v extends b {
  }
  class y extends _ {
  }
  y.kind = "else";
  class p extends _ {
    constructor(o, d) {
      super(d), this.condition = o;
    }
    render(o) {
      let d = `if(${this.condition})` + super.render(o);
      return this.else && (d += "else " + this.else.render(o)), d;
    }
    optimizeNodes() {
      super.optimizeNodes();
      const o = this.condition;
      if (o === !0)
        return this.nodes;
      let d = this.else;
      if (d) {
        const w = d.optimizeNodes();
        d = this.else = Array.isArray(w) ? new y(w) : w;
      }
      if (d)
        return o === !1 ? d instanceof p ? d : d.nodes : this.nodes.length ? this : new p(he(o), d instanceof p ? [d] : d.nodes);
      if (!(o === !1 || !this.nodes.length))
        return this;
    }
    optimizeNames(o, d) {
      var w;
      if (this.else = (w = this.else) === null || w === void 0 ? void 0 : w.optimizeNames(o, d), !!(super.optimizeNames(o, d) || this.else))
        return this.condition = F(this.condition, o, d), this;
    }
    get names() {
      const o = super.names;
      return H(o, this.condition), this.else && D(o, this.else.names), o;
    }
  }
  p.kind = "if";
  class $ extends _ {
  }
  $.kind = "for";
  class P extends $ {
    constructor(o) {
      super(), this.iteration = o;
    }
    render(o) {
      return `for(${this.iteration})` + super.render(o);
    }
    optimizeNames(o, d) {
      if (super.optimizeNames(o, d))
        return this.iteration = F(this.iteration, o, d), this;
    }
    get names() {
      return D(super.names, this.iteration.names);
    }
  }
  class j extends $ {
    constructor(o, d, w, I) {
      super(), this.varKind = o, this.name = d, this.from = w, this.to = I;
    }
    render(o) {
      const d = o.es5 ? r.varKinds.var : this.varKind, { name: w, from: I, to: R } = this;
      return `for(${d} ${w}=${I}; ${w}<${R}; ${w}++)` + super.render(o);
    }
    get names() {
      const o = H(super.names, this.from);
      return H(o, this.to);
    }
  }
  class O extends $ {
    constructor(o, d, w, I) {
      super(), this.loop = o, this.varKind = d, this.name = w, this.iterable = I;
    }
    render(o) {
      return `for(${this.varKind} ${this.name} ${this.loop} ${this.iterable})` + super.render(o);
    }
    optimizeNames(o, d) {
      if (super.optimizeNames(o, d))
        return this.iterable = F(this.iterable, o, d), this;
    }
    get names() {
      return D(super.names, this.iterable.names);
    }
  }
  class U extends _ {
    constructor(o, d, w) {
      super(), this.name = o, this.args = d, this.async = w;
    }
    render(o) {
      return `${this.async ? "async " : ""}function ${this.name}(${this.args})` + super.render(o);
    }
  }
  U.kind = "func";
  class G extends b {
    render(o) {
      return "return " + super.render(o);
    }
  }
  G.kind = "return";
  class ne extends _ {
    render(o) {
      let d = "try" + super.render(o);
      return this.catch && (d += this.catch.render(o)), this.finally && (d += this.finally.render(o)), d;
    }
    optimizeNodes() {
      var o, d;
      return super.optimizeNodes(), (o = this.catch) === null || o === void 0 || o.optimizeNodes(), (d = this.finally) === null || d === void 0 || d.optimizeNodes(), this;
    }
    optimizeNames(o, d) {
      var w, I;
      return super.optimizeNames(o, d), (w = this.catch) === null || w === void 0 || w.optimizeNames(o, d), (I = this.finally) === null || I === void 0 || I.optimizeNames(o, d), this;
    }
    get names() {
      const o = super.names;
      return this.catch && D(o, this.catch.names), this.finally && D(o, this.finally.names), o;
    }
  }
  class ue extends _ {
    constructor(o) {
      super(), this.error = o;
    }
    render(o) {
      return `catch(${this.error})` + super.render(o);
    }
  }
  ue.kind = "catch";
  class me extends _ {
    render(o) {
      return "finally" + super.render(o);
    }
  }
  me.kind = "finally";
  class C {
    constructor(o, d = {}) {
      this._values = {}, this._blockStarts = [], this._constants = {}, this.opts = { ...d, _n: d.lines ? `
` : "" }, this._extScope = o, this._scope = new r.Scope({ parent: o }), this._nodes = [new v()];
    }
    toString() {
      return this._root.render(this.opts);
    }
    // returns unique name in the internal scope
    name(o) {
      return this._scope.name(o);
    }
    // reserves unique name in the external scope
    scopeName(o) {
      return this._extScope.name(o);
    }
    // reserves unique name in the external scope and assigns value to it
    scopeValue(o, d) {
      const w = this._extScope.value(o, d);
      return (this._values[w.prefix] || (this._values[w.prefix] = /* @__PURE__ */ new Set())).add(w), w;
    }
    getScopeValue(o, d) {
      return this._extScope.getValue(o, d);
    }
    // return code that assigns values in the external scope to the names that are used internally
    // (same names that were returned by gen.scopeName or gen.scopeValue)
    scopeRefs(o) {
      return this._extScope.scopeRefs(o, this._values);
    }
    scopeCode() {
      return this._extScope.scopeCode(this._values);
    }
    _def(o, d, w, I) {
      const R = this._scope.toName(d);
      return w !== void 0 && I && (this._constants[R.str] = w), this._leafNode(new i(o, R, w)), R;
    }
    // `const` declaration (`var` in es5 mode)
    const(o, d, w) {
      return this._def(r.varKinds.const, o, d, w);
    }
    // `let` declaration with optional assignment (`var` in es5 mode)
    let(o, d, w) {
      return this._def(r.varKinds.let, o, d, w);
    }
    // `var` declaration with optional assignment
    var(o, d, w) {
      return this._def(r.varKinds.var, o, d, w);
    }
    // assignment code
    assign(o, d, w) {
      return this._leafNode(new c(o, d, w));
    }
    // `+=` code
    add(o, d) {
      return this._leafNode(new l(o, e.operators.ADD, d));
    }
    // appends passed SafeExpr to code or executes Block
    code(o) {
      return typeof o == "function" ? o() : o !== t.nil && this._leafNode(new S(o)), this;
    }
    // returns code for object literal for the passed argument list of key-value pairs
    object(...o) {
      const d = ["{"];
      for (const [w, I] of o)
        d.length > 1 && d.push(","), d.push(w), (w !== I || this.opts.es5) && (d.push(":"), (0, t.addCodeArg)(d, I));
      return d.push("}"), new t._Code(d);
    }
    // `if` clause (or statement if `thenBody` and, optionally, `elseBody` are passed)
    if(o, d, w) {
      if (this._blockNode(new p(o)), d && w)
        this.code(d).else().code(w).endIf();
      else if (d)
        this.code(d).endIf();
      else if (w)
        throw new Error('CodeGen: "else" body without "then" body');
      return this;
    }
    // `else if` clause - invalid without `if` or after `else` clauses
    elseIf(o) {
      return this._elseNode(new p(o));
    }
    // `else` clause - only valid after `if` or `else if` clauses
    else() {
      return this._elseNode(new y());
    }
    // end `if` statement (needed if gen.if was used only with condition)
    endIf() {
      return this._endBlockNode(p, y);
    }
    _for(o, d) {
      return this._blockNode(o), d && this.code(d).endFor(), this;
    }
    // a generic `for` clause (or statement if `forBody` is passed)
    for(o, d) {
      return this._for(new P(o), d);
    }
    // `for` statement for a range of values
    forRange(o, d, w, I, R = this.opts.es5 ? r.varKinds.var : r.varKinds.let) {
      const x = this._scope.toName(o);
      return this._for(new j(R, x, d, w), () => I(x));
    }
    // `for-of` statement (in es5 mode replace with a normal for loop)
    forOf(o, d, w, I = r.varKinds.const) {
      const R = this._scope.toName(o);
      if (this.opts.es5) {
        const x = d instanceof t.Name ? d : this.var("_arr", d);
        return this.forRange("_i", 0, (0, t._)`${x}.length`, (q) => {
          this.var(R, (0, t._)`${x}[${q}]`), w(R);
        });
      }
      return this._for(new O("of", I, R, d), () => w(R));
    }
    // `for-in` statement.
    // With option `ownProperties` replaced with a `for-of` loop for object keys
    forIn(o, d, w, I = this.opts.es5 ? r.varKinds.var : r.varKinds.const) {
      if (this.opts.ownProperties)
        return this.forOf(o, (0, t._)`Object.keys(${d})`, w);
      const R = this._scope.toName(o);
      return this._for(new O("in", I, R, d), () => w(R));
    }
    // end `for` loop
    endFor() {
      return this._endBlockNode($);
    }
    // `label` statement
    label(o) {
      return this._leafNode(new u(o));
    }
    // `break` statement
    break(o) {
      return this._leafNode(new f(o));
    }
    // `return` statement
    return(o) {
      const d = new G();
      if (this._blockNode(d), this.code(o), d.nodes.length !== 1)
        throw new Error('CodeGen: "return" should have one node');
      return this._endBlockNode(G);
    }
    // `try` statement
    try(o, d, w) {
      if (!d && !w)
        throw new Error('CodeGen: "try" without "catch" and "finally"');
      const I = new ne();
      if (this._blockNode(I), this.code(o), d) {
        const R = this.name("e");
        this._currNode = I.catch = new ue(R), d(R);
      }
      return w && (this._currNode = I.finally = new me(), this.code(w)), this._endBlockNode(ue, me);
    }
    // `throw` statement
    throw(o) {
      return this._leafNode(new m(o));
    }
    // start self-balancing block
    block(o, d) {
      return this._blockStarts.push(this._nodes.length), o && this.code(o).endBlock(d), this;
    }
    // end the current self-balancing block
    endBlock(o) {
      const d = this._blockStarts.pop();
      if (d === void 0)
        throw new Error("CodeGen: not in self-balancing block");
      const w = this._nodes.length - d;
      if (w < 0 || o !== void 0 && w !== o)
        throw new Error(`CodeGen: wrong number of nodes: ${w} vs ${o} expected`);
      return this._nodes.length = d, this;
    }
    // `function` heading (or definition if funcBody is passed)
    func(o, d = t.nil, w, I) {
      return this._blockNode(new U(o, d, w)), I && this.code(I).endFunc(), this;
    }
    // end function definition
    endFunc() {
      return this._endBlockNode(U);
    }
    optimize(o = 1) {
      for (; o-- > 0; )
        this._root.optimizeNodes(), this._root.optimizeNames(this._root.names, this._constants);
    }
    _leafNode(o) {
      return this._currNode.nodes.push(o), this;
    }
    _blockNode(o) {
      this._currNode.nodes.push(o), this._nodes.push(o);
    }
    _endBlockNode(o, d) {
      const w = this._currNode;
      if (w instanceof o || d && w instanceof d)
        return this._nodes.pop(), this;
      throw new Error(`CodeGen: not in block "${d ? `${o.kind}/${d.kind}` : o.kind}"`);
    }
    _elseNode(o) {
      const d = this._currNode;
      if (!(d instanceof p))
        throw new Error('CodeGen: "else" without "if"');
      return this._currNode = d.else = o, this;
    }
    get _root() {
      return this._nodes[0];
    }
    get _currNode() {
      const o = this._nodes;
      return o[o.length - 1];
    }
    set _currNode(o) {
      const d = this._nodes;
      d[d.length - 1] = o;
    }
  }
  e.CodeGen = C;
  function D(g, o) {
    for (const d in o)
      g[d] = (g[d] || 0) + (o[d] || 0);
    return g;
  }
  function H(g, o) {
    return o instanceof t._CodeOrName ? D(g, o.names) : g;
  }
  function F(g, o, d) {
    if (g instanceof t.Name)
      return w(g);
    if (!I(g))
      return g;
    return new t._Code(g._items.reduce((R, x) => (x instanceof t.Name && (x = w(x)), x instanceof t._Code ? R.push(...x._items) : R.push(x), R), []));
    function w(R) {
      const x = d[R.str];
      return x === void 0 || o[R.str] !== 1 ? R : (delete o[R.str], x);
    }
    function I(R) {
      return R instanceof t._Code && R._items.some((x) => x instanceof t.Name && o[x.str] === 1 && d[x.str] !== void 0);
    }
  }
  function Z(g, o) {
    for (const d in o)
      g[d] = (g[d] || 0) - (o[d] || 0);
  }
  function he(g) {
    return typeof g == "boolean" || typeof g == "number" || g === null ? !g : (0, t._)`!${E(g)}`;
  }
  e.not = he;
  const ye = h(e.operators.AND);
  function Ke(...g) {
    return g.reduce(ye);
  }
  e.and = Ke;
  const ke = h(e.operators.OR);
  function k(...g) {
    return g.reduce(ke);
  }
  e.or = k;
  function h(g) {
    return (o, d) => o === t.nil ? d : d === t.nil ? o : (0, t._)`${E(o)} ${g} ${E(d)}`;
  }
  function E(g) {
    return g instanceof t.Name ? g : (0, t._)`(${g})`;
  }
})(z);
var N = {};
Object.defineProperty(N, "__esModule", { value: !0 });
N.checkStrictMode = N.getErrorPath = N.Type = N.useFunc = N.setEvaluated = N.evaluatedPropsToName = N.mergeEvaluated = N.eachItem = N.unescapeJsonPointer = N.escapeJsonPointer = N.escapeFragment = N.unescapeFragment = N.schemaRefOrVal = N.schemaHasRulesButRef = N.schemaHasRules = N.checkUnknownRules = N.alwaysValidSchema = N.toHash = void 0;
const L = z, Ja = tt;
function Wa(e) {
  const t = {};
  for (const r of e)
    t[r] = !0;
  return t;
}
N.toHash = Wa;
function Za(e, t) {
  return typeof t == "boolean" ? t : Object.keys(t).length === 0 ? !0 : (Tn(e, t), !In(t, e.self.RULES.all));
}
N.alwaysValidSchema = Za;
function Tn(e, t = e.schema) {
  const { opts: r, self: n } = e;
  if (!r.strictSchema || typeof t == "boolean")
    return;
  const a = n.RULES.keywords;
  for (const s in t)
    a[s] || An(e, `unknown keyword: "${s}"`);
}
N.checkUnknownRules = Tn;
function In(e, t) {
  if (typeof e == "boolean")
    return !e;
  for (const r in e)
    if (t[r])
      return !0;
  return !1;
}
N.schemaHasRules = In;
function Xa(e, t) {
  if (typeof e == "boolean")
    return !e;
  for (const r in e)
    if (r !== "$ref" && t.all[r])
      return !0;
  return !1;
}
N.schemaHasRulesButRef = Xa;
function Ya({ topSchemaRef: e, schemaPath: t }, r, n, a) {
  if (!a) {
    if (typeof r == "number" || typeof r == "boolean")
      return r;
    if (typeof r == "string")
      return (0, L._)`${r}`;
  }
  return (0, L._)`${e}${t}${(0, L.getProperty)(n)}`;
}
N.schemaRefOrVal = Ya;
function Qa(e) {
  return Rn(decodeURIComponent(e));
}
N.unescapeFragment = Qa;
function es(e) {
  return encodeURIComponent(rr(e));
}
N.escapeFragment = es;
function rr(e) {
  return typeof e == "number" ? `${e}` : e.replace(/~/g, "~0").replace(/\//g, "~1");
}
N.escapeJsonPointer = rr;
function Rn(e) {
  return e.replace(/~1/g, "/").replace(/~0/g, "~");
}
N.unescapeJsonPointer = Rn;
function ts(e, t) {
  if (Array.isArray(e))
    for (const r of e)
      t(r);
  else
    t(e);
}
N.eachItem = ts;
function Yr({ mergeNames: e, mergeToName: t, mergeValues: r, resultToName: n }) {
  return (a, s, i, c) => {
    const l = i === void 0 ? s : i instanceof L.Name ? (s instanceof L.Name ? e(a, s, i) : t(a, s, i), i) : s instanceof L.Name ? (t(a, i, s), s) : r(s, i);
    return c === L.Name && !(l instanceof L.Name) ? n(a, l) : l;
  };
}
N.mergeEvaluated = {
  props: Yr({
    mergeNames: (e, t, r) => e.if((0, L._)`${r} !== true && ${t} !== undefined`, () => {
      e.if((0, L._)`${t} === true`, () => e.assign(r, !0), () => e.assign(r, (0, L._)`${r} || {}`).code((0, L._)`Object.assign(${r}, ${t})`));
    }),
    mergeToName: (e, t, r) => e.if((0, L._)`${r} !== true`, () => {
      t === !0 ? e.assign(r, !0) : (e.assign(r, (0, L._)`${r} || {}`), nr(e, r, t));
    }),
    mergeValues: (e, t) => e === !0 ? !0 : { ...e, ...t },
    resultToName: Cn
  }),
  items: Yr({
    mergeNames: (e, t, r) => e.if((0, L._)`${r} !== true && ${t} !== undefined`, () => e.assign(r, (0, L._)`${t} === true ? true : ${r} > ${t} ? ${r} : ${t}`)),
    mergeToName: (e, t, r) => e.if((0, L._)`${r} !== true`, () => e.assign(r, t === !0 ? !0 : (0, L._)`${r} > ${t} ? ${r} : ${t}`)),
    mergeValues: (e, t) => e === !0 ? !0 : Math.max(e, t),
    resultToName: (e, t) => e.var("items", t)
  })
};
function Cn(e, t) {
  if (t === !0)
    return e.var("props", !0);
  const r = e.var("props", (0, L._)`{}`);
  return t !== void 0 && nr(e, r, t), r;
}
N.evaluatedPropsToName = Cn;
function nr(e, t, r) {
  Object.keys(r).forEach((n) => e.assign((0, L._)`${t}${(0, L.getProperty)(n)}`, !0));
}
N.setEvaluated = nr;
const Qr = {};
function rs(e, t) {
  return e.scopeValue("func", {
    ref: t,
    code: Qr[t.code] || (Qr[t.code] = new Ja._Code(t.code))
  });
}
N.useFunc = rs;
var Wt;
(function(e) {
  e[e.Num = 0] = "Num", e[e.Str = 1] = "Str";
})(Wt || (N.Type = Wt = {}));
function ns(e, t, r) {
  if (e instanceof L.Name) {
    const n = t === Wt.Num;
    return r ? n ? (0, L._)`"[" + ${e} + "]"` : (0, L._)`"['" + ${e} + "']"` : n ? (0, L._)`"/" + ${e}` : (0, L._)`"/" + ${e}.replace(/~/g, "~0").replace(/\\//g, "~1")`;
  }
  return r ? (0, L.getProperty)(e).toString() : "/" + rr(e);
}
N.getErrorPath = ns;
function An(e, t, r = e.opts.strictSchema) {
  if (r) {
    if (t = `strict mode: ${t}`, r === !0)
      throw new Error(t);
    e.self.logger.warn(t);
  }
}
N.checkStrictMode = An;
var pe = {};
Object.defineProperty(pe, "__esModule", { value: !0 });
const Q = z, as = {
  // validation function arguments
  data: new Q.Name("data"),
  // data passed to validation function
  // args passed from referencing schema
  valCxt: new Q.Name("valCxt"),
  // validation/data context - should not be used directly, it is destructured to the names below
  instancePath: new Q.Name("instancePath"),
  parentData: new Q.Name("parentData"),
  parentDataProperty: new Q.Name("parentDataProperty"),
  rootData: new Q.Name("rootData"),
  // root data - same as the data passed to the first/top validation function
  dynamicAnchors: new Q.Name("dynamicAnchors"),
  // used to support recursiveRef and dynamicRef
  // function scoped variables
  vErrors: new Q.Name("vErrors"),
  // null or array of validation errors
  errors: new Q.Name("errors"),
  // counter of validation errors
  this: new Q.Name("this"),
  // "globals"
  self: new Q.Name("self"),
  scope: new Q.Name("scope"),
  // JTD serialize/parse name for JSON string and position
  json: new Q.Name("json"),
  jsonPos: new Q.Name("jsonPos"),
  jsonLen: new Q.Name("jsonLen"),
  jsonPart: new Q.Name("jsonPart")
};
pe.default = as;
(function(e) {
  Object.defineProperty(e, "__esModule", { value: !0 }), e.extendErrors = e.resetErrorsCount = e.reportExtraError = e.reportError = e.keyword$DataError = e.keywordError = void 0;
  const t = z, r = N, n = pe;
  e.keywordError = {
    message: ({ keyword: y }) => (0, t.str)`must pass "${y}" keyword validation`
  }, e.keyword$DataError = {
    message: ({ keyword: y, schemaType: p }) => p ? (0, t.str)`"${y}" keyword must be ${p} ($data)` : (0, t.str)`"${y}" keyword is invalid ($data)`
  };
  function a(y, p = e.keywordError, $, P) {
    const { it: j } = y, { gen: O, compositeRule: U, allErrors: G } = j, ne = m(y, p, $);
    P ?? (U || G) ? l(O, ne) : u(j, (0, t._)`[${ne}]`);
  }
  e.reportError = a;
  function s(y, p = e.keywordError, $) {
    const { it: P } = y, { gen: j, compositeRule: O, allErrors: U } = P, G = m(y, p, $);
    l(j, G), O || U || u(P, n.default.vErrors);
  }
  e.reportExtraError = s;
  function i(y, p) {
    y.assign(n.default.errors, p), y.if((0, t._)`${n.default.vErrors} !== null`, () => y.if(p, () => y.assign((0, t._)`${n.default.vErrors}.length`, p), () => y.assign(n.default.vErrors, null)));
  }
  e.resetErrorsCount = i;
  function c({ gen: y, keyword: p, schemaValue: $, data: P, errsCount: j, it: O }) {
    if (j === void 0)
      throw new Error("ajv implementation error");
    const U = y.name("err");
    y.forRange("i", j, n.default.errors, (G) => {
      y.const(U, (0, t._)`${n.default.vErrors}[${G}]`), y.if((0, t._)`${U}.instancePath === undefined`, () => y.assign((0, t._)`${U}.instancePath`, (0, t.strConcat)(n.default.instancePath, O.errorPath))), y.assign((0, t._)`${U}.schemaPath`, (0, t.str)`${O.errSchemaPath}/${p}`), O.opts.verbose && (y.assign((0, t._)`${U}.schema`, $), y.assign((0, t._)`${U}.data`, P));
    });
  }
  e.extendErrors = c;
  function l(y, p) {
    const $ = y.const("err", p);
    y.if((0, t._)`${n.default.vErrors} === null`, () => y.assign(n.default.vErrors, (0, t._)`[${$}]`), (0, t._)`${n.default.vErrors}.push(${$})`), y.code((0, t._)`${n.default.errors}++`);
  }
  function u(y, p) {
    const { gen: $, validateName: P, schemaEnv: j } = y;
    j.$async ? $.throw((0, t._)`new ${y.ValidationError}(${p})`) : ($.assign((0, t._)`${P}.errors`, p), $.return(!1));
  }
  const f = {
    keyword: new t.Name("keyword"),
    schemaPath: new t.Name("schemaPath"),
    // also used in JTD errors
    params: new t.Name("params"),
    propertyName: new t.Name("propertyName"),
    message: new t.Name("message"),
    schema: new t.Name("schema"),
    parentSchema: new t.Name("parentSchema")
  };
  function m(y, p, $) {
    const { createErrors: P } = y.it;
    return P === !1 ? (0, t._)`{}` : S(y, p, $);
  }
  function S(y, p, $ = {}) {
    const { gen: P, it: j } = y, O = [
      b(j, $),
      _(y, $)
    ];
    return v(y, p, O), P.object(...O);
  }
  function b({ errorPath: y }, { instancePath: p }) {
    const $ = p ? (0, t.str)`${y}${(0, r.getErrorPath)(p, r.Type.Str)}` : y;
    return [n.default.instancePath, (0, t.strConcat)(n.default.instancePath, $)];
  }
  function _({ keyword: y, it: { errSchemaPath: p } }, { schemaPath: $, parentSchema: P }) {
    let j = P ? p : (0, t.str)`${p}/${y}`;
    return $ && (j = (0, t.str)`${j}${(0, r.getErrorPath)($, r.Type.Str)}`), [f.schemaPath, j];
  }
  function v(y, { params: p, message: $ }, P) {
    const { keyword: j, data: O, schemaValue: U, it: G } = y, { opts: ne, propertyName: ue, topSchemaRef: me, schemaPath: C } = G;
    P.push([f.keyword, j], [f.params, typeof p == "function" ? p(y) : p || (0, t._)`{}`]), ne.messages && P.push([f.message, typeof $ == "function" ? $(y) : $]), ne.verbose && P.push([f.schema, U], [f.parentSchema, (0, t._)`${me}${C}`], [n.default.data, O]), ue && P.push([f.propertyName, ue]);
  }
})(rt);
Object.defineProperty(Fe, "__esModule", { value: !0 });
Fe.boolOrEmptySchema = Fe.topBoolOrEmptySchema = void 0;
const ss = rt, os = z, is = pe, cs = {
  message: "boolean schema is false"
};
function us(e) {
  const { gen: t, schema: r, validateName: n } = e;
  r === !1 ? Mn(e, !1) : typeof r == "object" && r.$async === !0 ? t.return(is.default.data) : (t.assign((0, os._)`${n}.errors`, null), t.return(!0));
}
Fe.topBoolOrEmptySchema = us;
function ds(e, t) {
  const { gen: r, schema: n } = e;
  n === !1 ? (r.var(t, !1), Mn(e)) : r.var(t, !0);
}
Fe.boolOrEmptySchema = ds;
function Mn(e, t) {
  const { gen: r, data: n } = e, a = {
    gen: r,
    keyword: "false schema",
    data: n,
    schema: !1,
    schemaCode: !1,
    schemaValue: !1,
    params: {},
    it: e
  };
  (0, ss.reportError)(a, cs, void 0, t);
}
var J = {}, Ie = {};
Object.defineProperty(Ie, "__esModule", { value: !0 });
Ie.getRules = Ie.isJSONType = void 0;
const ls = ["string", "number", "integer", "boolean", "null", "object", "array"], fs = new Set(ls);
function ps(e) {
  return typeof e == "string" && fs.has(e);
}
Ie.isJSONType = ps;
function ms() {
  const e = {
    number: { type: "number", rules: [] },
    string: { type: "string", rules: [] },
    array: { type: "array", rules: [] },
    object: { type: "object", rules: [] }
  };
  return {
    types: { ...e, integer: !0, boolean: !0, null: !0 },
    rules: [{ rules: [] }, e.number, e.string, e.array, e.object],
    post: { rules: [] },
    all: {},
    keywords: {}
  };
}
Ie.getRules = ms;
var ge = {};
Object.defineProperty(ge, "__esModule", { value: !0 });
ge.shouldUseRule = ge.shouldUseGroup = ge.schemaHasRulesForType = void 0;
function hs({ schema: e, self: t }, r) {
  const n = t.RULES.types[r];
  return n && n !== !0 && zn(e, n);
}
ge.schemaHasRulesForType = hs;
function zn(e, t) {
  return t.rules.some((r) => Dn(e, r));
}
ge.shouldUseGroup = zn;
function Dn(e, t) {
  var r;
  return e[t.keyword] !== void 0 || ((r = t.definition.implements) === null || r === void 0 ? void 0 : r.some((n) => e[n] !== void 0));
}
ge.shouldUseRule = Dn;
Object.defineProperty(J, "__esModule", { value: !0 });
J.reportTypeError = J.checkDataTypes = J.checkDataType = J.coerceAndCheckDataType = J.getJSONTypes = J.getSchemaTypes = J.DataType = void 0;
const ys = Ie, gs = ge, $s = rt, M = z, Vn = N;
var De;
(function(e) {
  e[e.Correct = 0] = "Correct", e[e.Wrong = 1] = "Wrong";
})(De || (J.DataType = De = {}));
function _s(e) {
  const t = Fn(e.type);
  if (t.includes("null")) {
    if (e.nullable === !1)
      throw new Error("type: null contradicts nullable: false");
  } else {
    if (!t.length && e.nullable !== void 0)
      throw new Error('"nullable" cannot be used without "type"');
    e.nullable === !0 && t.push("null");
  }
  return t;
}
J.getSchemaTypes = _s;
function Fn(e) {
  const t = Array.isArray(e) ? e : e ? [e] : [];
  if (t.every(ys.isJSONType))
    return t;
  throw new Error("type must be JSONType or JSONType[]: " + t.join(","));
}
J.getJSONTypes = Fn;
function vs(e, t) {
  const { gen: r, data: n, opts: a } = e, s = bs(t, a.coerceTypes), i = t.length > 0 && !(s.length === 0 && t.length === 1 && (0, gs.schemaHasRulesForType)(e, t[0]));
  if (i) {
    const c = ar(t, n, a.strictNumbers, De.Wrong);
    r.if(c, () => {
      s.length ? ws(e, t, s) : sr(e);
    });
  }
  return i;
}
J.coerceAndCheckDataType = vs;
const qn = /* @__PURE__ */ new Set(["string", "number", "integer", "boolean", "null"]);
function bs(e, t) {
  return t ? e.filter((r) => qn.has(r) || t === "array" && r === "array") : [];
}
function ws(e, t, r) {
  const { gen: n, data: a, opts: s } = e, i = n.let("dataType", (0, M._)`typeof ${a}`), c = n.let("coerced", (0, M._)`undefined`);
  s.coerceTypes === "array" && n.if((0, M._)`${i} == 'object' && Array.isArray(${a}) && ${a}.length == 1`, () => n.assign(a, (0, M._)`${a}[0]`).assign(i, (0, M._)`typeof ${a}`).if(ar(t, a, s.strictNumbers), () => n.assign(c, a))), n.if((0, M._)`${c} !== undefined`);
  for (const u of r)
    (qn.has(u) || u === "array" && s.coerceTypes === "array") && l(u);
  n.else(), sr(e), n.endIf(), n.if((0, M._)`${c} !== undefined`, () => {
    n.assign(a, c), Ss(e, c);
  });
  function l(u) {
    switch (u) {
      case "string":
        n.elseIf((0, M._)`${i} == "number" || ${i} == "boolean"`).assign(c, (0, M._)`"" + ${a}`).elseIf((0, M._)`${a} === null`).assign(c, (0, M._)`""`);
        return;
      case "number":
        n.elseIf((0, M._)`${i} == "boolean" || ${a} === null
              || (${i} == "string" && ${a} && ${a} == +${a})`).assign(c, (0, M._)`+${a}`);
        return;
      case "integer":
        n.elseIf((0, M._)`${i} === "boolean" || ${a} === null
              || (${i} === "string" && ${a} && ${a} == +${a} && !(${a} % 1))`).assign(c, (0, M._)`+${a}`);
        return;
      case "boolean":
        n.elseIf((0, M._)`${a} === "false" || ${a} === 0 || ${a} === null`).assign(c, !1).elseIf((0, M._)`${a} === "true" || ${a} === 1`).assign(c, !0);
        return;
      case "null":
        n.elseIf((0, M._)`${a} === "" || ${a} === 0 || ${a} === false`), n.assign(c, null);
        return;
      case "array":
        n.elseIf((0, M._)`${i} === "string" || ${i} === "number"
              || ${i} === "boolean" || ${a} === null`).assign(c, (0, M._)`[${a}]`);
    }
  }
}
function Ss({ gen: e, parentData: t, parentDataProperty: r }, n) {
  e.if((0, M._)`${t} !== undefined`, () => e.assign((0, M._)`${t}[${r}]`, n));
}
function Zt(e, t, r, n = De.Correct) {
  const a = n === De.Correct ? M.operators.EQ : M.operators.NEQ;
  let s;
  switch (e) {
    case "null":
      return (0, M._)`${t} ${a} null`;
    case "array":
      s = (0, M._)`Array.isArray(${t})`;
      break;
    case "object":
      s = (0, M._)`${t} && typeof ${t} == "object" && !Array.isArray(${t})`;
      break;
    case "integer":
      s = i((0, M._)`!(${t} % 1) && !isNaN(${t})`);
      break;
    case "number":
      s = i();
      break;
    default:
      return (0, M._)`typeof ${t} ${a} ${e}`;
  }
  return n === De.Correct ? s : (0, M.not)(s);
  function i(c = M.nil) {
    return (0, M.and)((0, M._)`typeof ${t} == "number"`, c, r ? (0, M._)`isFinite(${t})` : M.nil);
  }
}
J.checkDataType = Zt;
function ar(e, t, r, n) {
  if (e.length === 1)
    return Zt(e[0], t, r, n);
  let a;
  const s = (0, Vn.toHash)(e);
  if (s.array && s.object) {
    const i = (0, M._)`typeof ${t} != "object"`;
    a = s.null ? i : (0, M._)`!${t} || ${i}`, delete s.null, delete s.array, delete s.object;
  } else
    a = M.nil;
  s.number && delete s.integer;
  for (const i in s)
    a = (0, M.and)(a, Zt(i, t, r, n));
  return a;
}
J.checkDataTypes = ar;
const Es = {
  message: ({ schema: e }) => `must be ${e}`,
  params: ({ schema: e, schemaValue: t }) => typeof e == "string" ? (0, M._)`{type: ${e}}` : (0, M._)`{type: ${t}}`
};
function sr(e) {
  const t = Ps(e);
  (0, $s.reportError)(t, Es);
}
J.reportTypeError = sr;
function Ps(e) {
  const { gen: t, data: r, schema: n } = e, a = (0, Vn.schemaRefOrVal)(e, n, "type");
  return {
    gen: t,
    keyword: "type",
    data: r,
    schema: n.type,
    schemaCode: a,
    schemaValue: a,
    parentSchema: n,
    params: {},
    it: e
  };
}
var bt = {};
Object.defineProperty(bt, "__esModule", { value: !0 });
bt.assignDefaults = void 0;
const Ae = z, ks = N;
function js(e, t) {
  const { properties: r, items: n } = e.schema;
  if (t === "object" && r)
    for (const a in r)
      en(e, a, r[a].default);
  else t === "array" && Array.isArray(n) && n.forEach((a, s) => en(e, s, a.default));
}
bt.assignDefaults = js;
function en(e, t, r) {
  const { gen: n, compositeRule: a, data: s, opts: i } = e;
  if (r === void 0)
    return;
  const c = (0, Ae._)`${s}${(0, Ae.getProperty)(t)}`;
  if (a) {
    (0, ks.checkStrictMode)(e, `default is ignored for: ${c}`);
    return;
  }
  let l = (0, Ae._)`${c} === undefined`;
  i.useDefaults === "empty" && (l = (0, Ae._)`${l} || ${c} === null || ${c} === ""`), n.if(l, (0, Ae._)`${c} = ${(0, Ae.stringify)(r)}`);
}
var le = {}, V = {};
Object.defineProperty(V, "__esModule", { value: !0 });
V.validateUnion = V.validateArray = V.usePattern = V.callValidateCode = V.schemaProperties = V.allSchemaProperties = V.noPropertyInData = V.propertyInData = V.isOwnProperty = V.hasPropFunc = V.reportMissingProp = V.checkMissingProp = V.checkReportMissingProp = void 0;
const K = z, or = N, be = pe, Ns = N;
function Os(e, t) {
  const { gen: r, data: n, it: a } = e;
  r.if(cr(r, n, t, a.opts.ownProperties), () => {
    e.setParams({ missingProperty: (0, K._)`${t}` }, !0), e.error();
  });
}
V.checkReportMissingProp = Os;
function Ts({ gen: e, data: t, it: { opts: r } }, n, a) {
  return (0, K.or)(...n.map((s) => (0, K.and)(cr(e, t, s, r.ownProperties), (0, K._)`${a} = ${s}`)));
}
V.checkMissingProp = Ts;
function Is(e, t) {
  e.setParams({ missingProperty: t }, !0), e.error();
}
V.reportMissingProp = Is;
function xn(e) {
  return e.scopeValue("func", {
    // eslint-disable-next-line @typescript-eslint/unbound-method
    ref: Object.prototype.hasOwnProperty,
    code: (0, K._)`Object.prototype.hasOwnProperty`
  });
}
V.hasPropFunc = xn;
function ir(e, t, r) {
  return (0, K._)`${xn(e)}.call(${t}, ${r})`;
}
V.isOwnProperty = ir;
function Rs(e, t, r, n) {
  const a = (0, K._)`${t}${(0, K.getProperty)(r)} !== undefined`;
  return n ? (0, K._)`${a} && ${ir(e, t, r)}` : a;
}
V.propertyInData = Rs;
function cr(e, t, r, n) {
  const a = (0, K._)`${t}${(0, K.getProperty)(r)} === undefined`;
  return n ? (0, K.or)(a, (0, K.not)(ir(e, t, r))) : a;
}
V.noPropertyInData = cr;
function Un(e) {
  return e ? Object.keys(e).filter((t) => t !== "__proto__") : [];
}
V.allSchemaProperties = Un;
function Cs(e, t) {
  return Un(t).filter((r) => !(0, or.alwaysValidSchema)(e, t[r]));
}
V.schemaProperties = Cs;
function As({ schemaCode: e, data: t, it: { gen: r, topSchemaRef: n, schemaPath: a, errorPath: s }, it: i }, c, l, u) {
  const f = u ? (0, K._)`${e}, ${t}, ${n}${a}` : t, m = [
    [be.default.instancePath, (0, K.strConcat)(be.default.instancePath, s)],
    [be.default.parentData, i.parentData],
    [be.default.parentDataProperty, i.parentDataProperty],
    [be.default.rootData, be.default.rootData]
  ];
  i.opts.dynamicRef && m.push([be.default.dynamicAnchors, be.default.dynamicAnchors]);
  const S = (0, K._)`${f}, ${r.object(...m)}`;
  return l !== K.nil ? (0, K._)`${c}.call(${l}, ${S})` : (0, K._)`${c}(${S})`;
}
V.callValidateCode = As;
const Ms = (0, K._)`new RegExp`;
function zs({ gen: e, it: { opts: t } }, r) {
  const n = t.unicodeRegExp ? "u" : "", { regExp: a } = t.code, s = a(r, n);
  return e.scopeValue("pattern", {
    key: s.toString(),
    ref: s,
    code: (0, K._)`${a.code === "new RegExp" ? Ms : (0, Ns.useFunc)(e, a)}(${r}, ${n})`
  });
}
V.usePattern = zs;
function Ds(e) {
  const { gen: t, data: r, keyword: n, it: a } = e, s = t.name("valid");
  if (a.allErrors) {
    const c = t.let("valid", !0);
    return i(() => t.assign(c, !1)), c;
  }
  return t.var(s, !0), i(() => t.break()), s;
  function i(c) {
    const l = t.const("len", (0, K._)`${r}.length`);
    t.forRange("i", 0, l, (u) => {
      e.subschema({
        keyword: n,
        dataProp: u,
        dataPropType: or.Type.Num
      }, s), t.if((0, K.not)(s), c);
    });
  }
}
V.validateArray = Ds;
function Vs(e) {
  const { gen: t, schema: r, keyword: n, it: a } = e;
  if (!Array.isArray(r))
    throw new Error("ajv implementation error");
  if (r.some((l) => (0, or.alwaysValidSchema)(a, l)) && !a.opts.unevaluated)
    return;
  const i = t.let("valid", !1), c = t.name("_valid");
  t.block(() => r.forEach((l, u) => {
    const f = e.subschema({
      keyword: n,
      schemaProp: u,
      compositeRule: !0
    }, c);
    t.assign(i, (0, K._)`${i} || ${c}`), e.mergeValidEvaluated(f, c) || t.if((0, K.not)(i));
  })), e.result(i, () => e.reset(), () => e.error(!0));
}
V.validateUnion = Vs;
Object.defineProperty(le, "__esModule", { value: !0 });
le.validateKeywordUsage = le.validSchemaType = le.funcKeywordCode = le.macroKeywordCode = void 0;
const ee = z, Ne = pe, Fs = V, qs = rt;
function xs(e, t) {
  const { gen: r, keyword: n, schema: a, parentSchema: s, it: i } = e, c = t.macro.call(i.self, a, s, i), l = Ln(r, n, c);
  i.opts.validateSchema !== !1 && i.self.validateSchema(c, !0);
  const u = r.name("valid");
  e.subschema({
    schema: c,
    schemaPath: ee.nil,
    errSchemaPath: `${i.errSchemaPath}/${n}`,
    topSchemaRef: l,
    compositeRule: !0
  }, u), e.pass(u, () => e.error(!0));
}
le.macroKeywordCode = xs;
function Us(e, t) {
  var r;
  const { gen: n, keyword: a, schema: s, parentSchema: i, $data: c, it: l } = e;
  Ks(l, t);
  const u = !c && t.compile ? t.compile.call(l.self, s, i, l) : t.validate, f = Ln(n, a, u), m = n.let("valid");
  e.block$data(m, S), e.ok((r = t.valid) !== null && r !== void 0 ? r : m);
  function S() {
    if (t.errors === !1)
      v(), t.modifying && tn(e), y(() => e.error());
    else {
      const p = t.async ? b() : _();
      t.modifying && tn(e), y(() => Ls(e, p));
    }
  }
  function b() {
    const p = n.let("ruleErrs", null);
    return n.try(() => v((0, ee._)`await `), ($) => n.assign(m, !1).if((0, ee._)`${$} instanceof ${l.ValidationError}`, () => n.assign(p, (0, ee._)`${$}.errors`), () => n.throw($))), p;
  }
  function _() {
    const p = (0, ee._)`${f}.errors`;
    return n.assign(p, null), v(ee.nil), p;
  }
  function v(p = t.async ? (0, ee._)`await ` : ee.nil) {
    const $ = l.opts.passContext ? Ne.default.this : Ne.default.self, P = !("compile" in t && !c || t.schema === !1);
    n.assign(m, (0, ee._)`${p}${(0, Fs.callValidateCode)(e, f, $, P)}`, t.modifying);
  }
  function y(p) {
    var $;
    n.if((0, ee.not)(($ = t.valid) !== null && $ !== void 0 ? $ : m), p);
  }
}
le.funcKeywordCode = Us;
function tn(e) {
  const { gen: t, data: r, it: n } = e;
  t.if(n.parentData, () => t.assign(r, (0, ee._)`${n.parentData}[${n.parentDataProperty}]`));
}
function Ls(e, t) {
  const { gen: r } = e;
  r.if((0, ee._)`Array.isArray(${t})`, () => {
    r.assign(Ne.default.vErrors, (0, ee._)`${Ne.default.vErrors} === null ? ${t} : ${Ne.default.vErrors}.concat(${t})`).assign(Ne.default.errors, (0, ee._)`${Ne.default.vErrors}.length`), (0, qs.extendErrors)(e);
  }, () => e.error());
}
function Ks({ schemaEnv: e }, t) {
  if (t.async && !e.$async)
    throw new Error("async keyword in sync schema");
}
function Ln(e, t, r) {
  if (r === void 0)
    throw new Error(`keyword "${t}" failed to compile`);
  return e.scopeValue("keyword", typeof r == "function" ? { ref: r } : { ref: r, code: (0, ee.stringify)(r) });
}
function Hs(e, t, r = !1) {
  return !t.length || t.some((n) => n === "array" ? Array.isArray(e) : n === "object" ? e && typeof e == "object" && !Array.isArray(e) : typeof e == n || r && typeof e > "u");
}
le.validSchemaType = Hs;
function Gs({ schema: e, opts: t, self: r, errSchemaPath: n }, a, s) {
  if (Array.isArray(a.keyword) ? !a.keyword.includes(s) : a.keyword !== s)
    throw new Error("ajv implementation error");
  const i = a.dependencies;
  if (i != null && i.some((c) => !Object.prototype.hasOwnProperty.call(e, c)))
    throw new Error(`parent schema must have dependencies of ${s}: ${i.join(",")}`);
  if (a.validateSchema && !a.validateSchema(e[s])) {
    const l = `keyword "${s}" value is invalid at path "${n}": ` + r.errorsText(a.validateSchema.errors);
    if (t.validateSchema === "log")
      r.logger.error(l);
    else
      throw new Error(l);
  }
}
le.validateKeywordUsage = Gs;
var Pe = {};
Object.defineProperty(Pe, "__esModule", { value: !0 });
Pe.extendSubschemaMode = Pe.extendSubschemaData = Pe.getSubschema = void 0;
const de = z, Kn = N;
function Bs(e, { keyword: t, schemaProp: r, schema: n, schemaPath: a, errSchemaPath: s, topSchemaRef: i }) {
  if (t !== void 0 && n !== void 0)
    throw new Error('both "keyword" and "schema" passed, only one allowed');
  if (t !== void 0) {
    const c = e.schema[t];
    return r === void 0 ? {
      schema: c,
      schemaPath: (0, de._)`${e.schemaPath}${(0, de.getProperty)(t)}`,
      errSchemaPath: `${e.errSchemaPath}/${t}`
    } : {
      schema: c[r],
      schemaPath: (0, de._)`${e.schemaPath}${(0, de.getProperty)(t)}${(0, de.getProperty)(r)}`,
      errSchemaPath: `${e.errSchemaPath}/${t}/${(0, Kn.escapeFragment)(r)}`
    };
  }
  if (n !== void 0) {
    if (a === void 0 || s === void 0 || i === void 0)
      throw new Error('"schemaPath", "errSchemaPath" and "topSchemaRef" are required with "schema"');
    return {
      schema: n,
      schemaPath: a,
      topSchemaRef: i,
      errSchemaPath: s
    };
  }
  throw new Error('either "keyword" or "schema" must be passed');
}
Pe.getSubschema = Bs;
function Js(e, t, { dataProp: r, dataPropType: n, data: a, dataTypes: s, propertyName: i }) {
  if (a !== void 0 && r !== void 0)
    throw new Error('both "data" and "dataProp" passed, only one allowed');
  const { gen: c } = t;
  if (r !== void 0) {
    const { errorPath: u, dataPathArr: f, opts: m } = t, S = c.let("data", (0, de._)`${t.data}${(0, de.getProperty)(r)}`, !0);
    l(S), e.errorPath = (0, de.str)`${u}${(0, Kn.getErrorPath)(r, n, m.jsPropertySyntax)}`, e.parentDataProperty = (0, de._)`${r}`, e.dataPathArr = [...f, e.parentDataProperty];
  }
  if (a !== void 0) {
    const u = a instanceof de.Name ? a : c.let("data", a, !0);
    l(u), i !== void 0 && (e.propertyName = i);
  }
  s && (e.dataTypes = s);
  function l(u) {
    e.data = u, e.dataLevel = t.dataLevel + 1, e.dataTypes = [], t.definedProperties = /* @__PURE__ */ new Set(), e.parentData = t.data, e.dataNames = [...t.dataNames, u];
  }
}
Pe.extendSubschemaData = Js;
function Ws(e, { jtdDiscriminator: t, jtdMetadata: r, compositeRule: n, createErrors: a, allErrors: s }) {
  n !== void 0 && (e.compositeRule = n), a !== void 0 && (e.createErrors = a), s !== void 0 && (e.allErrors = s), e.jtdDiscriminator = t, e.jtdMetadata = r;
}
Pe.extendSubschemaMode = Ws;
var Y = {}, Hn = function e(t, r) {
  if (t === r) return !0;
  if (t && r && typeof t == "object" && typeof r == "object") {
    if (t.constructor !== r.constructor) return !1;
    var n, a, s;
    if (Array.isArray(t)) {
      if (n = t.length, n != r.length) return !1;
      for (a = n; a-- !== 0; )
        if (!e(t[a], r[a])) return !1;
      return !0;
    }
    if (t.constructor === RegExp) return t.source === r.source && t.flags === r.flags;
    if (t.valueOf !== Object.prototype.valueOf) return t.valueOf() === r.valueOf();
    if (t.toString !== Object.prototype.toString) return t.toString() === r.toString();
    if (s = Object.keys(t), n = s.length, n !== Object.keys(r).length) return !1;
    for (a = n; a-- !== 0; )
      if (!Object.prototype.hasOwnProperty.call(r, s[a])) return !1;
    for (a = n; a-- !== 0; ) {
      var i = s[a];
      if (!e(t[i], r[i])) return !1;
    }
    return !0;
  }
  return t !== t && r !== r;
}, Gn = { exports: {} }, Ee = Gn.exports = function(e, t, r) {
  typeof t == "function" && (r = t, t = {}), r = t.cb || r;
  var n = typeof r == "function" ? r : r.pre || function() {
  }, a = r.post || function() {
  };
  lt(t, n, a, e, "", e);
};
Ee.keywords = {
  additionalItems: !0,
  items: !0,
  contains: !0,
  additionalProperties: !0,
  propertyNames: !0,
  not: !0,
  if: !0,
  then: !0,
  else: !0
};
Ee.arrayKeywords = {
  items: !0,
  allOf: !0,
  anyOf: !0,
  oneOf: !0
};
Ee.propsKeywords = {
  $defs: !0,
  definitions: !0,
  properties: !0,
  patternProperties: !0,
  dependencies: !0
};
Ee.skipKeywords = {
  default: !0,
  enum: !0,
  const: !0,
  required: !0,
  maximum: !0,
  minimum: !0,
  exclusiveMaximum: !0,
  exclusiveMinimum: !0,
  multipleOf: !0,
  maxLength: !0,
  minLength: !0,
  pattern: !0,
  format: !0,
  maxItems: !0,
  minItems: !0,
  uniqueItems: !0,
  maxProperties: !0,
  minProperties: !0
};
function lt(e, t, r, n, a, s, i, c, l, u) {
  if (n && typeof n == "object" && !Array.isArray(n)) {
    t(n, a, s, i, c, l, u);
    for (var f in n) {
      var m = n[f];
      if (Array.isArray(m)) {
        if (f in Ee.arrayKeywords)
          for (var S = 0; S < m.length; S++)
            lt(e, t, r, m[S], a + "/" + f + "/" + S, s, a, f, n, S);
      } else if (f in Ee.propsKeywords) {
        if (m && typeof m == "object")
          for (var b in m)
            lt(e, t, r, m[b], a + "/" + f + "/" + Zs(b), s, a, f, n, b);
      } else (f in Ee.keywords || e.allKeys && !(f in Ee.skipKeywords)) && lt(e, t, r, m, a + "/" + f, s, a, f, n);
    }
    r(n, a, s, i, c, l, u);
  }
}
function Zs(e) {
  return e.replace(/~/g, "~0").replace(/\//g, "~1");
}
var Xs = Gn.exports;
Object.defineProperty(Y, "__esModule", { value: !0 });
Y.getSchemaRefs = Y.resolveUrl = Y.normalizeId = Y._getFullPath = Y.getFullPath = Y.inlineRef = void 0;
const Ys = N, Qs = Hn, eo = Xs, to = /* @__PURE__ */ new Set([
  "type",
  "format",
  "pattern",
  "maxLength",
  "minLength",
  "maxProperties",
  "minProperties",
  "maxItems",
  "minItems",
  "maximum",
  "minimum",
  "uniqueItems",
  "multipleOf",
  "required",
  "enum",
  "const"
]);
function ro(e, t = !0) {
  return typeof e == "boolean" ? !0 : t === !0 ? !Xt(e) : t ? Bn(e) <= t : !1;
}
Y.inlineRef = ro;
const no = /* @__PURE__ */ new Set([
  "$ref",
  "$recursiveRef",
  "$recursiveAnchor",
  "$dynamicRef",
  "$dynamicAnchor"
]);
function Xt(e) {
  for (const t in e) {
    if (no.has(t))
      return !0;
    const r = e[t];
    if (Array.isArray(r) && r.some(Xt) || typeof r == "object" && Xt(r))
      return !0;
  }
  return !1;
}
function Bn(e) {
  let t = 0;
  for (const r in e) {
    if (r === "$ref")
      return 1 / 0;
    if (t++, !to.has(r) && (typeof e[r] == "object" && (0, Ys.eachItem)(e[r], (n) => t += Bn(n)), t === 1 / 0))
      return 1 / 0;
  }
  return t;
}
function Jn(e, t = "", r) {
  r !== !1 && (t = Ve(t));
  const n = e.parse(t);
  return Wn(e, n);
}
Y.getFullPath = Jn;
function Wn(e, t) {
  return e.serialize(t).split("#")[0] + "#";
}
Y._getFullPath = Wn;
const ao = /#\/?$/;
function Ve(e) {
  return e ? e.replace(ao, "") : "";
}
Y.normalizeId = Ve;
function so(e, t, r) {
  return r = Ve(r), e.resolve(t, r);
}
Y.resolveUrl = so;
const oo = /^[a-z_][-a-z0-9._]*$/i;
function io(e, t) {
  if (typeof e == "boolean")
    return {};
  const { schemaId: r, uriResolver: n } = this.opts, a = Ve(e[r] || t), s = { "": a }, i = Jn(n, a, !1), c = {}, l = /* @__PURE__ */ new Set();
  return eo(e, { allKeys: !0 }, (m, S, b, _) => {
    if (_ === void 0)
      return;
    const v = i + S;
    let y = s[_];
    typeof m[r] == "string" && (y = p.call(this, m[r])), $.call(this, m.$anchor), $.call(this, m.$dynamicAnchor), s[S] = y;
    function p(P) {
      const j = this.opts.uriResolver.resolve;
      if (P = Ve(y ? j(y, P) : P), l.has(P))
        throw f(P);
      l.add(P);
      let O = this.refs[P];
      return typeof O == "string" && (O = this.refs[O]), typeof O == "object" ? u(m, O.schema, P) : P !== Ve(v) && (P[0] === "#" ? (u(m, c[P], P), c[P] = m) : this.refs[P] = v), P;
    }
    function $(P) {
      if (typeof P == "string") {
        if (!oo.test(P))
          throw new Error(`invalid anchor "${P}"`);
        p.call(this, `#${P}`);
      }
    }
  }), c;
  function u(m, S, b) {
    if (S !== void 0 && !Qs(m, S))
      throw f(b);
  }
  function f(m) {
    return new Error(`reference "${m}" resolves to more than one schema`);
  }
}
Y.getSchemaRefs = io;
Object.defineProperty(ce, "__esModule", { value: !0 });
ce.getData = ce.KeywordCxt = ce.validateFunctionCode = void 0;
const Zn = Fe, rn = J, ur = ge, yt = J, co = bt, Ze = le, xt = Pe, T = z, A = pe, uo = Y, $e = N, Ge = rt;
function lo(e) {
  if (Qn(e) && (ea(e), Yn(e))) {
    mo(e);
    return;
  }
  Xn(e, () => (0, Zn.topBoolOrEmptySchema)(e));
}
ce.validateFunctionCode = lo;
function Xn({ gen: e, validateName: t, schema: r, schemaEnv: n, opts: a }, s) {
  a.code.es5 ? e.func(t, (0, T._)`${A.default.data}, ${A.default.valCxt}`, n.$async, () => {
    e.code((0, T._)`"use strict"; ${nn(r, a)}`), po(e, a), e.code(s);
  }) : e.func(t, (0, T._)`${A.default.data}, ${fo(a)}`, n.$async, () => e.code(nn(r, a)).code(s));
}
function fo(e) {
  return (0, T._)`{${A.default.instancePath}="", ${A.default.parentData}, ${A.default.parentDataProperty}, ${A.default.rootData}=${A.default.data}${e.dynamicRef ? (0, T._)`, ${A.default.dynamicAnchors}={}` : T.nil}}={}`;
}
function po(e, t) {
  e.if(A.default.valCxt, () => {
    e.var(A.default.instancePath, (0, T._)`${A.default.valCxt}.${A.default.instancePath}`), e.var(A.default.parentData, (0, T._)`${A.default.valCxt}.${A.default.parentData}`), e.var(A.default.parentDataProperty, (0, T._)`${A.default.valCxt}.${A.default.parentDataProperty}`), e.var(A.default.rootData, (0, T._)`${A.default.valCxt}.${A.default.rootData}`), t.dynamicRef && e.var(A.default.dynamicAnchors, (0, T._)`${A.default.valCxt}.${A.default.dynamicAnchors}`);
  }, () => {
    e.var(A.default.instancePath, (0, T._)`""`), e.var(A.default.parentData, (0, T._)`undefined`), e.var(A.default.parentDataProperty, (0, T._)`undefined`), e.var(A.default.rootData, A.default.data), t.dynamicRef && e.var(A.default.dynamicAnchors, (0, T._)`{}`);
  });
}
function mo(e) {
  const { schema: t, opts: r, gen: n } = e;
  Xn(e, () => {
    r.$comment && t.$comment && ra(e), _o(e), n.let(A.default.vErrors, null), n.let(A.default.errors, 0), r.unevaluated && ho(e), ta(e), wo(e);
  });
}
function ho(e) {
  const { gen: t, validateName: r } = e;
  e.evaluated = t.const("evaluated", (0, T._)`${r}.evaluated`), t.if((0, T._)`${e.evaluated}.dynamicProps`, () => t.assign((0, T._)`${e.evaluated}.props`, (0, T._)`undefined`)), t.if((0, T._)`${e.evaluated}.dynamicItems`, () => t.assign((0, T._)`${e.evaluated}.items`, (0, T._)`undefined`));
}
function nn(e, t) {
  const r = typeof e == "object" && e[t.schemaId];
  return r && (t.code.source || t.code.process) ? (0, T._)`/*# sourceURL=${r} */` : T.nil;
}
function yo(e, t) {
  if (Qn(e) && (ea(e), Yn(e))) {
    go(e, t);
    return;
  }
  (0, Zn.boolOrEmptySchema)(e, t);
}
function Yn({ schema: e, self: t }) {
  if (typeof e == "boolean")
    return !e;
  for (const r in e)
    if (t.RULES.all[r])
      return !0;
  return !1;
}
function Qn(e) {
  return typeof e.schema != "boolean";
}
function go(e, t) {
  const { schema: r, gen: n, opts: a } = e;
  a.$comment && r.$comment && ra(e), vo(e), bo(e);
  const s = n.const("_errs", A.default.errors);
  ta(e, s), n.var(t, (0, T._)`${s} === ${A.default.errors}`);
}
function ea(e) {
  (0, $e.checkUnknownRules)(e), $o(e);
}
function ta(e, t) {
  if (e.opts.jtd)
    return an(e, [], !1, t);
  const r = (0, rn.getSchemaTypes)(e.schema), n = (0, rn.coerceAndCheckDataType)(e, r);
  an(e, r, !n, t);
}
function $o(e) {
  const { schema: t, errSchemaPath: r, opts: n, self: a } = e;
  t.$ref && n.ignoreKeywordsWithRef && (0, $e.schemaHasRulesButRef)(t, a.RULES) && a.logger.warn(`$ref: keywords ignored in schema at path "${r}"`);
}
function _o(e) {
  const { schema: t, opts: r } = e;
  t.default !== void 0 && r.useDefaults && r.strictSchema && (0, $e.checkStrictMode)(e, "default is ignored in the schema root");
}
function vo(e) {
  const t = e.schema[e.opts.schemaId];
  t && (e.baseId = (0, uo.resolveUrl)(e.opts.uriResolver, e.baseId, t));
}
function bo(e) {
  if (e.schema.$async && !e.schemaEnv.$async)
    throw new Error("async schema in sync schema");
}
function ra({ gen: e, schemaEnv: t, schema: r, errSchemaPath: n, opts: a }) {
  const s = r.$comment;
  if (a.$comment === !0)
    e.code((0, T._)`${A.default.self}.logger.log(${s})`);
  else if (typeof a.$comment == "function") {
    const i = (0, T.str)`${n}/$comment`, c = e.scopeValue("root", { ref: t.root });
    e.code((0, T._)`${A.default.self}.opts.$comment(${s}, ${i}, ${c}.schema)`);
  }
}
function wo(e) {
  const { gen: t, schemaEnv: r, validateName: n, ValidationError: a, opts: s } = e;
  r.$async ? t.if((0, T._)`${A.default.errors} === 0`, () => t.return(A.default.data), () => t.throw((0, T._)`new ${a}(${A.default.vErrors})`)) : (t.assign((0, T._)`${n}.errors`, A.default.vErrors), s.unevaluated && So(e), t.return((0, T._)`${A.default.errors} === 0`));
}
function So({ gen: e, evaluated: t, props: r, items: n }) {
  r instanceof T.Name && e.assign((0, T._)`${t}.props`, r), n instanceof T.Name && e.assign((0, T._)`${t}.items`, n);
}
function an(e, t, r, n) {
  const { gen: a, schema: s, data: i, allErrors: c, opts: l, self: u } = e, { RULES: f } = u;
  if (s.$ref && (l.ignoreKeywordsWithRef || !(0, $e.schemaHasRulesButRef)(s, f))) {
    a.block(() => sa(e, "$ref", f.all.$ref.definition));
    return;
  }
  l.jtd || Eo(e, t), a.block(() => {
    for (const S of f.rules)
      m(S);
    m(f.post);
  });
  function m(S) {
    (0, ur.shouldUseGroup)(s, S) && (S.type ? (a.if((0, yt.checkDataType)(S.type, i, l.strictNumbers)), sn(e, S), t.length === 1 && t[0] === S.type && r && (a.else(), (0, yt.reportTypeError)(e)), a.endIf()) : sn(e, S), c || a.if((0, T._)`${A.default.errors} === ${n || 0}`));
  }
}
function sn(e, t) {
  const { gen: r, schema: n, opts: { useDefaults: a } } = e;
  a && (0, co.assignDefaults)(e, t.type), r.block(() => {
    for (const s of t.rules)
      (0, ur.shouldUseRule)(n, s) && sa(e, s.keyword, s.definition, t.type);
  });
}
function Eo(e, t) {
  e.schemaEnv.meta || !e.opts.strictTypes || (Po(e, t), e.opts.allowUnionTypes || ko(e, t), jo(e, e.dataTypes));
}
function Po(e, t) {
  if (t.length) {
    if (!e.dataTypes.length) {
      e.dataTypes = t;
      return;
    }
    t.forEach((r) => {
      na(e.dataTypes, r) || dr(e, `type "${r}" not allowed by context "${e.dataTypes.join(",")}"`);
    }), Oo(e, t);
  }
}
function ko(e, t) {
  t.length > 1 && !(t.length === 2 && t.includes("null")) && dr(e, "use allowUnionTypes to allow union type keyword");
}
function jo(e, t) {
  const r = e.self.RULES.all;
  for (const n in r) {
    const a = r[n];
    if (typeof a == "object" && (0, ur.shouldUseRule)(e.schema, a)) {
      const { type: s } = a.definition;
      s.length && !s.some((i) => No(t, i)) && dr(e, `missing type "${s.join(",")}" for keyword "${n}"`);
    }
  }
}
function No(e, t) {
  return e.includes(t) || t === "number" && e.includes("integer");
}
function na(e, t) {
  return e.includes(t) || t === "integer" && e.includes("number");
}
function Oo(e, t) {
  const r = [];
  for (const n of e.dataTypes)
    na(t, n) ? r.push(n) : t.includes("integer") && n === "number" && r.push("integer");
  e.dataTypes = r;
}
function dr(e, t) {
  const r = e.schemaEnv.baseId + e.errSchemaPath;
  t += ` at "${r}" (strictTypes)`, (0, $e.checkStrictMode)(e, t, e.opts.strictTypes);
}
class aa {
  constructor(t, r, n) {
    if ((0, Ze.validateKeywordUsage)(t, r, n), this.gen = t.gen, this.allErrors = t.allErrors, this.keyword = n, this.data = t.data, this.schema = t.schema[n], this.$data = r.$data && t.opts.$data && this.schema && this.schema.$data, this.schemaValue = (0, $e.schemaRefOrVal)(t, this.schema, n, this.$data), this.schemaType = r.schemaType, this.parentSchema = t.schema, this.params = {}, this.it = t, this.def = r, this.$data)
      this.schemaCode = t.gen.const("vSchema", oa(this.$data, t));
    else if (this.schemaCode = this.schemaValue, !(0, Ze.validSchemaType)(this.schema, r.schemaType, r.allowUndefined))
      throw new Error(`${n} value must be ${JSON.stringify(r.schemaType)}`);
    ("code" in r ? r.trackErrors : r.errors !== !1) && (this.errsCount = t.gen.const("_errs", A.default.errors));
  }
  result(t, r, n) {
    this.failResult((0, T.not)(t), r, n);
  }
  failResult(t, r, n) {
    this.gen.if(t), n ? n() : this.error(), r ? (this.gen.else(), r(), this.allErrors && this.gen.endIf()) : this.allErrors ? this.gen.endIf() : this.gen.else();
  }
  pass(t, r) {
    this.failResult((0, T.not)(t), void 0, r);
  }
  fail(t) {
    if (t === void 0) {
      this.error(), this.allErrors || this.gen.if(!1);
      return;
    }
    this.gen.if(t), this.error(), this.allErrors ? this.gen.endIf() : this.gen.else();
  }
  fail$data(t) {
    if (!this.$data)
      return this.fail(t);
    const { schemaCode: r } = this;
    this.fail((0, T._)`${r} !== undefined && (${(0, T.or)(this.invalid$data(), t)})`);
  }
  error(t, r, n) {
    if (r) {
      this.setParams(r), this._error(t, n), this.setParams({});
      return;
    }
    this._error(t, n);
  }
  _error(t, r) {
    (t ? Ge.reportExtraError : Ge.reportError)(this, this.def.error, r);
  }
  $dataError() {
    (0, Ge.reportError)(this, this.def.$dataError || Ge.keyword$DataError);
  }
  reset() {
    if (this.errsCount === void 0)
      throw new Error('add "trackErrors" to keyword definition');
    (0, Ge.resetErrorsCount)(this.gen, this.errsCount);
  }
  ok(t) {
    this.allErrors || this.gen.if(t);
  }
  setParams(t, r) {
    r ? Object.assign(this.params, t) : this.params = t;
  }
  block$data(t, r, n = T.nil) {
    this.gen.block(() => {
      this.check$data(t, n), r();
    });
  }
  check$data(t = T.nil, r = T.nil) {
    if (!this.$data)
      return;
    const { gen: n, schemaCode: a, schemaType: s, def: i } = this;
    n.if((0, T.or)((0, T._)`${a} === undefined`, r)), t !== T.nil && n.assign(t, !0), (s.length || i.validateSchema) && (n.elseIf(this.invalid$data()), this.$dataError(), t !== T.nil && n.assign(t, !1)), n.else();
  }
  invalid$data() {
    const { gen: t, schemaCode: r, schemaType: n, def: a, it: s } = this;
    return (0, T.or)(i(), c());
    function i() {
      if (n.length) {
        if (!(r instanceof T.Name))
          throw new Error("ajv implementation error");
        const l = Array.isArray(n) ? n : [n];
        return (0, T._)`${(0, yt.checkDataTypes)(l, r, s.opts.strictNumbers, yt.DataType.Wrong)}`;
      }
      return T.nil;
    }
    function c() {
      if (a.validateSchema) {
        const l = t.scopeValue("validate$data", { ref: a.validateSchema });
        return (0, T._)`!${l}(${r})`;
      }
      return T.nil;
    }
  }
  subschema(t, r) {
    const n = (0, xt.getSubschema)(this.it, t);
    (0, xt.extendSubschemaData)(n, this.it, t), (0, xt.extendSubschemaMode)(n, t);
    const a = { ...this.it, ...n, items: void 0, props: void 0 };
    return yo(a, r), a;
  }
  mergeEvaluated(t, r) {
    const { it: n, gen: a } = this;
    n.opts.unevaluated && (n.props !== !0 && t.props !== void 0 && (n.props = $e.mergeEvaluated.props(a, t.props, n.props, r)), n.items !== !0 && t.items !== void 0 && (n.items = $e.mergeEvaluated.items(a, t.items, n.items, r)));
  }
  mergeValidEvaluated(t, r) {
    const { it: n, gen: a } = this;
    if (n.opts.unevaluated && (n.props !== !0 || n.items !== !0))
      return a.if(r, () => this.mergeEvaluated(t, T.Name)), !0;
  }
}
ce.KeywordCxt = aa;
function sa(e, t, r, n) {
  const a = new aa(e, r, t);
  "code" in r ? r.code(a, n) : a.$data && r.validate ? (0, Ze.funcKeywordCode)(a, r) : "macro" in r ? (0, Ze.macroKeywordCode)(a, r) : (r.compile || r.validate) && (0, Ze.funcKeywordCode)(a, r);
}
const To = /^\/(?:[^~]|~0|~1)*$/, Io = /^([0-9]+)(#|\/(?:[^~]|~0|~1)*)?$/;
function oa(e, { dataLevel: t, dataNames: r, dataPathArr: n }) {
  let a, s;
  if (e === "")
    return A.default.rootData;
  if (e[0] === "/") {
    if (!To.test(e))
      throw new Error(`Invalid JSON-pointer: ${e}`);
    a = e, s = A.default.rootData;
  } else {
    const u = Io.exec(e);
    if (!u)
      throw new Error(`Invalid JSON-pointer: ${e}`);
    const f = +u[1];
    if (a = u[2], a === "#") {
      if (f >= t)
        throw new Error(l("property/index", f));
      return n[t - f];
    }
    if (f > t)
      throw new Error(l("data", f));
    if (s = r[t - f], !a)
      return s;
  }
  let i = s;
  const c = a.split("/");
  for (const u of c)
    u && (s = (0, T._)`${s}${(0, T.getProperty)((0, $e.unescapeJsonPointer)(u))}`, i = (0, T._)`${i} && ${s}`);
  return i;
  function l(u, f) {
    return `Cannot access ${u} ${f} levels up, current level is ${t}`;
  }
}
ce.getData = oa;
var nt = {};
Object.defineProperty(nt, "__esModule", { value: !0 });
class Ro extends Error {
  constructor(t) {
    super("validation failed"), this.errors = t, this.ajv = this.validation = !0;
  }
}
nt.default = Ro;
var xe = {};
Object.defineProperty(xe, "__esModule", { value: !0 });
const Ut = Y;
class Co extends Error {
  constructor(t, r, n, a) {
    super(a || `can't resolve reference ${n} from id ${r}`), this.missingRef = (0, Ut.resolveUrl)(t, r, n), this.missingSchema = (0, Ut.normalizeId)((0, Ut.getFullPath)(t, this.missingRef));
  }
}
xe.default = Co;
var re = {};
Object.defineProperty(re, "__esModule", { value: !0 });
re.resolveSchema = re.getCompilingSchema = re.resolveRef = re.compileSchema = re.SchemaEnv = void 0;
const se = z, Ao = nt, je = pe, ie = Y, on = N, Mo = ce;
class wt {
  constructor(t) {
    var r;
    this.refs = {}, this.dynamicAnchors = {};
    let n;
    typeof t.schema == "object" && (n = t.schema), this.schema = t.schema, this.schemaId = t.schemaId, this.root = t.root || this, this.baseId = (r = t.baseId) !== null && r !== void 0 ? r : (0, ie.normalizeId)(n == null ? void 0 : n[t.schemaId || "$id"]), this.schemaPath = t.schemaPath, this.localRefs = t.localRefs, this.meta = t.meta, this.$async = n == null ? void 0 : n.$async, this.refs = {};
  }
}
re.SchemaEnv = wt;
function lr(e) {
  const t = ia.call(this, e);
  if (t)
    return t;
  const r = (0, ie.getFullPath)(this.opts.uriResolver, e.root.baseId), { es5: n, lines: a } = this.opts.code, { ownProperties: s } = this.opts, i = new se.CodeGen(this.scope, { es5: n, lines: a, ownProperties: s });
  let c;
  e.$async && (c = i.scopeValue("Error", {
    ref: Ao.default,
    code: (0, se._)`require("ajv/dist/runtime/validation_error").default`
  }));
  const l = i.scopeName("validate");
  e.validateName = l;
  const u = {
    gen: i,
    allErrors: this.opts.allErrors,
    data: je.default.data,
    parentData: je.default.parentData,
    parentDataProperty: je.default.parentDataProperty,
    dataNames: [je.default.data],
    dataPathArr: [se.nil],
    // TODO can its length be used as dataLevel if nil is removed?
    dataLevel: 0,
    dataTypes: [],
    definedProperties: /* @__PURE__ */ new Set(),
    topSchemaRef: i.scopeValue("schema", this.opts.code.source === !0 ? { ref: e.schema, code: (0, se.stringify)(e.schema) } : { ref: e.schema }),
    validateName: l,
    ValidationError: c,
    schema: e.schema,
    schemaEnv: e,
    rootId: r,
    baseId: e.baseId || r,
    schemaPath: se.nil,
    errSchemaPath: e.schemaPath || (this.opts.jtd ? "" : "#"),
    errorPath: (0, se._)`""`,
    opts: this.opts,
    self: this
  };
  let f;
  try {
    this._compilations.add(e), (0, Mo.validateFunctionCode)(u), i.optimize(this.opts.code.optimize);
    const m = i.toString();
    f = `${i.scopeRefs(je.default.scope)}return ${m}`, this.opts.code.process && (f = this.opts.code.process(f, e));
    const b = new Function(`${je.default.self}`, `${je.default.scope}`, f)(this, this.scope.get());
    if (this.scope.value(l, { ref: b }), b.errors = null, b.schema = e.schema, b.schemaEnv = e, e.$async && (b.$async = !0), this.opts.code.source === !0 && (b.source = { validateName: l, validateCode: m, scopeValues: i._values }), this.opts.unevaluated) {
      const { props: _, items: v } = u;
      b.evaluated = {
        props: _ instanceof se.Name ? void 0 : _,
        items: v instanceof se.Name ? void 0 : v,
        dynamicProps: _ instanceof se.Name,
        dynamicItems: v instanceof se.Name
      }, b.source && (b.source.evaluated = (0, se.stringify)(b.evaluated));
    }
    return e.validate = b, e;
  } catch (m) {
    throw delete e.validate, delete e.validateName, f && this.logger.error("Error compiling schema, function code:", f), m;
  } finally {
    this._compilations.delete(e);
  }
}
re.compileSchema = lr;
function zo(e, t, r) {
  var n;
  r = (0, ie.resolveUrl)(this.opts.uriResolver, t, r);
  const a = e.refs[r];
  if (a)
    return a;
  let s = Fo.call(this, e, r);
  if (s === void 0) {
    const i = (n = e.localRefs) === null || n === void 0 ? void 0 : n[r], { schemaId: c } = this.opts;
    i && (s = new wt({ schema: i, schemaId: c, root: e, baseId: t }));
  }
  if (s !== void 0)
    return e.refs[r] = Do.call(this, s);
}
re.resolveRef = zo;
function Do(e) {
  return (0, ie.inlineRef)(e.schema, this.opts.inlineRefs) ? e.schema : e.validate ? e : lr.call(this, e);
}
function ia(e) {
  for (const t of this._compilations)
    if (Vo(t, e))
      return t;
}
re.getCompilingSchema = ia;
function Vo(e, t) {
  return e.schema === t.schema && e.root === t.root && e.baseId === t.baseId;
}
function Fo(e, t) {
  let r;
  for (; typeof (r = this.refs[t]) == "string"; )
    t = r;
  return r || this.schemas[t] || St.call(this, e, t);
}
function St(e, t) {
  const r = this.opts.uriResolver.parse(t), n = (0, ie._getFullPath)(this.opts.uriResolver, r);
  let a = (0, ie.getFullPath)(this.opts.uriResolver, e.baseId, void 0);
  if (Object.keys(e.schema).length > 0 && n === a)
    return Lt.call(this, r, e);
  const s = (0, ie.normalizeId)(n), i = this.refs[s] || this.schemas[s];
  if (typeof i == "string") {
    const c = St.call(this, e, i);
    return typeof (c == null ? void 0 : c.schema) != "object" ? void 0 : Lt.call(this, r, c);
  }
  if (typeof (i == null ? void 0 : i.schema) == "object") {
    if (i.validate || lr.call(this, i), s === (0, ie.normalizeId)(t)) {
      const { schema: c } = i, { schemaId: l } = this.opts, u = c[l];
      return u && (a = (0, ie.resolveUrl)(this.opts.uriResolver, a, u)), new wt({ schema: c, schemaId: l, root: e, baseId: a });
    }
    return Lt.call(this, r, i);
  }
}
re.resolveSchema = St;
const qo = /* @__PURE__ */ new Set([
  "properties",
  "patternProperties",
  "enum",
  "dependencies",
  "definitions"
]);
function Lt(e, { baseId: t, schema: r, root: n }) {
  var a;
  if (((a = e.fragment) === null || a === void 0 ? void 0 : a[0]) !== "/")
    return;
  for (const c of e.fragment.slice(1).split("/")) {
    if (typeof r == "boolean")
      return;
    const l = r[(0, on.unescapeFragment)(c)];
    if (l === void 0)
      return;
    r = l;
    const u = typeof r == "object" && r[this.opts.schemaId];
    !qo.has(c) && u && (t = (0, ie.resolveUrl)(this.opts.uriResolver, t, u));
  }
  let s;
  if (typeof r != "boolean" && r.$ref && !(0, on.schemaHasRulesButRef)(r, this.RULES)) {
    const c = (0, ie.resolveUrl)(this.opts.uriResolver, t, r.$ref);
    s = St.call(this, n, c);
  }
  const { schemaId: i } = this.opts;
  if (s = s || new wt({ schema: r, schemaId: i, root: n, baseId: t }), s.schema !== s.root.schema)
    return s;
}
const xo = "https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#", Uo = "Meta-schema for $data reference (JSON AnySchema extension proposal)", Lo = "object", Ko = [
  "$data"
], Ho = {
  $data: {
    type: "string",
    anyOf: [
      {
        format: "relative-json-pointer"
      },
      {
        format: "json-pointer"
      }
    ]
  }
}, Go = !1, Bo = {
  $id: xo,
  description: Uo,
  type: Lo,
  required: Ko,
  properties: Ho,
  additionalProperties: Go
};
var fr = {}, Et = { exports: {} };
const Jo = {
  0: 0,
  1: 1,
  2: 2,
  3: 3,
  4: 4,
  5: 5,
  6: 6,
  7: 7,
  8: 8,
  9: 9,
  a: 10,
  A: 10,
  b: 11,
  B: 11,
  c: 12,
  C: 12,
  d: 13,
  D: 13,
  e: 14,
  E: 14,
  f: 15,
  F: 15
};
var Wo = {
  HEX: Jo
};
const { HEX: Zo } = Wo, Xo = /^(?:(?:25[0-5]|2[0-4]\d|1\d{2}|[1-9]\d|\d)\.){3}(?:25[0-5]|2[0-4]\d|1\d{2}|[1-9]\d|\d)$/u;
function ca(e) {
  if (da(e, ".") < 3)
    return { host: e, isIPV4: !1 };
  const t = e.match(Xo) || [], [r] = t;
  return r ? { host: Qo(r, "."), isIPV4: !0 } : { host: e, isIPV4: !1 };
}
function cn(e, t = !1) {
  let r = "", n = !0;
  for (const a of e) {
    if (Zo[a] === void 0) return;
    a !== "0" && n === !0 && (n = !1), n || (r += a);
  }
  return t && r.length === 0 && (r = "0"), r;
}
function Yo(e) {
  let t = 0;
  const r = { error: !1, address: "", zone: "" }, n = [], a = [];
  let s = !1, i = !1, c = !1;
  function l() {
    if (a.length) {
      if (s === !1) {
        const u = cn(a);
        if (u !== void 0)
          n.push(u);
        else
          return r.error = !0, !1;
      }
      a.length = 0;
    }
    return !0;
  }
  for (let u = 0; u < e.length; u++) {
    const f = e[u];
    if (!(f === "[" || f === "]"))
      if (f === ":") {
        if (i === !0 && (c = !0), !l())
          break;
        if (t++, n.push(":"), t > 7) {
          r.error = !0;
          break;
        }
        u - 1 >= 0 && e[u - 1] === ":" && (i = !0);
        continue;
      } else if (f === "%") {
        if (!l())
          break;
        s = !0;
      } else {
        a.push(f);
        continue;
      }
  }
  return a.length && (s ? r.zone = a.join("") : c ? n.push(a.join("")) : n.push(cn(a))), r.address = n.join(""), r;
}
function ua(e) {
  if (da(e, ":") < 2)
    return { host: e, isIPV6: !1 };
  const t = Yo(e);
  if (t.error)
    return { host: e, isIPV6: !1 };
  {
    let r = t.address, n = t.address;
    return t.zone && (r += "%" + t.zone, n += "%25" + t.zone), { host: r, escapedHost: n, isIPV6: !0 };
  }
}
function Qo(e, t) {
  let r = "", n = !0;
  const a = e.length;
  for (let s = 0; s < a; s++) {
    const i = e[s];
    i === "0" && n ? (s + 1 <= a && e[s + 1] === t || s + 1 === a) && (r += i, n = !1) : (i === t ? n = !0 : n = !1, r += i);
  }
  return r;
}
function da(e, t) {
  let r = 0;
  for (let n = 0; n < e.length; n++)
    e[n] === t && r++;
  return r;
}
const un = /^\.\.?\//u, dn = /^\/\.(?:\/|$)/u, ln = /^\/\.\.(?:\/|$)/u, ei = /^\/?(?:.|\n)*?(?=\/|$)/u;
function ti(e) {
  const t = [];
  for (; e.length; )
    if (e.match(un))
      e = e.replace(un, "");
    else if (e.match(dn))
      e = e.replace(dn, "/");
    else if (e.match(ln))
      e = e.replace(ln, "/"), t.pop();
    else if (e === "." || e === "..")
      e = "";
    else {
      const r = e.match(ei);
      if (r) {
        const n = r[0];
        e = e.slice(n.length), t.push(n);
      } else
        throw new Error("Unexpected dot segment condition");
    }
  return t.join("");
}
function ri(e, t) {
  const r = t !== !0 ? escape : unescape;
  return e.scheme !== void 0 && (e.scheme = r(e.scheme)), e.userinfo !== void 0 && (e.userinfo = r(e.userinfo)), e.host !== void 0 && (e.host = r(e.host)), e.path !== void 0 && (e.path = r(e.path)), e.query !== void 0 && (e.query = r(e.query)), e.fragment !== void 0 && (e.fragment = r(e.fragment)), e;
}
function ni(e) {
  const t = [];
  if (e.userinfo !== void 0 && (t.push(e.userinfo), t.push("@")), e.host !== void 0) {
    let r = unescape(e.host);
    const n = ca(r);
    if (n.isIPV4)
      r = n.host;
    else {
      const a = ua(n.host);
      a.isIPV6 === !0 ? r = `[${a.escapedHost}]` : r = e.host;
    }
    t.push(r);
  }
  return (typeof e.port == "number" || typeof e.port == "string") && (t.push(":"), t.push(String(e.port))), t.length ? t.join("") : void 0;
}
var ai = {
  recomposeAuthority: ni,
  normalizeComponentEncoding: ri,
  removeDotSegments: ti,
  normalizeIPv4: ca,
  normalizeIPv6: ua
};
const si = /^[\da-f]{8}-[\da-f]{4}-[\da-f]{4}-[\da-f]{4}-[\da-f]{12}$/iu, oi = /([\da-z][\d\-a-z]{0,31}):((?:[\w!$'()*+,\-.:;=@]|%[\da-f]{2})+)/iu;
function la(e) {
  return typeof e.secure == "boolean" ? e.secure : String(e.scheme).toLowerCase() === "wss";
}
function fa(e) {
  return e.host || (e.error = e.error || "HTTP URIs must have a host."), e;
}
function pa(e) {
  const t = String(e.scheme).toLowerCase() === "https";
  return (e.port === (t ? 443 : 80) || e.port === "") && (e.port = void 0), e.path || (e.path = "/"), e;
}
function ii(e) {
  return e.secure = la(e), e.resourceName = (e.path || "/") + (e.query ? "?" + e.query : ""), e.path = void 0, e.query = void 0, e;
}
function ci(e) {
  if ((e.port === (la(e) ? 443 : 80) || e.port === "") && (e.port = void 0), typeof e.secure == "boolean" && (e.scheme = e.secure ? "wss" : "ws", e.secure = void 0), e.resourceName) {
    const [t, r] = e.resourceName.split("?");
    e.path = t && t !== "/" ? t : void 0, e.query = r, e.resourceName = void 0;
  }
  return e.fragment = void 0, e;
}
function ui(e, t) {
  if (!e.path)
    return e.error = "URN can not be parsed", e;
  const r = e.path.match(oi);
  if (r) {
    const n = t.scheme || e.scheme || "urn";
    e.nid = r[1].toLowerCase(), e.nss = r[2];
    const a = `${n}:${t.nid || e.nid}`, s = pr[a];
    e.path = void 0, s && (e = s.parse(e, t));
  } else
    e.error = e.error || "URN can not be parsed.";
  return e;
}
function di(e, t) {
  const r = t.scheme || e.scheme || "urn", n = e.nid.toLowerCase(), a = `${r}:${t.nid || n}`, s = pr[a];
  s && (e = s.serialize(e, t));
  const i = e, c = e.nss;
  return i.path = `${n || t.nid}:${c}`, t.skipEscape = !0, i;
}
function li(e, t) {
  const r = e;
  return r.uuid = r.nss, r.nss = void 0, !t.tolerant && (!r.uuid || !si.test(r.uuid)) && (r.error = r.error || "UUID is not valid."), r;
}
function fi(e) {
  const t = e;
  return t.nss = (e.uuid || "").toLowerCase(), t;
}
const ma = {
  scheme: "http",
  domainHost: !0,
  parse: fa,
  serialize: pa
}, pi = {
  scheme: "https",
  domainHost: ma.domainHost,
  parse: fa,
  serialize: pa
}, ft = {
  scheme: "ws",
  domainHost: !0,
  parse: ii,
  serialize: ci
}, mi = {
  scheme: "wss",
  domainHost: ft.domainHost,
  parse: ft.parse,
  serialize: ft.serialize
}, hi = {
  scheme: "urn",
  parse: ui,
  serialize: di,
  skipNormalize: !0
}, yi = {
  scheme: "urn:uuid",
  parse: li,
  serialize: fi,
  skipNormalize: !0
}, pr = {
  http: ma,
  https: pi,
  ws: ft,
  wss: mi,
  urn: hi,
  "urn:uuid": yi
};
var gi = pr;
const { normalizeIPv6: $i, normalizeIPv4: _i, removeDotSegments: Je, recomposeAuthority: vi, normalizeComponentEncoding: st } = ai, mr = gi;
function bi(e, t) {
  return typeof e == "string" ? e = fe(_e(e, t), t) : typeof e == "object" && (e = _e(fe(e, t), t)), e;
}
function wi(e, t, r) {
  const n = Object.assign({ scheme: "null" }, r), a = ha(_e(e, n), _e(t, n), n, !0);
  return fe(a, { ...n, skipEscape: !0 });
}
function ha(e, t, r, n) {
  const a = {};
  return n || (e = _e(fe(e, r), r), t = _e(fe(t, r), r)), r = r || {}, !r.tolerant && t.scheme ? (a.scheme = t.scheme, a.userinfo = t.userinfo, a.host = t.host, a.port = t.port, a.path = Je(t.path || ""), a.query = t.query) : (t.userinfo !== void 0 || t.host !== void 0 || t.port !== void 0 ? (a.userinfo = t.userinfo, a.host = t.host, a.port = t.port, a.path = Je(t.path || ""), a.query = t.query) : (t.path ? (t.path.charAt(0) === "/" ? a.path = Je(t.path) : ((e.userinfo !== void 0 || e.host !== void 0 || e.port !== void 0) && !e.path ? a.path = "/" + t.path : e.path ? a.path = e.path.slice(0, e.path.lastIndexOf("/") + 1) + t.path : a.path = t.path, a.path = Je(a.path)), a.query = t.query) : (a.path = e.path, t.query !== void 0 ? a.query = t.query : a.query = e.query), a.userinfo = e.userinfo, a.host = e.host, a.port = e.port), a.scheme = e.scheme), a.fragment = t.fragment, a;
}
function Si(e, t, r) {
  return typeof e == "string" ? (e = unescape(e), e = fe(st(_e(e, r), !0), { ...r, skipEscape: !0 })) : typeof e == "object" && (e = fe(st(e, !0), { ...r, skipEscape: !0 })), typeof t == "string" ? (t = unescape(t), t = fe(st(_e(t, r), !0), { ...r, skipEscape: !0 })) : typeof t == "object" && (t = fe(st(t, !0), { ...r, skipEscape: !0 })), e.toLowerCase() === t.toLowerCase();
}
function fe(e, t) {
  const r = {
    host: e.host,
    scheme: e.scheme,
    userinfo: e.userinfo,
    port: e.port,
    path: e.path,
    query: e.query,
    nid: e.nid,
    nss: e.nss,
    uuid: e.uuid,
    fragment: e.fragment,
    reference: e.reference,
    resourceName: e.resourceName,
    secure: e.secure,
    error: ""
  }, n = Object.assign({}, t), a = [], s = mr[(n.scheme || r.scheme || "").toLowerCase()];
  s && s.serialize && s.serialize(r, n), r.path !== void 0 && (n.skipEscape ? r.path = unescape(r.path) : (r.path = escape(r.path), r.scheme !== void 0 && (r.path = r.path.split("%3A").join(":")))), n.reference !== "suffix" && r.scheme && a.push(r.scheme, ":");
  const i = vi(r);
  if (i !== void 0 && (n.reference !== "suffix" && a.push("//"), a.push(i), r.path && r.path.charAt(0) !== "/" && a.push("/")), r.path !== void 0) {
    let c = r.path;
    !n.absolutePath && (!s || !s.absolutePath) && (c = Je(c)), i === void 0 && (c = c.replace(/^\/\//u, "/%2F")), a.push(c);
  }
  return r.query !== void 0 && a.push("?", r.query), r.fragment !== void 0 && a.push("#", r.fragment), a.join("");
}
const Ei = Array.from({ length: 127 }, (e, t) => /[^!"$&'()*+,\-.;=_`a-z{}~]/u.test(String.fromCharCode(t)));
function Pi(e) {
  let t = 0;
  for (let r = 0, n = e.length; r < n; ++r)
    if (t = e.charCodeAt(r), t > 126 || Ei[t])
      return !0;
  return !1;
}
const ki = /^(?:([^#/:?]+):)?(?:\/\/((?:([^#/?@]*)@)?(\[[^#/?\]]+\]|[^#/:?]*)(?::(\d*))?))?([^#?]*)(?:\?([^#]*))?(?:#((?:.|[\n\r])*))?/u;
function _e(e, t) {
  const r = Object.assign({}, t), n = {
    scheme: void 0,
    userinfo: void 0,
    host: "",
    port: void 0,
    path: "",
    query: void 0,
    fragment: void 0
  }, a = e.indexOf("%") !== -1;
  let s = !1;
  r.reference === "suffix" && (e = (r.scheme ? r.scheme + ":" : "") + "//" + e);
  const i = e.match(ki);
  if (i) {
    if (n.scheme = i[1], n.userinfo = i[3], n.host = i[4], n.port = parseInt(i[5], 10), n.path = i[6] || "", n.query = i[7], n.fragment = i[8], isNaN(n.port) && (n.port = i[5]), n.host) {
      const l = _i(n.host);
      if (l.isIPV4 === !1) {
        const u = $i(l.host);
        n.host = u.host.toLowerCase(), s = u.isIPV6;
      } else
        n.host = l.host, s = !0;
    }
    n.scheme === void 0 && n.userinfo === void 0 && n.host === void 0 && n.port === void 0 && n.query === void 0 && !n.path ? n.reference = "same-document" : n.scheme === void 0 ? n.reference = "relative" : n.fragment === void 0 ? n.reference = "absolute" : n.reference = "uri", r.reference && r.reference !== "suffix" && r.reference !== n.reference && (n.error = n.error || "URI is not a " + r.reference + " reference.");
    const c = mr[(r.scheme || n.scheme || "").toLowerCase()];
    if (!r.unicodeSupport && (!c || !c.unicodeSupport) && n.host && (r.domainHost || c && c.domainHost) && s === !1 && Pi(n.host))
      try {
        n.host = URL.domainToASCII(n.host.toLowerCase());
      } catch (l) {
        n.error = n.error || "Host's domain name can not be converted to ASCII: " + l;
      }
    (!c || c && !c.skipNormalize) && (a && n.scheme !== void 0 && (n.scheme = unescape(n.scheme)), a && n.host !== void 0 && (n.host = unescape(n.host)), n.path && (n.path = escape(unescape(n.path))), n.fragment && (n.fragment = encodeURI(decodeURIComponent(n.fragment)))), c && c.parse && c.parse(n, r);
  } else
    n.error = n.error || "URI can not be parsed.";
  return n;
}
const hr = {
  SCHEMES: mr,
  normalize: bi,
  resolve: wi,
  resolveComponents: ha,
  equal: Si,
  serialize: fe,
  parse: _e
};
Et.exports = hr;
Et.exports.default = hr;
Et.exports.fastUri = hr;
var ji = Et.exports;
Object.defineProperty(fr, "__esModule", { value: !0 });
const ya = ji;
ya.code = 'require("ajv/dist/runtime/uri").default';
fr.default = ya;
(function(e) {
  Object.defineProperty(e, "__esModule", { value: !0 }), e.CodeGen = e.Name = e.nil = e.stringify = e.str = e._ = e.KeywordCxt = void 0;
  var t = ce;
  Object.defineProperty(e, "KeywordCxt", { enumerable: !0, get: function() {
    return t.KeywordCxt;
  } });
  var r = z;
  Object.defineProperty(e, "_", { enumerable: !0, get: function() {
    return r._;
  } }), Object.defineProperty(e, "str", { enumerable: !0, get: function() {
    return r.str;
  } }), Object.defineProperty(e, "stringify", { enumerable: !0, get: function() {
    return r.stringify;
  } }), Object.defineProperty(e, "nil", { enumerable: !0, get: function() {
    return r.nil;
  } }), Object.defineProperty(e, "Name", { enumerable: !0, get: function() {
    return r.Name;
  } }), Object.defineProperty(e, "CodeGen", { enumerable: !0, get: function() {
    return r.CodeGen;
  } });
  const n = nt, a = xe, s = Ie, i = re, c = z, l = Y, u = J, f = N, m = Bo, S = fr, b = (k, h) => new RegExp(k, h);
  b.code = "new RegExp";
  const _ = ["removeAdditional", "useDefaults", "coerceTypes"], v = /* @__PURE__ */ new Set([
    "validate",
    "serialize",
    "parse",
    "wrapper",
    "root",
    "schema",
    "keyword",
    "pattern",
    "formats",
    "validate$data",
    "func",
    "obj",
    "Error"
  ]), y = {
    errorDataPath: "",
    format: "`validateFormats: false` can be used instead.",
    nullable: '"nullable" keyword is supported by default.',
    jsonPointers: "Deprecated jsPropertySyntax can be used instead.",
    extendRefs: "Deprecated ignoreKeywordsWithRef can be used instead.",
    missingRefs: "Pass empty schema with $id that should be ignored to ajv.addSchema.",
    processCode: "Use option `code: {process: (code, schemaEnv: object) => string}`",
    sourceCode: "Use option `code: {source: true}`",
    strictDefaults: "It is default now, see option `strict`.",
    strictKeywords: "It is default now, see option `strict`.",
    uniqueItems: '"uniqueItems" keyword is always validated.',
    unknownFormats: "Disable strict mode or pass `true` to `ajv.addFormat` (or `formats` option).",
    cache: "Map is used as cache, schema object as key.",
    serialize: "Map is used as cache, schema object as key.",
    ajvErrors: "It is default now."
  }, p = {
    ignoreKeywordsWithRef: "",
    jsPropertySyntax: "",
    unicode: '"minLength"/"maxLength" account for unicode characters by default.'
  }, $ = 200;
  function P(k) {
    var h, E, g, o, d, w, I, R, x, q, W, Ce, jt, Nt, Ot, Tt, It, Rt, Ct, At, Mt, zt, Dt, Vt, Ft;
    const He = k.strict, qt = (h = k.code) === null || h === void 0 ? void 0 : h.optimize, Wr = qt === !0 || qt === void 0 ? 1 : qt || 0, Zr = (g = (E = k.code) === null || E === void 0 ? void 0 : E.regExp) !== null && g !== void 0 ? g : b, Ia = (o = k.uriResolver) !== null && o !== void 0 ? o : S.default;
    return {
      strictSchema: (w = (d = k.strictSchema) !== null && d !== void 0 ? d : He) !== null && w !== void 0 ? w : !0,
      strictNumbers: (R = (I = k.strictNumbers) !== null && I !== void 0 ? I : He) !== null && R !== void 0 ? R : !0,
      strictTypes: (q = (x = k.strictTypes) !== null && x !== void 0 ? x : He) !== null && q !== void 0 ? q : "log",
      strictTuples: (Ce = (W = k.strictTuples) !== null && W !== void 0 ? W : He) !== null && Ce !== void 0 ? Ce : "log",
      strictRequired: (Nt = (jt = k.strictRequired) !== null && jt !== void 0 ? jt : He) !== null && Nt !== void 0 ? Nt : !1,
      code: k.code ? { ...k.code, optimize: Wr, regExp: Zr } : { optimize: Wr, regExp: Zr },
      loopRequired: (Ot = k.loopRequired) !== null && Ot !== void 0 ? Ot : $,
      loopEnum: (Tt = k.loopEnum) !== null && Tt !== void 0 ? Tt : $,
      meta: (It = k.meta) !== null && It !== void 0 ? It : !0,
      messages: (Rt = k.messages) !== null && Rt !== void 0 ? Rt : !0,
      inlineRefs: (Ct = k.inlineRefs) !== null && Ct !== void 0 ? Ct : !0,
      schemaId: (At = k.schemaId) !== null && At !== void 0 ? At : "$id",
      addUsedSchema: (Mt = k.addUsedSchema) !== null && Mt !== void 0 ? Mt : !0,
      validateSchema: (zt = k.validateSchema) !== null && zt !== void 0 ? zt : !0,
      validateFormats: (Dt = k.validateFormats) !== null && Dt !== void 0 ? Dt : !0,
      unicodeRegExp: (Vt = k.unicodeRegExp) !== null && Vt !== void 0 ? Vt : !0,
      int32range: (Ft = k.int32range) !== null && Ft !== void 0 ? Ft : !0,
      uriResolver: Ia
    };
  }
  class j {
    constructor(h = {}) {
      this.schemas = {}, this.refs = {}, this.formats = {}, this._compilations = /* @__PURE__ */ new Set(), this._loading = {}, this._cache = /* @__PURE__ */ new Map(), h = this.opts = { ...h, ...P(h) };
      const { es5: E, lines: g } = this.opts.code;
      this.scope = new c.ValueScope({ scope: {}, prefixes: v, es5: E, lines: g }), this.logger = D(h.logger);
      const o = h.validateFormats;
      h.validateFormats = !1, this.RULES = (0, s.getRules)(), O.call(this, y, h, "NOT SUPPORTED"), O.call(this, p, h, "DEPRECATED", "warn"), this._metaOpts = me.call(this), h.formats && ne.call(this), this._addVocabularies(), this._addDefaultMetaSchema(), h.keywords && ue.call(this, h.keywords), typeof h.meta == "object" && this.addMetaSchema(h.meta), G.call(this), h.validateFormats = o;
    }
    _addVocabularies() {
      this.addKeyword("$async");
    }
    _addDefaultMetaSchema() {
      const { $data: h, meta: E, schemaId: g } = this.opts;
      let o = m;
      g === "id" && (o = { ...m }, o.id = o.$id, delete o.$id), E && h && this.addMetaSchema(o, o[g], !1);
    }
    defaultMeta() {
      const { meta: h, schemaId: E } = this.opts;
      return this.opts.defaultMeta = typeof h == "object" ? h[E] || h : void 0;
    }
    validate(h, E) {
      let g;
      if (typeof h == "string") {
        if (g = this.getSchema(h), !g)
          throw new Error(`no schema with key or ref "${h}"`);
      } else
        g = this.compile(h);
      const o = g(E);
      return "$async" in g || (this.errors = g.errors), o;
    }
    compile(h, E) {
      const g = this._addSchema(h, E);
      return g.validate || this._compileSchemaEnv(g);
    }
    compileAsync(h, E) {
      if (typeof this.opts.loadSchema != "function")
        throw new Error("options.loadSchema should be a function");
      const { loadSchema: g } = this.opts;
      return o.call(this, h, E);
      async function o(q, W) {
        await d.call(this, q.$schema);
        const Ce = this._addSchema(q, W);
        return Ce.validate || w.call(this, Ce);
      }
      async function d(q) {
        q && !this.getSchema(q) && await o.call(this, { $ref: q }, !0);
      }
      async function w(q) {
        try {
          return this._compileSchemaEnv(q);
        } catch (W) {
          if (!(W instanceof a.default))
            throw W;
          return I.call(this, W), await R.call(this, W.missingSchema), w.call(this, q);
        }
      }
      function I({ missingSchema: q, missingRef: W }) {
        if (this.refs[q])
          throw new Error(`AnySchema ${q} is loaded but ${W} cannot be resolved`);
      }
      async function R(q) {
        const W = await x.call(this, q);
        this.refs[q] || await d.call(this, W.$schema), this.refs[q] || this.addSchema(W, q, E);
      }
      async function x(q) {
        const W = this._loading[q];
        if (W)
          return W;
        try {
          return await (this._loading[q] = g(q));
        } finally {
          delete this._loading[q];
        }
      }
    }
    // Adds schema to the instance
    addSchema(h, E, g, o = this.opts.validateSchema) {
      if (Array.isArray(h)) {
        for (const w of h)
          this.addSchema(w, void 0, g, o);
        return this;
      }
      let d;
      if (typeof h == "object") {
        const { schemaId: w } = this.opts;
        if (d = h[w], d !== void 0 && typeof d != "string")
          throw new Error(`schema ${w} must be string`);
      }
      return E = (0, l.normalizeId)(E || d), this._checkUnique(E), this.schemas[E] = this._addSchema(h, g, E, o, !0), this;
    }
    // Add schema that will be used to validate other schemas
    // options in META_IGNORE_OPTIONS are alway set to false
    addMetaSchema(h, E, g = this.opts.validateSchema) {
      return this.addSchema(h, E, !0, g), this;
    }
    //  Validate schema against its meta-schema
    validateSchema(h, E) {
      if (typeof h == "boolean")
        return !0;
      let g;
      if (g = h.$schema, g !== void 0 && typeof g != "string")
        throw new Error("$schema must be a string");
      if (g = g || this.opts.defaultMeta || this.defaultMeta(), !g)
        return this.logger.warn("meta-schema not available"), this.errors = null, !0;
      const o = this.validate(g, h);
      if (!o && E) {
        const d = "schema is invalid: " + this.errorsText();
        if (this.opts.validateSchema === "log")
          this.logger.error(d);
        else
          throw new Error(d);
      }
      return o;
    }
    // Get compiled schema by `key` or `ref`.
    // (`key` that was passed to `addSchema` or full schema reference - `schema.$id` or resolved id)
    getSchema(h) {
      let E;
      for (; typeof (E = U.call(this, h)) == "string"; )
        h = E;
      if (E === void 0) {
        const { schemaId: g } = this.opts, o = new i.SchemaEnv({ schema: {}, schemaId: g });
        if (E = i.resolveSchema.call(this, o, h), !E)
          return;
        this.refs[h] = E;
      }
      return E.validate || this._compileSchemaEnv(E);
    }
    // Remove cached schema(s).
    // If no parameter is passed all schemas but meta-schemas are removed.
    // If RegExp is passed all schemas with key/id matching pattern but meta-schemas are removed.
    // Even if schema is referenced by other schemas it still can be removed as other schemas have local references.
    removeSchema(h) {
      if (h instanceof RegExp)
        return this._removeAllSchemas(this.schemas, h), this._removeAllSchemas(this.refs, h), this;
      switch (typeof h) {
        case "undefined":
          return this._removeAllSchemas(this.schemas), this._removeAllSchemas(this.refs), this._cache.clear(), this;
        case "string": {
          const E = U.call(this, h);
          return typeof E == "object" && this._cache.delete(E.schema), delete this.schemas[h], delete this.refs[h], this;
        }
        case "object": {
          const E = h;
          this._cache.delete(E);
          let g = h[this.opts.schemaId];
          return g && (g = (0, l.normalizeId)(g), delete this.schemas[g], delete this.refs[g]), this;
        }
        default:
          throw new Error("ajv.removeSchema: invalid parameter");
      }
    }
    // add "vocabulary" - a collection of keywords
    addVocabulary(h) {
      for (const E of h)
        this.addKeyword(E);
      return this;
    }
    addKeyword(h, E) {
      let g;
      if (typeof h == "string")
        g = h, typeof E == "object" && (this.logger.warn("these parameters are deprecated, see docs for addKeyword"), E.keyword = g);
      else if (typeof h == "object" && E === void 0) {
        if (E = h, g = E.keyword, Array.isArray(g) && !g.length)
          throw new Error("addKeywords: keyword must be string or non-empty array");
      } else
        throw new Error("invalid addKeywords parameters");
      if (F.call(this, g, E), !E)
        return (0, f.eachItem)(g, (d) => Z.call(this, d)), this;
      ye.call(this, E);
      const o = {
        ...E,
        type: (0, u.getJSONTypes)(E.type),
        schemaType: (0, u.getJSONTypes)(E.schemaType)
      };
      return (0, f.eachItem)(g, o.type.length === 0 ? (d) => Z.call(this, d, o) : (d) => o.type.forEach((w) => Z.call(this, d, o, w))), this;
    }
    getKeyword(h) {
      const E = this.RULES.all[h];
      return typeof E == "object" ? E.definition : !!E;
    }
    // Remove keyword
    removeKeyword(h) {
      const { RULES: E } = this;
      delete E.keywords[h], delete E.all[h];
      for (const g of E.rules) {
        const o = g.rules.findIndex((d) => d.keyword === h);
        o >= 0 && g.rules.splice(o, 1);
      }
      return this;
    }
    // Add format
    addFormat(h, E) {
      return typeof E == "string" && (E = new RegExp(E)), this.formats[h] = E, this;
    }
    errorsText(h = this.errors, { separator: E = ", ", dataVar: g = "data" } = {}) {
      return !h || h.length === 0 ? "No errors" : h.map((o) => `${g}${o.instancePath} ${o.message}`).reduce((o, d) => o + E + d);
    }
    $dataMetaSchema(h, E) {
      const g = this.RULES.all;
      h = JSON.parse(JSON.stringify(h));
      for (const o of E) {
        const d = o.split("/").slice(1);
        let w = h;
        for (const I of d)
          w = w[I];
        for (const I in g) {
          const R = g[I];
          if (typeof R != "object")
            continue;
          const { $data: x } = R.definition, q = w[I];
          x && q && (w[I] = ke(q));
        }
      }
      return h;
    }
    _removeAllSchemas(h, E) {
      for (const g in h) {
        const o = h[g];
        (!E || E.test(g)) && (typeof o == "string" ? delete h[g] : o && !o.meta && (this._cache.delete(o.schema), delete h[g]));
      }
    }
    _addSchema(h, E, g, o = this.opts.validateSchema, d = this.opts.addUsedSchema) {
      let w;
      const { schemaId: I } = this.opts;
      if (typeof h == "object")
        w = h[I];
      else {
        if (this.opts.jtd)
          throw new Error("schema must be object");
        if (typeof h != "boolean")
          throw new Error("schema must be object or boolean");
      }
      let R = this._cache.get(h);
      if (R !== void 0)
        return R;
      g = (0, l.normalizeId)(w || g);
      const x = l.getSchemaRefs.call(this, h, g);
      return R = new i.SchemaEnv({ schema: h, schemaId: I, meta: E, baseId: g, localRefs: x }), this._cache.set(R.schema, R), d && !g.startsWith("#") && (g && this._checkUnique(g), this.refs[g] = R), o && this.validateSchema(h, !0), R;
    }
    _checkUnique(h) {
      if (this.schemas[h] || this.refs[h])
        throw new Error(`schema with key or id "${h}" already exists`);
    }
    _compileSchemaEnv(h) {
      if (h.meta ? this._compileMetaSchema(h) : i.compileSchema.call(this, h), !h.validate)
        throw new Error("ajv implementation error");
      return h.validate;
    }
    _compileMetaSchema(h) {
      const E = this.opts;
      this.opts = this._metaOpts;
      try {
        i.compileSchema.call(this, h);
      } finally {
        this.opts = E;
      }
    }
  }
  j.ValidationError = n.default, j.MissingRefError = a.default, e.default = j;
  function O(k, h, E, g = "error") {
    for (const o in k) {
      const d = o;
      d in h && this.logger[g](`${E}: option ${o}. ${k[d]}`);
    }
  }
  function U(k) {
    return k = (0, l.normalizeId)(k), this.schemas[k] || this.refs[k];
  }
  function G() {
    const k = this.opts.schemas;
    if (k)
      if (Array.isArray(k))
        this.addSchema(k);
      else
        for (const h in k)
          this.addSchema(k[h], h);
  }
  function ne() {
    for (const k in this.opts.formats) {
      const h = this.opts.formats[k];
      h && this.addFormat(k, h);
    }
  }
  function ue(k) {
    if (Array.isArray(k)) {
      this.addVocabulary(k);
      return;
    }
    this.logger.warn("keywords option as map is deprecated, pass array");
    for (const h in k) {
      const E = k[h];
      E.keyword || (E.keyword = h), this.addKeyword(E);
    }
  }
  function me() {
    const k = { ...this.opts };
    for (const h of _)
      delete k[h];
    return k;
  }
  const C = { log() {
  }, warn() {
  }, error() {
  } };
  function D(k) {
    if (k === !1)
      return C;
    if (k === void 0)
      return console;
    if (k.log && k.warn && k.error)
      return k;
    throw new Error("logger must implement log, warn and error methods");
  }
  const H = /^[a-z_$][a-z0-9_$:-]*$/i;
  function F(k, h) {
    const { RULES: E } = this;
    if ((0, f.eachItem)(k, (g) => {
      if (E.keywords[g])
        throw new Error(`Keyword ${g} is already defined`);
      if (!H.test(g))
        throw new Error(`Keyword ${g} has invalid name`);
    }), !!h && h.$data && !("code" in h || "validate" in h))
      throw new Error('$data keyword must have "code" or "validate" function');
  }
  function Z(k, h, E) {
    var g;
    const o = h == null ? void 0 : h.post;
    if (E && o)
      throw new Error('keyword with "post" flag cannot have "type"');
    const { RULES: d } = this;
    let w = o ? d.post : d.rules.find(({ type: R }) => R === E);
    if (w || (w = { type: E, rules: [] }, d.rules.push(w)), d.keywords[k] = !0, !h)
      return;
    const I = {
      keyword: k,
      definition: {
        ...h,
        type: (0, u.getJSONTypes)(h.type),
        schemaType: (0, u.getJSONTypes)(h.schemaType)
      }
    };
    h.before ? he.call(this, w, I, h.before) : w.rules.push(I), d.all[k] = I, (g = h.implements) === null || g === void 0 || g.forEach((R) => this.addKeyword(R));
  }
  function he(k, h, E) {
    const g = k.rules.findIndex((o) => o.keyword === E);
    g >= 0 ? k.rules.splice(g, 0, h) : (k.rules.push(h), this.logger.warn(`rule ${E} is not defined`));
  }
  function ye(k) {
    let { metaSchema: h } = k;
    h !== void 0 && (k.$data && this.opts.$data && (h = ke(h)), k.validateSchema = this.compile(h, !0));
  }
  const Ke = {
    $ref: "https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#"
  };
  function ke(k) {
    return { anyOf: [k, Ke] };
  }
})(On);
var yr = {}, gr = {}, $r = {};
Object.defineProperty($r, "__esModule", { value: !0 });
const Ni = {
  keyword: "id",
  code() {
    throw new Error('NOT SUPPORTED: keyword "id", use "$id" for schema ID');
  }
};
$r.default = Ni;
var Re = {};
Object.defineProperty(Re, "__esModule", { value: !0 });
Re.callRef = Re.getValidate = void 0;
const Oi = xe, fn = V, te = z, Me = pe, pn = re, ot = N, Ti = {
  keyword: "$ref",
  schemaType: "string",
  code(e) {
    const { gen: t, schema: r, it: n } = e, { baseId: a, schemaEnv: s, validateName: i, opts: c, self: l } = n, { root: u } = s;
    if ((r === "#" || r === "#/") && a === u.baseId)
      return m();
    const f = pn.resolveRef.call(l, u, a, r);
    if (f === void 0)
      throw new Oi.default(n.opts.uriResolver, a, r);
    if (f instanceof pn.SchemaEnv)
      return S(f);
    return b(f);
    function m() {
      if (s === u)
        return pt(e, i, s, s.$async);
      const _ = t.scopeValue("root", { ref: u });
      return pt(e, (0, te._)`${_}.validate`, u, u.$async);
    }
    function S(_) {
      const v = ga(e, _);
      pt(e, v, _, _.$async);
    }
    function b(_) {
      const v = t.scopeValue("schema", c.code.source === !0 ? { ref: _, code: (0, te.stringify)(_) } : { ref: _ }), y = t.name("valid"), p = e.subschema({
        schema: _,
        dataTypes: [],
        schemaPath: te.nil,
        topSchemaRef: v,
        errSchemaPath: r
      }, y);
      e.mergeEvaluated(p), e.ok(y);
    }
  }
};
function ga(e, t) {
  const { gen: r } = e;
  return t.validate ? r.scopeValue("validate", { ref: t.validate }) : (0, te._)`${r.scopeValue("wrapper", { ref: t })}.validate`;
}
Re.getValidate = ga;
function pt(e, t, r, n) {
  const { gen: a, it: s } = e, { allErrors: i, schemaEnv: c, opts: l } = s, u = l.passContext ? Me.default.this : te.nil;
  n ? f() : m();
  function f() {
    if (!c.$async)
      throw new Error("async schema referenced by sync schema");
    const _ = a.let("valid");
    a.try(() => {
      a.code((0, te._)`await ${(0, fn.callValidateCode)(e, t, u)}`), b(t), i || a.assign(_, !0);
    }, (v) => {
      a.if((0, te._)`!(${v} instanceof ${s.ValidationError})`, () => a.throw(v)), S(v), i || a.assign(_, !1);
    }), e.ok(_);
  }
  function m() {
    e.result((0, fn.callValidateCode)(e, t, u), () => b(t), () => S(t));
  }
  function S(_) {
    const v = (0, te._)`${_}.errors`;
    a.assign(Me.default.vErrors, (0, te._)`${Me.default.vErrors} === null ? ${v} : ${Me.default.vErrors}.concat(${v})`), a.assign(Me.default.errors, (0, te._)`${Me.default.vErrors}.length`);
  }
  function b(_) {
    var v;
    if (!s.opts.unevaluated)
      return;
    const y = (v = r == null ? void 0 : r.validate) === null || v === void 0 ? void 0 : v.evaluated;
    if (s.props !== !0)
      if (y && !y.dynamicProps)
        y.props !== void 0 && (s.props = ot.mergeEvaluated.props(a, y.props, s.props));
      else {
        const p = a.var("props", (0, te._)`${_}.evaluated.props`);
        s.props = ot.mergeEvaluated.props(a, p, s.props, te.Name);
      }
    if (s.items !== !0)
      if (y && !y.dynamicItems)
        y.items !== void 0 && (s.items = ot.mergeEvaluated.items(a, y.items, s.items));
      else {
        const p = a.var("items", (0, te._)`${_}.evaluated.items`);
        s.items = ot.mergeEvaluated.items(a, p, s.items, te.Name);
      }
  }
}
Re.callRef = pt;
Re.default = Ti;
Object.defineProperty(gr, "__esModule", { value: !0 });
const Ii = $r, Ri = Re, Ci = [
  "$schema",
  "$id",
  "$defs",
  "$vocabulary",
  { keyword: "$comment" },
  "definitions",
  Ii.default,
  Ri.default
];
gr.default = Ci;
var _r = {}, vr = {};
Object.defineProperty(vr, "__esModule", { value: !0 });
const gt = z, we = gt.operators, $t = {
  maximum: { okStr: "<=", ok: we.LTE, fail: we.GT },
  minimum: { okStr: ">=", ok: we.GTE, fail: we.LT },
  exclusiveMaximum: { okStr: "<", ok: we.LT, fail: we.GTE },
  exclusiveMinimum: { okStr: ">", ok: we.GT, fail: we.LTE }
}, Ai = {
  message: ({ keyword: e, schemaCode: t }) => (0, gt.str)`must be ${$t[e].okStr} ${t}`,
  params: ({ keyword: e, schemaCode: t }) => (0, gt._)`{comparison: ${$t[e].okStr}, limit: ${t}}`
}, Mi = {
  keyword: Object.keys($t),
  type: "number",
  schemaType: "number",
  $data: !0,
  error: Ai,
  code(e) {
    const { keyword: t, data: r, schemaCode: n } = e;
    e.fail$data((0, gt._)`${r} ${$t[t].fail} ${n} || isNaN(${r})`);
  }
};
vr.default = Mi;
var br = {};
Object.defineProperty(br, "__esModule", { value: !0 });
const Xe = z, zi = {
  message: ({ schemaCode: e }) => (0, Xe.str)`must be multiple of ${e}`,
  params: ({ schemaCode: e }) => (0, Xe._)`{multipleOf: ${e}}`
}, Di = {
  keyword: "multipleOf",
  type: "number",
  schemaType: "number",
  $data: !0,
  error: zi,
  code(e) {
    const { gen: t, data: r, schemaCode: n, it: a } = e, s = a.opts.multipleOfPrecision, i = t.let("res"), c = s ? (0, Xe._)`Math.abs(Math.round(${i}) - ${i}) > 1e-${s}` : (0, Xe._)`${i} !== parseInt(${i})`;
    e.fail$data((0, Xe._)`(${n} === 0 || (${i} = ${r}/${n}, ${c}))`);
  }
};
br.default = Di;
var wr = {}, Sr = {};
Object.defineProperty(Sr, "__esModule", { value: !0 });
function $a(e) {
  const t = e.length;
  let r = 0, n = 0, a;
  for (; n < t; )
    r++, a = e.charCodeAt(n++), a >= 55296 && a <= 56319 && n < t && (a = e.charCodeAt(n), (a & 64512) === 56320 && n++);
  return r;
}
Sr.default = $a;
$a.code = 'require("ajv/dist/runtime/ucs2length").default';
Object.defineProperty(wr, "__esModule", { value: !0 });
const Oe = z, Vi = N, Fi = Sr, qi = {
  message({ keyword: e, schemaCode: t }) {
    const r = e === "maxLength" ? "more" : "fewer";
    return (0, Oe.str)`must NOT have ${r} than ${t} characters`;
  },
  params: ({ schemaCode: e }) => (0, Oe._)`{limit: ${e}}`
}, xi = {
  keyword: ["maxLength", "minLength"],
  type: "string",
  schemaType: "number",
  $data: !0,
  error: qi,
  code(e) {
    const { keyword: t, data: r, schemaCode: n, it: a } = e, s = t === "maxLength" ? Oe.operators.GT : Oe.operators.LT, i = a.opts.unicode === !1 ? (0, Oe._)`${r}.length` : (0, Oe._)`${(0, Vi.useFunc)(e.gen, Fi.default)}(${r})`;
    e.fail$data((0, Oe._)`${i} ${s} ${n}`);
  }
};
wr.default = xi;
var Er = {};
Object.defineProperty(Er, "__esModule", { value: !0 });
const Ui = V, _t = z, Li = {
  message: ({ schemaCode: e }) => (0, _t.str)`must match pattern "${e}"`,
  params: ({ schemaCode: e }) => (0, _t._)`{pattern: ${e}}`
}, Ki = {
  keyword: "pattern",
  type: "string",
  schemaType: "string",
  $data: !0,
  error: Li,
  code(e) {
    const { data: t, $data: r, schema: n, schemaCode: a, it: s } = e, i = s.opts.unicodeRegExp ? "u" : "", c = r ? (0, _t._)`(new RegExp(${a}, ${i}))` : (0, Ui.usePattern)(e, n);
    e.fail$data((0, _t._)`!${c}.test(${t})`);
  }
};
Er.default = Ki;
var Pr = {};
Object.defineProperty(Pr, "__esModule", { value: !0 });
const Ye = z, Hi = {
  message({ keyword: e, schemaCode: t }) {
    const r = e === "maxProperties" ? "more" : "fewer";
    return (0, Ye.str)`must NOT have ${r} than ${t} properties`;
  },
  params: ({ schemaCode: e }) => (0, Ye._)`{limit: ${e}}`
}, Gi = {
  keyword: ["maxProperties", "minProperties"],
  type: "object",
  schemaType: "number",
  $data: !0,
  error: Hi,
  code(e) {
    const { keyword: t, data: r, schemaCode: n } = e, a = t === "maxProperties" ? Ye.operators.GT : Ye.operators.LT;
    e.fail$data((0, Ye._)`Object.keys(${r}).length ${a} ${n}`);
  }
};
Pr.default = Gi;
var kr = {};
Object.defineProperty(kr, "__esModule", { value: !0 });
const Be = V, Qe = z, Bi = N, Ji = {
  message: ({ params: { missingProperty: e } }) => (0, Qe.str)`must have required property '${e}'`,
  params: ({ params: { missingProperty: e } }) => (0, Qe._)`{missingProperty: ${e}}`
}, Wi = {
  keyword: "required",
  type: "object",
  schemaType: "array",
  $data: !0,
  error: Ji,
  code(e) {
    const { gen: t, schema: r, schemaCode: n, data: a, $data: s, it: i } = e, { opts: c } = i;
    if (!s && r.length === 0)
      return;
    const l = r.length >= c.loopRequired;
    if (i.allErrors ? u() : f(), c.strictRequired) {
      const b = e.parentSchema.properties, { definedProperties: _ } = e.it;
      for (const v of r)
        if ((b == null ? void 0 : b[v]) === void 0 && !_.has(v)) {
          const y = i.schemaEnv.baseId + i.errSchemaPath, p = `required property "${v}" is not defined at "${y}" (strictRequired)`;
          (0, Bi.checkStrictMode)(i, p, i.opts.strictRequired);
        }
    }
    function u() {
      if (l || s)
        e.block$data(Qe.nil, m);
      else
        for (const b of r)
          (0, Be.checkReportMissingProp)(e, b);
    }
    function f() {
      const b = t.let("missing");
      if (l || s) {
        const _ = t.let("valid", !0);
        e.block$data(_, () => S(b, _)), e.ok(_);
      } else
        t.if((0, Be.checkMissingProp)(e, r, b)), (0, Be.reportMissingProp)(e, b), t.else();
    }
    function m() {
      t.forOf("prop", n, (b) => {
        e.setParams({ missingProperty: b }), t.if((0, Be.noPropertyInData)(t, a, b, c.ownProperties), () => e.error());
      });
    }
    function S(b, _) {
      e.setParams({ missingProperty: b }), t.forOf(b, n, () => {
        t.assign(_, (0, Be.propertyInData)(t, a, b, c.ownProperties)), t.if((0, Qe.not)(_), () => {
          e.error(), t.break();
        });
      }, Qe.nil);
    }
  }
};
kr.default = Wi;
var jr = {};
Object.defineProperty(jr, "__esModule", { value: !0 });
const et = z, Zi = {
  message({ keyword: e, schemaCode: t }) {
    const r = e === "maxItems" ? "more" : "fewer";
    return (0, et.str)`must NOT have ${r} than ${t} items`;
  },
  params: ({ schemaCode: e }) => (0, et._)`{limit: ${e}}`
}, Xi = {
  keyword: ["maxItems", "minItems"],
  type: "array",
  schemaType: "number",
  $data: !0,
  error: Zi,
  code(e) {
    const { keyword: t, data: r, schemaCode: n } = e, a = t === "maxItems" ? et.operators.GT : et.operators.LT;
    e.fail$data((0, et._)`${r}.length ${a} ${n}`);
  }
};
jr.default = Xi;
var Nr = {}, at = {};
Object.defineProperty(at, "__esModule", { value: !0 });
const _a = Hn;
_a.code = 'require("ajv/dist/runtime/equal").default';
at.default = _a;
Object.defineProperty(Nr, "__esModule", { value: !0 });
const Kt = J, X = z, Yi = N, Qi = at, ec = {
  message: ({ params: { i: e, j: t } }) => (0, X.str)`must NOT have duplicate items (items ## ${t} and ${e} are identical)`,
  params: ({ params: { i: e, j: t } }) => (0, X._)`{i: ${e}, j: ${t}}`
}, tc = {
  keyword: "uniqueItems",
  type: "array",
  schemaType: "boolean",
  $data: !0,
  error: ec,
  code(e) {
    const { gen: t, data: r, $data: n, schema: a, parentSchema: s, schemaCode: i, it: c } = e;
    if (!n && !a)
      return;
    const l = t.let("valid"), u = s.items ? (0, Kt.getSchemaTypes)(s.items) : [];
    e.block$data(l, f, (0, X._)`${i} === false`), e.ok(l);
    function f() {
      const _ = t.let("i", (0, X._)`${r}.length`), v = t.let("j");
      e.setParams({ i: _, j: v }), t.assign(l, !0), t.if((0, X._)`${_} > 1`, () => (m() ? S : b)(_, v));
    }
    function m() {
      return u.length > 0 && !u.some((_) => _ === "object" || _ === "array");
    }
    function S(_, v) {
      const y = t.name("item"), p = (0, Kt.checkDataTypes)(u, y, c.opts.strictNumbers, Kt.DataType.Wrong), $ = t.const("indices", (0, X._)`{}`);
      t.for((0, X._)`;${_}--;`, () => {
        t.let(y, (0, X._)`${r}[${_}]`), t.if(p, (0, X._)`continue`), u.length > 1 && t.if((0, X._)`typeof ${y} == "string"`, (0, X._)`${y} += "_"`), t.if((0, X._)`typeof ${$}[${y}] == "number"`, () => {
          t.assign(v, (0, X._)`${$}[${y}]`), e.error(), t.assign(l, !1).break();
        }).code((0, X._)`${$}[${y}] = ${_}`);
      });
    }
    function b(_, v) {
      const y = (0, Yi.useFunc)(t, Qi.default), p = t.name("outer");
      t.label(p).for((0, X._)`;${_}--;`, () => t.for((0, X._)`${v} = ${_}; ${v}--;`, () => t.if((0, X._)`${y}(${r}[${_}], ${r}[${v}])`, () => {
        e.error(), t.assign(l, !1).break(p);
      })));
    }
  }
};
Nr.default = tc;
var Or = {};
Object.defineProperty(Or, "__esModule", { value: !0 });
const Yt = z, rc = N, nc = at, ac = {
  message: "must be equal to constant",
  params: ({ schemaCode: e }) => (0, Yt._)`{allowedValue: ${e}}`
}, sc = {
  keyword: "const",
  $data: !0,
  error: ac,
  code(e) {
    const { gen: t, data: r, $data: n, schemaCode: a, schema: s } = e;
    n || s && typeof s == "object" ? e.fail$data((0, Yt._)`!${(0, rc.useFunc)(t, nc.default)}(${r}, ${a})`) : e.fail((0, Yt._)`${s} !== ${r}`);
  }
};
Or.default = sc;
var Tr = {};
Object.defineProperty(Tr, "__esModule", { value: !0 });
const We = z, oc = N, ic = at, cc = {
  message: "must be equal to one of the allowed values",
  params: ({ schemaCode: e }) => (0, We._)`{allowedValues: ${e}}`
}, uc = {
  keyword: "enum",
  schemaType: "array",
  $data: !0,
  error: cc,
  code(e) {
    const { gen: t, data: r, $data: n, schema: a, schemaCode: s, it: i } = e;
    if (!n && a.length === 0)
      throw new Error("enum must have non-empty array");
    const c = a.length >= i.opts.loopEnum;
    let l;
    const u = () => l ?? (l = (0, oc.useFunc)(t, ic.default));
    let f;
    if (c || n)
      f = t.let("valid"), e.block$data(f, m);
    else {
      if (!Array.isArray(a))
        throw new Error("ajv implementation error");
      const b = t.const("vSchema", s);
      f = (0, We.or)(...a.map((_, v) => S(b, v)));
    }
    e.pass(f);
    function m() {
      t.assign(f, !1), t.forOf("v", s, (b) => t.if((0, We._)`${u()}(${r}, ${b})`, () => t.assign(f, !0).break()));
    }
    function S(b, _) {
      const v = a[_];
      return typeof v == "object" && v !== null ? (0, We._)`${u()}(${r}, ${b}[${_}])` : (0, We._)`${r} === ${v}`;
    }
  }
};
Tr.default = uc;
Object.defineProperty(_r, "__esModule", { value: !0 });
const dc = vr, lc = br, fc = wr, pc = Er, mc = Pr, hc = kr, yc = jr, gc = Nr, $c = Or, _c = Tr, vc = [
  // number
  dc.default,
  lc.default,
  // string
  fc.default,
  pc.default,
  // object
  mc.default,
  hc.default,
  // array
  yc.default,
  gc.default,
  // any
  { keyword: "type", schemaType: ["string", "array"] },
  { keyword: "nullable", schemaType: "boolean" },
  $c.default,
  _c.default
];
_r.default = vc;
var Ir = {}, Ue = {};
Object.defineProperty(Ue, "__esModule", { value: !0 });
Ue.validateAdditionalItems = void 0;
const Te = z, Qt = N, bc = {
  message: ({ params: { len: e } }) => (0, Te.str)`must NOT have more than ${e} items`,
  params: ({ params: { len: e } }) => (0, Te._)`{limit: ${e}}`
}, wc = {
  keyword: "additionalItems",
  type: "array",
  schemaType: ["boolean", "object"],
  before: "uniqueItems",
  error: bc,
  code(e) {
    const { parentSchema: t, it: r } = e, { items: n } = t;
    if (!Array.isArray(n)) {
      (0, Qt.checkStrictMode)(r, '"additionalItems" is ignored when "items" is not an array of schemas');
      return;
    }
    va(e, n);
  }
};
function va(e, t) {
  const { gen: r, schema: n, data: a, keyword: s, it: i } = e;
  i.items = !0;
  const c = r.const("len", (0, Te._)`${a}.length`);
  if (n === !1)
    e.setParams({ len: t.length }), e.pass((0, Te._)`${c} <= ${t.length}`);
  else if (typeof n == "object" && !(0, Qt.alwaysValidSchema)(i, n)) {
    const u = r.var("valid", (0, Te._)`${c} <= ${t.length}`);
    r.if((0, Te.not)(u), () => l(u)), e.ok(u);
  }
  function l(u) {
    r.forRange("i", t.length, c, (f) => {
      e.subschema({ keyword: s, dataProp: f, dataPropType: Qt.Type.Num }, u), i.allErrors || r.if((0, Te.not)(u), () => r.break());
    });
  }
}
Ue.validateAdditionalItems = va;
Ue.default = wc;
var Rr = {}, Le = {};
Object.defineProperty(Le, "__esModule", { value: !0 });
Le.validateTuple = void 0;
const mn = z, mt = N, Sc = V, Ec = {
  keyword: "items",
  type: "array",
  schemaType: ["object", "array", "boolean"],
  before: "uniqueItems",
  code(e) {
    const { schema: t, it: r } = e;
    if (Array.isArray(t))
      return ba(e, "additionalItems", t);
    r.items = !0, !(0, mt.alwaysValidSchema)(r, t) && e.ok((0, Sc.validateArray)(e));
  }
};
function ba(e, t, r = e.schema) {
  const { gen: n, parentSchema: a, data: s, keyword: i, it: c } = e;
  f(a), c.opts.unevaluated && r.length && c.items !== !0 && (c.items = mt.mergeEvaluated.items(n, r.length, c.items));
  const l = n.name("valid"), u = n.const("len", (0, mn._)`${s}.length`);
  r.forEach((m, S) => {
    (0, mt.alwaysValidSchema)(c, m) || (n.if((0, mn._)`${u} > ${S}`, () => e.subschema({
      keyword: i,
      schemaProp: S,
      dataProp: S
    }, l)), e.ok(l));
  });
  function f(m) {
    const { opts: S, errSchemaPath: b } = c, _ = r.length, v = _ === m.minItems && (_ === m.maxItems || m[t] === !1);
    if (S.strictTuples && !v) {
      const y = `"${i}" is ${_}-tuple, but minItems or maxItems/${t} are not specified or different at path "${b}"`;
      (0, mt.checkStrictMode)(c, y, S.strictTuples);
    }
  }
}
Le.validateTuple = ba;
Le.default = Ec;
Object.defineProperty(Rr, "__esModule", { value: !0 });
const Pc = Le, kc = {
  keyword: "prefixItems",
  type: "array",
  schemaType: ["array"],
  before: "uniqueItems",
  code: (e) => (0, Pc.validateTuple)(e, "items")
};
Rr.default = kc;
var Cr = {};
Object.defineProperty(Cr, "__esModule", { value: !0 });
const hn = z, jc = N, Nc = V, Oc = Ue, Tc = {
  message: ({ params: { len: e } }) => (0, hn.str)`must NOT have more than ${e} items`,
  params: ({ params: { len: e } }) => (0, hn._)`{limit: ${e}}`
}, Ic = {
  keyword: "items",
  type: "array",
  schemaType: ["object", "boolean"],
  before: "uniqueItems",
  error: Tc,
  code(e) {
    const { schema: t, parentSchema: r, it: n } = e, { prefixItems: a } = r;
    n.items = !0, !(0, jc.alwaysValidSchema)(n, t) && (a ? (0, Oc.validateAdditionalItems)(e, a) : e.ok((0, Nc.validateArray)(e)));
  }
};
Cr.default = Ic;
var Ar = {};
Object.defineProperty(Ar, "__esModule", { value: !0 });
const ae = z, it = N, Rc = {
  message: ({ params: { min: e, max: t } }) => t === void 0 ? (0, ae.str)`must contain at least ${e} valid item(s)` : (0, ae.str)`must contain at least ${e} and no more than ${t} valid item(s)`,
  params: ({ params: { min: e, max: t } }) => t === void 0 ? (0, ae._)`{minContains: ${e}}` : (0, ae._)`{minContains: ${e}, maxContains: ${t}}`
}, Cc = {
  keyword: "contains",
  type: "array",
  schemaType: ["object", "boolean"],
  before: "uniqueItems",
  trackErrors: !0,
  error: Rc,
  code(e) {
    const { gen: t, schema: r, parentSchema: n, data: a, it: s } = e;
    let i, c;
    const { minContains: l, maxContains: u } = n;
    s.opts.next ? (i = l === void 0 ? 1 : l, c = u) : i = 1;
    const f = t.const("len", (0, ae._)`${a}.length`);
    if (e.setParams({ min: i, max: c }), c === void 0 && i === 0) {
      (0, it.checkStrictMode)(s, '"minContains" == 0 without "maxContains": "contains" keyword ignored');
      return;
    }
    if (c !== void 0 && i > c) {
      (0, it.checkStrictMode)(s, '"minContains" > "maxContains" is always invalid'), e.fail();
      return;
    }
    if ((0, it.alwaysValidSchema)(s, r)) {
      let v = (0, ae._)`${f} >= ${i}`;
      c !== void 0 && (v = (0, ae._)`${v} && ${f} <= ${c}`), e.pass(v);
      return;
    }
    s.items = !0;
    const m = t.name("valid");
    c === void 0 && i === 1 ? b(m, () => t.if(m, () => t.break())) : i === 0 ? (t.let(m, !0), c !== void 0 && t.if((0, ae._)`${a}.length > 0`, S)) : (t.let(m, !1), S()), e.result(m, () => e.reset());
    function S() {
      const v = t.name("_valid"), y = t.let("count", 0);
      b(v, () => t.if(v, () => _(y)));
    }
    function b(v, y) {
      t.forRange("i", 0, f, (p) => {
        e.subschema({
          keyword: "contains",
          dataProp: p,
          dataPropType: it.Type.Num,
          compositeRule: !0
        }, v), y();
      });
    }
    function _(v) {
      t.code((0, ae._)`${v}++`), c === void 0 ? t.if((0, ae._)`${v} >= ${i}`, () => t.assign(m, !0).break()) : (t.if((0, ae._)`${v} > ${c}`, () => t.assign(m, !1).break()), i === 1 ? t.assign(m, !0) : t.if((0, ae._)`${v} >= ${i}`, () => t.assign(m, !0)));
    }
  }
};
Ar.default = Cc;
var wa = {};
(function(e) {
  Object.defineProperty(e, "__esModule", { value: !0 }), e.validateSchemaDeps = e.validatePropertyDeps = e.error = void 0;
  const t = z, r = N, n = V;
  e.error = {
    message: ({ params: { property: l, depsCount: u, deps: f } }) => {
      const m = u === 1 ? "property" : "properties";
      return (0, t.str)`must have ${m} ${f} when property ${l} is present`;
    },
    params: ({ params: { property: l, depsCount: u, deps: f, missingProperty: m } }) => (0, t._)`{property: ${l},
    missingProperty: ${m},
    depsCount: ${u},
    deps: ${f}}`
    // TODO change to reference
  };
  const a = {
    keyword: "dependencies",
    type: "object",
    schemaType: "object",
    error: e.error,
    code(l) {
      const [u, f] = s(l);
      i(l, u), c(l, f);
    }
  };
  function s({ schema: l }) {
    const u = {}, f = {};
    for (const m in l) {
      if (m === "__proto__")
        continue;
      const S = Array.isArray(l[m]) ? u : f;
      S[m] = l[m];
    }
    return [u, f];
  }
  function i(l, u = l.schema) {
    const { gen: f, data: m, it: S } = l;
    if (Object.keys(u).length === 0)
      return;
    const b = f.let("missing");
    for (const _ in u) {
      const v = u[_];
      if (v.length === 0)
        continue;
      const y = (0, n.propertyInData)(f, m, _, S.opts.ownProperties);
      l.setParams({
        property: _,
        depsCount: v.length,
        deps: v.join(", ")
      }), S.allErrors ? f.if(y, () => {
        for (const p of v)
          (0, n.checkReportMissingProp)(l, p);
      }) : (f.if((0, t._)`${y} && (${(0, n.checkMissingProp)(l, v, b)})`), (0, n.reportMissingProp)(l, b), f.else());
    }
  }
  e.validatePropertyDeps = i;
  function c(l, u = l.schema) {
    const { gen: f, data: m, keyword: S, it: b } = l, _ = f.name("valid");
    for (const v in u)
      (0, r.alwaysValidSchema)(b, u[v]) || (f.if(
        (0, n.propertyInData)(f, m, v, b.opts.ownProperties),
        () => {
          const y = l.subschema({ keyword: S, schemaProp: v }, _);
          l.mergeValidEvaluated(y, _);
        },
        () => f.var(_, !0)
        // TODO var
      ), l.ok(_));
  }
  e.validateSchemaDeps = c, e.default = a;
})(wa);
var Mr = {};
Object.defineProperty(Mr, "__esModule", { value: !0 });
const Sa = z, Ac = N, Mc = {
  message: "property name must be valid",
  params: ({ params: e }) => (0, Sa._)`{propertyName: ${e.propertyName}}`
}, zc = {
  keyword: "propertyNames",
  type: "object",
  schemaType: ["object", "boolean"],
  error: Mc,
  code(e) {
    const { gen: t, schema: r, data: n, it: a } = e;
    if ((0, Ac.alwaysValidSchema)(a, r))
      return;
    const s = t.name("valid");
    t.forIn("key", n, (i) => {
      e.setParams({ propertyName: i }), e.subschema({
        keyword: "propertyNames",
        data: i,
        dataTypes: ["string"],
        propertyName: i,
        compositeRule: !0
      }, s), t.if((0, Sa.not)(s), () => {
        e.error(!0), a.allErrors || t.break();
      });
    }), e.ok(s);
  }
};
Mr.default = zc;
var Pt = {};
Object.defineProperty(Pt, "__esModule", { value: !0 });
const ct = V, oe = z, Dc = pe, ut = N, Vc = {
  message: "must NOT have additional properties",
  params: ({ params: e }) => (0, oe._)`{additionalProperty: ${e.additionalProperty}}`
}, Fc = {
  keyword: "additionalProperties",
  type: ["object"],
  schemaType: ["boolean", "object"],
  allowUndefined: !0,
  trackErrors: !0,
  error: Vc,
  code(e) {
    const { gen: t, schema: r, parentSchema: n, data: a, errsCount: s, it: i } = e;
    if (!s)
      throw new Error("ajv implementation error");
    const { allErrors: c, opts: l } = i;
    if (i.props = !0, l.removeAdditional !== "all" && (0, ut.alwaysValidSchema)(i, r))
      return;
    const u = (0, ct.allSchemaProperties)(n.properties), f = (0, ct.allSchemaProperties)(n.patternProperties);
    m(), e.ok((0, oe._)`${s} === ${Dc.default.errors}`);
    function m() {
      t.forIn("key", a, (y) => {
        !u.length && !f.length ? _(y) : t.if(S(y), () => _(y));
      });
    }
    function S(y) {
      let p;
      if (u.length > 8) {
        const $ = (0, ut.schemaRefOrVal)(i, n.properties, "properties");
        p = (0, ct.isOwnProperty)(t, $, y);
      } else u.length ? p = (0, oe.or)(...u.map(($) => (0, oe._)`${y} === ${$}`)) : p = oe.nil;
      return f.length && (p = (0, oe.or)(p, ...f.map(($) => (0, oe._)`${(0, ct.usePattern)(e, $)}.test(${y})`))), (0, oe.not)(p);
    }
    function b(y) {
      t.code((0, oe._)`delete ${a}[${y}]`);
    }
    function _(y) {
      if (l.removeAdditional === "all" || l.removeAdditional && r === !1) {
        b(y);
        return;
      }
      if (r === !1) {
        e.setParams({ additionalProperty: y }), e.error(), c || t.break();
        return;
      }
      if (typeof r == "object" && !(0, ut.alwaysValidSchema)(i, r)) {
        const p = t.name("valid");
        l.removeAdditional === "failing" ? (v(y, p, !1), t.if((0, oe.not)(p), () => {
          e.reset(), b(y);
        })) : (v(y, p), c || t.if((0, oe.not)(p), () => t.break()));
      }
    }
    function v(y, p, $) {
      const P = {
        keyword: "additionalProperties",
        dataProp: y,
        dataPropType: ut.Type.Str
      };
      $ === !1 && Object.assign(P, {
        compositeRule: !0,
        createErrors: !1,
        allErrors: !1
      }), e.subschema(P, p);
    }
  }
};
Pt.default = Fc;
var zr = {};
Object.defineProperty(zr, "__esModule", { value: !0 });
const qc = ce, yn = V, Ht = N, gn = Pt, xc = {
  keyword: "properties",
  type: "object",
  schemaType: "object",
  code(e) {
    const { gen: t, schema: r, parentSchema: n, data: a, it: s } = e;
    s.opts.removeAdditional === "all" && n.additionalProperties === void 0 && gn.default.code(new qc.KeywordCxt(s, gn.default, "additionalProperties"));
    const i = (0, yn.allSchemaProperties)(r);
    for (const m of i)
      s.definedProperties.add(m);
    s.opts.unevaluated && i.length && s.props !== !0 && (s.props = Ht.mergeEvaluated.props(t, (0, Ht.toHash)(i), s.props));
    const c = i.filter((m) => !(0, Ht.alwaysValidSchema)(s, r[m]));
    if (c.length === 0)
      return;
    const l = t.name("valid");
    for (const m of c)
      u(m) ? f(m) : (t.if((0, yn.propertyInData)(t, a, m, s.opts.ownProperties)), f(m), s.allErrors || t.else().var(l, !0), t.endIf()), e.it.definedProperties.add(m), e.ok(l);
    function u(m) {
      return s.opts.useDefaults && !s.compositeRule && r[m].default !== void 0;
    }
    function f(m) {
      e.subschema({
        keyword: "properties",
        schemaProp: m,
        dataProp: m
      }, l);
    }
  }
};
zr.default = xc;
var Dr = {};
Object.defineProperty(Dr, "__esModule", { value: !0 });
const $n = V, dt = z, _n = N, vn = N, Uc = {
  keyword: "patternProperties",
  type: "object",
  schemaType: "object",
  code(e) {
    const { gen: t, schema: r, data: n, parentSchema: a, it: s } = e, { opts: i } = s, c = (0, $n.allSchemaProperties)(r), l = c.filter((v) => (0, _n.alwaysValidSchema)(s, r[v]));
    if (c.length === 0 || l.length === c.length && (!s.opts.unevaluated || s.props === !0))
      return;
    const u = i.strictSchema && !i.allowMatchingProperties && a.properties, f = t.name("valid");
    s.props !== !0 && !(s.props instanceof dt.Name) && (s.props = (0, vn.evaluatedPropsToName)(t, s.props));
    const { props: m } = s;
    S();
    function S() {
      for (const v of c)
        u && b(v), s.allErrors ? _(v) : (t.var(f, !0), _(v), t.if(f));
    }
    function b(v) {
      for (const y in u)
        new RegExp(v).test(y) && (0, _n.checkStrictMode)(s, `property ${y} matches pattern ${v} (use allowMatchingProperties)`);
    }
    function _(v) {
      t.forIn("key", n, (y) => {
        t.if((0, dt._)`${(0, $n.usePattern)(e, v)}.test(${y})`, () => {
          const p = l.includes(v);
          p || e.subschema({
            keyword: "patternProperties",
            schemaProp: v,
            dataProp: y,
            dataPropType: vn.Type.Str
          }, f), s.opts.unevaluated && m !== !0 ? t.assign((0, dt._)`${m}[${y}]`, !0) : !p && !s.allErrors && t.if((0, dt.not)(f), () => t.break());
        });
      });
    }
  }
};
Dr.default = Uc;
var Vr = {};
Object.defineProperty(Vr, "__esModule", { value: !0 });
const Lc = N, Kc = {
  keyword: "not",
  schemaType: ["object", "boolean"],
  trackErrors: !0,
  code(e) {
    const { gen: t, schema: r, it: n } = e;
    if ((0, Lc.alwaysValidSchema)(n, r)) {
      e.fail();
      return;
    }
    const a = t.name("valid");
    e.subschema({
      keyword: "not",
      compositeRule: !0,
      createErrors: !1,
      allErrors: !1
    }, a), e.failResult(a, () => e.reset(), () => e.error());
  },
  error: { message: "must NOT be valid" }
};
Vr.default = Kc;
var Fr = {};
Object.defineProperty(Fr, "__esModule", { value: !0 });
const Hc = V, Gc = {
  keyword: "anyOf",
  schemaType: "array",
  trackErrors: !0,
  code: Hc.validateUnion,
  error: { message: "must match a schema in anyOf" }
};
Fr.default = Gc;
var qr = {};
Object.defineProperty(qr, "__esModule", { value: !0 });
const ht = z, Bc = N, Jc = {
  message: "must match exactly one schema in oneOf",
  params: ({ params: e }) => (0, ht._)`{passingSchemas: ${e.passing}}`
}, Wc = {
  keyword: "oneOf",
  schemaType: "array",
  trackErrors: !0,
  error: Jc,
  code(e) {
    const { gen: t, schema: r, parentSchema: n, it: a } = e;
    if (!Array.isArray(r))
      throw new Error("ajv implementation error");
    if (a.opts.discriminator && n.discriminator)
      return;
    const s = r, i = t.let("valid", !1), c = t.let("passing", null), l = t.name("_valid");
    e.setParams({ passing: c }), t.block(u), e.result(i, () => e.reset(), () => e.error(!0));
    function u() {
      s.forEach((f, m) => {
        let S;
        (0, Bc.alwaysValidSchema)(a, f) ? t.var(l, !0) : S = e.subschema({
          keyword: "oneOf",
          schemaProp: m,
          compositeRule: !0
        }, l), m > 0 && t.if((0, ht._)`${l} && ${i}`).assign(i, !1).assign(c, (0, ht._)`[${c}, ${m}]`).else(), t.if(l, () => {
          t.assign(i, !0), t.assign(c, m), S && e.mergeEvaluated(S, ht.Name);
        });
      });
    }
  }
};
qr.default = Wc;
var xr = {};
Object.defineProperty(xr, "__esModule", { value: !0 });
const Zc = N, Xc = {
  keyword: "allOf",
  schemaType: "array",
  code(e) {
    const { gen: t, schema: r, it: n } = e;
    if (!Array.isArray(r))
      throw new Error("ajv implementation error");
    const a = t.name("valid");
    r.forEach((s, i) => {
      if ((0, Zc.alwaysValidSchema)(n, s))
        return;
      const c = e.subschema({ keyword: "allOf", schemaProp: i }, a);
      e.ok(a), e.mergeEvaluated(c);
    });
  }
};
xr.default = Xc;
var Ur = {};
Object.defineProperty(Ur, "__esModule", { value: !0 });
const vt = z, Ea = N, Yc = {
  message: ({ params: e }) => (0, vt.str)`must match "${e.ifClause}" schema`,
  params: ({ params: e }) => (0, vt._)`{failingKeyword: ${e.ifClause}}`
}, Qc = {
  keyword: "if",
  schemaType: ["object", "boolean"],
  trackErrors: !0,
  error: Yc,
  code(e) {
    const { gen: t, parentSchema: r, it: n } = e;
    r.then === void 0 && r.else === void 0 && (0, Ea.checkStrictMode)(n, '"if" without "then" and "else" is ignored');
    const a = bn(n, "then"), s = bn(n, "else");
    if (!a && !s)
      return;
    const i = t.let("valid", !0), c = t.name("_valid");
    if (l(), e.reset(), a && s) {
      const f = t.let("ifClause");
      e.setParams({ ifClause: f }), t.if(c, u("then", f), u("else", f));
    } else a ? t.if(c, u("then")) : t.if((0, vt.not)(c), u("else"));
    e.pass(i, () => e.error(!0));
    function l() {
      const f = e.subschema({
        keyword: "if",
        compositeRule: !0,
        createErrors: !1,
        allErrors: !1
      }, c);
      e.mergeEvaluated(f);
    }
    function u(f, m) {
      return () => {
        const S = e.subschema({ keyword: f }, c);
        t.assign(i, c), e.mergeValidEvaluated(S, i), m ? t.assign(m, (0, vt._)`${f}`) : e.setParams({ ifClause: f });
      };
    }
  }
};
function bn(e, t) {
  const r = e.schema[t];
  return r !== void 0 && !(0, Ea.alwaysValidSchema)(e, r);
}
Ur.default = Qc;
var Lr = {};
Object.defineProperty(Lr, "__esModule", { value: !0 });
const eu = N, tu = {
  keyword: ["then", "else"],
  schemaType: ["object", "boolean"],
  code({ keyword: e, parentSchema: t, it: r }) {
    t.if === void 0 && (0, eu.checkStrictMode)(r, `"${e}" without "if" is ignored`);
  }
};
Lr.default = tu;
Object.defineProperty(Ir, "__esModule", { value: !0 });
const ru = Ue, nu = Rr, au = Le, su = Cr, ou = Ar, iu = wa, cu = Mr, uu = Pt, du = zr, lu = Dr, fu = Vr, pu = Fr, mu = qr, hu = xr, yu = Ur, gu = Lr;
function $u(e = !1) {
  const t = [
    // any
    fu.default,
    pu.default,
    mu.default,
    hu.default,
    yu.default,
    gu.default,
    // object
    cu.default,
    uu.default,
    iu.default,
    du.default,
    lu.default
  ];
  return e ? t.push(nu.default, su.default) : t.push(ru.default, au.default), t.push(ou.default), t;
}
Ir.default = $u;
var Kr = {}, Hr = {};
Object.defineProperty(Hr, "__esModule", { value: !0 });
const B = z, _u = {
  message: ({ schemaCode: e }) => (0, B.str)`must match format "${e}"`,
  params: ({ schemaCode: e }) => (0, B._)`{format: ${e}}`
}, vu = {
  keyword: "format",
  type: ["number", "string"],
  schemaType: "string",
  $data: !0,
  error: _u,
  code(e, t) {
    const { gen: r, data: n, $data: a, schema: s, schemaCode: i, it: c } = e, { opts: l, errSchemaPath: u, schemaEnv: f, self: m } = c;
    if (!l.validateFormats)
      return;
    a ? S() : b();
    function S() {
      const _ = r.scopeValue("formats", {
        ref: m.formats,
        code: l.code.formats
      }), v = r.const("fDef", (0, B._)`${_}[${i}]`), y = r.let("fType"), p = r.let("format");
      r.if((0, B._)`typeof ${v} == "object" && !(${v} instanceof RegExp)`, () => r.assign(y, (0, B._)`${v}.type || "string"`).assign(p, (0, B._)`${v}.validate`), () => r.assign(y, (0, B._)`"string"`).assign(p, v)), e.fail$data((0, B.or)($(), P()));
      function $() {
        return l.strictSchema === !1 ? B.nil : (0, B._)`${i} && !${p}`;
      }
      function P() {
        const j = f.$async ? (0, B._)`(${v}.async ? await ${p}(${n}) : ${p}(${n}))` : (0, B._)`${p}(${n})`, O = (0, B._)`(typeof ${p} == "function" ? ${j} : ${p}.test(${n}))`;
        return (0, B._)`${p} && ${p} !== true && ${y} === ${t} && !${O}`;
      }
    }
    function b() {
      const _ = m.formats[s];
      if (!_) {
        $();
        return;
      }
      if (_ === !0)
        return;
      const [v, y, p] = P(_);
      v === t && e.pass(j());
      function $() {
        if (l.strictSchema === !1) {
          m.logger.warn(O());
          return;
        }
        throw new Error(O());
        function O() {
          return `unknown format "${s}" ignored in schema at path "${u}"`;
        }
      }
      function P(O) {
        const U = O instanceof RegExp ? (0, B.regexpCode)(O) : l.code.formats ? (0, B._)`${l.code.formats}${(0, B.getProperty)(s)}` : void 0, G = r.scopeValue("formats", { key: s, ref: O, code: U });
        return typeof O == "object" && !(O instanceof RegExp) ? [O.type || "string", O.validate, (0, B._)`${G}.validate`] : ["string", O, G];
      }
      function j() {
        if (typeof _ == "object" && !(_ instanceof RegExp) && _.async) {
          if (!f.$async)
            throw new Error("async format in sync schema");
          return (0, B._)`await ${p}(${n})`;
        }
        return typeof y == "function" ? (0, B._)`${p}(${n})` : (0, B._)`${p}.test(${n})`;
      }
    }
  }
};
Hr.default = vu;
Object.defineProperty(Kr, "__esModule", { value: !0 });
const bu = Hr, wu = [bu.default];
Kr.default = wu;
var qe = {};
Object.defineProperty(qe, "__esModule", { value: !0 });
qe.contentVocabulary = qe.metadataVocabulary = void 0;
qe.metadataVocabulary = [
  "title",
  "description",
  "default",
  "deprecated",
  "readOnly",
  "writeOnly",
  "examples"
];
qe.contentVocabulary = [
  "contentMediaType",
  "contentEncoding",
  "contentSchema"
];
Object.defineProperty(yr, "__esModule", { value: !0 });
const Su = gr, Eu = _r, Pu = Ir, ku = Kr, wn = qe, ju = [
  Su.default,
  Eu.default,
  (0, Pu.default)(),
  ku.default,
  wn.metadataVocabulary,
  wn.contentVocabulary
];
yr.default = ju;
var Gr = {}, kt = {};
Object.defineProperty(kt, "__esModule", { value: !0 });
kt.DiscrError = void 0;
var Sn;
(function(e) {
  e.Tag = "tag", e.Mapping = "mapping";
})(Sn || (kt.DiscrError = Sn = {}));
Object.defineProperty(Gr, "__esModule", { value: !0 });
const ze = z, er = kt, En = re, Nu = xe, Ou = N, Tu = {
  message: ({ params: { discrError: e, tagName: t } }) => e === er.DiscrError.Tag ? `tag "${t}" must be string` : `value of tag "${t}" must be in oneOf`,
  params: ({ params: { discrError: e, tag: t, tagName: r } }) => (0, ze._)`{error: ${e}, tag: ${r}, tagValue: ${t}}`
}, Iu = {
  keyword: "discriminator",
  type: "object",
  schemaType: "object",
  error: Tu,
  code(e) {
    const { gen: t, data: r, schema: n, parentSchema: a, it: s } = e, { oneOf: i } = a;
    if (!s.opts.discriminator)
      throw new Error("discriminator: requires discriminator option");
    const c = n.propertyName;
    if (typeof c != "string")
      throw new Error("discriminator: requires propertyName");
    if (n.mapping)
      throw new Error("discriminator: mapping is not supported");
    if (!i)
      throw new Error("discriminator: requires oneOf keyword");
    const l = t.let("valid", !1), u = t.const("tag", (0, ze._)`${r}${(0, ze.getProperty)(c)}`);
    t.if((0, ze._)`typeof ${u} == "string"`, () => f(), () => e.error(!1, { discrError: er.DiscrError.Tag, tag: u, tagName: c })), e.ok(l);
    function f() {
      const b = S();
      t.if(!1);
      for (const _ in b)
        t.elseIf((0, ze._)`${u} === ${_}`), t.assign(l, m(b[_]));
      t.else(), e.error(!1, { discrError: er.DiscrError.Mapping, tag: u, tagName: c }), t.endIf();
    }
    function m(b) {
      const _ = t.name("valid"), v = e.subschema({ keyword: "oneOf", schemaProp: b }, _);
      return e.mergeEvaluated(v, ze.Name), _;
    }
    function S() {
      var b;
      const _ = {}, v = p(a);
      let y = !0;
      for (let j = 0; j < i.length; j++) {
        let O = i[j];
        if (O != null && O.$ref && !(0, Ou.schemaHasRulesButRef)(O, s.self.RULES)) {
          const G = O.$ref;
          if (O = En.resolveRef.call(s.self, s.schemaEnv.root, s.baseId, G), O instanceof En.SchemaEnv && (O = O.schema), O === void 0)
            throw new Nu.default(s.opts.uriResolver, s.baseId, G);
        }
        const U = (b = O == null ? void 0 : O.properties) === null || b === void 0 ? void 0 : b[c];
        if (typeof U != "object")
          throw new Error(`discriminator: oneOf subschemas (or referenced schemas) must have "properties/${c}"`);
        y = y && (v || p(O)), $(U, j);
      }
      if (!y)
        throw new Error(`discriminator: "${c}" must be required`);
      return _;
      function p({ required: j }) {
        return Array.isArray(j) && j.includes(c);
      }
      function $(j, O) {
        if (j.const)
          P(j.const, O);
        else if (j.enum)
          for (const U of j.enum)
            P(U, O);
        else
          throw new Error(`discriminator: "properties/${c}" must have "const" or "enum"`);
      }
      function P(j, O) {
        if (typeof j != "string" || j in _)
          throw new Error(`discriminator: "${c}" values must be unique strings`);
        _[j] = O;
      }
    }
  }
};
Gr.default = Iu;
const Ru = "http://json-schema.org/draft-07/schema#", Cu = "http://json-schema.org/draft-07/schema#", Au = "Core schema meta-schema", Mu = {
  schemaArray: {
    type: "array",
    minItems: 1,
    items: {
      $ref: "#"
    }
  },
  nonNegativeInteger: {
    type: "integer",
    minimum: 0
  },
  nonNegativeIntegerDefault0: {
    allOf: [
      {
        $ref: "#/definitions/nonNegativeInteger"
      },
      {
        default: 0
      }
    ]
  },
  simpleTypes: {
    enum: [
      "array",
      "boolean",
      "integer",
      "null",
      "number",
      "object",
      "string"
    ]
  },
  stringArray: {
    type: "array",
    items: {
      type: "string"
    },
    uniqueItems: !0,
    default: []
  }
}, zu = [
  "object",
  "boolean"
], Du = {
  $id: {
    type: "string",
    format: "uri-reference"
  },
  $schema: {
    type: "string",
    format: "uri"
  },
  $ref: {
    type: "string",
    format: "uri-reference"
  },
  $comment: {
    type: "string"
  },
  title: {
    type: "string"
  },
  description: {
    type: "string"
  },
  default: !0,
  readOnly: {
    type: "boolean",
    default: !1
  },
  examples: {
    type: "array",
    items: !0
  },
  multipleOf: {
    type: "number",
    exclusiveMinimum: 0
  },
  maximum: {
    type: "number"
  },
  exclusiveMaximum: {
    type: "number"
  },
  minimum: {
    type: "number"
  },
  exclusiveMinimum: {
    type: "number"
  },
  maxLength: {
    $ref: "#/definitions/nonNegativeInteger"
  },
  minLength: {
    $ref: "#/definitions/nonNegativeIntegerDefault0"
  },
  pattern: {
    type: "string",
    format: "regex"
  },
  additionalItems: {
    $ref: "#"
  },
  items: {
    anyOf: [
      {
        $ref: "#"
      },
      {
        $ref: "#/definitions/schemaArray"
      }
    ],
    default: !0
  },
  maxItems: {
    $ref: "#/definitions/nonNegativeInteger"
  },
  minItems: {
    $ref: "#/definitions/nonNegativeIntegerDefault0"
  },
  uniqueItems: {
    type: "boolean",
    default: !1
  },
  contains: {
    $ref: "#"
  },
  maxProperties: {
    $ref: "#/definitions/nonNegativeInteger"
  },
  minProperties: {
    $ref: "#/definitions/nonNegativeIntegerDefault0"
  },
  required: {
    $ref: "#/definitions/stringArray"
  },
  additionalProperties: {
    $ref: "#"
  },
  definitions: {
    type: "object",
    additionalProperties: {
      $ref: "#"
    },
    default: {}
  },
  properties: {
    type: "object",
    additionalProperties: {
      $ref: "#"
    },
    default: {}
  },
  patternProperties: {
    type: "object",
    additionalProperties: {
      $ref: "#"
    },
    propertyNames: {
      format: "regex"
    },
    default: {}
  },
  dependencies: {
    type: "object",
    additionalProperties: {
      anyOf: [
        {
          $ref: "#"
        },
        {
          $ref: "#/definitions/stringArray"
        }
      ]
    }
  },
  propertyNames: {
    $ref: "#"
  },
  const: !0,
  enum: {
    type: "array",
    items: !0,
    minItems: 1,
    uniqueItems: !0
  },
  type: {
    anyOf: [
      {
        $ref: "#/definitions/simpleTypes"
      },
      {
        type: "array",
        items: {
          $ref: "#/definitions/simpleTypes"
        },
        minItems: 1,
        uniqueItems: !0
      }
    ]
  },
  format: {
    type: "string"
  },
  contentMediaType: {
    type: "string"
  },
  contentEncoding: {
    type: "string"
  },
  if: {
    $ref: "#"
  },
  then: {
    $ref: "#"
  },
  else: {
    $ref: "#"
  },
  allOf: {
    $ref: "#/definitions/schemaArray"
  },
  anyOf: {
    $ref: "#/definitions/schemaArray"
  },
  oneOf: {
    $ref: "#/definitions/schemaArray"
  },
  not: {
    $ref: "#"
  }
}, Vu = {
  $schema: Ru,
  $id: Cu,
  title: Au,
  definitions: Mu,
  type: zu,
  properties: Du,
  default: !0
};
(function(e, t) {
  Object.defineProperty(t, "__esModule", { value: !0 }), t.MissingRefError = t.ValidationError = t.CodeGen = t.Name = t.nil = t.stringify = t.str = t._ = t.KeywordCxt = t.Ajv = void 0;
  const r = On, n = yr, a = Gr, s = Vu, i = ["/properties"], c = "http://json-schema.org/draft-07/schema";
  class l extends r.default {
    _addVocabularies() {
      super._addVocabularies(), n.default.forEach((_) => this.addVocabulary(_)), this.opts.discriminator && this.addKeyword(a.default);
    }
    _addDefaultMetaSchema() {
      if (super._addDefaultMetaSchema(), !this.opts.meta)
        return;
      const _ = this.opts.$data ? this.$dataMetaSchema(s, i) : s;
      this.addMetaSchema(_, c, !1), this.refs["http://json-schema.org/schema"] = c;
    }
    defaultMeta() {
      return this.opts.defaultMeta = super.defaultMeta() || (this.getSchema(c) ? c : void 0);
    }
  }
  t.Ajv = l, e.exports = t = l, e.exports.Ajv = l, Object.defineProperty(t, "__esModule", { value: !0 }), t.default = l;
  var u = ce;
  Object.defineProperty(t, "KeywordCxt", { enumerable: !0, get: function() {
    return u.KeywordCxt;
  } });
  var f = z;
  Object.defineProperty(t, "_", { enumerable: !0, get: function() {
    return f._;
  } }), Object.defineProperty(t, "str", { enumerable: !0, get: function() {
    return f.str;
  } }), Object.defineProperty(t, "stringify", { enumerable: !0, get: function() {
    return f.stringify;
  } }), Object.defineProperty(t, "nil", { enumerable: !0, get: function() {
    return f.nil;
  } }), Object.defineProperty(t, "Name", { enumerable: !0, get: function() {
    return f.Name;
  } }), Object.defineProperty(t, "CodeGen", { enumerable: !0, get: function() {
    return f.CodeGen;
  } });
  var m = nt;
  Object.defineProperty(t, "ValidationError", { enumerable: !0, get: function() {
    return m.default;
  } });
  var S = xe;
  Object.defineProperty(t, "MissingRefError", { enumerable: !0, get: function() {
    return S.default;
  } });
})(Bt, Bt.exports);
var Pa = Bt.exports;
const Fu = /* @__PURE__ */ Nn(Pa);
var tr = { exports: {} }, ka = {};
(function(e) {
  Object.defineProperty(e, "__esModule", { value: !0 }), e.formatNames = e.fastFormats = e.fullFormats = void 0;
  function t(C, D) {
    return { validate: C, compare: D };
  }
  e.fullFormats = {
    // date: http://tools.ietf.org/html/rfc3339#section-5.6
    date: t(s, i),
    // date-time: http://tools.ietf.org/html/rfc3339#section-5.6
    time: t(l(!0), u),
    "date-time": t(S(!0), b),
    "iso-time": t(l(), f),
    "iso-date-time": t(S(), _),
    // duration: https://tools.ietf.org/html/rfc3339#appendix-A
    duration: /^P(?!$)((\d+Y)?(\d+M)?(\d+D)?(T(?=\d)(\d+H)?(\d+M)?(\d+S)?)?|(\d+W)?)$/,
    uri: p,
    "uri-reference": /^(?:[a-z][a-z0-9+\-.]*:)?(?:\/?\/(?:(?:[a-z0-9\-._~!$&'()*+,;=:]|%[0-9a-f]{2})*@)?(?:\[(?:(?:(?:(?:[0-9a-f]{1,4}:){6}|::(?:[0-9a-f]{1,4}:){5}|(?:[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){4}|(?:(?:[0-9a-f]{1,4}:){0,1}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){3}|(?:(?:[0-9a-f]{1,4}:){0,2}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){2}|(?:(?:[0-9a-f]{1,4}:){0,3}[0-9a-f]{1,4})?::[0-9a-f]{1,4}:|(?:(?:[0-9a-f]{1,4}:){0,4}[0-9a-f]{1,4})?::)(?:[0-9a-f]{1,4}:[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?))|(?:(?:[0-9a-f]{1,4}:){0,5}[0-9a-f]{1,4})?::[0-9a-f]{1,4}|(?:(?:[0-9a-f]{1,4}:){0,6}[0-9a-f]{1,4})?::)|[Vv][0-9a-f]+\.[a-z0-9\-._~!$&'()*+,;=:]+)\]|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)|(?:[a-z0-9\-._~!$&'"()*+,;=]|%[0-9a-f]{2})*)(?::\d*)?(?:\/(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})*)*|\/(?:(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})*)*)?|(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})*)*)?(?:\?(?:[a-z0-9\-._~!$&'"()*+,;=:@/?]|%[0-9a-f]{2})*)?(?:#(?:[a-z0-9\-._~!$&'"()*+,;=:@/?]|%[0-9a-f]{2})*)?$/i,
    // uri-template: https://tools.ietf.org/html/rfc6570
    "uri-template": /^(?:(?:[^\x00-\x20"'<>%\\^`{|}]|%[0-9a-f]{2})|\{[+#./;?&=,!@|]?(?:[a-z0-9_]|%[0-9a-f]{2})+(?::[1-9][0-9]{0,3}|\*)?(?:,(?:[a-z0-9_]|%[0-9a-f]{2})+(?::[1-9][0-9]{0,3}|\*)?)*\})*$/i,
    // For the source: https://gist.github.com/dperini/729294
    // For test cases: https://mathiasbynens.be/demo/url-regex
    url: /^(?:https?|ftp):\/\/(?:\S+(?::\S*)?@)?(?:(?!(?:10|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z0-9\u{00a1}-\u{ffff}]+-)*[a-z0-9\u{00a1}-\u{ffff}]+)(?:\.(?:[a-z0-9\u{00a1}-\u{ffff}]+-)*[a-z0-9\u{00a1}-\u{ffff}]+)*(?:\.(?:[a-z\u{00a1}-\u{ffff}]{2,})))(?::\d{2,5})?(?:\/[^\s]*)?$/iu,
    email: /^[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?$/i,
    hostname: /^(?=.{1,253}\.?$)[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?(?:\.[a-z0-9](?:[-0-9a-z]{0,61}[0-9a-z])?)*\.?$/i,
    // optimized https://www.safaribooksonline.com/library/view/regular-expressions-cookbook/9780596802837/ch07s16.html
    ipv4: /^(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)\.){3}(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)$/,
    ipv6: /^((([0-9a-f]{1,4}:){7}([0-9a-f]{1,4}|:))|(([0-9a-f]{1,4}:){6}(:[0-9a-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9a-f]{1,4}:){5}(((:[0-9a-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9a-f]{1,4}:){4}(((:[0-9a-f]{1,4}){1,3})|((:[0-9a-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9a-f]{1,4}:){3}(((:[0-9a-f]{1,4}){1,4})|((:[0-9a-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9a-f]{1,4}:){2}(((:[0-9a-f]{1,4}){1,5})|((:[0-9a-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9a-f]{1,4}:){1}(((:[0-9a-f]{1,4}){1,6})|((:[0-9a-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9a-f]{1,4}){1,7})|((:[0-9a-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))$/i,
    regex: me,
    // uuid: http://tools.ietf.org/html/rfc4122
    uuid: /^(?:urn:uuid:)?[0-9a-f]{8}-(?:[0-9a-f]{4}-){3}[0-9a-f]{12}$/i,
    // JSON-pointer: https://tools.ietf.org/html/rfc6901
    // uri fragment: https://tools.ietf.org/html/rfc3986#appendix-A
    "json-pointer": /^(?:\/(?:[^~/]|~0|~1)*)*$/,
    "json-pointer-uri-fragment": /^#(?:\/(?:[a-z0-9_\-.!$&'()*+,;:=@]|%[0-9a-f]{2}|~0|~1)*)*$/i,
    // relative JSON-pointer: http://tools.ietf.org/html/draft-luff-relative-json-pointer-00
    "relative-json-pointer": /^(?:0|[1-9][0-9]*)(?:#|(?:\/(?:[^~/]|~0|~1)*)*)$/,
    // the following formats are used by the openapi specification: https://spec.openapis.org/oas/v3.0.0#data-types
    // byte: https://github.com/miguelmota/is-base64
    byte: P,
    // signed 32 bit integer
    int32: { type: "number", validate: U },
    // signed 64 bit integer
    int64: { type: "number", validate: G },
    // C-type float
    float: { type: "number", validate: ne },
    // C-type double
    double: { type: "number", validate: ne },
    // hint to the UI to hide input strings
    password: !0,
    // unchecked string payload
    binary: !0
  }, e.fastFormats = {
    ...e.fullFormats,
    date: t(/^\d\d\d\d-[0-1]\d-[0-3]\d$/, i),
    time: t(/^(?:[0-2]\d:[0-5]\d:[0-5]\d|23:59:60)(?:\.\d+)?(?:z|[+-]\d\d(?::?\d\d)?)$/i, u),
    "date-time": t(/^\d\d\d\d-[0-1]\d-[0-3]\dt(?:[0-2]\d:[0-5]\d:[0-5]\d|23:59:60)(?:\.\d+)?(?:z|[+-]\d\d(?::?\d\d)?)$/i, b),
    "iso-time": t(/^(?:[0-2]\d:[0-5]\d:[0-5]\d|23:59:60)(?:\.\d+)?(?:z|[+-]\d\d(?::?\d\d)?)?$/i, f),
    "iso-date-time": t(/^\d\d\d\d-[0-1]\d-[0-3]\d[t\s](?:[0-2]\d:[0-5]\d:[0-5]\d|23:59:60)(?:\.\d+)?(?:z|[+-]\d\d(?::?\d\d)?)?$/i, _),
    // uri: https://github.com/mafintosh/is-my-json-valid/blob/master/formats.js
    uri: /^(?:[a-z][a-z0-9+\-.]*:)(?:\/?\/)?[^\s]*$/i,
    "uri-reference": /^(?:(?:[a-z][a-z0-9+\-.]*:)?\/?\/)?(?:[^\\\s#][^\s#]*)?(?:#[^\\\s]*)?$/i,
    // email (sources from jsen validator):
    // http://stackoverflow.com/questions/201323/using-a-regular-expression-to-validate-an-email-address#answer-8829363
    // http://www.w3.org/TR/html5/forms.html#valid-e-mail-address (search for 'wilful violation')
    email: /^[a-z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?(?:\.[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?)*$/i
  }, e.formatNames = Object.keys(e.fullFormats);
  function r(C) {
    return C % 4 === 0 && (C % 100 !== 0 || C % 400 === 0);
  }
  const n = /^(\d\d\d\d)-(\d\d)-(\d\d)$/, a = [0, 31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
  function s(C) {
    const D = n.exec(C);
    if (!D)
      return !1;
    const H = +D[1], F = +D[2], Z = +D[3];
    return F >= 1 && F <= 12 && Z >= 1 && Z <= (F === 2 && r(H) ? 29 : a[F]);
  }
  function i(C, D) {
    if (C && D)
      return C > D ? 1 : C < D ? -1 : 0;
  }
  const c = /^(\d\d):(\d\d):(\d\d(?:\.\d+)?)(z|([+-])(\d\d)(?::?(\d\d))?)?$/i;
  function l(C) {
    return function(H) {
      const F = c.exec(H);
      if (!F)
        return !1;
      const Z = +F[1], he = +F[2], ye = +F[3], Ke = F[4], ke = F[5] === "-" ? -1 : 1, k = +(F[6] || 0), h = +(F[7] || 0);
      if (k > 23 || h > 59 || C && !Ke)
        return !1;
      if (Z <= 23 && he <= 59 && ye < 60)
        return !0;
      const E = he - h * ke, g = Z - k * ke - (E < 0 ? 1 : 0);
      return (g === 23 || g === -1) && (E === 59 || E === -1) && ye < 61;
    };
  }
  function u(C, D) {
    if (!(C && D))
      return;
    const H = (/* @__PURE__ */ new Date("2020-01-01T" + C)).valueOf(), F = (/* @__PURE__ */ new Date("2020-01-01T" + D)).valueOf();
    if (H && F)
      return H - F;
  }
  function f(C, D) {
    if (!(C && D))
      return;
    const H = c.exec(C), F = c.exec(D);
    if (H && F)
      return C = H[1] + H[2] + H[3], D = F[1] + F[2] + F[3], C > D ? 1 : C < D ? -1 : 0;
  }
  const m = /t|\s/i;
  function S(C) {
    const D = l(C);
    return function(F) {
      const Z = F.split(m);
      return Z.length === 2 && s(Z[0]) && D(Z[1]);
    };
  }
  function b(C, D) {
    if (!(C && D))
      return;
    const H = new Date(C).valueOf(), F = new Date(D).valueOf();
    if (H && F)
      return H - F;
  }
  function _(C, D) {
    if (!(C && D))
      return;
    const [H, F] = C.split(m), [Z, he] = D.split(m), ye = i(H, Z);
    if (ye !== void 0)
      return ye || u(F, he);
  }
  const v = /\/|:/, y = /^(?:[a-z][a-z0-9+\-.]*:)(?:\/?\/(?:(?:[a-z0-9\-._~!$&'()*+,;=:]|%[0-9a-f]{2})*@)?(?:\[(?:(?:(?:(?:[0-9a-f]{1,4}:){6}|::(?:[0-9a-f]{1,4}:){5}|(?:[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){4}|(?:(?:[0-9a-f]{1,4}:){0,1}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){3}|(?:(?:[0-9a-f]{1,4}:){0,2}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){2}|(?:(?:[0-9a-f]{1,4}:){0,3}[0-9a-f]{1,4})?::[0-9a-f]{1,4}:|(?:(?:[0-9a-f]{1,4}:){0,4}[0-9a-f]{1,4})?::)(?:[0-9a-f]{1,4}:[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?))|(?:(?:[0-9a-f]{1,4}:){0,5}[0-9a-f]{1,4})?::[0-9a-f]{1,4}|(?:(?:[0-9a-f]{1,4}:){0,6}[0-9a-f]{1,4})?::)|[Vv][0-9a-f]+\.[a-z0-9\-._~!$&'()*+,;=:]+)\]|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)|(?:[a-z0-9\-._~!$&'()*+,;=]|%[0-9a-f]{2})*)(?::\d*)?(?:\/(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*|\/(?:(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*)?|(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*)(?:\?(?:[a-z0-9\-._~!$&'()*+,;=:@/?]|%[0-9a-f]{2})*)?(?:#(?:[a-z0-9\-._~!$&'()*+,;=:@/?]|%[0-9a-f]{2})*)?$/i;
  function p(C) {
    return v.test(C) && y.test(C);
  }
  const $ = /^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/gm;
  function P(C) {
    return $.lastIndex = 0, $.test(C);
  }
  const j = -2147483648, O = 2 ** 31 - 1;
  function U(C) {
    return Number.isInteger(C) && C <= O && C >= j;
  }
  function G(C) {
    return Number.isInteger(C);
  }
  function ne() {
    return !0;
  }
  const ue = /[^\\]\\Z/;
  function me(C) {
    if (ue.test(C))
      return !1;
    try {
      return new RegExp(C), !0;
    } catch {
      return !1;
    }
  }
})(ka);
var ja = {};
(function(e) {
  Object.defineProperty(e, "__esModule", { value: !0 }), e.formatLimitDefinition = void 0;
  const t = Pa, r = z, n = r.operators, a = {
    formatMaximum: { okStr: "<=", ok: n.LTE, fail: n.GT },
    formatMinimum: { okStr: ">=", ok: n.GTE, fail: n.LT },
    formatExclusiveMaximum: { okStr: "<", ok: n.LT, fail: n.GTE },
    formatExclusiveMinimum: { okStr: ">", ok: n.GT, fail: n.LTE }
  }, s = {
    message: ({ keyword: c, schemaCode: l }) => (0, r.str)`should be ${a[c].okStr} ${l}`,
    params: ({ keyword: c, schemaCode: l }) => (0, r._)`{comparison: ${a[c].okStr}, limit: ${l}}`
  };
  e.formatLimitDefinition = {
    keyword: Object.keys(a),
    type: "string",
    schemaType: "string",
    $data: !0,
    error: s,
    code(c) {
      const { gen: l, data: u, schemaCode: f, keyword: m, it: S } = c, { opts: b, self: _ } = S;
      if (!b.validateFormats)
        return;
      const v = new t.KeywordCxt(S, _.RULES.all.format.definition, "format");
      v.$data ? y() : p();
      function y() {
        const P = l.scopeValue("formats", {
          ref: _.formats,
          code: b.code.formats
        }), j = l.const("fmt", (0, r._)`${P}[${v.schemaCode}]`);
        c.fail$data((0, r.or)((0, r._)`typeof ${j} != "object"`, (0, r._)`${j} instanceof RegExp`, (0, r._)`typeof ${j}.compare != "function"`, $(j)));
      }
      function p() {
        const P = v.schema, j = _.formats[P];
        if (!j || j === !0)
          return;
        if (typeof j != "object" || j instanceof RegExp || typeof j.compare != "function")
          throw new Error(`"${m}": format "${P}" does not define "compare" function`);
        const O = l.scopeValue("formats", {
          key: P,
          ref: j,
          code: b.code.formats ? (0, r._)`${b.code.formats}${(0, r.getProperty)(P)}` : void 0
        });
        c.fail$data($(O));
      }
      function $(P) {
        return (0, r._)`${P}.compare(${u}, ${f}) ${a[m].fail} 0`;
      }
    },
    dependencies: ["format"]
  };
  const i = (c) => (c.addKeyword(e.formatLimitDefinition), c);
  e.default = i;
})(ja);
(function(e, t) {
  Object.defineProperty(t, "__esModule", { value: !0 });
  const r = ka, n = ja, a = z, s = new a.Name("fullFormats"), i = new a.Name("fastFormats"), c = (u, f = { keywords: !0 }) => {
    if (Array.isArray(f))
      return l(u, f, r.fullFormats, s), u;
    const [m, S] = f.mode === "fast" ? [r.fastFormats, i] : [r.fullFormats, s], b = f.formats || r.formatNames;
    return l(u, b, m, S), f.keywords && (0, n.default)(u), u;
  };
  c.get = (u, f = "full") => {
    const S = (f === "fast" ? r.fastFormats : r.fullFormats)[u];
    if (!S)
      throw new Error(`Unknown format "${u}"`);
    return S;
  };
  function l(u, f, m, S) {
    var b, _;
    (b = (_ = u.opts.code).formats) !== null && b !== void 0 || (_.formats = (0, a._)`require("ajv-formats/dist/formats").${S}`);
    for (const v of f)
      u.addFormat(v, m[v]);
  }
  e.exports = t = c, Object.defineProperty(t, "__esModule", { value: !0 }), t.default = c;
})(tr, tr.exports);
var qu = tr.exports;
const xu = /* @__PURE__ */ Nn(qu), Na = new Fu({
  allErrors: !0,
  verbose: !0,
  strict: !1
});
xu(Na);
function Br(e, t) {
  const r = Na.compile(e);
  return r(t) ? { valid: !0, errors: [] } : { valid: !1, errors: (r.errors || []).map((s) => ({
    path: s.instancePath || s.schemaPath || "",
    message: s.message || "Validation error",
    value: s.data,
    schema: s.schema
  })) };
}
function rd(e) {
  return (t) => Br(e, t);
}
function nd(e, t) {
  return Br(e, t).valid;
}
class Oa {
  constructor() {
    Xr(this, "schemas", /* @__PURE__ */ new Map());
  }
  /**
   * Register a schema for a block type
   * @param blockType - Block type identifier
   * @param schema - JSON Schema for the block
   */
  register(t, r) {
    if (!t || typeof t != "string")
      throw new Error("Block type must be a non-empty string");
    if (!r || typeof r != "object")
      throw new Error("Schema must be a valid JSON Schema object");
    this.schemas.set(t, r);
  }
  /**
   * Get schema for a block type
   * @param blockType - Block type identifier
   * @returns Schema or undefined if not found
   */
  get(t) {
    return this.schemas.get(t);
  }
  /**
   * Validate data against a block type's schema
   * @param blockType - Block type identifier
   * @param data - Data to validate
   * @returns Validation result
   */
  validate(t, r) {
    const n = this.schemas.get(t);
    return n ? Br(n, r) : {
      valid: !1,
      errors: [{
        path: "",
        message: `No schema registered for block type: ${t}`,
        value: r
      }]
    };
  }
  /**
   * Get all registered schemas
   * @returns Record of all schemas by block type
   */
  getAll() {
    const t = {};
    for (const [r, n] of this.schemas.entries())
      t[r] = n;
    return t;
  }
  /**
   * Check if a block type is registered
   * @param blockType - Block type identifier
   * @returns True if registered
   */
  has(t) {
    return this.schemas.has(t);
  }
  /**
   * Unregister a block type
   * @param blockType - Block type identifier
   * @returns True if was registered and removed
   */
  unregister(t) {
    return this.schemas.delete(t);
  }
  /**
   * Clear all registered schemas
   */
  clear() {
    this.schemas.clear();
  }
  /**
   * Get list of all registered block types
   * @returns Array of block type identifiers
   */
  getBlockTypes() {
    return Array.from(this.schemas.keys());
  }
}
const Se = new Oa();
function ad() {
  return new Oa();
}
function sd() {
  Se.register("text", Da), Se.register("image", Va), Se.register("video", Fa), Se.register("heading", qa), Se.register("paragraph", xa), Se.register("spacer", Ua), Se.register("container", La), Se.register("grid", Ka);
}
function Uu() {
  return [
    "text",
    "image",
    "video",
    "heading",
    "paragraph",
    "spacer",
    "container",
    "grid"
  ];
}
function od(e) {
  return Uu().includes(e);
}
const Pn = {
  50: "#eff6ff",
  100: "#dbeafe",
  200: "#bfdbfe",
  300: "#93c5fd",
  400: "#60a5fa",
  500: "#3b82f6",
  600: "#2563eb",
  700: "#1d4ed8",
  800: "#1e40af",
  900: "#1e3a8a",
  950: "#172554"
}, Lu = {
  50: "#f8fafc",
  100: "#f1f5f9",
  200: "#e2e8f0",
  300: "#cbd5e1",
  400: "#94a3b8",
  500: "#64748b",
  600: "#475569",
  700: "#334155",
  800: "#1e293b",
  900: "#0f172a",
  950: "#020617"
}, Ku = {
  50: "#f9fafb",
  100: "#f3f4f6",
  200: "#e5e7eb",
  300: "#d1d5db",
  400: "#9ca3af",
  500: "#6b7280",
  600: "#4b5563",
  700: "#374151",
  800: "#1f2937",
  900: "#111827",
  950: "#030712"
}, Hu = {
  50: "#ecfdf5",
  100: "#d1fae5",
  200: "#a7f3d0",
  300: "#6ee7b7",
  400: "#34d399",
  500: "#10b981",
  600: "#059669",
  700: "#047857",
  800: "#065f46",
  900: "#064e3b",
  950: "#022c22"
}, Gu = {
  50: "#fffbeb",
  100: "#fef3c7",
  200: "#fde68a",
  300: "#fcd34d",
  400: "#fbbf24",
  500: "#f59e0b",
  600: "#d97706",
  700: "#b45309",
  800: "#92400e",
  900: "#78350f",
  950: "#451a03"
}, Bu = {
  50: "#fef2f2",
  100: "#fee2e2",
  200: "#fecaca",
  300: "#fca5a5",
  400: "#f87171",
  500: "#ef4444",
  600: "#dc2626",
  700: "#b91c1c",
  800: "#991b1b",
  900: "#7f1d1d",
  950: "#450a0a"
}, Ju = {
  50: "#f0f9ff",
  100: "#e0f2fe",
  200: "#bae6fd",
  300: "#7dd3fc",
  400: "#38bdf8",
  500: "#0ea5e9",
  600: "#0284c7",
  700: "#0369a1",
  800: "#075985",
  900: "#0c4a6e",
  950: "#082f49"
}, Ta = {
  colors: {
    primary: Pn,
    secondary: Lu,
    accent: Pn,
    // Use primary as default accent
    gray: Ku,
    success: Hu,
    warning: Gu,
    error: Bu,
    info: Ju,
    background: "#ffffff",
    surface: "#f8fafc",
    overlay: "rgba(0, 0, 0, 0.5)",
    text: "#0f172a",
    textMuted: "#64748b",
    textInverse: "#ffffff",
    border: "#e2e8f0",
    borderMuted: "#f1f5f9",
    borderStrong: "#cbd5e1"
  },
  typography: {
    fontFamily: {
      sans: 'Inter, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif',
      serif: 'Georgia, Cambria, "Times New Roman", Times, serif',
      mono: 'Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Liberation Mono", "Courier New", monospace'
    },
    fontSize: {
      xs: "0.75rem",
      sm: "0.875rem",
      base: "1rem",
      lg: "1.125rem",
      xl: "1.25rem",
      "2xl": "1.5rem",
      "3xl": "1.875rem",
      "4xl": "2.25rem",
      "5xl": "3rem",
      "6xl": "3.75rem"
    },
    fontWeight: {
      thin: 100,
      light: 300,
      normal: 400,
      medium: 500,
      semibold: 600,
      bold: 700,
      extrabold: 800,
      black: 900
    },
    lineHeight: {
      none: 1,
      tight: 1.25,
      snug: 1.375,
      normal: 1.5,
      relaxed: 1.625,
      loose: 2
    },
    letterSpacing: {
      tighter: "-0.05em",
      tight: "-0.025em",
      normal: "0em",
      wide: "0.025em",
      wider: "0.05em",
      widest: "0.1em"
    }
  },
  spacing: {
    0: "0px",
    px: "1px",
    0.5: "0.125rem",
    1: "0.25rem",
    1.5: "0.375rem",
    2: "0.5rem",
    2.5: "0.625rem",
    3: "0.75rem",
    3.5: "0.875rem",
    4: "1rem",
    5: "1.25rem",
    6: "1.5rem",
    7: "1.75rem",
    8: "2rem",
    9: "2.25rem",
    10: "2.5rem",
    11: "2.75rem",
    12: "3rem",
    14: "3.5rem",
    16: "4rem",
    20: "5rem",
    24: "6rem",
    28: "7rem",
    32: "8rem",
    36: "9rem",
    40: "10rem",
    44: "11rem",
    48: "12rem",
    52: "13rem",
    56: "14rem",
    60: "15rem",
    64: "16rem",
    72: "18rem",
    80: "20rem",
    96: "24rem"
  },
  borderRadius: {
    none: "0px",
    sm: "0.125rem",
    base: "0.25rem",
    md: "0.375rem",
    lg: "0.5rem",
    xl: "0.75rem",
    "2xl": "1rem",
    "3xl": "1.5rem",
    full: "9999px"
  },
  shadows: {
    sm: "0 1px 2px 0 rgb(0 0 0 / 0.05)",
    base: "0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)",
    md: "0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)",
    lg: "0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)",
    xl: "0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)",
    "2xl": "0 25px 50px -12px rgb(0 0 0 / 0.25)",
    inner: "inset 0 2px 4px 0 rgb(0 0 0 / 0.05)",
    none: "none"
  },
  breakpoints: {
    sm: "640px",
    md: "768px",
    lg: "1024px",
    xl: "1280px",
    "2xl": "1536px"
  },
  animations: {
    duration: {
      75: "75ms",
      100: "100ms",
      150: "150ms",
      200: "200ms",
      300: "300ms",
      500: "500ms",
      700: "700ms",
      1e3: "1000ms"
    },
    easing: {
      linear: "linear",
      in: "cubic-bezier(0.4, 0, 1, 1)",
      out: "cubic-bezier(0, 0, 0.2, 1)",
      "in-out": "cubic-bezier(0.4, 0, 0.2, 1)"
    }
  }
}, Wu = {
  colors: {
    ...Ta.colors,
    background: "#0f172a",
    surface: "#1e293b",
    overlay: "rgba(0, 0, 0, 0.8)",
    text: "#f8fafc",
    textMuted: "#94a3b8",
    textInverse: "#0f172a",
    border: "#334155",
    borderMuted: "#1e293b",
    borderStrong: "#475569"
  }
};
function Jr(e, t) {
  const r = { ...e };
  for (const n in t)
    if (Object.prototype.hasOwnProperty.call(t, n)) {
      const a = t[n], s = r[n];
      a && typeof a == "object" && !Array.isArray(a) && s && typeof s == "object" && !Array.isArray(s) ? r[n] = Jr(s, a) : a !== void 0 && (r[n] = a);
    }
  return r;
}
function id(e = {}) {
  return Jr(Ta, e);
}
function cd(e, t) {
  return t === "dark" ? Jr(e, Wu) : e;
}
function ud(e, t = "guti") {
  const r = {};
  return Object.entries(e.colors).forEach(([n, a]) => {
    typeof a == "object" && a !== null ? Object.entries(a).forEach(([s, i]) => {
      r[`--${t}-color-${n}-${s}`] = i;
    }) : r[`--${t}-color-${n}`] = a;
  }), Object.entries(e.typography.fontFamily).forEach(([n, a]) => {
    r[`--${t}-font-family-${n}`] = a;
  }), Object.entries(e.typography.fontSize).forEach(([n, a]) => {
    r[`--${t}-font-size-${n}`] = a;
  }), Object.entries(e.typography.fontWeight).forEach(([n, a]) => {
    r[`--${t}-font-weight-${n}`] = a.toString();
  }), Object.entries(e.typography.lineHeight).forEach(([n, a]) => {
    r[`--${t}-line-height-${n}`] = a.toString();
  }), Object.entries(e.typography.letterSpacing).forEach(([n, a]) => {
    r[`--${t}-letter-spacing-${n}`] = a;
  }), Object.entries(e.spacing).forEach(([n, a]) => {
    r[`--${t}-spacing-${n}`] = a;
  }), Object.entries(e.borderRadius).forEach(([n, a]) => {
    r[`--${t}-radius-${n}`] = a;
  }), Object.entries(e.shadows).forEach(([n, a]) => {
    r[`--${t}-shadow-${n}`] = a;
  }), Object.entries(e.breakpoints).forEach(([n, a]) => {
    r[`--${t}-breakpoint-${n}`] = a;
  }), Object.entries(e.animations.duration).forEach(([n, a]) => {
    r[`--${t}-duration-${n}`] = a;
  }), Object.entries(e.animations.easing).forEach(([n, a]) => {
    r[`--${t}-easing-${n}`] = a;
  }), r;
}
const dd = "0.1.0";
export {
  dd as VERSION,
  cd as applyThemeMode,
  jn as cloneBlock,
  Qu as cloneBlocks,
  La as containerBlockSchema,
  ad as createSchemaRegistry,
  id as createThemeTokens,
  rd as createValidator,
  Wu as darkThemeOverrides,
  Gt as deepClone,
  Ta as defaultThemeTokens,
  za as generateBlockId,
  ud as generateCSSCustomProperties,
  kn as generateId,
  Xu as generatePageId,
  Uu as getBuiltInBlockTypes,
  Ka as gridBlockSchema,
  qa as headingBlockSchema,
  Va as imageBlockSchema,
  od as isBuiltInBlockType,
  nd as isValid,
  Yu as isValidId,
  ed as pageSchema,
  xa as paragraphBlockSchema,
  sd as registerBuiltInSchemas,
  Se as schemaRegistry,
  Ua as spacerBlockSchema,
  Da as textBlockSchema,
  td as themeSchema,
  Br as validateSchema,
  Fa as videoBlockSchema
};
