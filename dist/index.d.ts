export * from './types';
export * from './utils';
export * from './schemas';
export type { GutiBuilderConfig, PageData, BlockData, BlockDefinition, ThemeConfig, ThemeTokens, ValidationResult, SchemaRegistry, } from './types';
export { createThemeTokens, applyThemeMode, generateCSSCustomProperties, } from './theme/utils';
export { defaultThemeTokens, darkThemeOverrides, } from './theme/tokens';
export { schemaRegistry, createSchemaRegistry, registerBuiltInSchemas, } from './schemas';
export declare const VERSION = "0.1.0";
