(function(M,ae){typeof exports=="object"&&typeof module<"u"?ae(exports):typeof define=="function"&&define.amd?define(["exports"],ae):(M=typeof globalThis<"u"?globalThis:M||self,ae(<PERSON><PERSON>={}))})(this,function(M){"use strict";var _d=Object.defineProperty;var vd=(M,ae,Me)=>ae in M?_d(M,ae,{enumerable:!0,configurable:!0,writable:!0,value:Me}):M[ae]=Me;var Ka=(M,ae,Me)=>vd(M,typeof ae!="symbol"?ae+"":ae,Me);const ae="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict";let Me=(e=21)=>{let t="",r=crypto.getRandomValues(new Uint8Array(e|=0));for(;e--;)t+=ae[r[e]&63];return t};function Rt(e){const t=Me(10);return e?`${e}_${t}`:t}function tn(){return Rt("block")}function Ha(){return Rt("page")}function Ga(e){return/^[a-zA-Z0-9_-]+$/.test(e)}function ct(e){if(e===null||typeof e!="object")return e;if(e instanceof Date)return new Date(e.getTime());if(Array.isArray(e))return e.map(r=>ct(r));const t={};for(const r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=ct(e[r]));return t}function Ct(e){const t=ct(e);return t.id=tn(),t.children&&(t.children=t.children.map(r=>Ct(r))),t}function Ba(e){return e.map(t=>Ct(t))}const Ja={type:"object",required:["id","title","blocks","createdAt","updatedAt"],properties:{id:{type:"string",description:"Unique page identifier"},title:{type:"string",minLength:1,maxLength:200,description:"Page title","x-ui":{component:"input",label:"Page Title",placeholder:"Enter page title"}},description:{type:"string",maxLength:500,description:"Page description","x-ui":{component:"textarea",label:"Page Description",placeholder:"Enter page description"}},meta:{type:"object",description:"Page metadata",additionalProperties:!0},blocks:{type:"array",description:"Root blocks in the page",items:{$ref:"#/definitions/blockData"}},theme:{type:"object",description:"Page-specific theme overrides",additionalProperties:!0},settings:{type:"object",description:"Page settings",properties:{layout:{type:"string",enum:["full-width","contained","centered"],default:"contained","x-ui":{component:"select",label:"Page Layout"}},background:{type:"string",description:"Page background color or image","x-ui":{component:"color",label:"Background"}},className:{type:"string",description:"Custom CSS classes","x-ui":{component:"input",label:"CSS Classes",placeholder:"custom-class another-class"}}}},createdAt:{type:"string",format:"date-time",description:"Page creation timestamp"},updatedAt:{type:"string",format:"date-time",description:"Page last update timestamp"}},definitions:{blockData:{type:"object",required:["id","type","props"],properties:{id:{type:"string",description:"Unique block identifier"},type:{type:"string",description:"Block type identifier"},props:{type:"object",description:"Block properties/content",additionalProperties:!0},children:{type:"array",description:"Child blocks",items:{$ref:"#/definitions/blockData"}},style:{type:"object",description:"Block styling",additionalProperties:!0},settings:{type:"object",description:"Block settings",properties:{className:{type:"string"},responsive:{type:"object",properties:{mobile:{type:"boolean"},tablet:{type:"boolean"},desktop:{type:"boolean"}}},animation:{type:"object",properties:{type:{type:"string"},duration:{type:"number"},delay:{type:"number"}}}}}}}}},rn={type:"object",required:["content"],properties:{content:{type:"string",description:"Text content","x-ui":{component:"rich-text",label:"Content",placeholder:"Enter text content"}},tag:{type:"string",enum:["span","p","div"],default:"span","x-ui":{component:"select",label:"HTML Tag"}},className:{type:"string","x-ui":{component:"input",label:"CSS Classes",placeholder:"custom-class"}}}},nn={type:"object",required:["src","alt"],properties:{src:{type:"string",format:"uri",description:"Image source URL","x-ui":{component:"image",label:"Image"}},alt:{type:"string",description:"Alternative text","x-ui":{component:"input",label:"Alt Text",placeholder:"Describe the image"}},width:{type:"number",minimum:1,"x-ui":{component:"input",label:"Width",placeholder:"400"}},height:{type:"number",minimum:1,"x-ui":{component:"input",label:"Height",placeholder:"300"}},objectFit:{type:"string",enum:["cover","contain","fill","none","scale-down"],default:"cover","x-ui":{component:"select",label:"Object Fit"}},loading:{type:"string",enum:["lazy","eager"],default:"lazy","x-ui":{component:"select",label:"Loading"}},className:{type:"string","x-ui":{component:"input",label:"CSS Classes"}}}},an={type:"object",properties:{src:{type:"string",format:"uri","x-ui":{component:"video",label:"Video File"}},poster:{type:"string",format:"uri","x-ui":{component:"image",label:"Poster Image"}},width:{type:"number",minimum:1,"x-ui":{component:"input",label:"Width"}},height:{type:"number",minimum:1,"x-ui":{component:"input",label:"Height"}},autoplay:{type:"boolean",default:!1,"x-ui":{component:"toggle",label:"Autoplay"}},loop:{type:"boolean",default:!1,"x-ui":{component:"toggle",label:"Loop"}},muted:{type:"boolean",default:!1,"x-ui":{component:"toggle",label:"Muted"}},controls:{type:"boolean",default:!0,"x-ui":{component:"toggle",label:"Show Controls"}},embedUrl:{type:"string",format:"uri","x-ui":{component:"input",label:"Embed URL",placeholder:"YouTube or Vimeo URL"}},embedType:{type:"string",enum:["youtube","vimeo","custom"],"x-ui":{component:"select",label:"Embed Type"}},className:{type:"string","x-ui":{component:"input",label:"CSS Classes"}}},oneOf:[{required:["src"]},{required:["embedUrl"]}]},sn={type:"object",required:["content","level"],properties:{content:{type:"string",minLength:1,"x-ui":{component:"input",label:"Heading Text",placeholder:"Enter heading"}},level:{type:"integer",minimum:1,maximum:6,default:1,"x-ui":{component:"select",label:"Heading Level"}},className:{type:"string","x-ui":{component:"input",label:"CSS Classes"}}}},on={type:"object",required:["content"],properties:{content:{type:"string",minLength:1,"x-ui":{component:"rich-text",label:"Paragraph Content",placeholder:"Enter paragraph text"}},className:{type:"string","x-ui":{component:"input",label:"CSS Classes"}}}},cn={type:"object",required:["height"],properties:{height:{type:"string",pattern:"^\\d+(px|rem|em|vh|%)$",default:"20px","x-ui":{component:"input",label:"Height",placeholder:"20px, 2rem, 5vh"}},className:{type:"string","x-ui":{component:"input",label:"CSS Classes"}}}},dn={type:"object",properties:{direction:{type:"string",enum:["row","column"],default:"row","x-ui":{component:"select",label:"Direction"}},gap:{type:"string",pattern:"^\\d+(px|rem|em)$",default:"16px","x-ui":{component:"input",label:"Gap",placeholder:"16px, 1rem"}},alignItems:{type:"string",enum:["flex-start","center","flex-end","stretch"],default:"stretch","x-ui":{component:"select",label:"Align Items"}},justifyContent:{type:"string",enum:["flex-start","center","flex-end","space-between","space-around","space-evenly"],default:"flex-start","x-ui":{component:"select",label:"Justify Content"}},wrap:{type:"boolean",default:!1,"x-ui":{component:"toggle",label:"Wrap Items"}},className:{type:"string","x-ui":{component:"input",label:"CSS Classes"}}}},un={type:"object",required:["columns"],properties:{columns:{type:"integer",minimum:1,maximum:12,default:2,"x-ui":{component:"slider",label:"Columns"}},gap:{type:"string",pattern:"^\\d+(px|rem|em)$",default:"16px","x-ui":{component:"input",label:"Gap"}},columnGap:{type:"string",pattern:"^\\d+(px|rem|em)$","x-ui":{component:"input",label:"Column Gap"}},rowGap:{type:"string",pattern:"^\\d+(px|rem|em)$","x-ui":{component:"input",label:"Row Gap"}},mobileColumns:{type:"integer",minimum:1,maximum:12,default:1,"x-ui":{component:"slider",label:"Mobile Columns"}},tabletColumns:{type:"integer",minimum:1,maximum:12,"x-ui":{component:"slider",label:"Tablet Columns"}},className:{type:"string","x-ui":{component:"input",label:"CSS Classes"}}}},Se={type:"object",properties:{50:{type:"string",pattern:"^#[0-9a-fA-F]{6}$"},100:{type:"string",pattern:"^#[0-9a-fA-F]{6}$"},200:{type:"string",pattern:"^#[0-9a-fA-F]{6}$"},300:{type:"string",pattern:"^#[0-9a-fA-F]{6}$"},400:{type:"string",pattern:"^#[0-9a-fA-F]{6}$"},500:{type:"string",pattern:"^#[0-9a-fA-F]{6}$"},600:{type:"string",pattern:"^#[0-9a-fA-F]{6}$"},700:{type:"string",pattern:"^#[0-9a-fA-F]{6}$"},800:{type:"string",pattern:"^#[0-9a-fA-F]{6}$"},900:{type:"string",pattern:"^#[0-9a-fA-F]{6}$"},950:{type:"string",pattern:"^#[0-9a-fA-F]{6}$"}},required:["500"],additionalProperties:!1},Wa={type:"object",properties:{colors:{type:"object",properties:{primary:Se,secondary:Se,accent:Se,gray:Se,success:Se,warning:Se,error:Se,info:Se,background:{type:"string",pattern:"^#[0-9a-fA-F]{6}$"},surface:{type:"string",pattern:"^#[0-9a-fA-F]{6}$"},overlay:{type:"string",pattern:"^#[0-9a-fA-F]{6}$"},text:{type:"string",pattern:"^#[0-9a-fA-F]{6}$"},textMuted:{type:"string",pattern:"^#[0-9a-fA-F]{6}$"},textInverse:{type:"string",pattern:"^#[0-9a-fA-F]{6}$"},border:{type:"string",pattern:"^#[0-9a-fA-F]{6}$"},borderMuted:{type:"string",pattern:"^#[0-9a-fA-F]{6}$"},borderStrong:{type:"string",pattern:"^#[0-9a-fA-F]{6}$"}},required:["primary","background","text"],additionalProperties:!1},typography:{type:"object",properties:{fontFamily:{type:"object",properties:{sans:{type:"string"},serif:{type:"string"},mono:{type:"string"}},required:["sans"],additionalProperties:!0},fontSize:{type:"object",properties:{xs:{type:"string",pattern:"^\\d+(\\.\\d+)?(px|rem|em)$"},sm:{type:"string",pattern:"^\\d+(\\.\\d+)?(px|rem|em)$"},base:{type:"string",pattern:"^\\d+(\\.\\d+)?(px|rem|em)$"},lg:{type:"string",pattern:"^\\d+(\\.\\d+)?(px|rem|em)$"},xl:{type:"string",pattern:"^\\d+(\\.\\d+)?(px|rem|em)$"},"2xl":{type:"string",pattern:"^\\d+(\\.\\d+)?(px|rem|em)$"},"3xl":{type:"string",pattern:"^\\d+(\\.\\d+)?(px|rem|em)$"},"4xl":{type:"string",pattern:"^\\d+(\\.\\d+)?(px|rem|em)$"},"5xl":{type:"string",pattern:"^\\d+(\\.\\d+)?(px|rem|em)$"},"6xl":{type:"string",pattern:"^\\d+(\\.\\d+)?(px|rem|em)$"}},required:["base"],additionalProperties:!0},fontWeight:{type:"object",properties:{thin:{type:"number",minimum:100,maximum:900},light:{type:"number",minimum:100,maximum:900},normal:{type:"number",minimum:100,maximum:900},medium:{type:"number",minimum:100,maximum:900},semibold:{type:"number",minimum:100,maximum:900},bold:{type:"number",minimum:100,maximum:900},extrabold:{type:"number",minimum:100,maximum:900},black:{type:"number",minimum:100,maximum:900}},required:["normal"],additionalProperties:!0},lineHeight:{type:"object",properties:{none:{type:"number",minimum:.5,maximum:3},tight:{type:"number",minimum:.5,maximum:3},snug:{type:"number",minimum:.5,maximum:3},normal:{type:"number",minimum:.5,maximum:3},relaxed:{type:"number",minimum:.5,maximum:3},loose:{type:"number",minimum:.5,maximum:3}},required:["normal"],additionalProperties:!0},letterSpacing:{type:"object",properties:{tighter:{type:"string",pattern:"^-?\\d+(\\.\\d+)?(px|em)$"},tight:{type:"string",pattern:"^-?\\d+(\\.\\d+)?(px|em)$"},normal:{type:"string",pattern:"^-?\\d+(\\.\\d+)?(px|em)$"},wide:{type:"string",pattern:"^-?\\d+(\\.\\d+)?(px|em)$"},wider:{type:"string",pattern:"^-?\\d+(\\.\\d+)?(px|em)$"},widest:{type:"string",pattern:"^-?\\d+(\\.\\d+)?(px|em)$"}},required:["normal"],additionalProperties:!0}},required:["fontFamily","fontSize","fontWeight","lineHeight"],additionalProperties:!1},spacing:{type:"object",patternProperties:{"^(0|px|0\\.5|[1-9]\\d*(\\.5)?)$":{type:"string",pattern:"^\\d+(\\.\\d+)?(px|rem|em)$"}},additionalProperties:!1},borderRadius:{type:"object",properties:{none:{type:"string",pattern:"^\\d+(\\.\\d+)?(px|rem|em)$"},sm:{type:"string",pattern:"^\\d+(\\.\\d+)?(px|rem|em)$"},base:{type:"string",pattern:"^\\d+(\\.\\d+)?(px|rem|em)$"},md:{type:"string",pattern:"^\\d+(\\.\\d+)?(px|rem|em)$"},lg:{type:"string",pattern:"^\\d+(\\.\\d+)?(px|rem|em)$"},xl:{type:"string",pattern:"^\\d+(\\.\\d+)?(px|rem|em)$"},"2xl":{type:"string",pattern:"^\\d+(\\.\\d+)?(px|rem|em)$"},"3xl":{type:"string",pattern:"^\\d+(\\.\\d+)?(px|rem|em)$"},full:{type:"string",enum:["9999px","50%","100%"]}},required:["base"],additionalProperties:!0},shadows:{type:"object",properties:{sm:{type:"string"},base:{type:"string"},md:{type:"string"},lg:{type:"string"},xl:{type:"string"},"2xl":{type:"string"},inner:{type:"string"},none:{type:"string",enum:["none"]}},required:["base"],additionalProperties:!0},breakpoints:{type:"object",properties:{sm:{type:"string",pattern:"^\\d+px$"},md:{type:"string",pattern:"^\\d+px$"},lg:{type:"string",pattern:"^\\d+px$"},xl:{type:"string",pattern:"^\\d+px$"},"2xl":{type:"string",pattern:"^\\d+px$"}},required:["sm","md","lg"],additionalProperties:!0},animations:{type:"object",properties:{duration:{type:"object",patternProperties:{"^\\d+$":{type:"string",pattern:"^\\d+ms$"}},additionalProperties:!1},easing:{type:"object",properties:{linear:{type:"string"},in:{type:"string"},out:{type:"string"},"in-out":{type:"string"}},required:["linear"],additionalProperties:!0}},additionalProperties:!1}},required:["colors","typography","spacing"],additionalProperties:!1};function ln(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var At={exports:{}},fn={},ie={},ze={},Be={},D={},Je={};(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.regexpCode=e.getEsmExportName=e.getProperty=e.safeStringify=e.stringify=e.strConcat=e.addCodeArg=e.str=e._=e.nil=e._Code=e.Name=e.IDENTIFIER=e._CodeOrName=void 0;class t{}e._CodeOrName=t,e.IDENTIFIER=/^[a-z$_][a-z$_0-9]*$/i;class r extends t{constructor($){if(super(),!e.IDENTIFIER.test($))throw new Error("CodeGen: name must be a valid identifier");this.str=$}toString(){return this.str}emptyStr(){return!1}get names(){return{[this.str]:1}}}e.Name=r;class n extends t{constructor($){super(),this._items=typeof $=="string"?[$]:$}toString(){return this.str}emptyStr(){if(this._items.length>1)return!1;const $=this._items[0];return $===""||$==='""'}get str(){var $;return($=this._str)!==null&&$!==void 0?$:this._str=this._items.reduce((P,j)=>`${P}${j}`,"")}get names(){var $;return($=this._names)!==null&&$!==void 0?$:this._names=this._items.reduce((P,j)=>(j instanceof r&&(P[j.str]=(P[j.str]||0)+1),P),{})}}e._Code=n,e.nil=new n("");function a(m,...$){const P=[m[0]];let j=0;for(;j<$.length;)c(P,$[j]),P.push(m[++j]);return new n(P)}e._=a;const s=new n("+");function i(m,...$){const P=[b(m[0])];let j=0;for(;j<$.length;)P.push(s),c(P,$[j]),P.push(s,b(m[++j]));return l(P),new n(P)}e.str=i;function c(m,$){$ instanceof n?m.push(...$._items):$ instanceof r?m.push($):m.push(p($))}e.addCodeArg=c;function l(m){let $=1;for(;$<m.length-1;){if(m[$]===s){const P=d(m[$-1],m[$+1]);if(P!==void 0){m.splice($-1,3,P);continue}m[$++]="+"}$++}}function d(m,$){if($==='""')return m;if(m==='""')return $;if(typeof m=="string")return $ instanceof r||m[m.length-1]!=='"'?void 0:typeof $!="string"?`${m.slice(0,-1)}${$}"`:$[0]==='"'?m.slice(0,-1)+$.slice(1):void 0;if(typeof $=="string"&&$[0]==='"'&&!(m instanceof r))return`"${m}${$.slice(1)}`}function f(m,$){return $.emptyStr()?m:m.emptyStr()?$:i`${m}${$}`}e.strConcat=f;function p(m){return typeof m=="number"||typeof m=="boolean"||m===null?m:b(Array.isArray(m)?m.join(","):m)}function S(m){return new n(b(m))}e.stringify=S;function b(m){return JSON.stringify(m).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029")}e.safeStringify=b;function _(m){return typeof m=="string"&&e.IDENTIFIER.test(m)?new n(`.${m}`):a`[${m}]`}e.getProperty=_;function v(m){if(typeof m=="string"&&e.IDENTIFIER.test(m))return new n(`${m}`);throw new Error(`CodeGen: invalid export name: ${m}, use explicit $id name mapping`)}e.getEsmExportName=v;function y(m){return new n(m.toString())}e.regexpCode=y})(Je);var Mt={};(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.ValueScope=e.ValueScopeName=e.Scope=e.varKinds=e.UsedValueState=void 0;const t=Je;class r extends Error{constructor(d){super(`CodeGen: "code" for ${d} not defined`),this.value=d.value}}var n;(function(l){l[l.Started=0]="Started",l[l.Completed=1]="Completed"})(n||(e.UsedValueState=n={})),e.varKinds={const:new t.Name("const"),let:new t.Name("let"),var:new t.Name("var")};class a{constructor({prefixes:d,parent:f}={}){this._names={},this._prefixes=d,this._parent=f}toName(d){return d instanceof t.Name?d:this.name(d)}name(d){return new t.Name(this._newName(d))}_newName(d){const f=this._names[d]||this._nameGroup(d);return`${d}${f.index++}`}_nameGroup(d){var f,p;if(!((p=(f=this._parent)===null||f===void 0?void 0:f._prefixes)===null||p===void 0)&&p.has(d)||this._prefixes&&!this._prefixes.has(d))throw new Error(`CodeGen: prefix "${d}" is not allowed in this scope`);return this._names[d]={prefix:d,index:0}}}e.Scope=a;class s extends t.Name{constructor(d,f){super(f),this.prefix=d}setValue(d,{property:f,itemIndex:p}){this.value=d,this.scopePath=(0,t._)`.${new t.Name(f)}[${p}]`}}e.ValueScopeName=s;const i=(0,t._)`\n`;class c extends a{constructor(d){super(d),this._values={},this._scope=d.scope,this.opts={...d,_n:d.lines?i:t.nil}}get(){return this._scope}name(d){return new s(d,this._newName(d))}value(d,f){var p;if(f.ref===void 0)throw new Error("CodeGen: ref must be passed in value");const S=this.toName(d),{prefix:b}=S,_=(p=f.key)!==null&&p!==void 0?p:f.ref;let v=this._values[b];if(v){const $=v.get(_);if($)return $}else v=this._values[b]=new Map;v.set(_,S);const y=this._scope[b]||(this._scope[b]=[]),m=y.length;return y[m]=f.ref,S.setValue(f,{property:b,itemIndex:m}),S}getValue(d,f){const p=this._values[d];if(p)return p.get(f)}scopeRefs(d,f=this._values){return this._reduceValues(f,p=>{if(p.scopePath===void 0)throw new Error(`CodeGen: name "${p}" has no value`);return(0,t._)`${d}${p.scopePath}`})}scopeCode(d=this._values,f,p){return this._reduceValues(d,S=>{if(S.value===void 0)throw new Error(`CodeGen: name "${S}" has no value`);return S.value.code},f,p)}_reduceValues(d,f,p={},S){let b=t.nil;for(const _ in d){const v=d[_];if(!v)continue;const y=p[_]=p[_]||new Map;v.forEach(m=>{if(y.has(m))return;y.set(m,n.Started);let $=f(m);if($){const P=this.opts.es5?e.varKinds.var:e.varKinds.const;b=(0,t._)`${b}${P} ${m} = ${$};${this.opts._n}`}else if($=S==null?void 0:S(m))b=(0,t._)`${b}${$}${this.opts._n}`;else throw new r(m);y.set(m,n.Completed)})}return b}}e.ValueScope=c})(Mt),function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.or=e.and=e.not=e.CodeGen=e.operators=e.varKinds=e.ValueScopeName=e.ValueScope=e.Scope=e.Name=e.regexpCode=e.stringify=e.getProperty=e.nil=e.strConcat=e.str=e._=void 0;const t=Je,r=Mt;var n=Je;Object.defineProperty(e,"_",{enumerable:!0,get:function(){return n._}}),Object.defineProperty(e,"str",{enumerable:!0,get:function(){return n.str}}),Object.defineProperty(e,"strConcat",{enumerable:!0,get:function(){return n.strConcat}}),Object.defineProperty(e,"nil",{enumerable:!0,get:function(){return n.nil}}),Object.defineProperty(e,"getProperty",{enumerable:!0,get:function(){return n.getProperty}}),Object.defineProperty(e,"stringify",{enumerable:!0,get:function(){return n.stringify}}),Object.defineProperty(e,"regexpCode",{enumerable:!0,get:function(){return n.regexpCode}}),Object.defineProperty(e,"Name",{enumerable:!0,get:function(){return n.Name}});var a=Mt;Object.defineProperty(e,"Scope",{enumerable:!0,get:function(){return a.Scope}}),Object.defineProperty(e,"ValueScope",{enumerable:!0,get:function(){return a.ValueScope}}),Object.defineProperty(e,"ValueScopeName",{enumerable:!0,get:function(){return a.ValueScopeName}}),Object.defineProperty(e,"varKinds",{enumerable:!0,get:function(){return a.varKinds}}),e.operators={GT:new t._Code(">"),GTE:new t._Code(">="),LT:new t._Code("<"),LTE:new t._Code("<="),EQ:new t._Code("==="),NEQ:new t._Code("!=="),NOT:new t._Code("!"),OR:new t._Code("||"),AND:new t._Code("&&"),ADD:new t._Code("+")};class s{optimizeNodes(){return this}optimizeNames(o,u){return this}}class i extends s{constructor(o,u,w){super(),this.varKind=o,this.name=u,this.rhs=w}render({es5:o,_n:u}){const w=o?r.varKinds.var:this.varKind,I=this.rhs===void 0?"":` = ${this.rhs}`;return`${w} ${this.name}${I};`+u}optimizeNames(o,u){if(o[this.name.str])return this.rhs&&(this.rhs=F(this.rhs,o,u)),this}get names(){return this.rhs instanceof t._CodeOrName?this.rhs.names:{}}}class c extends s{constructor(o,u,w){super(),this.lhs=o,this.rhs=u,this.sideEffects=w}render({_n:o}){return`${this.lhs} = ${this.rhs};`+o}optimizeNames(o,u){if(!(this.lhs instanceof t.Name&&!o[this.lhs.str]&&!this.sideEffects))return this.rhs=F(this.rhs,o,u),this}get names(){const o=this.lhs instanceof t.Name?{}:{...this.lhs.names};return G(o,this.rhs)}}class l extends c{constructor(o,u,w,I){super(o,w,I),this.op=u}render({_n:o}){return`${this.lhs} ${this.op}= ${this.rhs};`+o}}class d extends s{constructor(o){super(),this.label=o,this.names={}}render({_n:o}){return`${this.label}:`+o}}class f extends s{constructor(o){super(),this.label=o,this.names={}}render({_n:o}){return`break${this.label?` ${this.label}`:""};`+o}}class p extends s{constructor(o){super(),this.error=o}render({_n:o}){return`throw ${this.error};`+o}get names(){return this.error.names}}class S extends s{constructor(o){super(),this.code=o}render({_n:o}){return`${this.code};`+o}optimizeNodes(){return`${this.code}`?this:void 0}optimizeNames(o,u){return this.code=F(this.code,o,u),this}get names(){return this.code instanceof t._CodeOrName?this.code.names:{}}}class b extends s{constructor(o=[]){super(),this.nodes=o}render(o){return this.nodes.reduce((u,w)=>u+w.render(o),"")}optimizeNodes(){const{nodes:o}=this;let u=o.length;for(;u--;){const w=o[u].optimizeNodes();Array.isArray(w)?o.splice(u,1,...w):w?o[u]=w:o.splice(u,1)}return o.length>0?this:void 0}optimizeNames(o,u){const{nodes:w}=this;let I=w.length;for(;I--;){const R=w[I];R.optimizeNames(o,u)||(Q(o,R.names),w.splice(I,1))}return w.length>0?this:void 0}get names(){return this.nodes.reduce((o,u)=>V(o,u.names),{})}}class _ extends b{render(o){return"{"+o._n+super.render(o)+"}"+o._n}}class v extends b{}class y extends _{}y.kind="else";class m extends _{constructor(o,u){super(u),this.condition=o}render(o){let u=`if(${this.condition})`+super.render(o);return this.else&&(u+="else "+this.else.render(o)),u}optimizeNodes(){super.optimizeNodes();const o=this.condition;if(o===!0)return this.nodes;let u=this.else;if(u){const w=u.optimizeNodes();u=this.else=Array.isArray(w)?new y(w):w}if(u)return o===!1?u instanceof m?u:u.nodes:this.nodes.length?this:new m(be(o),u instanceof m?[u]:u.nodes);if(!(o===!1||!this.nodes.length))return this}optimizeNames(o,u){var w;if(this.else=(w=this.else)===null||w===void 0?void 0:w.optimizeNames(o,u),!!(super.optimizeNames(o,u)||this.else))return this.condition=F(this.condition,o,u),this}get names(){const o=super.names;return G(o,this.condition),this.else&&V(o,this.else.names),o}}m.kind="if";class $ extends _{}$.kind="for";class P extends ${constructor(o){super(),this.iteration=o}render(o){return`for(${this.iteration})`+super.render(o)}optimizeNames(o,u){if(super.optimizeNames(o,u))return this.iteration=F(this.iteration,o,u),this}get names(){return V(super.names,this.iteration.names)}}class j extends ${constructor(o,u,w,I){super(),this.varKind=o,this.name=u,this.from=w,this.to=I}render(o){const u=o.es5?r.varKinds.var:this.varKind,{name:w,from:I,to:R}=this;return`for(${u} ${w}=${I}; ${w}<${R}; ${w}++)`+super.render(o)}get names(){const o=G(super.names,this.from);return G(o,this.to)}}class O extends ${constructor(o,u,w,I){super(),this.loop=o,this.varKind=u,this.name=w,this.iterable=I}render(o){return`for(${this.varKind} ${this.name} ${this.loop} ${this.iterable})`+super.render(o)}optimizeNames(o,u){if(super.optimizeNames(o,u))return this.iterable=F(this.iterable,o,u),this}get names(){return V(super.names,this.iterable.names)}}class L extends _{constructor(o,u,w){super(),this.name=o,this.args=u,this.async=w}render(o){return`${this.async?"async ":""}function ${this.name}(${this.args})`+super.render(o)}}L.kind="func";class J extends b{render(o){return"return "+super.render(o)}}J.kind="return";class oe extends _{render(o){let u="try"+super.render(o);return this.catch&&(u+=this.catch.render(o)),this.finally&&(u+=this.finally.render(o)),u}optimizeNodes(){var o,u;return super.optimizeNodes(),(o=this.catch)===null||o===void 0||o.optimizeNodes(),(u=this.finally)===null||u===void 0||u.optimizeNodes(),this}optimizeNames(o,u){var w,I;return super.optimizeNames(o,u),(w=this.catch)===null||w===void 0||w.optimizeNames(o,u),(I=this.finally)===null||I===void 0||I.optimizeNames(o,u),this}get names(){const o=super.names;return this.catch&&V(o,this.catch.names),this.finally&&V(o,this.finally.names),o}}class he extends _{constructor(o){super(),this.error=o}render(o){return`catch(${this.error})`+super.render(o)}}he.kind="catch";class ve extends _{render(o){return"finally"+super.render(o)}}ve.kind="finally";class C{constructor(o,u={}){this._values={},this._blockStarts=[],this._constants={},this.opts={...u,_n:u.lines?`
`:""},this._extScope=o,this._scope=new r.Scope({parent:o}),this._nodes=[new v]}toString(){return this._root.render(this.opts)}name(o){return this._scope.name(o)}scopeName(o){return this._extScope.name(o)}scopeValue(o,u){const w=this._extScope.value(o,u);return(this._values[w.prefix]||(this._values[w.prefix]=new Set)).add(w),w}getScopeValue(o,u){return this._extScope.getValue(o,u)}scopeRefs(o){return this._extScope.scopeRefs(o,this._values)}scopeCode(){return this._extScope.scopeCode(this._values)}_def(o,u,w,I){const R=this._scope.toName(u);return w!==void 0&&I&&(this._constants[R.str]=w),this._leafNode(new i(o,R,w)),R}const(o,u,w){return this._def(r.varKinds.const,o,u,w)}let(o,u,w){return this._def(r.varKinds.let,o,u,w)}var(o,u,w){return this._def(r.varKinds.var,o,u,w)}assign(o,u,w){return this._leafNode(new c(o,u,w))}add(o,u){return this._leafNode(new l(o,e.operators.ADD,u))}code(o){return typeof o=="function"?o():o!==t.nil&&this._leafNode(new S(o)),this}object(...o){const u=["{"];for(const[w,I]of o)u.length>1&&u.push(","),u.push(w),(w!==I||this.opts.es5)&&(u.push(":"),(0,t.addCodeArg)(u,I));return u.push("}"),new t._Code(u)}if(o,u,w){if(this._blockNode(new m(o)),u&&w)this.code(u).else().code(w).endIf();else if(u)this.code(u).endIf();else if(w)throw new Error('CodeGen: "else" body without "then" body');return this}elseIf(o){return this._elseNode(new m(o))}else(){return this._elseNode(new y)}endIf(){return this._endBlockNode(m,y)}_for(o,u){return this._blockNode(o),u&&this.code(u).endFor(),this}for(o,u){return this._for(new P(o),u)}forRange(o,u,w,I,R=this.opts.es5?r.varKinds.var:r.varKinds.let){const x=this._scope.toName(o);return this._for(new j(R,x,u,w),()=>I(x))}forOf(o,u,w,I=r.varKinds.const){const R=this._scope.toName(o);if(this.opts.es5){const x=u instanceof t.Name?u:this.var("_arr",u);return this.forRange("_i",0,(0,t._)`${x}.length`,U=>{this.var(R,(0,t._)`${x}[${U}]`),w(R)})}return this._for(new O("of",I,R,u),()=>w(R))}forIn(o,u,w,I=this.opts.es5?r.varKinds.var:r.varKinds.const){if(this.opts.ownProperties)return this.forOf(o,(0,t._)`Object.keys(${u})`,w);const R=this._scope.toName(o);return this._for(new O("in",I,R,u),()=>w(R))}endFor(){return this._endBlockNode($)}label(o){return this._leafNode(new d(o))}break(o){return this._leafNode(new f(o))}return(o){const u=new J;if(this._blockNode(u),this.code(o),u.nodes.length!==1)throw new Error('CodeGen: "return" should have one node');return this._endBlockNode(J)}try(o,u,w){if(!u&&!w)throw new Error('CodeGen: "try" without "catch" and "finally"');const I=new oe;if(this._blockNode(I),this.code(o),u){const R=this.name("e");this._currNode=I.catch=new he(R),u(R)}return w&&(this._currNode=I.finally=new ve,this.code(w)),this._endBlockNode(he,ve)}throw(o){return this._leafNode(new p(o))}block(o,u){return this._blockStarts.push(this._nodes.length),o&&this.code(o).endBlock(u),this}endBlock(o){const u=this._blockStarts.pop();if(u===void 0)throw new Error("CodeGen: not in self-balancing block");const w=this._nodes.length-u;if(w<0||o!==void 0&&w!==o)throw new Error(`CodeGen: wrong number of nodes: ${w} vs ${o} expected`);return this._nodes.length=u,this}func(o,u=t.nil,w,I){return this._blockNode(new L(o,u,w)),I&&this.code(I).endFunc(),this}endFunc(){return this._endBlockNode(L)}optimize(o=1){for(;o-- >0;)this._root.optimizeNodes(),this._root.optimizeNames(this._root.names,this._constants)}_leafNode(o){return this._currNode.nodes.push(o),this}_blockNode(o){this._currNode.nodes.push(o),this._nodes.push(o)}_endBlockNode(o,u){const w=this._currNode;if(w instanceof o||u&&w instanceof u)return this._nodes.pop(),this;throw new Error(`CodeGen: not in block "${u?`${o.kind}/${u.kind}`:o.kind}"`)}_elseNode(o){const u=this._currNode;if(!(u instanceof m))throw new Error('CodeGen: "else" without "if"');return this._currNode=u.else=o,this}get _root(){return this._nodes[0]}get _currNode(){const o=this._nodes;return o[o.length-1]}set _currNode(o){const u=this._nodes;u[u.length-1]=o}}e.CodeGen=C;function V(g,o){for(const u in o)g[u]=(g[u]||0)+(o[u]||0);return g}function G(g,o){return o instanceof t._CodeOrName?V(g,o.names):g}function F(g,o,u){if(g instanceof t.Name)return w(g);if(!I(g))return g;return new t._Code(g._items.reduce((R,x)=>(x instanceof t.Name&&(x=w(x)),x instanceof t._Code?R.push(...x._items):R.push(x),R),[]));function w(R){const x=u[R.str];return x===void 0||o[R.str]!==1?R:(delete o[R.str],x)}function I(R){return R instanceof t._Code&&R._items.some(x=>x instanceof t.Name&&o[x.str]===1&&u[x.str]!==void 0)}}function Q(g,o){for(const u in o)g[u]=(g[u]||0)-(o[u]||0)}function be(g){return typeof g=="boolean"||typeof g=="number"||g===null?!g:(0,t._)`!${E(g)}`}e.not=be;const we=h(e.operators.AND);function ot(...g){return g.reduce(we)}e.and=ot;const Ae=h(e.operators.OR);function k(...g){return g.reduce(Ae)}e.or=k;function h(g){return(o,u)=>o===t.nil?u:u===t.nil?o:(0,t._)`${E(o)} ${g} ${E(u)}`}function E(g){return g instanceof t.Name?g:(0,t._)`(${g})`}}(D);var N={};Object.defineProperty(N,"__esModule",{value:!0}),N.checkStrictMode=N.getErrorPath=N.Type=N.useFunc=N.setEvaluated=N.evaluatedPropsToName=N.mergeEvaluated=N.eachItem=N.unescapeJsonPointer=N.escapeJsonPointer=N.escapeFragment=N.unescapeFragment=N.schemaRefOrVal=N.schemaHasRulesButRef=N.schemaHasRules=N.checkUnknownRules=N.alwaysValidSchema=N.toHash=void 0;const K=D,Xa=Je;function Za(e){const t={};for(const r of e)t[r]=!0;return t}N.toHash=Za;function Ya(e,t){return typeof t=="boolean"?t:Object.keys(t).length===0?!0:(mn(e,t),!pn(t,e.self.RULES.all))}N.alwaysValidSchema=Ya;function mn(e,t=e.schema){const{opts:r,self:n}=e;if(!r.strictSchema||typeof t=="boolean")return;const a=n.RULES.keywords;for(const s in t)a[s]||_n(e,`unknown keyword: "${s}"`)}N.checkUnknownRules=mn;function pn(e,t){if(typeof e=="boolean")return!e;for(const r in e)if(t[r])return!0;return!1}N.schemaHasRules=pn;function Qa(e,t){if(typeof e=="boolean")return!e;for(const r in e)if(r!=="$ref"&&t.all[r])return!0;return!1}N.schemaHasRulesButRef=Qa;function es({topSchemaRef:e,schemaPath:t},r,n,a){if(!a){if(typeof r=="number"||typeof r=="boolean")return r;if(typeof r=="string")return(0,K._)`${r}`}return(0,K._)`${e}${t}${(0,K.getProperty)(n)}`}N.schemaRefOrVal=es;function ts(e){return hn(decodeURIComponent(e))}N.unescapeFragment=ts;function rs(e){return encodeURIComponent(zt(e))}N.escapeFragment=rs;function zt(e){return typeof e=="number"?`${e}`:e.replace(/~/g,"~0").replace(/\//g,"~1")}N.escapeJsonPointer=zt;function hn(e){return e.replace(/~1/g,"/").replace(/~0/g,"~")}N.unescapeJsonPointer=hn;function ns(e,t){if(Array.isArray(e))for(const r of e)t(r);else t(e)}N.eachItem=ns;function yn({mergeNames:e,mergeToName:t,mergeValues:r,resultToName:n}){return(a,s,i,c)=>{const l=i===void 0?s:i instanceof K.Name?(s instanceof K.Name?e(a,s,i):t(a,s,i),i):s instanceof K.Name?(t(a,i,s),s):r(s,i);return c===K.Name&&!(l instanceof K.Name)?n(a,l):l}}N.mergeEvaluated={props:yn({mergeNames:(e,t,r)=>e.if((0,K._)`${r} !== true && ${t} !== undefined`,()=>{e.if((0,K._)`${t} === true`,()=>e.assign(r,!0),()=>e.assign(r,(0,K._)`${r} || {}`).code((0,K._)`Object.assign(${r}, ${t})`))}),mergeToName:(e,t,r)=>e.if((0,K._)`${r} !== true`,()=>{t===!0?e.assign(r,!0):(e.assign(r,(0,K._)`${r} || {}`),Dt(e,r,t))}),mergeValues:(e,t)=>e===!0?!0:{...e,...t},resultToName:gn}),items:yn({mergeNames:(e,t,r)=>e.if((0,K._)`${r} !== true && ${t} !== undefined`,()=>e.assign(r,(0,K._)`${t} === true ? true : ${r} > ${t} ? ${r} : ${t}`)),mergeToName:(e,t,r)=>e.if((0,K._)`${r} !== true`,()=>e.assign(r,t===!0?!0:(0,K._)`${r} > ${t} ? ${r} : ${t}`)),mergeValues:(e,t)=>e===!0?!0:Math.max(e,t),resultToName:(e,t)=>e.var("items",t)})};function gn(e,t){if(t===!0)return e.var("props",!0);const r=e.var("props",(0,K._)`{}`);return t!==void 0&&Dt(e,r,t),r}N.evaluatedPropsToName=gn;function Dt(e,t,r){Object.keys(r).forEach(n=>e.assign((0,K._)`${t}${(0,K.getProperty)(n)}`,!0))}N.setEvaluated=Dt;const $n={};function as(e,t){return e.scopeValue("func",{ref:t,code:$n[t.code]||($n[t.code]=new Xa._Code(t.code))})}N.useFunc=as;var Vt;(function(e){e[e.Num=0]="Num",e[e.Str=1]="Str"})(Vt||(N.Type=Vt={}));function ss(e,t,r){if(e instanceof K.Name){const n=t===Vt.Num;return r?n?(0,K._)`"[" + ${e} + "]"`:(0,K._)`"['" + ${e} + "']"`:n?(0,K._)`"/" + ${e}`:(0,K._)`"/" + ${e}.replace(/~/g, "~0").replace(/\\//g, "~1")`}return r?(0,K.getProperty)(e).toString():"/"+zt(e)}N.getErrorPath=ss;function _n(e,t,r=e.opts.strictSchema){if(r){if(t=`strict mode: ${t}`,r===!0)throw new Error(t);e.self.logger.warn(t)}}N.checkStrictMode=_n;var le={};Object.defineProperty(le,"__esModule",{value:!0});const ee=D,os={data:new ee.Name("data"),valCxt:new ee.Name("valCxt"),instancePath:new ee.Name("instancePath"),parentData:new ee.Name("parentData"),parentDataProperty:new ee.Name("parentDataProperty"),rootData:new ee.Name("rootData"),dynamicAnchors:new ee.Name("dynamicAnchors"),vErrors:new ee.Name("vErrors"),errors:new ee.Name("errors"),this:new ee.Name("this"),self:new ee.Name("self"),scope:new ee.Name("scope"),json:new ee.Name("json"),jsonPos:new ee.Name("jsonPos"),jsonLen:new ee.Name("jsonLen"),jsonPart:new ee.Name("jsonPart")};le.default=os,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.extendErrors=e.resetErrorsCount=e.reportExtraError=e.reportError=e.keyword$DataError=e.keywordError=void 0;const t=D,r=N,n=le;e.keywordError={message:({keyword:y})=>(0,t.str)`must pass "${y}" keyword validation`},e.keyword$DataError={message:({keyword:y,schemaType:m})=>m?(0,t.str)`"${y}" keyword must be ${m} ($data)`:(0,t.str)`"${y}" keyword is invalid ($data)`};function a(y,m=e.keywordError,$,P){const{it:j}=y,{gen:O,compositeRule:L,allErrors:J}=j,oe=p(y,m,$);P??(L||J)?l(O,oe):d(j,(0,t._)`[${oe}]`)}e.reportError=a;function s(y,m=e.keywordError,$){const{it:P}=y,{gen:j,compositeRule:O,allErrors:L}=P,J=p(y,m,$);l(j,J),O||L||d(P,n.default.vErrors)}e.reportExtraError=s;function i(y,m){y.assign(n.default.errors,m),y.if((0,t._)`${n.default.vErrors} !== null`,()=>y.if(m,()=>y.assign((0,t._)`${n.default.vErrors}.length`,m),()=>y.assign(n.default.vErrors,null)))}e.resetErrorsCount=i;function c({gen:y,keyword:m,schemaValue:$,data:P,errsCount:j,it:O}){if(j===void 0)throw new Error("ajv implementation error");const L=y.name("err");y.forRange("i",j,n.default.errors,J=>{y.const(L,(0,t._)`${n.default.vErrors}[${J}]`),y.if((0,t._)`${L}.instancePath === undefined`,()=>y.assign((0,t._)`${L}.instancePath`,(0,t.strConcat)(n.default.instancePath,O.errorPath))),y.assign((0,t._)`${L}.schemaPath`,(0,t.str)`${O.errSchemaPath}/${m}`),O.opts.verbose&&(y.assign((0,t._)`${L}.schema`,$),y.assign((0,t._)`${L}.data`,P))})}e.extendErrors=c;function l(y,m){const $=y.const("err",m);y.if((0,t._)`${n.default.vErrors} === null`,()=>y.assign(n.default.vErrors,(0,t._)`[${$}]`),(0,t._)`${n.default.vErrors}.push(${$})`),y.code((0,t._)`${n.default.errors}++`)}function d(y,m){const{gen:$,validateName:P,schemaEnv:j}=y;j.$async?$.throw((0,t._)`new ${y.ValidationError}(${m})`):($.assign((0,t._)`${P}.errors`,m),$.return(!1))}const f={keyword:new t.Name("keyword"),schemaPath:new t.Name("schemaPath"),params:new t.Name("params"),propertyName:new t.Name("propertyName"),message:new t.Name("message"),schema:new t.Name("schema"),parentSchema:new t.Name("parentSchema")};function p(y,m,$){const{createErrors:P}=y.it;return P===!1?(0,t._)`{}`:S(y,m,$)}function S(y,m,$={}){const{gen:P,it:j}=y,O=[b(j,$),_(y,$)];return v(y,m,O),P.object(...O)}function b({errorPath:y},{instancePath:m}){const $=m?(0,t.str)`${y}${(0,r.getErrorPath)(m,r.Type.Str)}`:y;return[n.default.instancePath,(0,t.strConcat)(n.default.instancePath,$)]}function _({keyword:y,it:{errSchemaPath:m}},{schemaPath:$,parentSchema:P}){let j=P?m:(0,t.str)`${m}/${y}`;return $&&(j=(0,t.str)`${j}${(0,r.getErrorPath)($,r.Type.Str)}`),[f.schemaPath,j]}function v(y,{params:m,message:$},P){const{keyword:j,data:O,schemaValue:L,it:J}=y,{opts:oe,propertyName:he,topSchemaRef:ve,schemaPath:C}=J;P.push([f.keyword,j],[f.params,typeof m=="function"?m(y):m||(0,t._)`{}`]),oe.messages&&P.push([f.message,typeof $=="function"?$(y):$]),oe.verbose&&P.push([f.schema,L],[f.parentSchema,(0,t._)`${ve}${C}`],[n.default.data,O]),he&&P.push([f.propertyName,he])}}(Be),Object.defineProperty(ze,"__esModule",{value:!0}),ze.boolOrEmptySchema=ze.topBoolOrEmptySchema=void 0;const is=Be,cs=D,ds=le,us={message:"boolean schema is false"};function ls(e){const{gen:t,schema:r,validateName:n}=e;r===!1?vn(e,!1):typeof r=="object"&&r.$async===!0?t.return(ds.default.data):(t.assign((0,cs._)`${n}.errors`,null),t.return(!0))}ze.topBoolOrEmptySchema=ls;function fs(e,t){const{gen:r,schema:n}=e;n===!1?(r.var(t,!1),vn(e)):r.var(t,!0)}ze.boolOrEmptySchema=fs;function vn(e,t){const{gen:r,data:n}=e,a={gen:r,keyword:"false schema",data:n,schema:!1,schemaCode:!1,schemaValue:!1,params:{},it:e};(0,is.reportError)(a,us,void 0,t)}var W={},Ne={};Object.defineProperty(Ne,"__esModule",{value:!0}),Ne.getRules=Ne.isJSONType=void 0;const ms=["string","number","integer","boolean","null","object","array"],ps=new Set(ms);function hs(e){return typeof e=="string"&&ps.has(e)}Ne.isJSONType=hs;function ys(){const e={number:{type:"number",rules:[]},string:{type:"string",rules:[]},array:{type:"array",rules:[]},object:{type:"object",rules:[]}};return{types:{...e,integer:!0,boolean:!0,null:!0},rules:[{rules:[]},e.number,e.string,e.array,e.object],post:{rules:[]},all:{},keywords:{}}}Ne.getRules=ys;var ye={};Object.defineProperty(ye,"__esModule",{value:!0}),ye.shouldUseRule=ye.shouldUseGroup=ye.schemaHasRulesForType=void 0;function gs({schema:e,self:t},r){const n=t.RULES.types[r];return n&&n!==!0&&bn(e,n)}ye.schemaHasRulesForType=gs;function bn(e,t){return t.rules.some(r=>wn(e,r))}ye.shouldUseGroup=bn;function wn(e,t){var r;return e[t.keyword]!==void 0||((r=t.definition.implements)===null||r===void 0?void 0:r.some(n=>e[n]!==void 0))}ye.shouldUseRule=wn,Object.defineProperty(W,"__esModule",{value:!0}),W.reportTypeError=W.checkDataTypes=W.checkDataType=W.coerceAndCheckDataType=W.getJSONTypes=W.getSchemaTypes=W.DataType=void 0;const $s=Ne,_s=ye,vs=Be,z=D,Sn=N;var De;(function(e){e[e.Correct=0]="Correct",e[e.Wrong=1]="Wrong"})(De||(W.DataType=De={}));function bs(e){const t=En(e.type);if(t.includes("null")){if(e.nullable===!1)throw new Error("type: null contradicts nullable: false")}else{if(!t.length&&e.nullable!==void 0)throw new Error('"nullable" cannot be used without "type"');e.nullable===!0&&t.push("null")}return t}W.getSchemaTypes=bs;function En(e){const t=Array.isArray(e)?e:e?[e]:[];if(t.every($s.isJSONType))return t;throw new Error("type must be JSONType or JSONType[]: "+t.join(","))}W.getJSONTypes=En;function ws(e,t){const{gen:r,data:n,opts:a}=e,s=Ss(t,a.coerceTypes),i=t.length>0&&!(s.length===0&&t.length===1&&(0,_s.schemaHasRulesForType)(e,t[0]));if(i){const c=Ft(t,n,a.strictNumbers,De.Wrong);r.if(c,()=>{s.length?Es(e,t,s):Ut(e)})}return i}W.coerceAndCheckDataType=ws;const Pn=new Set(["string","number","integer","boolean","null"]);function Ss(e,t){return t?e.filter(r=>Pn.has(r)||t==="array"&&r==="array"):[]}function Es(e,t,r){const{gen:n,data:a,opts:s}=e,i=n.let("dataType",(0,z._)`typeof ${a}`),c=n.let("coerced",(0,z._)`undefined`);s.coerceTypes==="array"&&n.if((0,z._)`${i} == 'object' && Array.isArray(${a}) && ${a}.length == 1`,()=>n.assign(a,(0,z._)`${a}[0]`).assign(i,(0,z._)`typeof ${a}`).if(Ft(t,a,s.strictNumbers),()=>n.assign(c,a))),n.if((0,z._)`${c} !== undefined`);for(const d of r)(Pn.has(d)||d==="array"&&s.coerceTypes==="array")&&l(d);n.else(),Ut(e),n.endIf(),n.if((0,z._)`${c} !== undefined`,()=>{n.assign(a,c),Ps(e,c)});function l(d){switch(d){case"string":n.elseIf((0,z._)`${i} == "number" || ${i} == "boolean"`).assign(c,(0,z._)`"" + ${a}`).elseIf((0,z._)`${a} === null`).assign(c,(0,z._)`""`);return;case"number":n.elseIf((0,z._)`${i} == "boolean" || ${a} === null
              || (${i} == "string" && ${a} && ${a} == +${a})`).assign(c,(0,z._)`+${a}`);return;case"integer":n.elseIf((0,z._)`${i} === "boolean" || ${a} === null
              || (${i} === "string" && ${a} && ${a} == +${a} && !(${a} % 1))`).assign(c,(0,z._)`+${a}`);return;case"boolean":n.elseIf((0,z._)`${a} === "false" || ${a} === 0 || ${a} === null`).assign(c,!1).elseIf((0,z._)`${a} === "true" || ${a} === 1`).assign(c,!0);return;case"null":n.elseIf((0,z._)`${a} === "" || ${a} === 0 || ${a} === false`),n.assign(c,null);return;case"array":n.elseIf((0,z._)`${i} === "string" || ${i} === "number"
              || ${i} === "boolean" || ${a} === null`).assign(c,(0,z._)`[${a}]`)}}}function Ps({gen:e,parentData:t,parentDataProperty:r},n){e.if((0,z._)`${t} !== undefined`,()=>e.assign((0,z._)`${t}[${r}]`,n))}function qt(e,t,r,n=De.Correct){const a=n===De.Correct?z.operators.EQ:z.operators.NEQ;let s;switch(e){case"null":return(0,z._)`${t} ${a} null`;case"array":s=(0,z._)`Array.isArray(${t})`;break;case"object":s=(0,z._)`${t} && typeof ${t} == "object" && !Array.isArray(${t})`;break;case"integer":s=i((0,z._)`!(${t} % 1) && !isNaN(${t})`);break;case"number":s=i();break;default:return(0,z._)`typeof ${t} ${a} ${e}`}return n===De.Correct?s:(0,z.not)(s);function i(c=z.nil){return(0,z.and)((0,z._)`typeof ${t} == "number"`,c,r?(0,z._)`isFinite(${t})`:z.nil)}}W.checkDataType=qt;function Ft(e,t,r,n){if(e.length===1)return qt(e[0],t,r,n);let a;const s=(0,Sn.toHash)(e);if(s.array&&s.object){const i=(0,z._)`typeof ${t} != "object"`;a=s.null?i:(0,z._)`!${t} || ${i}`,delete s.null,delete s.array,delete s.object}else a=z.nil;s.number&&delete s.integer;for(const i in s)a=(0,z.and)(a,qt(i,t,r,n));return a}W.checkDataTypes=Ft;const ks={message:({schema:e})=>`must be ${e}`,params:({schema:e,schemaValue:t})=>typeof e=="string"?(0,z._)`{type: ${e}}`:(0,z._)`{type: ${t}}`};function Ut(e){const t=js(e);(0,vs.reportError)(t,ks)}W.reportTypeError=Ut;function js(e){const{gen:t,data:r,schema:n}=e,a=(0,Sn.schemaRefOrVal)(e,n,"type");return{gen:t,keyword:"type",data:r,schema:n.type,schemaCode:a,schemaValue:a,parentSchema:n,params:{},it:e}}var dt={};Object.defineProperty(dt,"__esModule",{value:!0}),dt.assignDefaults=void 0;const Ve=D,Ns=N;function Os(e,t){const{properties:r,items:n}=e.schema;if(t==="object"&&r)for(const a in r)kn(e,a,r[a].default);else t==="array"&&Array.isArray(n)&&n.forEach((a,s)=>kn(e,s,a.default))}dt.assignDefaults=Os;function kn(e,t,r){const{gen:n,compositeRule:a,data:s,opts:i}=e;if(r===void 0)return;const c=(0,Ve._)`${s}${(0,Ve.getProperty)(t)}`;if(a){(0,Ns.checkStrictMode)(e,`default is ignored for: ${c}`);return}let l=(0,Ve._)`${c} === undefined`;i.useDefaults==="empty"&&(l=(0,Ve._)`${l} || ${c} === null || ${c} === ""`),n.if(l,(0,Ve._)`${c} = ${(0,Ve.stringify)(r)}`)}var fe={},q={};Object.defineProperty(q,"__esModule",{value:!0}),q.validateUnion=q.validateArray=q.usePattern=q.callValidateCode=q.schemaProperties=q.allSchemaProperties=q.noPropertyInData=q.propertyInData=q.isOwnProperty=q.hasPropFunc=q.reportMissingProp=q.checkMissingProp=q.checkReportMissingProp=void 0;const H=D,xt=N,Ee=le,Ts=N;function Is(e,t){const{gen:r,data:n,it:a}=e;r.if(Kt(r,n,t,a.opts.ownProperties),()=>{e.setParams({missingProperty:(0,H._)`${t}`},!0),e.error()})}q.checkReportMissingProp=Is;function Rs({gen:e,data:t,it:{opts:r}},n,a){return(0,H.or)(...n.map(s=>(0,H.and)(Kt(e,t,s,r.ownProperties),(0,H._)`${a} = ${s}`)))}q.checkMissingProp=Rs;function Cs(e,t){e.setParams({missingProperty:t},!0),e.error()}q.reportMissingProp=Cs;function jn(e){return e.scopeValue("func",{ref:Object.prototype.hasOwnProperty,code:(0,H._)`Object.prototype.hasOwnProperty`})}q.hasPropFunc=jn;function Lt(e,t,r){return(0,H._)`${jn(e)}.call(${t}, ${r})`}q.isOwnProperty=Lt;function As(e,t,r,n){const a=(0,H._)`${t}${(0,H.getProperty)(r)} !== undefined`;return n?(0,H._)`${a} && ${Lt(e,t,r)}`:a}q.propertyInData=As;function Kt(e,t,r,n){const a=(0,H._)`${t}${(0,H.getProperty)(r)} === undefined`;return n?(0,H.or)(a,(0,H.not)(Lt(e,t,r))):a}q.noPropertyInData=Kt;function Nn(e){return e?Object.keys(e).filter(t=>t!=="__proto__"):[]}q.allSchemaProperties=Nn;function Ms(e,t){return Nn(t).filter(r=>!(0,xt.alwaysValidSchema)(e,t[r]))}q.schemaProperties=Ms;function zs({schemaCode:e,data:t,it:{gen:r,topSchemaRef:n,schemaPath:a,errorPath:s},it:i},c,l,d){const f=d?(0,H._)`${e}, ${t}, ${n}${a}`:t,p=[[Ee.default.instancePath,(0,H.strConcat)(Ee.default.instancePath,s)],[Ee.default.parentData,i.parentData],[Ee.default.parentDataProperty,i.parentDataProperty],[Ee.default.rootData,Ee.default.rootData]];i.opts.dynamicRef&&p.push([Ee.default.dynamicAnchors,Ee.default.dynamicAnchors]);const S=(0,H._)`${f}, ${r.object(...p)}`;return l!==H.nil?(0,H._)`${c}.call(${l}, ${S})`:(0,H._)`${c}(${S})`}q.callValidateCode=zs;const Ds=(0,H._)`new RegExp`;function Vs({gen:e,it:{opts:t}},r){const n=t.unicodeRegExp?"u":"",{regExp:a}=t.code,s=a(r,n);return e.scopeValue("pattern",{key:s.toString(),ref:s,code:(0,H._)`${a.code==="new RegExp"?Ds:(0,Ts.useFunc)(e,a)}(${r}, ${n})`})}q.usePattern=Vs;function qs(e){const{gen:t,data:r,keyword:n,it:a}=e,s=t.name("valid");if(a.allErrors){const c=t.let("valid",!0);return i(()=>t.assign(c,!1)),c}return t.var(s,!0),i(()=>t.break()),s;function i(c){const l=t.const("len",(0,H._)`${r}.length`);t.forRange("i",0,l,d=>{e.subschema({keyword:n,dataProp:d,dataPropType:xt.Type.Num},s),t.if((0,H.not)(s),c)})}}q.validateArray=qs;function Fs(e){const{gen:t,schema:r,keyword:n,it:a}=e;if(!Array.isArray(r))throw new Error("ajv implementation error");if(r.some(l=>(0,xt.alwaysValidSchema)(a,l))&&!a.opts.unevaluated)return;const i=t.let("valid",!1),c=t.name("_valid");t.block(()=>r.forEach((l,d)=>{const f=e.subschema({keyword:n,schemaProp:d,compositeRule:!0},c);t.assign(i,(0,H._)`${i} || ${c}`),e.mergeValidEvaluated(f,c)||t.if((0,H.not)(i))})),e.result(i,()=>e.reset(),()=>e.error(!0))}q.validateUnion=Fs,Object.defineProperty(fe,"__esModule",{value:!0}),fe.validateKeywordUsage=fe.validSchemaType=fe.funcKeywordCode=fe.macroKeywordCode=void 0;const te=D,Oe=le,Us=q,xs=Be;function Ls(e,t){const{gen:r,keyword:n,schema:a,parentSchema:s,it:i}=e,c=t.macro.call(i.self,a,s,i),l=Tn(r,n,c);i.opts.validateSchema!==!1&&i.self.validateSchema(c,!0);const d=r.name("valid");e.subschema({schema:c,schemaPath:te.nil,errSchemaPath:`${i.errSchemaPath}/${n}`,topSchemaRef:l,compositeRule:!0},d),e.pass(d,()=>e.error(!0))}fe.macroKeywordCode=Ls;function Ks(e,t){var r;const{gen:n,keyword:a,schema:s,parentSchema:i,$data:c,it:l}=e;Gs(l,t);const d=!c&&t.compile?t.compile.call(l.self,s,i,l):t.validate,f=Tn(n,a,d),p=n.let("valid");e.block$data(p,S),e.ok((r=t.valid)!==null&&r!==void 0?r:p);function S(){if(t.errors===!1)v(),t.modifying&&On(e),y(()=>e.error());else{const m=t.async?b():_();t.modifying&&On(e),y(()=>Hs(e,m))}}function b(){const m=n.let("ruleErrs",null);return n.try(()=>v((0,te._)`await `),$=>n.assign(p,!1).if((0,te._)`${$} instanceof ${l.ValidationError}`,()=>n.assign(m,(0,te._)`${$}.errors`),()=>n.throw($))),m}function _(){const m=(0,te._)`${f}.errors`;return n.assign(m,null),v(te.nil),m}function v(m=t.async?(0,te._)`await `:te.nil){const $=l.opts.passContext?Oe.default.this:Oe.default.self,P=!("compile"in t&&!c||t.schema===!1);n.assign(p,(0,te._)`${m}${(0,Us.callValidateCode)(e,f,$,P)}`,t.modifying)}function y(m){var $;n.if((0,te.not)(($=t.valid)!==null&&$!==void 0?$:p),m)}}fe.funcKeywordCode=Ks;function On(e){const{gen:t,data:r,it:n}=e;t.if(n.parentData,()=>t.assign(r,(0,te._)`${n.parentData}[${n.parentDataProperty}]`))}function Hs(e,t){const{gen:r}=e;r.if((0,te._)`Array.isArray(${t})`,()=>{r.assign(Oe.default.vErrors,(0,te._)`${Oe.default.vErrors} === null ? ${t} : ${Oe.default.vErrors}.concat(${t})`).assign(Oe.default.errors,(0,te._)`${Oe.default.vErrors}.length`),(0,xs.extendErrors)(e)},()=>e.error())}function Gs({schemaEnv:e},t){if(t.async&&!e.$async)throw new Error("async keyword in sync schema")}function Tn(e,t,r){if(r===void 0)throw new Error(`keyword "${t}" failed to compile`);return e.scopeValue("keyword",typeof r=="function"?{ref:r}:{ref:r,code:(0,te.stringify)(r)})}function Bs(e,t,r=!1){return!t.length||t.some(n=>n==="array"?Array.isArray(e):n==="object"?e&&typeof e=="object"&&!Array.isArray(e):typeof e==n||r&&typeof e>"u")}fe.validSchemaType=Bs;function Js({schema:e,opts:t,self:r,errSchemaPath:n},a,s){if(Array.isArray(a.keyword)?!a.keyword.includes(s):a.keyword!==s)throw new Error("ajv implementation error");const i=a.dependencies;if(i!=null&&i.some(c=>!Object.prototype.hasOwnProperty.call(e,c)))throw new Error(`parent schema must have dependencies of ${s}: ${i.join(",")}`);if(a.validateSchema&&!a.validateSchema(e[s])){const l=`keyword "${s}" value is invalid at path "${n}": `+r.errorsText(a.validateSchema.errors);if(t.validateSchema==="log")r.logger.error(l);else throw new Error(l)}}fe.validateKeywordUsage=Js;var Pe={};Object.defineProperty(Pe,"__esModule",{value:!0}),Pe.extendSubschemaMode=Pe.extendSubschemaData=Pe.getSubschema=void 0;const me=D,In=N;function Ws(e,{keyword:t,schemaProp:r,schema:n,schemaPath:a,errSchemaPath:s,topSchemaRef:i}){if(t!==void 0&&n!==void 0)throw new Error('both "keyword" and "schema" passed, only one allowed');if(t!==void 0){const c=e.schema[t];return r===void 0?{schema:c,schemaPath:(0,me._)`${e.schemaPath}${(0,me.getProperty)(t)}`,errSchemaPath:`${e.errSchemaPath}/${t}`}:{schema:c[r],schemaPath:(0,me._)`${e.schemaPath}${(0,me.getProperty)(t)}${(0,me.getProperty)(r)}`,errSchemaPath:`${e.errSchemaPath}/${t}/${(0,In.escapeFragment)(r)}`}}if(n!==void 0){if(a===void 0||s===void 0||i===void 0)throw new Error('"schemaPath", "errSchemaPath" and "topSchemaRef" are required with "schema"');return{schema:n,schemaPath:a,topSchemaRef:i,errSchemaPath:s}}throw new Error('either "keyword" or "schema" must be passed')}Pe.getSubschema=Ws;function Xs(e,t,{dataProp:r,dataPropType:n,data:a,dataTypes:s,propertyName:i}){if(a!==void 0&&r!==void 0)throw new Error('both "data" and "dataProp" passed, only one allowed');const{gen:c}=t;if(r!==void 0){const{errorPath:d,dataPathArr:f,opts:p}=t,S=c.let("data",(0,me._)`${t.data}${(0,me.getProperty)(r)}`,!0);l(S),e.errorPath=(0,me.str)`${d}${(0,In.getErrorPath)(r,n,p.jsPropertySyntax)}`,e.parentDataProperty=(0,me._)`${r}`,e.dataPathArr=[...f,e.parentDataProperty]}if(a!==void 0){const d=a instanceof me.Name?a:c.let("data",a,!0);l(d),i!==void 0&&(e.propertyName=i)}s&&(e.dataTypes=s);function l(d){e.data=d,e.dataLevel=t.dataLevel+1,e.dataTypes=[],t.definedProperties=new Set,e.parentData=t.data,e.dataNames=[...t.dataNames,d]}}Pe.extendSubschemaData=Xs;function Zs(e,{jtdDiscriminator:t,jtdMetadata:r,compositeRule:n,createErrors:a,allErrors:s}){n!==void 0&&(e.compositeRule=n),a!==void 0&&(e.createErrors=a),s!==void 0&&(e.allErrors=s),e.jtdDiscriminator=t,e.jtdMetadata=r}Pe.extendSubschemaMode=Zs;var Z={},Rn=function e(t,r){if(t===r)return!0;if(t&&r&&typeof t=="object"&&typeof r=="object"){if(t.constructor!==r.constructor)return!1;var n,a,s;if(Array.isArray(t)){if(n=t.length,n!=r.length)return!1;for(a=n;a--!==0;)if(!e(t[a],r[a]))return!1;return!0}if(t.constructor===RegExp)return t.source===r.source&&t.flags===r.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===r.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===r.toString();if(s=Object.keys(t),n=s.length,n!==Object.keys(r).length)return!1;for(a=n;a--!==0;)if(!Object.prototype.hasOwnProperty.call(r,s[a]))return!1;for(a=n;a--!==0;){var i=s[a];if(!e(t[i],r[i]))return!1}return!0}return t!==t&&r!==r},Cn={exports:{}},ke=Cn.exports=function(e,t,r){typeof t=="function"&&(r=t,t={}),r=t.cb||r;var n=typeof r=="function"?r:r.pre||function(){},a=r.post||function(){};ut(t,n,a,e,"",e)};ke.keywords={additionalItems:!0,items:!0,contains:!0,additionalProperties:!0,propertyNames:!0,not:!0,if:!0,then:!0,else:!0},ke.arrayKeywords={items:!0,allOf:!0,anyOf:!0,oneOf:!0},ke.propsKeywords={$defs:!0,definitions:!0,properties:!0,patternProperties:!0,dependencies:!0},ke.skipKeywords={default:!0,enum:!0,const:!0,required:!0,maximum:!0,minimum:!0,exclusiveMaximum:!0,exclusiveMinimum:!0,multipleOf:!0,maxLength:!0,minLength:!0,pattern:!0,format:!0,maxItems:!0,minItems:!0,uniqueItems:!0,maxProperties:!0,minProperties:!0};function ut(e,t,r,n,a,s,i,c,l,d){if(n&&typeof n=="object"&&!Array.isArray(n)){t(n,a,s,i,c,l,d);for(var f in n){var p=n[f];if(Array.isArray(p)){if(f in ke.arrayKeywords)for(var S=0;S<p.length;S++)ut(e,t,r,p[S],a+"/"+f+"/"+S,s,a,f,n,S)}else if(f in ke.propsKeywords){if(p&&typeof p=="object")for(var b in p)ut(e,t,r,p[b],a+"/"+f+"/"+Ys(b),s,a,f,n,b)}else(f in ke.keywords||e.allKeys&&!(f in ke.skipKeywords))&&ut(e,t,r,p,a+"/"+f,s,a,f,n)}r(n,a,s,i,c,l,d)}}function Ys(e){return e.replace(/~/g,"~0").replace(/\//g,"~1")}var Qs=Cn.exports;Object.defineProperty(Z,"__esModule",{value:!0}),Z.getSchemaRefs=Z.resolveUrl=Z.normalizeId=Z._getFullPath=Z.getFullPath=Z.inlineRef=void 0;const eo=N,to=Rn,ro=Qs,no=new Set(["type","format","pattern","maxLength","minLength","maxProperties","minProperties","maxItems","minItems","maximum","minimum","uniqueItems","multipleOf","required","enum","const"]);function ao(e,t=!0){return typeof e=="boolean"?!0:t===!0?!Ht(e):t?An(e)<=t:!1}Z.inlineRef=ao;const so=new Set(["$ref","$recursiveRef","$recursiveAnchor","$dynamicRef","$dynamicAnchor"]);function Ht(e){for(const t in e){if(so.has(t))return!0;const r=e[t];if(Array.isArray(r)&&r.some(Ht)||typeof r=="object"&&Ht(r))return!0}return!1}function An(e){let t=0;for(const r in e){if(r==="$ref")return 1/0;if(t++,!no.has(r)&&(typeof e[r]=="object"&&(0,eo.eachItem)(e[r],n=>t+=An(n)),t===1/0))return 1/0}return t}function Mn(e,t="",r){r!==!1&&(t=qe(t));const n=e.parse(t);return zn(e,n)}Z.getFullPath=Mn;function zn(e,t){return e.serialize(t).split("#")[0]+"#"}Z._getFullPath=zn;const oo=/#\/?$/;function qe(e){return e?e.replace(oo,""):""}Z.normalizeId=qe;function io(e,t,r){return r=qe(r),e.resolve(t,r)}Z.resolveUrl=io;const co=/^[a-z_][-a-z0-9._]*$/i;function uo(e,t){if(typeof e=="boolean")return{};const{schemaId:r,uriResolver:n}=this.opts,a=qe(e[r]||t),s={"":a},i=Mn(n,a,!1),c={},l=new Set;return ro(e,{allKeys:!0},(p,S,b,_)=>{if(_===void 0)return;const v=i+S;let y=s[_];typeof p[r]=="string"&&(y=m.call(this,p[r])),$.call(this,p.$anchor),$.call(this,p.$dynamicAnchor),s[S]=y;function m(P){const j=this.opts.uriResolver.resolve;if(P=qe(y?j(y,P):P),l.has(P))throw f(P);l.add(P);let O=this.refs[P];return typeof O=="string"&&(O=this.refs[O]),typeof O=="object"?d(p,O.schema,P):P!==qe(v)&&(P[0]==="#"?(d(p,c[P],P),c[P]=p):this.refs[P]=v),P}function $(P){if(typeof P=="string"){if(!co.test(P))throw new Error(`invalid anchor "${P}"`);m.call(this,`#${P}`)}}}),c;function d(p,S,b){if(S!==void 0&&!to(p,S))throw f(b)}function f(p){return new Error(`reference "${p}" resolves to more than one schema`)}}Z.getSchemaRefs=uo,Object.defineProperty(ie,"__esModule",{value:!0}),ie.getData=ie.KeywordCxt=ie.validateFunctionCode=void 0;const Dn=ze,Vn=W,Gt=ye,lt=W,lo=dt,We=fe,Bt=Pe,T=D,A=le,fo=Z,ge=N,Xe=Be;function mo(e){if(xn(e)&&(Ln(e),Un(e))){yo(e);return}qn(e,()=>(0,Dn.topBoolOrEmptySchema)(e))}ie.validateFunctionCode=mo;function qn({gen:e,validateName:t,schema:r,schemaEnv:n,opts:a},s){a.code.es5?e.func(t,(0,T._)`${A.default.data}, ${A.default.valCxt}`,n.$async,()=>{e.code((0,T._)`"use strict"; ${Fn(r,a)}`),ho(e,a),e.code(s)}):e.func(t,(0,T._)`${A.default.data}, ${po(a)}`,n.$async,()=>e.code(Fn(r,a)).code(s))}function po(e){return(0,T._)`{${A.default.instancePath}="", ${A.default.parentData}, ${A.default.parentDataProperty}, ${A.default.rootData}=${A.default.data}${e.dynamicRef?(0,T._)`, ${A.default.dynamicAnchors}={}`:T.nil}}={}`}function ho(e,t){e.if(A.default.valCxt,()=>{e.var(A.default.instancePath,(0,T._)`${A.default.valCxt}.${A.default.instancePath}`),e.var(A.default.parentData,(0,T._)`${A.default.valCxt}.${A.default.parentData}`),e.var(A.default.parentDataProperty,(0,T._)`${A.default.valCxt}.${A.default.parentDataProperty}`),e.var(A.default.rootData,(0,T._)`${A.default.valCxt}.${A.default.rootData}`),t.dynamicRef&&e.var(A.default.dynamicAnchors,(0,T._)`${A.default.valCxt}.${A.default.dynamicAnchors}`)},()=>{e.var(A.default.instancePath,(0,T._)`""`),e.var(A.default.parentData,(0,T._)`undefined`),e.var(A.default.parentDataProperty,(0,T._)`undefined`),e.var(A.default.rootData,A.default.data),t.dynamicRef&&e.var(A.default.dynamicAnchors,(0,T._)`{}`)})}function yo(e){const{schema:t,opts:r,gen:n}=e;qn(e,()=>{r.$comment&&t.$comment&&Hn(e),bo(e),n.let(A.default.vErrors,null),n.let(A.default.errors,0),r.unevaluated&&go(e),Kn(e),Eo(e)})}function go(e){const{gen:t,validateName:r}=e;e.evaluated=t.const("evaluated",(0,T._)`${r}.evaluated`),t.if((0,T._)`${e.evaluated}.dynamicProps`,()=>t.assign((0,T._)`${e.evaluated}.props`,(0,T._)`undefined`)),t.if((0,T._)`${e.evaluated}.dynamicItems`,()=>t.assign((0,T._)`${e.evaluated}.items`,(0,T._)`undefined`))}function Fn(e,t){const r=typeof e=="object"&&e[t.schemaId];return r&&(t.code.source||t.code.process)?(0,T._)`/*# sourceURL=${r} */`:T.nil}function $o(e,t){if(xn(e)&&(Ln(e),Un(e))){_o(e,t);return}(0,Dn.boolOrEmptySchema)(e,t)}function Un({schema:e,self:t}){if(typeof e=="boolean")return!e;for(const r in e)if(t.RULES.all[r])return!0;return!1}function xn(e){return typeof e.schema!="boolean"}function _o(e,t){const{schema:r,gen:n,opts:a}=e;a.$comment&&r.$comment&&Hn(e),wo(e),So(e);const s=n.const("_errs",A.default.errors);Kn(e,s),n.var(t,(0,T._)`${s} === ${A.default.errors}`)}function Ln(e){(0,ge.checkUnknownRules)(e),vo(e)}function Kn(e,t){if(e.opts.jtd)return Gn(e,[],!1,t);const r=(0,Vn.getSchemaTypes)(e.schema),n=(0,Vn.coerceAndCheckDataType)(e,r);Gn(e,r,!n,t)}function vo(e){const{schema:t,errSchemaPath:r,opts:n,self:a}=e;t.$ref&&n.ignoreKeywordsWithRef&&(0,ge.schemaHasRulesButRef)(t,a.RULES)&&a.logger.warn(`$ref: keywords ignored in schema at path "${r}"`)}function bo(e){const{schema:t,opts:r}=e;t.default!==void 0&&r.useDefaults&&r.strictSchema&&(0,ge.checkStrictMode)(e,"default is ignored in the schema root")}function wo(e){const t=e.schema[e.opts.schemaId];t&&(e.baseId=(0,fo.resolveUrl)(e.opts.uriResolver,e.baseId,t))}function So(e){if(e.schema.$async&&!e.schemaEnv.$async)throw new Error("async schema in sync schema")}function Hn({gen:e,schemaEnv:t,schema:r,errSchemaPath:n,opts:a}){const s=r.$comment;if(a.$comment===!0)e.code((0,T._)`${A.default.self}.logger.log(${s})`);else if(typeof a.$comment=="function"){const i=(0,T.str)`${n}/$comment`,c=e.scopeValue("root",{ref:t.root});e.code((0,T._)`${A.default.self}.opts.$comment(${s}, ${i}, ${c}.schema)`)}}function Eo(e){const{gen:t,schemaEnv:r,validateName:n,ValidationError:a,opts:s}=e;r.$async?t.if((0,T._)`${A.default.errors} === 0`,()=>t.return(A.default.data),()=>t.throw((0,T._)`new ${a}(${A.default.vErrors})`)):(t.assign((0,T._)`${n}.errors`,A.default.vErrors),s.unevaluated&&Po(e),t.return((0,T._)`${A.default.errors} === 0`))}function Po({gen:e,evaluated:t,props:r,items:n}){r instanceof T.Name&&e.assign((0,T._)`${t}.props`,r),n instanceof T.Name&&e.assign((0,T._)`${t}.items`,n)}function Gn(e,t,r,n){const{gen:a,schema:s,data:i,allErrors:c,opts:l,self:d}=e,{RULES:f}=d;if(s.$ref&&(l.ignoreKeywordsWithRef||!(0,ge.schemaHasRulesButRef)(s,f))){a.block(()=>Xn(e,"$ref",f.all.$ref.definition));return}l.jtd||ko(e,t),a.block(()=>{for(const S of f.rules)p(S);p(f.post)});function p(S){(0,Gt.shouldUseGroup)(s,S)&&(S.type?(a.if((0,lt.checkDataType)(S.type,i,l.strictNumbers)),Bn(e,S),t.length===1&&t[0]===S.type&&r&&(a.else(),(0,lt.reportTypeError)(e)),a.endIf()):Bn(e,S),c||a.if((0,T._)`${A.default.errors} === ${n||0}`))}}function Bn(e,t){const{gen:r,schema:n,opts:{useDefaults:a}}=e;a&&(0,lo.assignDefaults)(e,t.type),r.block(()=>{for(const s of t.rules)(0,Gt.shouldUseRule)(n,s)&&Xn(e,s.keyword,s.definition,t.type)})}function ko(e,t){e.schemaEnv.meta||!e.opts.strictTypes||(jo(e,t),e.opts.allowUnionTypes||No(e,t),Oo(e,e.dataTypes))}function jo(e,t){if(t.length){if(!e.dataTypes.length){e.dataTypes=t;return}t.forEach(r=>{Jn(e.dataTypes,r)||Jt(e,`type "${r}" not allowed by context "${e.dataTypes.join(",")}"`)}),Io(e,t)}}function No(e,t){t.length>1&&!(t.length===2&&t.includes("null"))&&Jt(e,"use allowUnionTypes to allow union type keyword")}function Oo(e,t){const r=e.self.RULES.all;for(const n in r){const a=r[n];if(typeof a=="object"&&(0,Gt.shouldUseRule)(e.schema,a)){const{type:s}=a.definition;s.length&&!s.some(i=>To(t,i))&&Jt(e,`missing type "${s.join(",")}" for keyword "${n}"`)}}}function To(e,t){return e.includes(t)||t==="number"&&e.includes("integer")}function Jn(e,t){return e.includes(t)||t==="integer"&&e.includes("number")}function Io(e,t){const r=[];for(const n of e.dataTypes)Jn(t,n)?r.push(n):t.includes("integer")&&n==="number"&&r.push("integer");e.dataTypes=r}function Jt(e,t){const r=e.schemaEnv.baseId+e.errSchemaPath;t+=` at "${r}" (strictTypes)`,(0,ge.checkStrictMode)(e,t,e.opts.strictTypes)}class Wn{constructor(t,r,n){if((0,We.validateKeywordUsage)(t,r,n),this.gen=t.gen,this.allErrors=t.allErrors,this.keyword=n,this.data=t.data,this.schema=t.schema[n],this.$data=r.$data&&t.opts.$data&&this.schema&&this.schema.$data,this.schemaValue=(0,ge.schemaRefOrVal)(t,this.schema,n,this.$data),this.schemaType=r.schemaType,this.parentSchema=t.schema,this.params={},this.it=t,this.def=r,this.$data)this.schemaCode=t.gen.const("vSchema",Zn(this.$data,t));else if(this.schemaCode=this.schemaValue,!(0,We.validSchemaType)(this.schema,r.schemaType,r.allowUndefined))throw new Error(`${n} value must be ${JSON.stringify(r.schemaType)}`);("code"in r?r.trackErrors:r.errors!==!1)&&(this.errsCount=t.gen.const("_errs",A.default.errors))}result(t,r,n){this.failResult((0,T.not)(t),r,n)}failResult(t,r,n){this.gen.if(t),n?n():this.error(),r?(this.gen.else(),r(),this.allErrors&&this.gen.endIf()):this.allErrors?this.gen.endIf():this.gen.else()}pass(t,r){this.failResult((0,T.not)(t),void 0,r)}fail(t){if(t===void 0){this.error(),this.allErrors||this.gen.if(!1);return}this.gen.if(t),this.error(),this.allErrors?this.gen.endIf():this.gen.else()}fail$data(t){if(!this.$data)return this.fail(t);const{schemaCode:r}=this;this.fail((0,T._)`${r} !== undefined && (${(0,T.or)(this.invalid$data(),t)})`)}error(t,r,n){if(r){this.setParams(r),this._error(t,n),this.setParams({});return}this._error(t,n)}_error(t,r){(t?Xe.reportExtraError:Xe.reportError)(this,this.def.error,r)}$dataError(){(0,Xe.reportError)(this,this.def.$dataError||Xe.keyword$DataError)}reset(){if(this.errsCount===void 0)throw new Error('add "trackErrors" to keyword definition');(0,Xe.resetErrorsCount)(this.gen,this.errsCount)}ok(t){this.allErrors||this.gen.if(t)}setParams(t,r){r?Object.assign(this.params,t):this.params=t}block$data(t,r,n=T.nil){this.gen.block(()=>{this.check$data(t,n),r()})}check$data(t=T.nil,r=T.nil){if(!this.$data)return;const{gen:n,schemaCode:a,schemaType:s,def:i}=this;n.if((0,T.or)((0,T._)`${a} === undefined`,r)),t!==T.nil&&n.assign(t,!0),(s.length||i.validateSchema)&&(n.elseIf(this.invalid$data()),this.$dataError(),t!==T.nil&&n.assign(t,!1)),n.else()}invalid$data(){const{gen:t,schemaCode:r,schemaType:n,def:a,it:s}=this;return(0,T.or)(i(),c());function i(){if(n.length){if(!(r instanceof T.Name))throw new Error("ajv implementation error");const l=Array.isArray(n)?n:[n];return(0,T._)`${(0,lt.checkDataTypes)(l,r,s.opts.strictNumbers,lt.DataType.Wrong)}`}return T.nil}function c(){if(a.validateSchema){const l=t.scopeValue("validate$data",{ref:a.validateSchema});return(0,T._)`!${l}(${r})`}return T.nil}}subschema(t,r){const n=(0,Bt.getSubschema)(this.it,t);(0,Bt.extendSubschemaData)(n,this.it,t),(0,Bt.extendSubschemaMode)(n,t);const a={...this.it,...n,items:void 0,props:void 0};return $o(a,r),a}mergeEvaluated(t,r){const{it:n,gen:a}=this;n.opts.unevaluated&&(n.props!==!0&&t.props!==void 0&&(n.props=ge.mergeEvaluated.props(a,t.props,n.props,r)),n.items!==!0&&t.items!==void 0&&(n.items=ge.mergeEvaluated.items(a,t.items,n.items,r)))}mergeValidEvaluated(t,r){const{it:n,gen:a}=this;if(n.opts.unevaluated&&(n.props!==!0||n.items!==!0))return a.if(r,()=>this.mergeEvaluated(t,T.Name)),!0}}ie.KeywordCxt=Wn;function Xn(e,t,r,n){const a=new Wn(e,r,t);"code"in r?r.code(a,n):a.$data&&r.validate?(0,We.funcKeywordCode)(a,r):"macro"in r?(0,We.macroKeywordCode)(a,r):(r.compile||r.validate)&&(0,We.funcKeywordCode)(a,r)}const Ro=/^\/(?:[^~]|~0|~1)*$/,Co=/^([0-9]+)(#|\/(?:[^~]|~0|~1)*)?$/;function Zn(e,{dataLevel:t,dataNames:r,dataPathArr:n}){let a,s;if(e==="")return A.default.rootData;if(e[0]==="/"){if(!Ro.test(e))throw new Error(`Invalid JSON-pointer: ${e}`);a=e,s=A.default.rootData}else{const d=Co.exec(e);if(!d)throw new Error(`Invalid JSON-pointer: ${e}`);const f=+d[1];if(a=d[2],a==="#"){if(f>=t)throw new Error(l("property/index",f));return n[t-f]}if(f>t)throw new Error(l("data",f));if(s=r[t-f],!a)return s}let i=s;const c=a.split("/");for(const d of c)d&&(s=(0,T._)`${s}${(0,T.getProperty)((0,ge.unescapeJsonPointer)(d))}`,i=(0,T._)`${i} && ${s}`);return i;function l(d,f){return`Cannot access ${d} ${f} levels up, current level is ${t}`}}ie.getData=Zn;var Ze={};Object.defineProperty(Ze,"__esModule",{value:!0});class Ao extends Error{constructor(t){super("validation failed"),this.errors=t,this.ajv=this.validation=!0}}Ze.default=Ao;var Fe={};Object.defineProperty(Fe,"__esModule",{value:!0});const Wt=Z;class Mo extends Error{constructor(t,r,n,a){super(a||`can't resolve reference ${n} from id ${r}`),this.missingRef=(0,Wt.resolveUrl)(t,r,n),this.missingSchema=(0,Wt.normalizeId)((0,Wt.getFullPath)(t,this.missingRef))}}Fe.default=Mo;var re={};Object.defineProperty(re,"__esModule",{value:!0}),re.resolveSchema=re.getCompilingSchema=re.resolveRef=re.compileSchema=re.SchemaEnv=void 0;const ce=D,zo=Ze,Te=le,de=Z,Yn=N,Do=ie;class ft{constructor(t){var r;this.refs={},this.dynamicAnchors={};let n;typeof t.schema=="object"&&(n=t.schema),this.schema=t.schema,this.schemaId=t.schemaId,this.root=t.root||this,this.baseId=(r=t.baseId)!==null&&r!==void 0?r:(0,de.normalizeId)(n==null?void 0:n[t.schemaId||"$id"]),this.schemaPath=t.schemaPath,this.localRefs=t.localRefs,this.meta=t.meta,this.$async=n==null?void 0:n.$async,this.refs={}}}re.SchemaEnv=ft;function Xt(e){const t=Qn.call(this,e);if(t)return t;const r=(0,de.getFullPath)(this.opts.uriResolver,e.root.baseId),{es5:n,lines:a}=this.opts.code,{ownProperties:s}=this.opts,i=new ce.CodeGen(this.scope,{es5:n,lines:a,ownProperties:s});let c;e.$async&&(c=i.scopeValue("Error",{ref:zo.default,code:(0,ce._)`require("ajv/dist/runtime/validation_error").default`}));const l=i.scopeName("validate");e.validateName=l;const d={gen:i,allErrors:this.opts.allErrors,data:Te.default.data,parentData:Te.default.parentData,parentDataProperty:Te.default.parentDataProperty,dataNames:[Te.default.data],dataPathArr:[ce.nil],dataLevel:0,dataTypes:[],definedProperties:new Set,topSchemaRef:i.scopeValue("schema",this.opts.code.source===!0?{ref:e.schema,code:(0,ce.stringify)(e.schema)}:{ref:e.schema}),validateName:l,ValidationError:c,schema:e.schema,schemaEnv:e,rootId:r,baseId:e.baseId||r,schemaPath:ce.nil,errSchemaPath:e.schemaPath||(this.opts.jtd?"":"#"),errorPath:(0,ce._)`""`,opts:this.opts,self:this};let f;try{this._compilations.add(e),(0,Do.validateFunctionCode)(d),i.optimize(this.opts.code.optimize);const p=i.toString();f=`${i.scopeRefs(Te.default.scope)}return ${p}`,this.opts.code.process&&(f=this.opts.code.process(f,e));const b=new Function(`${Te.default.self}`,`${Te.default.scope}`,f)(this,this.scope.get());if(this.scope.value(l,{ref:b}),b.errors=null,b.schema=e.schema,b.schemaEnv=e,e.$async&&(b.$async=!0),this.opts.code.source===!0&&(b.source={validateName:l,validateCode:p,scopeValues:i._values}),this.opts.unevaluated){const{props:_,items:v}=d;b.evaluated={props:_ instanceof ce.Name?void 0:_,items:v instanceof ce.Name?void 0:v,dynamicProps:_ instanceof ce.Name,dynamicItems:v instanceof ce.Name},b.source&&(b.source.evaluated=(0,ce.stringify)(b.evaluated))}return e.validate=b,e}catch(p){throw delete e.validate,delete e.validateName,f&&this.logger.error("Error compiling schema, function code:",f),p}finally{this._compilations.delete(e)}}re.compileSchema=Xt;function Vo(e,t,r){var n;r=(0,de.resolveUrl)(this.opts.uriResolver,t,r);const a=e.refs[r];if(a)return a;let s=Uo.call(this,e,r);if(s===void 0){const i=(n=e.localRefs)===null||n===void 0?void 0:n[r],{schemaId:c}=this.opts;i&&(s=new ft({schema:i,schemaId:c,root:e,baseId:t}))}if(s!==void 0)return e.refs[r]=qo.call(this,s)}re.resolveRef=Vo;function qo(e){return(0,de.inlineRef)(e.schema,this.opts.inlineRefs)?e.schema:e.validate?e:Xt.call(this,e)}function Qn(e){for(const t of this._compilations)if(Fo(t,e))return t}re.getCompilingSchema=Qn;function Fo(e,t){return e.schema===t.schema&&e.root===t.root&&e.baseId===t.baseId}function Uo(e,t){let r;for(;typeof(r=this.refs[t])=="string";)t=r;return r||this.schemas[t]||mt.call(this,e,t)}function mt(e,t){const r=this.opts.uriResolver.parse(t),n=(0,de._getFullPath)(this.opts.uriResolver,r);let a=(0,de.getFullPath)(this.opts.uriResolver,e.baseId,void 0);if(Object.keys(e.schema).length>0&&n===a)return Zt.call(this,r,e);const s=(0,de.normalizeId)(n),i=this.refs[s]||this.schemas[s];if(typeof i=="string"){const c=mt.call(this,e,i);return typeof(c==null?void 0:c.schema)!="object"?void 0:Zt.call(this,r,c)}if(typeof(i==null?void 0:i.schema)=="object"){if(i.validate||Xt.call(this,i),s===(0,de.normalizeId)(t)){const{schema:c}=i,{schemaId:l}=this.opts,d=c[l];return d&&(a=(0,de.resolveUrl)(this.opts.uriResolver,a,d)),new ft({schema:c,schemaId:l,root:e,baseId:a})}return Zt.call(this,r,i)}}re.resolveSchema=mt;const xo=new Set(["properties","patternProperties","enum","dependencies","definitions"]);function Zt(e,{baseId:t,schema:r,root:n}){var a;if(((a=e.fragment)===null||a===void 0?void 0:a[0])!=="/")return;for(const c of e.fragment.slice(1).split("/")){if(typeof r=="boolean")return;const l=r[(0,Yn.unescapeFragment)(c)];if(l===void 0)return;r=l;const d=typeof r=="object"&&r[this.opts.schemaId];!xo.has(c)&&d&&(t=(0,de.resolveUrl)(this.opts.uriResolver,t,d))}let s;if(typeof r!="boolean"&&r.$ref&&!(0,Yn.schemaHasRulesButRef)(r,this.RULES)){const c=(0,de.resolveUrl)(this.opts.uriResolver,t,r.$ref);s=mt.call(this,n,c)}const{schemaId:i}=this.opts;if(s=s||new ft({schema:r,schemaId:i,root:n,baseId:t}),s.schema!==s.root.schema)return s}const Lo={$id:"https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#",description:"Meta-schema for $data reference (JSON AnySchema extension proposal)",type:"object",required:["$data"],properties:{$data:{type:"string",anyOf:[{format:"relative-json-pointer"},{format:"json-pointer"}]}},additionalProperties:!1};var Yt={},pt={exports:{}},Ko={HEX:{0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,a:10,A:10,b:11,B:11,c:12,C:12,d:13,D:13,e:14,E:14,f:15,F:15}};const{HEX:Ho}=Ko,Go=/^(?:(?:25[0-5]|2[0-4]\d|1\d{2}|[1-9]\d|\d)\.){3}(?:25[0-5]|2[0-4]\d|1\d{2}|[1-9]\d|\d)$/u;function ea(e){if(na(e,".")<3)return{host:e,isIPV4:!1};const t=e.match(Go)||[],[r]=t;return r?{host:Jo(r,"."),isIPV4:!0}:{host:e,isIPV4:!1}}function ta(e,t=!1){let r="",n=!0;for(const a of e){if(Ho[a]===void 0)return;a!=="0"&&n===!0&&(n=!1),n||(r+=a)}return t&&r.length===0&&(r="0"),r}function Bo(e){let t=0;const r={error:!1,address:"",zone:""},n=[],a=[];let s=!1,i=!1,c=!1;function l(){if(a.length){if(s===!1){const d=ta(a);if(d!==void 0)n.push(d);else return r.error=!0,!1}a.length=0}return!0}for(let d=0;d<e.length;d++){const f=e[d];if(!(f==="["||f==="]"))if(f===":"){if(i===!0&&(c=!0),!l())break;if(t++,n.push(":"),t>7){r.error=!0;break}d-1>=0&&e[d-1]===":"&&(i=!0);continue}else if(f==="%"){if(!l())break;s=!0}else{a.push(f);continue}}return a.length&&(s?r.zone=a.join(""):c?n.push(a.join("")):n.push(ta(a))),r.address=n.join(""),r}function ra(e){if(na(e,":")<2)return{host:e,isIPV6:!1};const t=Bo(e);if(t.error)return{host:e,isIPV6:!1};{let r=t.address,n=t.address;return t.zone&&(r+="%"+t.zone,n+="%25"+t.zone),{host:r,escapedHost:n,isIPV6:!0}}}function Jo(e,t){let r="",n=!0;const a=e.length;for(let s=0;s<a;s++){const i=e[s];i==="0"&&n?(s+1<=a&&e[s+1]===t||s+1===a)&&(r+=i,n=!1):(i===t?n=!0:n=!1,r+=i)}return r}function na(e,t){let r=0;for(let n=0;n<e.length;n++)e[n]===t&&r++;return r}const aa=/^\.\.?\//u,sa=/^\/\.(?:\/|$)/u,oa=/^\/\.\.(?:\/|$)/u,Wo=/^\/?(?:.|\n)*?(?=\/|$)/u;function Xo(e){const t=[];for(;e.length;)if(e.match(aa))e=e.replace(aa,"");else if(e.match(sa))e=e.replace(sa,"/");else if(e.match(oa))e=e.replace(oa,"/"),t.pop();else if(e==="."||e==="..")e="";else{const r=e.match(Wo);if(r){const n=r[0];e=e.slice(n.length),t.push(n)}else throw new Error("Unexpected dot segment condition")}return t.join("")}function Zo(e,t){const r=t!==!0?escape:unescape;return e.scheme!==void 0&&(e.scheme=r(e.scheme)),e.userinfo!==void 0&&(e.userinfo=r(e.userinfo)),e.host!==void 0&&(e.host=r(e.host)),e.path!==void 0&&(e.path=r(e.path)),e.query!==void 0&&(e.query=r(e.query)),e.fragment!==void 0&&(e.fragment=r(e.fragment)),e}function Yo(e){const t=[];if(e.userinfo!==void 0&&(t.push(e.userinfo),t.push("@")),e.host!==void 0){let r=unescape(e.host);const n=ea(r);if(n.isIPV4)r=n.host;else{const a=ra(n.host);a.isIPV6===!0?r=`[${a.escapedHost}]`:r=e.host}t.push(r)}return(typeof e.port=="number"||typeof e.port=="string")&&(t.push(":"),t.push(String(e.port))),t.length?t.join(""):void 0}var Qo={recomposeAuthority:Yo,normalizeComponentEncoding:Zo,removeDotSegments:Xo,normalizeIPv4:ea,normalizeIPv6:ra};const ei=/^[\da-f]{8}-[\da-f]{4}-[\da-f]{4}-[\da-f]{4}-[\da-f]{12}$/iu,ti=/([\da-z][\d\-a-z]{0,31}):((?:[\w!$'()*+,\-.:;=@]|%[\da-f]{2})+)/iu;function ia(e){return typeof e.secure=="boolean"?e.secure:String(e.scheme).toLowerCase()==="wss"}function ca(e){return e.host||(e.error=e.error||"HTTP URIs must have a host."),e}function da(e){const t=String(e.scheme).toLowerCase()==="https";return(e.port===(t?443:80)||e.port==="")&&(e.port=void 0),e.path||(e.path="/"),e}function ri(e){return e.secure=ia(e),e.resourceName=(e.path||"/")+(e.query?"?"+e.query:""),e.path=void 0,e.query=void 0,e}function ni(e){if((e.port===(ia(e)?443:80)||e.port==="")&&(e.port=void 0),typeof e.secure=="boolean"&&(e.scheme=e.secure?"wss":"ws",e.secure=void 0),e.resourceName){const[t,r]=e.resourceName.split("?");e.path=t&&t!=="/"?t:void 0,e.query=r,e.resourceName=void 0}return e.fragment=void 0,e}function ai(e,t){if(!e.path)return e.error="URN can not be parsed",e;const r=e.path.match(ti);if(r){const n=t.scheme||e.scheme||"urn";e.nid=r[1].toLowerCase(),e.nss=r[2];const a=`${n}:${t.nid||e.nid}`,s=Qt[a];e.path=void 0,s&&(e=s.parse(e,t))}else e.error=e.error||"URN can not be parsed.";return e}function si(e,t){const r=t.scheme||e.scheme||"urn",n=e.nid.toLowerCase(),a=`${r}:${t.nid||n}`,s=Qt[a];s&&(e=s.serialize(e,t));const i=e,c=e.nss;return i.path=`${n||t.nid}:${c}`,t.skipEscape=!0,i}function oi(e,t){const r=e;return r.uuid=r.nss,r.nss=void 0,!t.tolerant&&(!r.uuid||!ei.test(r.uuid))&&(r.error=r.error||"UUID is not valid."),r}function ii(e){const t=e;return t.nss=(e.uuid||"").toLowerCase(),t}const ua={scheme:"http",domainHost:!0,parse:ca,serialize:da},ci={scheme:"https",domainHost:ua.domainHost,parse:ca,serialize:da},ht={scheme:"ws",domainHost:!0,parse:ri,serialize:ni},di={scheme:"wss",domainHost:ht.domainHost,parse:ht.parse,serialize:ht.serialize},Qt={http:ua,https:ci,ws:ht,wss:di,urn:{scheme:"urn",parse:ai,serialize:si,skipNormalize:!0},"urn:uuid":{scheme:"urn:uuid",parse:oi,serialize:ii,skipNormalize:!0}};var ui=Qt;const{normalizeIPv6:li,normalizeIPv4:fi,removeDotSegments:Ye,recomposeAuthority:mi,normalizeComponentEncoding:yt}=Qo,er=ui;function pi(e,t){return typeof e=="string"?e=pe($e(e,t),t):typeof e=="object"&&(e=$e(pe(e,t),t)),e}function hi(e,t,r){const n=Object.assign({scheme:"null"},r),a=la($e(e,n),$e(t,n),n,!0);return pe(a,{...n,skipEscape:!0})}function la(e,t,r,n){const a={};return n||(e=$e(pe(e,r),r),t=$e(pe(t,r),r)),r=r||{},!r.tolerant&&t.scheme?(a.scheme=t.scheme,a.userinfo=t.userinfo,a.host=t.host,a.port=t.port,a.path=Ye(t.path||""),a.query=t.query):(t.userinfo!==void 0||t.host!==void 0||t.port!==void 0?(a.userinfo=t.userinfo,a.host=t.host,a.port=t.port,a.path=Ye(t.path||""),a.query=t.query):(t.path?(t.path.charAt(0)==="/"?a.path=Ye(t.path):((e.userinfo!==void 0||e.host!==void 0||e.port!==void 0)&&!e.path?a.path="/"+t.path:e.path?a.path=e.path.slice(0,e.path.lastIndexOf("/")+1)+t.path:a.path=t.path,a.path=Ye(a.path)),a.query=t.query):(a.path=e.path,t.query!==void 0?a.query=t.query:a.query=e.query),a.userinfo=e.userinfo,a.host=e.host,a.port=e.port),a.scheme=e.scheme),a.fragment=t.fragment,a}function yi(e,t,r){return typeof e=="string"?(e=unescape(e),e=pe(yt($e(e,r),!0),{...r,skipEscape:!0})):typeof e=="object"&&(e=pe(yt(e,!0),{...r,skipEscape:!0})),typeof t=="string"?(t=unescape(t),t=pe(yt($e(t,r),!0),{...r,skipEscape:!0})):typeof t=="object"&&(t=pe(yt(t,!0),{...r,skipEscape:!0})),e.toLowerCase()===t.toLowerCase()}function pe(e,t){const r={host:e.host,scheme:e.scheme,userinfo:e.userinfo,port:e.port,path:e.path,query:e.query,nid:e.nid,nss:e.nss,uuid:e.uuid,fragment:e.fragment,reference:e.reference,resourceName:e.resourceName,secure:e.secure,error:""},n=Object.assign({},t),a=[],s=er[(n.scheme||r.scheme||"").toLowerCase()];s&&s.serialize&&s.serialize(r,n),r.path!==void 0&&(n.skipEscape?r.path=unescape(r.path):(r.path=escape(r.path),r.scheme!==void 0&&(r.path=r.path.split("%3A").join(":")))),n.reference!=="suffix"&&r.scheme&&a.push(r.scheme,":");const i=mi(r);if(i!==void 0&&(n.reference!=="suffix"&&a.push("//"),a.push(i),r.path&&r.path.charAt(0)!=="/"&&a.push("/")),r.path!==void 0){let c=r.path;!n.absolutePath&&(!s||!s.absolutePath)&&(c=Ye(c)),i===void 0&&(c=c.replace(/^\/\//u,"/%2F")),a.push(c)}return r.query!==void 0&&a.push("?",r.query),r.fragment!==void 0&&a.push("#",r.fragment),a.join("")}const gi=Array.from({length:127},(e,t)=>/[^!"$&'()*+,\-.;=_`a-z{}~]/u.test(String.fromCharCode(t)));function $i(e){let t=0;for(let r=0,n=e.length;r<n;++r)if(t=e.charCodeAt(r),t>126||gi[t])return!0;return!1}const _i=/^(?:([^#/:?]+):)?(?:\/\/((?:([^#/?@]*)@)?(\[[^#/?\]]+\]|[^#/:?]*)(?::(\d*))?))?([^#?]*)(?:\?([^#]*))?(?:#((?:.|[\n\r])*))?/u;function $e(e,t){const r=Object.assign({},t),n={scheme:void 0,userinfo:void 0,host:"",port:void 0,path:"",query:void 0,fragment:void 0},a=e.indexOf("%")!==-1;let s=!1;r.reference==="suffix"&&(e=(r.scheme?r.scheme+":":"")+"//"+e);const i=e.match(_i);if(i){if(n.scheme=i[1],n.userinfo=i[3],n.host=i[4],n.port=parseInt(i[5],10),n.path=i[6]||"",n.query=i[7],n.fragment=i[8],isNaN(n.port)&&(n.port=i[5]),n.host){const l=fi(n.host);if(l.isIPV4===!1){const d=li(l.host);n.host=d.host.toLowerCase(),s=d.isIPV6}else n.host=l.host,s=!0}n.scheme===void 0&&n.userinfo===void 0&&n.host===void 0&&n.port===void 0&&n.query===void 0&&!n.path?n.reference="same-document":n.scheme===void 0?n.reference="relative":n.fragment===void 0?n.reference="absolute":n.reference="uri",r.reference&&r.reference!=="suffix"&&r.reference!==n.reference&&(n.error=n.error||"URI is not a "+r.reference+" reference.");const c=er[(r.scheme||n.scheme||"").toLowerCase()];if(!r.unicodeSupport&&(!c||!c.unicodeSupport)&&n.host&&(r.domainHost||c&&c.domainHost)&&s===!1&&$i(n.host))try{n.host=URL.domainToASCII(n.host.toLowerCase())}catch(l){n.error=n.error||"Host's domain name can not be converted to ASCII: "+l}(!c||c&&!c.skipNormalize)&&(a&&n.scheme!==void 0&&(n.scheme=unescape(n.scheme)),a&&n.host!==void 0&&(n.host=unescape(n.host)),n.path&&(n.path=escape(unescape(n.path))),n.fragment&&(n.fragment=encodeURI(decodeURIComponent(n.fragment)))),c&&c.parse&&c.parse(n,r)}else n.error=n.error||"URI can not be parsed.";return n}const tr={SCHEMES:er,normalize:pi,resolve:hi,resolveComponents:la,equal:yi,serialize:pe,parse:$e};pt.exports=tr,pt.exports.default=tr,pt.exports.fastUri=tr;var vi=pt.exports;Object.defineProperty(Yt,"__esModule",{value:!0});const fa=vi;fa.code='require("ajv/dist/runtime/uri").default',Yt.default=fa,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.CodeGen=e.Name=e.nil=e.stringify=e.str=e._=e.KeywordCxt=void 0;var t=ie;Object.defineProperty(e,"KeywordCxt",{enumerable:!0,get:function(){return t.KeywordCxt}});var r=D;Object.defineProperty(e,"_",{enumerable:!0,get:function(){return r._}}),Object.defineProperty(e,"str",{enumerable:!0,get:function(){return r.str}}),Object.defineProperty(e,"stringify",{enumerable:!0,get:function(){return r.stringify}}),Object.defineProperty(e,"nil",{enumerable:!0,get:function(){return r.nil}}),Object.defineProperty(e,"Name",{enumerable:!0,get:function(){return r.Name}}),Object.defineProperty(e,"CodeGen",{enumerable:!0,get:function(){return r.CodeGen}});const n=Ze,a=Fe,s=Ne,i=re,c=D,l=Z,d=W,f=N,p=Lo,S=Yt,b=(k,h)=>new RegExp(k,h);b.code="new RegExp";const _=["removeAdditional","useDefaults","coerceTypes"],v=new Set(["validate","serialize","parse","wrapper","root","schema","keyword","pattern","formats","validate$data","func","obj","Error"]),y={errorDataPath:"",format:"`validateFormats: false` can be used instead.",nullable:'"nullable" keyword is supported by default.',jsonPointers:"Deprecated jsPropertySyntax can be used instead.",extendRefs:"Deprecated ignoreKeywordsWithRef can be used instead.",missingRefs:"Pass empty schema with $id that should be ignored to ajv.addSchema.",processCode:"Use option `code: {process: (code, schemaEnv: object) => string}`",sourceCode:"Use option `code: {source: true}`",strictDefaults:"It is default now, see option `strict`.",strictKeywords:"It is default now, see option `strict`.",uniqueItems:'"uniqueItems" keyword is always validated.',unknownFormats:"Disable strict mode or pass `true` to `ajv.addFormat` (or `formats` option).",cache:"Map is used as cache, schema object as key.",serialize:"Map is used as cache, schema object as key.",ajvErrors:"It is default now."},m={ignoreKeywordsWithRef:"",jsPropertySyntax:"",unicode:'"minLength"/"maxLength" account for unicode characters by default.'},$=200;function P(k){var h,E,g,o,u,w,I,R,x,U,X,Ge,Ur,xr,Lr,Kr,Hr,Gr,Br,Jr,Wr,Xr,Zr,Yr,Qr;const it=k.strict,en=(h=k.code)===null||h===void 0?void 0:h.optimize,xa=en===!0||en===void 0?1:en||0,La=(g=(E=k.code)===null||E===void 0?void 0:E.regExp)!==null&&g!==void 0?g:b,$d=(o=k.uriResolver)!==null&&o!==void 0?o:S.default;return{strictSchema:(w=(u=k.strictSchema)!==null&&u!==void 0?u:it)!==null&&w!==void 0?w:!0,strictNumbers:(R=(I=k.strictNumbers)!==null&&I!==void 0?I:it)!==null&&R!==void 0?R:!0,strictTypes:(U=(x=k.strictTypes)!==null&&x!==void 0?x:it)!==null&&U!==void 0?U:"log",strictTuples:(Ge=(X=k.strictTuples)!==null&&X!==void 0?X:it)!==null&&Ge!==void 0?Ge:"log",strictRequired:(xr=(Ur=k.strictRequired)!==null&&Ur!==void 0?Ur:it)!==null&&xr!==void 0?xr:!1,code:k.code?{...k.code,optimize:xa,regExp:La}:{optimize:xa,regExp:La},loopRequired:(Lr=k.loopRequired)!==null&&Lr!==void 0?Lr:$,loopEnum:(Kr=k.loopEnum)!==null&&Kr!==void 0?Kr:$,meta:(Hr=k.meta)!==null&&Hr!==void 0?Hr:!0,messages:(Gr=k.messages)!==null&&Gr!==void 0?Gr:!0,inlineRefs:(Br=k.inlineRefs)!==null&&Br!==void 0?Br:!0,schemaId:(Jr=k.schemaId)!==null&&Jr!==void 0?Jr:"$id",addUsedSchema:(Wr=k.addUsedSchema)!==null&&Wr!==void 0?Wr:!0,validateSchema:(Xr=k.validateSchema)!==null&&Xr!==void 0?Xr:!0,validateFormats:(Zr=k.validateFormats)!==null&&Zr!==void 0?Zr:!0,unicodeRegExp:(Yr=k.unicodeRegExp)!==null&&Yr!==void 0?Yr:!0,int32range:(Qr=k.int32range)!==null&&Qr!==void 0?Qr:!0,uriResolver:$d}}class j{constructor(h={}){this.schemas={},this.refs={},this.formats={},this._compilations=new Set,this._loading={},this._cache=new Map,h=this.opts={...h,...P(h)};const{es5:E,lines:g}=this.opts.code;this.scope=new c.ValueScope({scope:{},prefixes:v,es5:E,lines:g}),this.logger=V(h.logger);const o=h.validateFormats;h.validateFormats=!1,this.RULES=(0,s.getRules)(),O.call(this,y,h,"NOT SUPPORTED"),O.call(this,m,h,"DEPRECATED","warn"),this._metaOpts=ve.call(this),h.formats&&oe.call(this),this._addVocabularies(),this._addDefaultMetaSchema(),h.keywords&&he.call(this,h.keywords),typeof h.meta=="object"&&this.addMetaSchema(h.meta),J.call(this),h.validateFormats=o}_addVocabularies(){this.addKeyword("$async")}_addDefaultMetaSchema(){const{$data:h,meta:E,schemaId:g}=this.opts;let o=p;g==="id"&&(o={...p},o.id=o.$id,delete o.$id),E&&h&&this.addMetaSchema(o,o[g],!1)}defaultMeta(){const{meta:h,schemaId:E}=this.opts;return this.opts.defaultMeta=typeof h=="object"?h[E]||h:void 0}validate(h,E){let g;if(typeof h=="string"){if(g=this.getSchema(h),!g)throw new Error(`no schema with key or ref "${h}"`)}else g=this.compile(h);const o=g(E);return"$async"in g||(this.errors=g.errors),o}compile(h,E){const g=this._addSchema(h,E);return g.validate||this._compileSchemaEnv(g)}compileAsync(h,E){if(typeof this.opts.loadSchema!="function")throw new Error("options.loadSchema should be a function");const{loadSchema:g}=this.opts;return o.call(this,h,E);async function o(U,X){await u.call(this,U.$schema);const Ge=this._addSchema(U,X);return Ge.validate||w.call(this,Ge)}async function u(U){U&&!this.getSchema(U)&&await o.call(this,{$ref:U},!0)}async function w(U){try{return this._compileSchemaEnv(U)}catch(X){if(!(X instanceof a.default))throw X;return I.call(this,X),await R.call(this,X.missingSchema),w.call(this,U)}}function I({missingSchema:U,missingRef:X}){if(this.refs[U])throw new Error(`AnySchema ${U} is loaded but ${X} cannot be resolved`)}async function R(U){const X=await x.call(this,U);this.refs[U]||await u.call(this,X.$schema),this.refs[U]||this.addSchema(X,U,E)}async function x(U){const X=this._loading[U];if(X)return X;try{return await(this._loading[U]=g(U))}finally{delete this._loading[U]}}}addSchema(h,E,g,o=this.opts.validateSchema){if(Array.isArray(h)){for(const w of h)this.addSchema(w,void 0,g,o);return this}let u;if(typeof h=="object"){const{schemaId:w}=this.opts;if(u=h[w],u!==void 0&&typeof u!="string")throw new Error(`schema ${w} must be string`)}return E=(0,l.normalizeId)(E||u),this._checkUnique(E),this.schemas[E]=this._addSchema(h,g,E,o,!0),this}addMetaSchema(h,E,g=this.opts.validateSchema){return this.addSchema(h,E,!0,g),this}validateSchema(h,E){if(typeof h=="boolean")return!0;let g;if(g=h.$schema,g!==void 0&&typeof g!="string")throw new Error("$schema must be a string");if(g=g||this.opts.defaultMeta||this.defaultMeta(),!g)return this.logger.warn("meta-schema not available"),this.errors=null,!0;const o=this.validate(g,h);if(!o&&E){const u="schema is invalid: "+this.errorsText();if(this.opts.validateSchema==="log")this.logger.error(u);else throw new Error(u)}return o}getSchema(h){let E;for(;typeof(E=L.call(this,h))=="string";)h=E;if(E===void 0){const{schemaId:g}=this.opts,o=new i.SchemaEnv({schema:{},schemaId:g});if(E=i.resolveSchema.call(this,o,h),!E)return;this.refs[h]=E}return E.validate||this._compileSchemaEnv(E)}removeSchema(h){if(h instanceof RegExp)return this._removeAllSchemas(this.schemas,h),this._removeAllSchemas(this.refs,h),this;switch(typeof h){case"undefined":return this._removeAllSchemas(this.schemas),this._removeAllSchemas(this.refs),this._cache.clear(),this;case"string":{const E=L.call(this,h);return typeof E=="object"&&this._cache.delete(E.schema),delete this.schemas[h],delete this.refs[h],this}case"object":{const E=h;this._cache.delete(E);let g=h[this.opts.schemaId];return g&&(g=(0,l.normalizeId)(g),delete this.schemas[g],delete this.refs[g]),this}default:throw new Error("ajv.removeSchema: invalid parameter")}}addVocabulary(h){for(const E of h)this.addKeyword(E);return this}addKeyword(h,E){let g;if(typeof h=="string")g=h,typeof E=="object"&&(this.logger.warn("these parameters are deprecated, see docs for addKeyword"),E.keyword=g);else if(typeof h=="object"&&E===void 0){if(E=h,g=E.keyword,Array.isArray(g)&&!g.length)throw new Error("addKeywords: keyword must be string or non-empty array")}else throw new Error("invalid addKeywords parameters");if(F.call(this,g,E),!E)return(0,f.eachItem)(g,u=>Q.call(this,u)),this;we.call(this,E);const o={...E,type:(0,d.getJSONTypes)(E.type),schemaType:(0,d.getJSONTypes)(E.schemaType)};return(0,f.eachItem)(g,o.type.length===0?u=>Q.call(this,u,o):u=>o.type.forEach(w=>Q.call(this,u,o,w))),this}getKeyword(h){const E=this.RULES.all[h];return typeof E=="object"?E.definition:!!E}removeKeyword(h){const{RULES:E}=this;delete E.keywords[h],delete E.all[h];for(const g of E.rules){const o=g.rules.findIndex(u=>u.keyword===h);o>=0&&g.rules.splice(o,1)}return this}addFormat(h,E){return typeof E=="string"&&(E=new RegExp(E)),this.formats[h]=E,this}errorsText(h=this.errors,{separator:E=", ",dataVar:g="data"}={}){return!h||h.length===0?"No errors":h.map(o=>`${g}${o.instancePath} ${o.message}`).reduce((o,u)=>o+E+u)}$dataMetaSchema(h,E){const g=this.RULES.all;h=JSON.parse(JSON.stringify(h));for(const o of E){const u=o.split("/").slice(1);let w=h;for(const I of u)w=w[I];for(const I in g){const R=g[I];if(typeof R!="object")continue;const{$data:x}=R.definition,U=w[I];x&&U&&(w[I]=Ae(U))}}return h}_removeAllSchemas(h,E){for(const g in h){const o=h[g];(!E||E.test(g))&&(typeof o=="string"?delete h[g]:o&&!o.meta&&(this._cache.delete(o.schema),delete h[g]))}}_addSchema(h,E,g,o=this.opts.validateSchema,u=this.opts.addUsedSchema){let w;const{schemaId:I}=this.opts;if(typeof h=="object")w=h[I];else{if(this.opts.jtd)throw new Error("schema must be object");if(typeof h!="boolean")throw new Error("schema must be object or boolean")}let R=this._cache.get(h);if(R!==void 0)return R;g=(0,l.normalizeId)(w||g);const x=l.getSchemaRefs.call(this,h,g);return R=new i.SchemaEnv({schema:h,schemaId:I,meta:E,baseId:g,localRefs:x}),this._cache.set(R.schema,R),u&&!g.startsWith("#")&&(g&&this._checkUnique(g),this.refs[g]=R),o&&this.validateSchema(h,!0),R}_checkUnique(h){if(this.schemas[h]||this.refs[h])throw new Error(`schema with key or id "${h}" already exists`)}_compileSchemaEnv(h){if(h.meta?this._compileMetaSchema(h):i.compileSchema.call(this,h),!h.validate)throw new Error("ajv implementation error");return h.validate}_compileMetaSchema(h){const E=this.opts;this.opts=this._metaOpts;try{i.compileSchema.call(this,h)}finally{this.opts=E}}}j.ValidationError=n.default,j.MissingRefError=a.default,e.default=j;function O(k,h,E,g="error"){for(const o in k){const u=o;u in h&&this.logger[g](`${E}: option ${o}. ${k[u]}`)}}function L(k){return k=(0,l.normalizeId)(k),this.schemas[k]||this.refs[k]}function J(){const k=this.opts.schemas;if(k)if(Array.isArray(k))this.addSchema(k);else for(const h in k)this.addSchema(k[h],h)}function oe(){for(const k in this.opts.formats){const h=this.opts.formats[k];h&&this.addFormat(k,h)}}function he(k){if(Array.isArray(k)){this.addVocabulary(k);return}this.logger.warn("keywords option as map is deprecated, pass array");for(const h in k){const E=k[h];E.keyword||(E.keyword=h),this.addKeyword(E)}}function ve(){const k={...this.opts};for(const h of _)delete k[h];return k}const C={log(){},warn(){},error(){}};function V(k){if(k===!1)return C;if(k===void 0)return console;if(k.log&&k.warn&&k.error)return k;throw new Error("logger must implement log, warn and error methods")}const G=/^[a-z_$][a-z0-9_$:-]*$/i;function F(k,h){const{RULES:E}=this;if((0,f.eachItem)(k,g=>{if(E.keywords[g])throw new Error(`Keyword ${g} is already defined`);if(!G.test(g))throw new Error(`Keyword ${g} has invalid name`)}),!!h&&h.$data&&!("code"in h||"validate"in h))throw new Error('$data keyword must have "code" or "validate" function')}function Q(k,h,E){var g;const o=h==null?void 0:h.post;if(E&&o)throw new Error('keyword with "post" flag cannot have "type"');const{RULES:u}=this;let w=o?u.post:u.rules.find(({type:R})=>R===E);if(w||(w={type:E,rules:[]},u.rules.push(w)),u.keywords[k]=!0,!h)return;const I={keyword:k,definition:{...h,type:(0,d.getJSONTypes)(h.type),schemaType:(0,d.getJSONTypes)(h.schemaType)}};h.before?be.call(this,w,I,h.before):w.rules.push(I),u.all[k]=I,(g=h.implements)===null||g===void 0||g.forEach(R=>this.addKeyword(R))}function be(k,h,E){const g=k.rules.findIndex(o=>o.keyword===E);g>=0?k.rules.splice(g,0,h):(k.rules.push(h),this.logger.warn(`rule ${E} is not defined`))}function we(k){let{metaSchema:h}=k;h!==void 0&&(k.$data&&this.opts.$data&&(h=Ae(h)),k.validateSchema=this.compile(h,!0))}const ot={$ref:"https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#"};function Ae(k){return{anyOf:[k,ot]}}}(fn);var rr={},nr={},ar={};Object.defineProperty(ar,"__esModule",{value:!0});const bi={keyword:"id",code(){throw new Error('NOT SUPPORTED: keyword "id", use "$id" for schema ID')}};ar.default=bi;var Ie={};Object.defineProperty(Ie,"__esModule",{value:!0}),Ie.callRef=Ie.getValidate=void 0;const wi=Fe,ma=q,ne=D,Ue=le,pa=re,gt=N,Si={keyword:"$ref",schemaType:"string",code(e){const{gen:t,schema:r,it:n}=e,{baseId:a,schemaEnv:s,validateName:i,opts:c,self:l}=n,{root:d}=s;if((r==="#"||r==="#/")&&a===d.baseId)return p();const f=pa.resolveRef.call(l,d,a,r);if(f===void 0)throw new wi.default(n.opts.uriResolver,a,r);if(f instanceof pa.SchemaEnv)return S(f);return b(f);function p(){if(s===d)return $t(e,i,s,s.$async);const _=t.scopeValue("root",{ref:d});return $t(e,(0,ne._)`${_}.validate`,d,d.$async)}function S(_){const v=ha(e,_);$t(e,v,_,_.$async)}function b(_){const v=t.scopeValue("schema",c.code.source===!0?{ref:_,code:(0,ne.stringify)(_)}:{ref:_}),y=t.name("valid"),m=e.subschema({schema:_,dataTypes:[],schemaPath:ne.nil,topSchemaRef:v,errSchemaPath:r},y);e.mergeEvaluated(m),e.ok(y)}}};function ha(e,t){const{gen:r}=e;return t.validate?r.scopeValue("validate",{ref:t.validate}):(0,ne._)`${r.scopeValue("wrapper",{ref:t})}.validate`}Ie.getValidate=ha;function $t(e,t,r,n){const{gen:a,it:s}=e,{allErrors:i,schemaEnv:c,opts:l}=s,d=l.passContext?Ue.default.this:ne.nil;n?f():p();function f(){if(!c.$async)throw new Error("async schema referenced by sync schema");const _=a.let("valid");a.try(()=>{a.code((0,ne._)`await ${(0,ma.callValidateCode)(e,t,d)}`),b(t),i||a.assign(_,!0)},v=>{a.if((0,ne._)`!(${v} instanceof ${s.ValidationError})`,()=>a.throw(v)),S(v),i||a.assign(_,!1)}),e.ok(_)}function p(){e.result((0,ma.callValidateCode)(e,t,d),()=>b(t),()=>S(t))}function S(_){const v=(0,ne._)`${_}.errors`;a.assign(Ue.default.vErrors,(0,ne._)`${Ue.default.vErrors} === null ? ${v} : ${Ue.default.vErrors}.concat(${v})`),a.assign(Ue.default.errors,(0,ne._)`${Ue.default.vErrors}.length`)}function b(_){var v;if(!s.opts.unevaluated)return;const y=(v=r==null?void 0:r.validate)===null||v===void 0?void 0:v.evaluated;if(s.props!==!0)if(y&&!y.dynamicProps)y.props!==void 0&&(s.props=gt.mergeEvaluated.props(a,y.props,s.props));else{const m=a.var("props",(0,ne._)`${_}.evaluated.props`);s.props=gt.mergeEvaluated.props(a,m,s.props,ne.Name)}if(s.items!==!0)if(y&&!y.dynamicItems)y.items!==void 0&&(s.items=gt.mergeEvaluated.items(a,y.items,s.items));else{const m=a.var("items",(0,ne._)`${_}.evaluated.items`);s.items=gt.mergeEvaluated.items(a,m,s.items,ne.Name)}}}Ie.callRef=$t,Ie.default=Si,Object.defineProperty(nr,"__esModule",{value:!0});const Ei=ar,Pi=Ie,ki=["$schema","$id","$defs","$vocabulary",{keyword:"$comment"},"definitions",Ei.default,Pi.default];nr.default=ki;var sr={},or={};Object.defineProperty(or,"__esModule",{value:!0});const _t=D,je=_t.operators,vt={maximum:{okStr:"<=",ok:je.LTE,fail:je.GT},minimum:{okStr:">=",ok:je.GTE,fail:je.LT},exclusiveMaximum:{okStr:"<",ok:je.LT,fail:je.GTE},exclusiveMinimum:{okStr:">",ok:je.GT,fail:je.LTE}},ji={message:({keyword:e,schemaCode:t})=>(0,_t.str)`must be ${vt[e].okStr} ${t}`,params:({keyword:e,schemaCode:t})=>(0,_t._)`{comparison: ${vt[e].okStr}, limit: ${t}}`},Ni={keyword:Object.keys(vt),type:"number",schemaType:"number",$data:!0,error:ji,code(e){const{keyword:t,data:r,schemaCode:n}=e;e.fail$data((0,_t._)`${r} ${vt[t].fail} ${n} || isNaN(${r})`)}};or.default=Ni;var ir={};Object.defineProperty(ir,"__esModule",{value:!0});const Qe=D,Oi={keyword:"multipleOf",type:"number",schemaType:"number",$data:!0,error:{message:({schemaCode:e})=>(0,Qe.str)`must be multiple of ${e}`,params:({schemaCode:e})=>(0,Qe._)`{multipleOf: ${e}}`},code(e){const{gen:t,data:r,schemaCode:n,it:a}=e,s=a.opts.multipleOfPrecision,i=t.let("res"),c=s?(0,Qe._)`Math.abs(Math.round(${i}) - ${i}) > 1e-${s}`:(0,Qe._)`${i} !== parseInt(${i})`;e.fail$data((0,Qe._)`(${n} === 0 || (${i} = ${r}/${n}, ${c}))`)}};ir.default=Oi;var cr={},dr={};Object.defineProperty(dr,"__esModule",{value:!0});function ya(e){const t=e.length;let r=0,n=0,a;for(;n<t;)r++,a=e.charCodeAt(n++),a>=55296&&a<=56319&&n<t&&(a=e.charCodeAt(n),(a&64512)===56320&&n++);return r}dr.default=ya,ya.code='require("ajv/dist/runtime/ucs2length").default',Object.defineProperty(cr,"__esModule",{value:!0});const Re=D,Ti=N,Ii=dr,Ri={keyword:["maxLength","minLength"],type:"string",schemaType:"number",$data:!0,error:{message({keyword:e,schemaCode:t}){const r=e==="maxLength"?"more":"fewer";return(0,Re.str)`must NOT have ${r} than ${t} characters`},params:({schemaCode:e})=>(0,Re._)`{limit: ${e}}`},code(e){const{keyword:t,data:r,schemaCode:n,it:a}=e,s=t==="maxLength"?Re.operators.GT:Re.operators.LT,i=a.opts.unicode===!1?(0,Re._)`${r}.length`:(0,Re._)`${(0,Ti.useFunc)(e.gen,Ii.default)}(${r})`;e.fail$data((0,Re._)`${i} ${s} ${n}`)}};cr.default=Ri;var ur={};Object.defineProperty(ur,"__esModule",{value:!0});const Ci=q,bt=D,Ai={keyword:"pattern",type:"string",schemaType:"string",$data:!0,error:{message:({schemaCode:e})=>(0,bt.str)`must match pattern "${e}"`,params:({schemaCode:e})=>(0,bt._)`{pattern: ${e}}`},code(e){const{data:t,$data:r,schema:n,schemaCode:a,it:s}=e,i=s.opts.unicodeRegExp?"u":"",c=r?(0,bt._)`(new RegExp(${a}, ${i}))`:(0,Ci.usePattern)(e,n);e.fail$data((0,bt._)`!${c}.test(${t})`)}};ur.default=Ai;var lr={};Object.defineProperty(lr,"__esModule",{value:!0});const et=D,Mi={keyword:["maxProperties","minProperties"],type:"object",schemaType:"number",$data:!0,error:{message({keyword:e,schemaCode:t}){const r=e==="maxProperties"?"more":"fewer";return(0,et.str)`must NOT have ${r} than ${t} properties`},params:({schemaCode:e})=>(0,et._)`{limit: ${e}}`},code(e){const{keyword:t,data:r,schemaCode:n}=e,a=t==="maxProperties"?et.operators.GT:et.operators.LT;e.fail$data((0,et._)`Object.keys(${r}).length ${a} ${n}`)}};lr.default=Mi;var fr={};Object.defineProperty(fr,"__esModule",{value:!0});const tt=q,rt=D,zi=N,Di={keyword:"required",type:"object",schemaType:"array",$data:!0,error:{message:({params:{missingProperty:e}})=>(0,rt.str)`must have required property '${e}'`,params:({params:{missingProperty:e}})=>(0,rt._)`{missingProperty: ${e}}`},code(e){const{gen:t,schema:r,schemaCode:n,data:a,$data:s,it:i}=e,{opts:c}=i;if(!s&&r.length===0)return;const l=r.length>=c.loopRequired;if(i.allErrors?d():f(),c.strictRequired){const b=e.parentSchema.properties,{definedProperties:_}=e.it;for(const v of r)if((b==null?void 0:b[v])===void 0&&!_.has(v)){const y=i.schemaEnv.baseId+i.errSchemaPath,m=`required property "${v}" is not defined at "${y}" (strictRequired)`;(0,zi.checkStrictMode)(i,m,i.opts.strictRequired)}}function d(){if(l||s)e.block$data(rt.nil,p);else for(const b of r)(0,tt.checkReportMissingProp)(e,b)}function f(){const b=t.let("missing");if(l||s){const _=t.let("valid",!0);e.block$data(_,()=>S(b,_)),e.ok(_)}else t.if((0,tt.checkMissingProp)(e,r,b)),(0,tt.reportMissingProp)(e,b),t.else()}function p(){t.forOf("prop",n,b=>{e.setParams({missingProperty:b}),t.if((0,tt.noPropertyInData)(t,a,b,c.ownProperties),()=>e.error())})}function S(b,_){e.setParams({missingProperty:b}),t.forOf(b,n,()=>{t.assign(_,(0,tt.propertyInData)(t,a,b,c.ownProperties)),t.if((0,rt.not)(_),()=>{e.error(),t.break()})},rt.nil)}}};fr.default=Di;var mr={};Object.defineProperty(mr,"__esModule",{value:!0});const nt=D,Vi={keyword:["maxItems","minItems"],type:"array",schemaType:"number",$data:!0,error:{message({keyword:e,schemaCode:t}){const r=e==="maxItems"?"more":"fewer";return(0,nt.str)`must NOT have ${r} than ${t} items`},params:({schemaCode:e})=>(0,nt._)`{limit: ${e}}`},code(e){const{keyword:t,data:r,schemaCode:n}=e,a=t==="maxItems"?nt.operators.GT:nt.operators.LT;e.fail$data((0,nt._)`${r}.length ${a} ${n}`)}};mr.default=Vi;var pr={},at={};Object.defineProperty(at,"__esModule",{value:!0});const ga=Rn;ga.code='require("ajv/dist/runtime/equal").default',at.default=ga,Object.defineProperty(pr,"__esModule",{value:!0});const hr=W,Y=D,qi=N,Fi=at,Ui={keyword:"uniqueItems",type:"array",schemaType:"boolean",$data:!0,error:{message:({params:{i:e,j:t}})=>(0,Y.str)`must NOT have duplicate items (items ## ${t} and ${e} are identical)`,params:({params:{i:e,j:t}})=>(0,Y._)`{i: ${e}, j: ${t}}`},code(e){const{gen:t,data:r,$data:n,schema:a,parentSchema:s,schemaCode:i,it:c}=e;if(!n&&!a)return;const l=t.let("valid"),d=s.items?(0,hr.getSchemaTypes)(s.items):[];e.block$data(l,f,(0,Y._)`${i} === false`),e.ok(l);function f(){const _=t.let("i",(0,Y._)`${r}.length`),v=t.let("j");e.setParams({i:_,j:v}),t.assign(l,!0),t.if((0,Y._)`${_} > 1`,()=>(p()?S:b)(_,v))}function p(){return d.length>0&&!d.some(_=>_==="object"||_==="array")}function S(_,v){const y=t.name("item"),m=(0,hr.checkDataTypes)(d,y,c.opts.strictNumbers,hr.DataType.Wrong),$=t.const("indices",(0,Y._)`{}`);t.for((0,Y._)`;${_}--;`,()=>{t.let(y,(0,Y._)`${r}[${_}]`),t.if(m,(0,Y._)`continue`),d.length>1&&t.if((0,Y._)`typeof ${y} == "string"`,(0,Y._)`${y} += "_"`),t.if((0,Y._)`typeof ${$}[${y}] == "number"`,()=>{t.assign(v,(0,Y._)`${$}[${y}]`),e.error(),t.assign(l,!1).break()}).code((0,Y._)`${$}[${y}] = ${_}`)})}function b(_,v){const y=(0,qi.useFunc)(t,Fi.default),m=t.name("outer");t.label(m).for((0,Y._)`;${_}--;`,()=>t.for((0,Y._)`${v} = ${_}; ${v}--;`,()=>t.if((0,Y._)`${y}(${r}[${_}], ${r}[${v}])`,()=>{e.error(),t.assign(l,!1).break(m)})))}}};pr.default=Ui;var yr={};Object.defineProperty(yr,"__esModule",{value:!0});const gr=D,xi=N,Li=at,Ki={keyword:"const",$data:!0,error:{message:"must be equal to constant",params:({schemaCode:e})=>(0,gr._)`{allowedValue: ${e}}`},code(e){const{gen:t,data:r,$data:n,schemaCode:a,schema:s}=e;n||s&&typeof s=="object"?e.fail$data((0,gr._)`!${(0,xi.useFunc)(t,Li.default)}(${r}, ${a})`):e.fail((0,gr._)`${s} !== ${r}`)}};yr.default=Ki;var $r={};Object.defineProperty($r,"__esModule",{value:!0});const st=D,Hi=N,Gi=at,Bi={keyword:"enum",schemaType:"array",$data:!0,error:{message:"must be equal to one of the allowed values",params:({schemaCode:e})=>(0,st._)`{allowedValues: ${e}}`},code(e){const{gen:t,data:r,$data:n,schema:a,schemaCode:s,it:i}=e;if(!n&&a.length===0)throw new Error("enum must have non-empty array");const c=a.length>=i.opts.loopEnum;let l;const d=()=>l??(l=(0,Hi.useFunc)(t,Gi.default));let f;if(c||n)f=t.let("valid"),e.block$data(f,p);else{if(!Array.isArray(a))throw new Error("ajv implementation error");const b=t.const("vSchema",s);f=(0,st.or)(...a.map((_,v)=>S(b,v)))}e.pass(f);function p(){t.assign(f,!1),t.forOf("v",s,b=>t.if((0,st._)`${d()}(${r}, ${b})`,()=>t.assign(f,!0).break()))}function S(b,_){const v=a[_];return typeof v=="object"&&v!==null?(0,st._)`${d()}(${r}, ${b}[${_}])`:(0,st._)`${r} === ${v}`}}};$r.default=Bi,Object.defineProperty(sr,"__esModule",{value:!0});const Ji=or,Wi=ir,Xi=cr,Zi=ur,Yi=lr,Qi=fr,ec=mr,tc=pr,rc=yr,nc=$r,ac=[Ji.default,Wi.default,Xi.default,Zi.default,Yi.default,Qi.default,ec.default,tc.default,{keyword:"type",schemaType:["string","array"]},{keyword:"nullable",schemaType:"boolean"},rc.default,nc.default];sr.default=ac;var _r={},xe={};Object.defineProperty(xe,"__esModule",{value:!0}),xe.validateAdditionalItems=void 0;const Ce=D,vr=N,sc={keyword:"additionalItems",type:"array",schemaType:["boolean","object"],before:"uniqueItems",error:{message:({params:{len:e}})=>(0,Ce.str)`must NOT have more than ${e} items`,params:({params:{len:e}})=>(0,Ce._)`{limit: ${e}}`},code(e){const{parentSchema:t,it:r}=e,{items:n}=t;if(!Array.isArray(n)){(0,vr.checkStrictMode)(r,'"additionalItems" is ignored when "items" is not an array of schemas');return}$a(e,n)}};function $a(e,t){const{gen:r,schema:n,data:a,keyword:s,it:i}=e;i.items=!0;const c=r.const("len",(0,Ce._)`${a}.length`);if(n===!1)e.setParams({len:t.length}),e.pass((0,Ce._)`${c} <= ${t.length}`);else if(typeof n=="object"&&!(0,vr.alwaysValidSchema)(i,n)){const d=r.var("valid",(0,Ce._)`${c} <= ${t.length}`);r.if((0,Ce.not)(d),()=>l(d)),e.ok(d)}function l(d){r.forRange("i",t.length,c,f=>{e.subschema({keyword:s,dataProp:f,dataPropType:vr.Type.Num},d),i.allErrors||r.if((0,Ce.not)(d),()=>r.break())})}}xe.validateAdditionalItems=$a,xe.default=sc;var br={},Le={};Object.defineProperty(Le,"__esModule",{value:!0}),Le.validateTuple=void 0;const _a=D,wt=N,oc=q,ic={keyword:"items",type:"array",schemaType:["object","array","boolean"],before:"uniqueItems",code(e){const{schema:t,it:r}=e;if(Array.isArray(t))return va(e,"additionalItems",t);r.items=!0,!(0,wt.alwaysValidSchema)(r,t)&&e.ok((0,oc.validateArray)(e))}};function va(e,t,r=e.schema){const{gen:n,parentSchema:a,data:s,keyword:i,it:c}=e;f(a),c.opts.unevaluated&&r.length&&c.items!==!0&&(c.items=wt.mergeEvaluated.items(n,r.length,c.items));const l=n.name("valid"),d=n.const("len",(0,_a._)`${s}.length`);r.forEach((p,S)=>{(0,wt.alwaysValidSchema)(c,p)||(n.if((0,_a._)`${d} > ${S}`,()=>e.subschema({keyword:i,schemaProp:S,dataProp:S},l)),e.ok(l))});function f(p){const{opts:S,errSchemaPath:b}=c,_=r.length,v=_===p.minItems&&(_===p.maxItems||p[t]===!1);if(S.strictTuples&&!v){const y=`"${i}" is ${_}-tuple, but minItems or maxItems/${t} are not specified or different at path "${b}"`;(0,wt.checkStrictMode)(c,y,S.strictTuples)}}}Le.validateTuple=va,Le.default=ic,Object.defineProperty(br,"__esModule",{value:!0});const cc=Le,dc={keyword:"prefixItems",type:"array",schemaType:["array"],before:"uniqueItems",code:e=>(0,cc.validateTuple)(e,"items")};br.default=dc;var wr={};Object.defineProperty(wr,"__esModule",{value:!0});const ba=D,uc=N,lc=q,fc=xe,mc={keyword:"items",type:"array",schemaType:["object","boolean"],before:"uniqueItems",error:{message:({params:{len:e}})=>(0,ba.str)`must NOT have more than ${e} items`,params:({params:{len:e}})=>(0,ba._)`{limit: ${e}}`},code(e){const{schema:t,parentSchema:r,it:n}=e,{prefixItems:a}=r;n.items=!0,!(0,uc.alwaysValidSchema)(n,t)&&(a?(0,fc.validateAdditionalItems)(e,a):e.ok((0,lc.validateArray)(e)))}};wr.default=mc;var Sr={};Object.defineProperty(Sr,"__esModule",{value:!0});const se=D,St=N,pc={keyword:"contains",type:"array",schemaType:["object","boolean"],before:"uniqueItems",trackErrors:!0,error:{message:({params:{min:e,max:t}})=>t===void 0?(0,se.str)`must contain at least ${e} valid item(s)`:(0,se.str)`must contain at least ${e} and no more than ${t} valid item(s)`,params:({params:{min:e,max:t}})=>t===void 0?(0,se._)`{minContains: ${e}}`:(0,se._)`{minContains: ${e}, maxContains: ${t}}`},code(e){const{gen:t,schema:r,parentSchema:n,data:a,it:s}=e;let i,c;const{minContains:l,maxContains:d}=n;s.opts.next?(i=l===void 0?1:l,c=d):i=1;const f=t.const("len",(0,se._)`${a}.length`);if(e.setParams({min:i,max:c}),c===void 0&&i===0){(0,St.checkStrictMode)(s,'"minContains" == 0 without "maxContains": "contains" keyword ignored');return}if(c!==void 0&&i>c){(0,St.checkStrictMode)(s,'"minContains" > "maxContains" is always invalid'),e.fail();return}if((0,St.alwaysValidSchema)(s,r)){let v=(0,se._)`${f} >= ${i}`;c!==void 0&&(v=(0,se._)`${v} && ${f} <= ${c}`),e.pass(v);return}s.items=!0;const p=t.name("valid");c===void 0&&i===1?b(p,()=>t.if(p,()=>t.break())):i===0?(t.let(p,!0),c!==void 0&&t.if((0,se._)`${a}.length > 0`,S)):(t.let(p,!1),S()),e.result(p,()=>e.reset());function S(){const v=t.name("_valid"),y=t.let("count",0);b(v,()=>t.if(v,()=>_(y)))}function b(v,y){t.forRange("i",0,f,m=>{e.subschema({keyword:"contains",dataProp:m,dataPropType:St.Type.Num,compositeRule:!0},v),y()})}function _(v){t.code((0,se._)`${v}++`),c===void 0?t.if((0,se._)`${v} >= ${i}`,()=>t.assign(p,!0).break()):(t.if((0,se._)`${v} > ${c}`,()=>t.assign(p,!1).break()),i===1?t.assign(p,!0):t.if((0,se._)`${v} >= ${i}`,()=>t.assign(p,!0)))}}};Sr.default=pc;var wa={};(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.validateSchemaDeps=e.validatePropertyDeps=e.error=void 0;const t=D,r=N,n=q;e.error={message:({params:{property:l,depsCount:d,deps:f}})=>{const p=d===1?"property":"properties";return(0,t.str)`must have ${p} ${f} when property ${l} is present`},params:({params:{property:l,depsCount:d,deps:f,missingProperty:p}})=>(0,t._)`{property: ${l},
    missingProperty: ${p},
    depsCount: ${d},
    deps: ${f}}`};const a={keyword:"dependencies",type:"object",schemaType:"object",error:e.error,code(l){const[d,f]=s(l);i(l,d),c(l,f)}};function s({schema:l}){const d={},f={};for(const p in l){if(p==="__proto__")continue;const S=Array.isArray(l[p])?d:f;S[p]=l[p]}return[d,f]}function i(l,d=l.schema){const{gen:f,data:p,it:S}=l;if(Object.keys(d).length===0)return;const b=f.let("missing");for(const _ in d){const v=d[_];if(v.length===0)continue;const y=(0,n.propertyInData)(f,p,_,S.opts.ownProperties);l.setParams({property:_,depsCount:v.length,deps:v.join(", ")}),S.allErrors?f.if(y,()=>{for(const m of v)(0,n.checkReportMissingProp)(l,m)}):(f.if((0,t._)`${y} && (${(0,n.checkMissingProp)(l,v,b)})`),(0,n.reportMissingProp)(l,b),f.else())}}e.validatePropertyDeps=i;function c(l,d=l.schema){const{gen:f,data:p,keyword:S,it:b}=l,_=f.name("valid");for(const v in d)(0,r.alwaysValidSchema)(b,d[v])||(f.if((0,n.propertyInData)(f,p,v,b.opts.ownProperties),()=>{const y=l.subschema({keyword:S,schemaProp:v},_);l.mergeValidEvaluated(y,_)},()=>f.var(_,!0)),l.ok(_))}e.validateSchemaDeps=c,e.default=a})(wa);var Er={};Object.defineProperty(Er,"__esModule",{value:!0});const Sa=D,hc=N,yc={keyword:"propertyNames",type:"object",schemaType:["object","boolean"],error:{message:"property name must be valid",params:({params:e})=>(0,Sa._)`{propertyName: ${e.propertyName}}`},code(e){const{gen:t,schema:r,data:n,it:a}=e;if((0,hc.alwaysValidSchema)(a,r))return;const s=t.name("valid");t.forIn("key",n,i=>{e.setParams({propertyName:i}),e.subschema({keyword:"propertyNames",data:i,dataTypes:["string"],propertyName:i,compositeRule:!0},s),t.if((0,Sa.not)(s),()=>{e.error(!0),a.allErrors||t.break()})}),e.ok(s)}};Er.default=yc;var Et={};Object.defineProperty(Et,"__esModule",{value:!0});const Pt=q,ue=D,gc=le,kt=N,$c={keyword:"additionalProperties",type:["object"],schemaType:["boolean","object"],allowUndefined:!0,trackErrors:!0,error:{message:"must NOT have additional properties",params:({params:e})=>(0,ue._)`{additionalProperty: ${e.additionalProperty}}`},code(e){const{gen:t,schema:r,parentSchema:n,data:a,errsCount:s,it:i}=e;if(!s)throw new Error("ajv implementation error");const{allErrors:c,opts:l}=i;if(i.props=!0,l.removeAdditional!=="all"&&(0,kt.alwaysValidSchema)(i,r))return;const d=(0,Pt.allSchemaProperties)(n.properties),f=(0,Pt.allSchemaProperties)(n.patternProperties);p(),e.ok((0,ue._)`${s} === ${gc.default.errors}`);function p(){t.forIn("key",a,y=>{!d.length&&!f.length?_(y):t.if(S(y),()=>_(y))})}function S(y){let m;if(d.length>8){const $=(0,kt.schemaRefOrVal)(i,n.properties,"properties");m=(0,Pt.isOwnProperty)(t,$,y)}else d.length?m=(0,ue.or)(...d.map($=>(0,ue._)`${y} === ${$}`)):m=ue.nil;return f.length&&(m=(0,ue.or)(m,...f.map($=>(0,ue._)`${(0,Pt.usePattern)(e,$)}.test(${y})`))),(0,ue.not)(m)}function b(y){t.code((0,ue._)`delete ${a}[${y}]`)}function _(y){if(l.removeAdditional==="all"||l.removeAdditional&&r===!1){b(y);return}if(r===!1){e.setParams({additionalProperty:y}),e.error(),c||t.break();return}if(typeof r=="object"&&!(0,kt.alwaysValidSchema)(i,r)){const m=t.name("valid");l.removeAdditional==="failing"?(v(y,m,!1),t.if((0,ue.not)(m),()=>{e.reset(),b(y)})):(v(y,m),c||t.if((0,ue.not)(m),()=>t.break()))}}function v(y,m,$){const P={keyword:"additionalProperties",dataProp:y,dataPropType:kt.Type.Str};$===!1&&Object.assign(P,{compositeRule:!0,createErrors:!1,allErrors:!1}),e.subschema(P,m)}}};Et.default=$c;var Pr={};Object.defineProperty(Pr,"__esModule",{value:!0});const _c=ie,Ea=q,kr=N,Pa=Et,vc={keyword:"properties",type:"object",schemaType:"object",code(e){const{gen:t,schema:r,parentSchema:n,data:a,it:s}=e;s.opts.removeAdditional==="all"&&n.additionalProperties===void 0&&Pa.default.code(new _c.KeywordCxt(s,Pa.default,"additionalProperties"));const i=(0,Ea.allSchemaProperties)(r);for(const p of i)s.definedProperties.add(p);s.opts.unevaluated&&i.length&&s.props!==!0&&(s.props=kr.mergeEvaluated.props(t,(0,kr.toHash)(i),s.props));const c=i.filter(p=>!(0,kr.alwaysValidSchema)(s,r[p]));if(c.length===0)return;const l=t.name("valid");for(const p of c)d(p)?f(p):(t.if((0,Ea.propertyInData)(t,a,p,s.opts.ownProperties)),f(p),s.allErrors||t.else().var(l,!0),t.endIf()),e.it.definedProperties.add(p),e.ok(l);function d(p){return s.opts.useDefaults&&!s.compositeRule&&r[p].default!==void 0}function f(p){e.subschema({keyword:"properties",schemaProp:p,dataProp:p},l)}}};Pr.default=vc;var jr={};Object.defineProperty(jr,"__esModule",{value:!0});const ka=q,jt=D,ja=N,Na=N,bc={keyword:"patternProperties",type:"object",schemaType:"object",code(e){const{gen:t,schema:r,data:n,parentSchema:a,it:s}=e,{opts:i}=s,c=(0,ka.allSchemaProperties)(r),l=c.filter(v=>(0,ja.alwaysValidSchema)(s,r[v]));if(c.length===0||l.length===c.length&&(!s.opts.unevaluated||s.props===!0))return;const d=i.strictSchema&&!i.allowMatchingProperties&&a.properties,f=t.name("valid");s.props!==!0&&!(s.props instanceof jt.Name)&&(s.props=(0,Na.evaluatedPropsToName)(t,s.props));const{props:p}=s;S();function S(){for(const v of c)d&&b(v),s.allErrors?_(v):(t.var(f,!0),_(v),t.if(f))}function b(v){for(const y in d)new RegExp(v).test(y)&&(0,ja.checkStrictMode)(s,`property ${y} matches pattern ${v} (use allowMatchingProperties)`)}function _(v){t.forIn("key",n,y=>{t.if((0,jt._)`${(0,ka.usePattern)(e,v)}.test(${y})`,()=>{const m=l.includes(v);m||e.subschema({keyword:"patternProperties",schemaProp:v,dataProp:y,dataPropType:Na.Type.Str},f),s.opts.unevaluated&&p!==!0?t.assign((0,jt._)`${p}[${y}]`,!0):!m&&!s.allErrors&&t.if((0,jt.not)(f),()=>t.break())})})}}};jr.default=bc;var Nr={};Object.defineProperty(Nr,"__esModule",{value:!0});const wc=N,Sc={keyword:"not",schemaType:["object","boolean"],trackErrors:!0,code(e){const{gen:t,schema:r,it:n}=e;if((0,wc.alwaysValidSchema)(n,r)){e.fail();return}const a=t.name("valid");e.subschema({keyword:"not",compositeRule:!0,createErrors:!1,allErrors:!1},a),e.failResult(a,()=>e.reset(),()=>e.error())},error:{message:"must NOT be valid"}};Nr.default=Sc;var Or={};Object.defineProperty(Or,"__esModule",{value:!0});const Ec={keyword:"anyOf",schemaType:"array",trackErrors:!0,code:q.validateUnion,error:{message:"must match a schema in anyOf"}};Or.default=Ec;var Tr={};Object.defineProperty(Tr,"__esModule",{value:!0});const Nt=D,Pc=N,kc={keyword:"oneOf",schemaType:"array",trackErrors:!0,error:{message:"must match exactly one schema in oneOf",params:({params:e})=>(0,Nt._)`{passingSchemas: ${e.passing}}`},code(e){const{gen:t,schema:r,parentSchema:n,it:a}=e;if(!Array.isArray(r))throw new Error("ajv implementation error");if(a.opts.discriminator&&n.discriminator)return;const s=r,i=t.let("valid",!1),c=t.let("passing",null),l=t.name("_valid");e.setParams({passing:c}),t.block(d),e.result(i,()=>e.reset(),()=>e.error(!0));function d(){s.forEach((f,p)=>{let S;(0,Pc.alwaysValidSchema)(a,f)?t.var(l,!0):S=e.subschema({keyword:"oneOf",schemaProp:p,compositeRule:!0},l),p>0&&t.if((0,Nt._)`${l} && ${i}`).assign(i,!1).assign(c,(0,Nt._)`[${c}, ${p}]`).else(),t.if(l,()=>{t.assign(i,!0),t.assign(c,p),S&&e.mergeEvaluated(S,Nt.Name)})})}}};Tr.default=kc;var Ir={};Object.defineProperty(Ir,"__esModule",{value:!0});const jc=N,Nc={keyword:"allOf",schemaType:"array",code(e){const{gen:t,schema:r,it:n}=e;if(!Array.isArray(r))throw new Error("ajv implementation error");const a=t.name("valid");r.forEach((s,i)=>{if((0,jc.alwaysValidSchema)(n,s))return;const c=e.subschema({keyword:"allOf",schemaProp:i},a);e.ok(a),e.mergeEvaluated(c)})}};Ir.default=Nc;var Rr={};Object.defineProperty(Rr,"__esModule",{value:!0});const Ot=D,Oa=N,Oc={keyword:"if",schemaType:["object","boolean"],trackErrors:!0,error:{message:({params:e})=>(0,Ot.str)`must match "${e.ifClause}" schema`,params:({params:e})=>(0,Ot._)`{failingKeyword: ${e.ifClause}}`},code(e){const{gen:t,parentSchema:r,it:n}=e;r.then===void 0&&r.else===void 0&&(0,Oa.checkStrictMode)(n,'"if" without "then" and "else" is ignored');const a=Ta(n,"then"),s=Ta(n,"else");if(!a&&!s)return;const i=t.let("valid",!0),c=t.name("_valid");if(l(),e.reset(),a&&s){const f=t.let("ifClause");e.setParams({ifClause:f}),t.if(c,d("then",f),d("else",f))}else a?t.if(c,d("then")):t.if((0,Ot.not)(c),d("else"));e.pass(i,()=>e.error(!0));function l(){const f=e.subschema({keyword:"if",compositeRule:!0,createErrors:!1,allErrors:!1},c);e.mergeEvaluated(f)}function d(f,p){return()=>{const S=e.subschema({keyword:f},c);t.assign(i,c),e.mergeValidEvaluated(S,i),p?t.assign(p,(0,Ot._)`${f}`):e.setParams({ifClause:f})}}}};function Ta(e,t){const r=e.schema[t];return r!==void 0&&!(0,Oa.alwaysValidSchema)(e,r)}Rr.default=Oc;var Cr={};Object.defineProperty(Cr,"__esModule",{value:!0});const Tc=N,Ic={keyword:["then","else"],schemaType:["object","boolean"],code({keyword:e,parentSchema:t,it:r}){t.if===void 0&&(0,Tc.checkStrictMode)(r,`"${e}" without "if" is ignored`)}};Cr.default=Ic,Object.defineProperty(_r,"__esModule",{value:!0});const Rc=xe,Cc=br,Ac=Le,Mc=wr,zc=Sr,Dc=wa,Vc=Er,qc=Et,Fc=Pr,Uc=jr,xc=Nr,Lc=Or,Kc=Tr,Hc=Ir,Gc=Rr,Bc=Cr;function Jc(e=!1){const t=[xc.default,Lc.default,Kc.default,Hc.default,Gc.default,Bc.default,Vc.default,qc.default,Dc.default,Fc.default,Uc.default];return e?t.push(Cc.default,Mc.default):t.push(Rc.default,Ac.default),t.push(zc.default),t}_r.default=Jc;var Ar={},Mr={};Object.defineProperty(Mr,"__esModule",{value:!0});const B=D,Wc={keyword:"format",type:["number","string"],schemaType:"string",$data:!0,error:{message:({schemaCode:e})=>(0,B.str)`must match format "${e}"`,params:({schemaCode:e})=>(0,B._)`{format: ${e}}`},code(e,t){const{gen:r,data:n,$data:a,schema:s,schemaCode:i,it:c}=e,{opts:l,errSchemaPath:d,schemaEnv:f,self:p}=c;if(!l.validateFormats)return;a?S():b();function S(){const _=r.scopeValue("formats",{ref:p.formats,code:l.code.formats}),v=r.const("fDef",(0,B._)`${_}[${i}]`),y=r.let("fType"),m=r.let("format");r.if((0,B._)`typeof ${v} == "object" && !(${v} instanceof RegExp)`,()=>r.assign(y,(0,B._)`${v}.type || "string"`).assign(m,(0,B._)`${v}.validate`),()=>r.assign(y,(0,B._)`"string"`).assign(m,v)),e.fail$data((0,B.or)($(),P()));function $(){return l.strictSchema===!1?B.nil:(0,B._)`${i} && !${m}`}function P(){const j=f.$async?(0,B._)`(${v}.async ? await ${m}(${n}) : ${m}(${n}))`:(0,B._)`${m}(${n})`,O=(0,B._)`(typeof ${m} == "function" ? ${j} : ${m}.test(${n}))`;return(0,B._)`${m} && ${m} !== true && ${y} === ${t} && !${O}`}}function b(){const _=p.formats[s];if(!_){$();return}if(_===!0)return;const[v,y,m]=P(_);v===t&&e.pass(j());function $(){if(l.strictSchema===!1){p.logger.warn(O());return}throw new Error(O());function O(){return`unknown format "${s}" ignored in schema at path "${d}"`}}function P(O){const L=O instanceof RegExp?(0,B.regexpCode)(O):l.code.formats?(0,B._)`${l.code.formats}${(0,B.getProperty)(s)}`:void 0,J=r.scopeValue("formats",{key:s,ref:O,code:L});return typeof O=="object"&&!(O instanceof RegExp)?[O.type||"string",O.validate,(0,B._)`${J}.validate`]:["string",O,J]}function j(){if(typeof _=="object"&&!(_ instanceof RegExp)&&_.async){if(!f.$async)throw new Error("async format in sync schema");return(0,B._)`await ${m}(${n})`}return typeof y=="function"?(0,B._)`${m}(${n})`:(0,B._)`${m}.test(${n})`}}}};Mr.default=Wc,Object.defineProperty(Ar,"__esModule",{value:!0});const Xc=[Mr.default];Ar.default=Xc;var Ke={};Object.defineProperty(Ke,"__esModule",{value:!0}),Ke.contentVocabulary=Ke.metadataVocabulary=void 0,Ke.metadataVocabulary=["title","description","default","deprecated","readOnly","writeOnly","examples"],Ke.contentVocabulary=["contentMediaType","contentEncoding","contentSchema"],Object.defineProperty(rr,"__esModule",{value:!0});const Zc=nr,Yc=sr,Qc=_r,ed=Ar,Ia=Ke,td=[Zc.default,Yc.default,(0,Qc.default)(),ed.default,Ia.metadataVocabulary,Ia.contentVocabulary];rr.default=td;var zr={},Tt={};Object.defineProperty(Tt,"__esModule",{value:!0}),Tt.DiscrError=void 0;var Ra;(function(e){e.Tag="tag",e.Mapping="mapping"})(Ra||(Tt.DiscrError=Ra={})),Object.defineProperty(zr,"__esModule",{value:!0});const He=D,Dr=Tt,Ca=re,rd=Fe,nd=N,ad={keyword:"discriminator",type:"object",schemaType:"object",error:{message:({params:{discrError:e,tagName:t}})=>e===Dr.DiscrError.Tag?`tag "${t}" must be string`:`value of tag "${t}" must be in oneOf`,params:({params:{discrError:e,tag:t,tagName:r}})=>(0,He._)`{error: ${e}, tag: ${r}, tagValue: ${t}}`},code(e){const{gen:t,data:r,schema:n,parentSchema:a,it:s}=e,{oneOf:i}=a;if(!s.opts.discriminator)throw new Error("discriminator: requires discriminator option");const c=n.propertyName;if(typeof c!="string")throw new Error("discriminator: requires propertyName");if(n.mapping)throw new Error("discriminator: mapping is not supported");if(!i)throw new Error("discriminator: requires oneOf keyword");const l=t.let("valid",!1),d=t.const("tag",(0,He._)`${r}${(0,He.getProperty)(c)}`);t.if((0,He._)`typeof ${d} == "string"`,()=>f(),()=>e.error(!1,{discrError:Dr.DiscrError.Tag,tag:d,tagName:c})),e.ok(l);function f(){const b=S();t.if(!1);for(const _ in b)t.elseIf((0,He._)`${d} === ${_}`),t.assign(l,p(b[_]));t.else(),e.error(!1,{discrError:Dr.DiscrError.Mapping,tag:d,tagName:c}),t.endIf()}function p(b){const _=t.name("valid"),v=e.subschema({keyword:"oneOf",schemaProp:b},_);return e.mergeEvaluated(v,He.Name),_}function S(){var b;const _={},v=m(a);let y=!0;for(let j=0;j<i.length;j++){let O=i[j];if(O!=null&&O.$ref&&!(0,nd.schemaHasRulesButRef)(O,s.self.RULES)){const J=O.$ref;if(O=Ca.resolveRef.call(s.self,s.schemaEnv.root,s.baseId,J),O instanceof Ca.SchemaEnv&&(O=O.schema),O===void 0)throw new rd.default(s.opts.uriResolver,s.baseId,J)}const L=(b=O==null?void 0:O.properties)===null||b===void 0?void 0:b[c];if(typeof L!="object")throw new Error(`discriminator: oneOf subschemas (or referenced schemas) must have "properties/${c}"`);y=y&&(v||m(O)),$(L,j)}if(!y)throw new Error(`discriminator: "${c}" must be required`);return _;function m({required:j}){return Array.isArray(j)&&j.includes(c)}function $(j,O){if(j.const)P(j.const,O);else if(j.enum)for(const L of j.enum)P(L,O);else throw new Error(`discriminator: "properties/${c}" must have "const" or "enum"`)}function P(j,O){if(typeof j!="string"||j in _)throw new Error(`discriminator: "${c}" values must be unique strings`);_[j]=O}}}};zr.default=ad;const sd={$schema:"http://json-schema.org/draft-07/schema#",$id:"http://json-schema.org/draft-07/schema#",title:"Core schema meta-schema",definitions:{schemaArray:{type:"array",minItems:1,items:{$ref:"#"}},nonNegativeInteger:{type:"integer",minimum:0},nonNegativeIntegerDefault0:{allOf:[{$ref:"#/definitions/nonNegativeInteger"},{default:0}]},simpleTypes:{enum:["array","boolean","integer","null","number","object","string"]},stringArray:{type:"array",items:{type:"string"},uniqueItems:!0,default:[]}},type:["object","boolean"],properties:{$id:{type:"string",format:"uri-reference"},$schema:{type:"string",format:"uri"},$ref:{type:"string",format:"uri-reference"},$comment:{type:"string"},title:{type:"string"},description:{type:"string"},default:!0,readOnly:{type:"boolean",default:!1},examples:{type:"array",items:!0},multipleOf:{type:"number",exclusiveMinimum:0},maximum:{type:"number"},exclusiveMaximum:{type:"number"},minimum:{type:"number"},exclusiveMinimum:{type:"number"},maxLength:{$ref:"#/definitions/nonNegativeInteger"},minLength:{$ref:"#/definitions/nonNegativeIntegerDefault0"},pattern:{type:"string",format:"regex"},additionalItems:{$ref:"#"},items:{anyOf:[{$ref:"#"},{$ref:"#/definitions/schemaArray"}],default:!0},maxItems:{$ref:"#/definitions/nonNegativeInteger"},minItems:{$ref:"#/definitions/nonNegativeIntegerDefault0"},uniqueItems:{type:"boolean",default:!1},contains:{$ref:"#"},maxProperties:{$ref:"#/definitions/nonNegativeInteger"},minProperties:{$ref:"#/definitions/nonNegativeIntegerDefault0"},required:{$ref:"#/definitions/stringArray"},additionalProperties:{$ref:"#"},definitions:{type:"object",additionalProperties:{$ref:"#"},default:{}},properties:{type:"object",additionalProperties:{$ref:"#"},default:{}},patternProperties:{type:"object",additionalProperties:{$ref:"#"},propertyNames:{format:"regex"},default:{}},dependencies:{type:"object",additionalProperties:{anyOf:[{$ref:"#"},{$ref:"#/definitions/stringArray"}]}},propertyNames:{$ref:"#"},const:!0,enum:{type:"array",items:!0,minItems:1,uniqueItems:!0},type:{anyOf:[{$ref:"#/definitions/simpleTypes"},{type:"array",items:{$ref:"#/definitions/simpleTypes"},minItems:1,uniqueItems:!0}]},format:{type:"string"},contentMediaType:{type:"string"},contentEncoding:{type:"string"},if:{$ref:"#"},then:{$ref:"#"},else:{$ref:"#"},allOf:{$ref:"#/definitions/schemaArray"},anyOf:{$ref:"#/definitions/schemaArray"},oneOf:{$ref:"#/definitions/schemaArray"},not:{$ref:"#"}},default:!0};(function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.MissingRefError=t.ValidationError=t.CodeGen=t.Name=t.nil=t.stringify=t.str=t._=t.KeywordCxt=t.Ajv=void 0;const r=fn,n=rr,a=zr,s=sd,i=["/properties"],c="http://json-schema.org/draft-07/schema";class l extends r.default{_addVocabularies(){super._addVocabularies(),n.default.forEach(_=>this.addVocabulary(_)),this.opts.discriminator&&this.addKeyword(a.default)}_addDefaultMetaSchema(){if(super._addDefaultMetaSchema(),!this.opts.meta)return;const _=this.opts.$data?this.$dataMetaSchema(s,i):s;this.addMetaSchema(_,c,!1),this.refs["http://json-schema.org/schema"]=c}defaultMeta(){return this.opts.defaultMeta=super.defaultMeta()||(this.getSchema(c)?c:void 0)}}t.Ajv=l,e.exports=t=l,e.exports.Ajv=l,Object.defineProperty(t,"__esModule",{value:!0}),t.default=l;var d=ie;Object.defineProperty(t,"KeywordCxt",{enumerable:!0,get:function(){return d.KeywordCxt}});var f=D;Object.defineProperty(t,"_",{enumerable:!0,get:function(){return f._}}),Object.defineProperty(t,"str",{enumerable:!0,get:function(){return f.str}}),Object.defineProperty(t,"stringify",{enumerable:!0,get:function(){return f.stringify}}),Object.defineProperty(t,"nil",{enumerable:!0,get:function(){return f.nil}}),Object.defineProperty(t,"Name",{enumerable:!0,get:function(){return f.Name}}),Object.defineProperty(t,"CodeGen",{enumerable:!0,get:function(){return f.CodeGen}});var p=Ze;Object.defineProperty(t,"ValidationError",{enumerable:!0,get:function(){return p.default}});var S=Fe;Object.defineProperty(t,"MissingRefError",{enumerable:!0,get:function(){return S.default}})})(At,At.exports);var Aa=At.exports;const od=ln(Aa);var Vr={exports:{}},Ma={};(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.formatNames=e.fastFormats=e.fullFormats=void 0;function t(C,V){return{validate:C,compare:V}}e.fullFormats={date:t(s,i),time:t(l(!0),d),"date-time":t(S(!0),b),"iso-time":t(l(),f),"iso-date-time":t(S(),_),duration:/^P(?!$)((\d+Y)?(\d+M)?(\d+D)?(T(?=\d)(\d+H)?(\d+M)?(\d+S)?)?|(\d+W)?)$/,uri:m,"uri-reference":/^(?:[a-z][a-z0-9+\-.]*:)?(?:\/?\/(?:(?:[a-z0-9\-._~!$&'()*+,;=:]|%[0-9a-f]{2})*@)?(?:\[(?:(?:(?:(?:[0-9a-f]{1,4}:){6}|::(?:[0-9a-f]{1,4}:){5}|(?:[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){4}|(?:(?:[0-9a-f]{1,4}:){0,1}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){3}|(?:(?:[0-9a-f]{1,4}:){0,2}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){2}|(?:(?:[0-9a-f]{1,4}:){0,3}[0-9a-f]{1,4})?::[0-9a-f]{1,4}:|(?:(?:[0-9a-f]{1,4}:){0,4}[0-9a-f]{1,4})?::)(?:[0-9a-f]{1,4}:[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?))|(?:(?:[0-9a-f]{1,4}:){0,5}[0-9a-f]{1,4})?::[0-9a-f]{1,4}|(?:(?:[0-9a-f]{1,4}:){0,6}[0-9a-f]{1,4})?::)|[Vv][0-9a-f]+\.[a-z0-9\-._~!$&'()*+,;=:]+)\]|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)|(?:[a-z0-9\-._~!$&'"()*+,;=]|%[0-9a-f]{2})*)(?::\d*)?(?:\/(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})*)*|\/(?:(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})*)*)?|(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})*)*)?(?:\?(?:[a-z0-9\-._~!$&'"()*+,;=:@/?]|%[0-9a-f]{2})*)?(?:#(?:[a-z0-9\-._~!$&'"()*+,;=:@/?]|%[0-9a-f]{2})*)?$/i,"uri-template":/^(?:(?:[^\x00-\x20"'<>%\\^`{|}]|%[0-9a-f]{2})|\{[+#./;?&=,!@|]?(?:[a-z0-9_]|%[0-9a-f]{2})+(?::[1-9][0-9]{0,3}|\*)?(?:,(?:[a-z0-9_]|%[0-9a-f]{2})+(?::[1-9][0-9]{0,3}|\*)?)*\})*$/i,url:/^(?:https?|ftp):\/\/(?:\S+(?::\S*)?@)?(?:(?!(?:10|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z0-9\u{00a1}-\u{ffff}]+-)*[a-z0-9\u{00a1}-\u{ffff}]+)(?:\.(?:[a-z0-9\u{00a1}-\u{ffff}]+-)*[a-z0-9\u{00a1}-\u{ffff}]+)*(?:\.(?:[a-z\u{00a1}-\u{ffff}]{2,})))(?::\d{2,5})?(?:\/[^\s]*)?$/iu,email:/^[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?$/i,hostname:/^(?=.{1,253}\.?$)[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?(?:\.[a-z0-9](?:[-0-9a-z]{0,61}[0-9a-z])?)*\.?$/i,ipv4:/^(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)\.){3}(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)$/,ipv6:/^((([0-9a-f]{1,4}:){7}([0-9a-f]{1,4}|:))|(([0-9a-f]{1,4}:){6}(:[0-9a-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9a-f]{1,4}:){5}(((:[0-9a-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9a-f]{1,4}:){4}(((:[0-9a-f]{1,4}){1,3})|((:[0-9a-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9a-f]{1,4}:){3}(((:[0-9a-f]{1,4}){1,4})|((:[0-9a-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9a-f]{1,4}:){2}(((:[0-9a-f]{1,4}){1,5})|((:[0-9a-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9a-f]{1,4}:){1}(((:[0-9a-f]{1,4}){1,6})|((:[0-9a-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9a-f]{1,4}){1,7})|((:[0-9a-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))$/i,regex:ve,uuid:/^(?:urn:uuid:)?[0-9a-f]{8}-(?:[0-9a-f]{4}-){3}[0-9a-f]{12}$/i,"json-pointer":/^(?:\/(?:[^~/]|~0|~1)*)*$/,"json-pointer-uri-fragment":/^#(?:\/(?:[a-z0-9_\-.!$&'()*+,;:=@]|%[0-9a-f]{2}|~0|~1)*)*$/i,"relative-json-pointer":/^(?:0|[1-9][0-9]*)(?:#|(?:\/(?:[^~/]|~0|~1)*)*)$/,byte:P,int32:{type:"number",validate:L},int64:{type:"number",validate:J},float:{type:"number",validate:oe},double:{type:"number",validate:oe},password:!0,binary:!0},e.fastFormats={...e.fullFormats,date:t(/^\d\d\d\d-[0-1]\d-[0-3]\d$/,i),time:t(/^(?:[0-2]\d:[0-5]\d:[0-5]\d|23:59:60)(?:\.\d+)?(?:z|[+-]\d\d(?::?\d\d)?)$/i,d),"date-time":t(/^\d\d\d\d-[0-1]\d-[0-3]\dt(?:[0-2]\d:[0-5]\d:[0-5]\d|23:59:60)(?:\.\d+)?(?:z|[+-]\d\d(?::?\d\d)?)$/i,b),"iso-time":t(/^(?:[0-2]\d:[0-5]\d:[0-5]\d|23:59:60)(?:\.\d+)?(?:z|[+-]\d\d(?::?\d\d)?)?$/i,f),"iso-date-time":t(/^\d\d\d\d-[0-1]\d-[0-3]\d[t\s](?:[0-2]\d:[0-5]\d:[0-5]\d|23:59:60)(?:\.\d+)?(?:z|[+-]\d\d(?::?\d\d)?)?$/i,_),uri:/^(?:[a-z][a-z0-9+\-.]*:)(?:\/?\/)?[^\s]*$/i,"uri-reference":/^(?:(?:[a-z][a-z0-9+\-.]*:)?\/?\/)?(?:[^\\\s#][^\s#]*)?(?:#[^\\\s]*)?$/i,email:/^[a-z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?(?:\.[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?)*$/i},e.formatNames=Object.keys(e.fullFormats);function r(C){return C%4===0&&(C%100!==0||C%400===0)}const n=/^(\d\d\d\d)-(\d\d)-(\d\d)$/,a=[0,31,28,31,30,31,30,31,31,30,31,30,31];function s(C){const V=n.exec(C);if(!V)return!1;const G=+V[1],F=+V[2],Q=+V[3];return F>=1&&F<=12&&Q>=1&&Q<=(F===2&&r(G)?29:a[F])}function i(C,V){if(C&&V)return C>V?1:C<V?-1:0}const c=/^(\d\d):(\d\d):(\d\d(?:\.\d+)?)(z|([+-])(\d\d)(?::?(\d\d))?)?$/i;function l(C){return function(G){const F=c.exec(G);if(!F)return!1;const Q=+F[1],be=+F[2],we=+F[3],ot=F[4],Ae=F[5]==="-"?-1:1,k=+(F[6]||0),h=+(F[7]||0);if(k>23||h>59||C&&!ot)return!1;if(Q<=23&&be<=59&&we<60)return!0;const E=be-h*Ae,g=Q-k*Ae-(E<0?1:0);return(g===23||g===-1)&&(E===59||E===-1)&&we<61}}function d(C,V){if(!(C&&V))return;const G=new Date("2020-01-01T"+C).valueOf(),F=new Date("2020-01-01T"+V).valueOf();if(G&&F)return G-F}function f(C,V){if(!(C&&V))return;const G=c.exec(C),F=c.exec(V);if(G&&F)return C=G[1]+G[2]+G[3],V=F[1]+F[2]+F[3],C>V?1:C<V?-1:0}const p=/t|\s/i;function S(C){const V=l(C);return function(F){const Q=F.split(p);return Q.length===2&&s(Q[0])&&V(Q[1])}}function b(C,V){if(!(C&&V))return;const G=new Date(C).valueOf(),F=new Date(V).valueOf();if(G&&F)return G-F}function _(C,V){if(!(C&&V))return;const[G,F]=C.split(p),[Q,be]=V.split(p),we=i(G,Q);if(we!==void 0)return we||d(F,be)}const v=/\/|:/,y=/^(?:[a-z][a-z0-9+\-.]*:)(?:\/?\/(?:(?:[a-z0-9\-._~!$&'()*+,;=:]|%[0-9a-f]{2})*@)?(?:\[(?:(?:(?:(?:[0-9a-f]{1,4}:){6}|::(?:[0-9a-f]{1,4}:){5}|(?:[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){4}|(?:(?:[0-9a-f]{1,4}:){0,1}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){3}|(?:(?:[0-9a-f]{1,4}:){0,2}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){2}|(?:(?:[0-9a-f]{1,4}:){0,3}[0-9a-f]{1,4})?::[0-9a-f]{1,4}:|(?:(?:[0-9a-f]{1,4}:){0,4}[0-9a-f]{1,4})?::)(?:[0-9a-f]{1,4}:[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?))|(?:(?:[0-9a-f]{1,4}:){0,5}[0-9a-f]{1,4})?::[0-9a-f]{1,4}|(?:(?:[0-9a-f]{1,4}:){0,6}[0-9a-f]{1,4})?::)|[Vv][0-9a-f]+\.[a-z0-9\-._~!$&'()*+,;=:]+)\]|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)|(?:[a-z0-9\-._~!$&'()*+,;=]|%[0-9a-f]{2})*)(?::\d*)?(?:\/(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*|\/(?:(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*)?|(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*)(?:\?(?:[a-z0-9\-._~!$&'()*+,;=:@/?]|%[0-9a-f]{2})*)?(?:#(?:[a-z0-9\-._~!$&'()*+,;=:@/?]|%[0-9a-f]{2})*)?$/i;function m(C){return v.test(C)&&y.test(C)}const $=/^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/gm;function P(C){return $.lastIndex=0,$.test(C)}const j=-2147483648,O=2**31-1;function L(C){return Number.isInteger(C)&&C<=O&&C>=j}function J(C){return Number.isInteger(C)}function oe(){return!0}const he=/[^\\]\\Z/;function ve(C){if(he.test(C))return!1;try{return new RegExp(C),!0}catch{return!1}}})(Ma);var za={};(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.formatLimitDefinition=void 0;const t=Aa,r=D,n=r.operators,a={formatMaximum:{okStr:"<=",ok:n.LTE,fail:n.GT},formatMinimum:{okStr:">=",ok:n.GTE,fail:n.LT},formatExclusiveMaximum:{okStr:"<",ok:n.LT,fail:n.GTE},formatExclusiveMinimum:{okStr:">",ok:n.GT,fail:n.LTE}},s={message:({keyword:c,schemaCode:l})=>(0,r.str)`should be ${a[c].okStr} ${l}`,params:({keyword:c,schemaCode:l})=>(0,r._)`{comparison: ${a[c].okStr}, limit: ${l}}`};e.formatLimitDefinition={keyword:Object.keys(a),type:"string",schemaType:"string",$data:!0,error:s,code(c){const{gen:l,data:d,schemaCode:f,keyword:p,it:S}=c,{opts:b,self:_}=S;if(!b.validateFormats)return;const v=new t.KeywordCxt(S,_.RULES.all.format.definition,"format");v.$data?y():m();function y(){const P=l.scopeValue("formats",{ref:_.formats,code:b.code.formats}),j=l.const("fmt",(0,r._)`${P}[${v.schemaCode}]`);c.fail$data((0,r.or)((0,r._)`typeof ${j} != "object"`,(0,r._)`${j} instanceof RegExp`,(0,r._)`typeof ${j}.compare != "function"`,$(j)))}function m(){const P=v.schema,j=_.formats[P];if(!j||j===!0)return;if(typeof j!="object"||j instanceof RegExp||typeof j.compare!="function")throw new Error(`"${p}": format "${P}" does not define "compare" function`);const O=l.scopeValue("formats",{key:P,ref:j,code:b.code.formats?(0,r._)`${b.code.formats}${(0,r.getProperty)(P)}`:void 0});c.fail$data($(O))}function $(P){return(0,r._)`${P}.compare(${d}, ${f}) ${a[p].fail} 0`}},dependencies:["format"]};const i=c=>(c.addKeyword(e.formatLimitDefinition),c);e.default=i})(za),function(e,t){Object.defineProperty(t,"__esModule",{value:!0});const r=Ma,n=za,a=D,s=new a.Name("fullFormats"),i=new a.Name("fastFormats"),c=(d,f={keywords:!0})=>{if(Array.isArray(f))return l(d,f,r.fullFormats,s),d;const[p,S]=f.mode==="fast"?[r.fastFormats,i]:[r.fullFormats,s],b=f.formats||r.formatNames;return l(d,b,p,S),f.keywords&&(0,n.default)(d),d};c.get=(d,f="full")=>{const S=(f==="fast"?r.fastFormats:r.fullFormats)[d];if(!S)throw new Error(`Unknown format "${d}"`);return S};function l(d,f,p,S){var b,_;(b=(_=d.opts.code).formats)!==null&&b!==void 0||(_.formats=(0,a._)`require("ajv-formats/dist/formats").${S}`);for(const v of f)d.addFormat(v,p[v])}e.exports=t=c,Object.defineProperty(t,"__esModule",{value:!0}),t.default=c}(Vr,Vr.exports);var id=Vr.exports;const cd=ln(id),Da=new od({allErrors:!0,verbose:!0,strict:!1});cd(Da);function It(e,t){const r=Da.compile(e);return r(t)?{valid:!0,errors:[]}:{valid:!1,errors:(r.errors||[]).map(s=>({path:s.instancePath||s.schemaPath||"",message:s.message||"Validation error",value:s.data,schema:s.schema}))}}function dd(e){return t=>It(e,t)}function ud(e,t){return It(e,t).valid}class Va{constructor(){Ka(this,"schemas",new Map)}register(t,r){if(!t||typeof t!="string")throw new Error("Block type must be a non-empty string");if(!r||typeof r!="object")throw new Error("Schema must be a valid JSON Schema object");this.schemas.set(t,r)}get(t){return this.schemas.get(t)}validate(t,r){const n=this.schemas.get(t);return n?It(n,r):{valid:!1,errors:[{path:"",message:`No schema registered for block type: ${t}`,value:r}]}}getAll(){const t={};for(const[r,n]of this.schemas.entries())t[r]=n;return t}has(t){return this.schemas.has(t)}unregister(t){return this.schemas.delete(t)}clear(){this.schemas.clear()}getBlockTypes(){return Array.from(this.schemas.keys())}}const _e=new Va;function ld(){return new Va}function fd(){_e.register("text",rn),_e.register("image",nn),_e.register("video",an),_e.register("heading",sn),_e.register("paragraph",on),_e.register("spacer",cn),_e.register("container",dn),_e.register("grid",un)}function qa(){return["text","image","video","heading","paragraph","spacer","container","grid"]}function md(e){return qa().includes(e)}const Fa={50:"#eff6ff",100:"#dbeafe",200:"#bfdbfe",300:"#93c5fd",400:"#60a5fa",500:"#3b82f6",600:"#2563eb",700:"#1d4ed8",800:"#1e40af",900:"#1e3a8a",950:"#172554"},qr={colors:{primary:Fa,secondary:{50:"#f8fafc",100:"#f1f5f9",200:"#e2e8f0",300:"#cbd5e1",400:"#94a3b8",500:"#64748b",600:"#475569",700:"#334155",800:"#1e293b",900:"#0f172a",950:"#020617"},accent:Fa,gray:{50:"#f9fafb",100:"#f3f4f6",200:"#e5e7eb",300:"#d1d5db",400:"#9ca3af",500:"#6b7280",600:"#4b5563",700:"#374151",800:"#1f2937",900:"#111827",950:"#030712"},success:{50:"#ecfdf5",100:"#d1fae5",200:"#a7f3d0",300:"#6ee7b7",400:"#34d399",500:"#10b981",600:"#059669",700:"#047857",800:"#065f46",900:"#064e3b",950:"#022c22"},warning:{50:"#fffbeb",100:"#fef3c7",200:"#fde68a",300:"#fcd34d",400:"#fbbf24",500:"#f59e0b",600:"#d97706",700:"#b45309",800:"#92400e",900:"#78350f",950:"#451a03"},error:{50:"#fef2f2",100:"#fee2e2",200:"#fecaca",300:"#fca5a5",400:"#f87171",500:"#ef4444",600:"#dc2626",700:"#b91c1c",800:"#991b1b",900:"#7f1d1d",950:"#450a0a"},info:{50:"#f0f9ff",100:"#e0f2fe",200:"#bae6fd",300:"#7dd3fc",400:"#38bdf8",500:"#0ea5e9",600:"#0284c7",700:"#0369a1",800:"#075985",900:"#0c4a6e",950:"#082f49"},background:"#ffffff",surface:"#f8fafc",overlay:"rgba(0, 0, 0, 0.5)",text:"#0f172a",textMuted:"#64748b",textInverse:"#ffffff",border:"#e2e8f0",borderMuted:"#f1f5f9",borderStrong:"#cbd5e1"},typography:{fontFamily:{sans:'Inter, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif',serif:'Georgia, Cambria, "Times New Roman", Times, serif',mono:'Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Liberation Mono", "Courier New", monospace'},fontSize:{xs:"0.75rem",sm:"0.875rem",base:"1rem",lg:"1.125rem",xl:"1.25rem","2xl":"1.5rem","3xl":"1.875rem","4xl":"2.25rem","5xl":"3rem","6xl":"3.75rem"},fontWeight:{thin:100,light:300,normal:400,medium:500,semibold:600,bold:700,extrabold:800,black:900},lineHeight:{none:1,tight:1.25,snug:1.375,normal:1.5,relaxed:1.625,loose:2},letterSpacing:{tighter:"-0.05em",tight:"-0.025em",normal:"0em",wide:"0.025em",wider:"0.05em",widest:"0.1em"}},spacing:{0:"0px",px:"1px",.5:"0.125rem",1:"0.25rem",1.5:"0.375rem",2:"0.5rem",2.5:"0.625rem",3:"0.75rem",3.5:"0.875rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem",11:"2.75rem",12:"3rem",14:"3.5rem",16:"4rem",20:"5rem",24:"6rem",28:"7rem",32:"8rem",36:"9rem",40:"10rem",44:"11rem",48:"12rem",52:"13rem",56:"14rem",60:"15rem",64:"16rem",72:"18rem",80:"20rem",96:"24rem"},borderRadius:{none:"0px",sm:"0.125rem",base:"0.25rem",md:"0.375rem",lg:"0.5rem",xl:"0.75rem","2xl":"1rem","3xl":"1.5rem",full:"9999px"},shadows:{sm:"0 1px 2px 0 rgb(0 0 0 / 0.05)",base:"0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)",md:"0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)",lg:"0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)",xl:"0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)","2xl":"0 25px 50px -12px rgb(0 0 0 / 0.25)",inner:"inset 0 2px 4px 0 rgb(0 0 0 / 0.05)",none:"none"},breakpoints:{sm:"640px",md:"768px",lg:"1024px",xl:"1280px","2xl":"1536px"},animations:{duration:{75:"75ms",100:"100ms",150:"150ms",200:"200ms",300:"300ms",500:"500ms",700:"700ms",1e3:"1000ms"},easing:{linear:"linear",in:"cubic-bezier(0.4, 0, 1, 1)",out:"cubic-bezier(0, 0, 0.2, 1)","in-out":"cubic-bezier(0.4, 0, 0.2, 1)"}}},Ua={colors:{...qr.colors,background:"#0f172a",surface:"#1e293b",overlay:"rgba(0, 0, 0, 0.8)",text:"#f8fafc",textMuted:"#94a3b8",textInverse:"#0f172a",border:"#334155",borderMuted:"#1e293b",borderStrong:"#475569"}};function Fr(e,t){const r={...e};for(const n in t)if(Object.prototype.hasOwnProperty.call(t,n)){const a=t[n],s=r[n];a&&typeof a=="object"&&!Array.isArray(a)&&s&&typeof s=="object"&&!Array.isArray(s)?r[n]=Fr(s,a):a!==void 0&&(r[n]=a)}return r}function pd(e={}){return Fr(qr,e)}function hd(e,t){return t==="dark"?Fr(e,Ua):e}function yd(e,t="guti"){const r={};return Object.entries(e.colors).forEach(([n,a])=>{typeof a=="object"&&a!==null?Object.entries(a).forEach(([s,i])=>{r[`--${t}-color-${n}-${s}`]=i}):r[`--${t}-color-${n}`]=a}),Object.entries(e.typography.fontFamily).forEach(([n,a])=>{r[`--${t}-font-family-${n}`]=a}),Object.entries(e.typography.fontSize).forEach(([n,a])=>{r[`--${t}-font-size-${n}`]=a}),Object.entries(e.typography.fontWeight).forEach(([n,a])=>{r[`--${t}-font-weight-${n}`]=a.toString()}),Object.entries(e.typography.lineHeight).forEach(([n,a])=>{r[`--${t}-line-height-${n}`]=a.toString()}),Object.entries(e.typography.letterSpacing).forEach(([n,a])=>{r[`--${t}-letter-spacing-${n}`]=a}),Object.entries(e.spacing).forEach(([n,a])=>{r[`--${t}-spacing-${n}`]=a}),Object.entries(e.borderRadius).forEach(([n,a])=>{r[`--${t}-radius-${n}`]=a}),Object.entries(e.shadows).forEach(([n,a])=>{r[`--${t}-shadow-${n}`]=a}),Object.entries(e.breakpoints).forEach(([n,a])=>{r[`--${t}-breakpoint-${n}`]=a}),Object.entries(e.animations.duration).forEach(([n,a])=>{r[`--${t}-duration-${n}`]=a}),Object.entries(e.animations.easing).forEach(([n,a])=>{r[`--${t}-easing-${n}`]=a}),r}const gd="0.1.0";M.VERSION=gd,M.applyThemeMode=hd,M.cloneBlock=Ct,M.cloneBlocks=Ba,M.containerBlockSchema=dn,M.createSchemaRegistry=ld,M.createThemeTokens=pd,M.createValidator=dd,M.darkThemeOverrides=Ua,M.deepClone=ct,M.defaultThemeTokens=qr,M.generateBlockId=tn,M.generateCSSCustomProperties=yd,M.generateId=Rt,M.generatePageId=Ha,M.getBuiltInBlockTypes=qa,M.gridBlockSchema=un,M.headingBlockSchema=sn,M.imageBlockSchema=nn,M.isBuiltInBlockType=md,M.isValid=ud,M.isValidId=Ga,M.pageSchema=Ja,M.paragraphBlockSchema=on,M.registerBuiltInSchemas=fd,M.schemaRegistry=_e,M.spacerBlockSchema=cn,M.textBlockSchema=rn,M.themeSchema=Wa,M.validateSchema=It,M.videoBlockSchema=an,Object.defineProperty(M,Symbol.toStringTag,{value:"Module"})});
