/**
 * Register all built-in block schemas with the global registry
 */
export declare function registerBuiltInSchemas(): void;
/**
 * Get list of all built-in block types
 * @returns Array of built-in block type identifiers
 */
export declare function getBuiltInBlockTypes(): string[];
/**
 * Check if a block type is a built-in type
 * @param blockType - Block type to check
 * @returns True if built-in block type
 */
export declare function isBuiltInBlockType(blockType: string): boolean;
