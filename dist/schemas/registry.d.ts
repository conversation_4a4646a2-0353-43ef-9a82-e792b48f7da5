import { GutiSchema, ValidationResult, SchemaRegistry } from '../types/schema';

/**
 * Implementation of SchemaRegistry for managing block schemas
 */
declare class SchemaRegistryImpl implements SchemaRegistry {
    private schemas;
    /**
     * Register a schema for a block type
     * @param blockType - Block type identifier
     * @param schema - JSON Schema for the block
     */
    register(blockType: string, schema: GutiSchema): void;
    /**
     * Get schema for a block type
     * @param blockType - Block type identifier
     * @returns Schema or undefined if not found
     */
    get(blockType: string): GutiSchema | undefined;
    /**
     * Validate data against a block type's schema
     * @param blockType - Block type identifier
     * @param data - Data to validate
     * @returns Validation result
     */
    validate(blockType: string, data: any): ValidationResult;
    /**
     * Get all registered schemas
     * @returns Record of all schemas by block type
     */
    getAll(): Record<string, GutiSchema>;
    /**
     * Check if a block type is registered
     * @param blockType - Block type identifier
     * @returns True if registered
     */
    has(blockType: string): boolean;
    /**
     * Unregister a block type
     * @param blockType - Block type identifier
     * @returns True if was registered and removed
     */
    unregister(blockType: string): boolean;
    /**
     * Clear all registered schemas
     */
    clear(): void;
    /**
     * Get list of all registered block types
     * @returns Array of block type identifiers
     */
    getBlockTypes(): string[];
}
/**
 * Global schema registry instance
 */
export declare const schemaRegistry: SchemaRegistryImpl;
/**
 * Create a new schema registry instance
 * @returns New schema registry
 */
export declare function createSchemaRegistry(): SchemaRegistry;
export {};
