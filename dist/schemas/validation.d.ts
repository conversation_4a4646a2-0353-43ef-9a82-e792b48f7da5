import { GutiSchema, ValidationResult } from '../types/schema';

/**
 * Validate data against a JSON Schema
 * @param schema - JSON Schema to validate against
 * @param data - Data to validate
 * @returns Validation result
 */
export declare function validateSchema(schema: GutiSchema, data: any): ValidationResult;
/**
 * Create a validation function for a specific schema
 * @param schema - JSON Schema
 * @returns Validation function
 */
export declare function createValidator(schema: GutiSchema): (data: any) => ValidationResult;
/**
 * Check if data is valid according to schema
 * @param schema - JSON Schema
 * @param data - Data to validate
 * @returns True if valid
 */
export declare function isValid(schema: GutiSchema, data: any): boolean;
