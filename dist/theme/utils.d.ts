import { ThemeTokens, ThemeMode } from '../types/theme';

/**
 * Create a complete theme configuration from partial tokens
 * @param tokens - Partial theme tokens to override defaults
 * @returns Complete theme tokens
 */
export declare function createThemeTokens(tokens?: Partial<ThemeTokens>): ThemeTokens;
/**
 * Apply theme mode overrides to theme tokens
 * @param tokens - Base theme tokens
 * @param mode - Theme mode
 * @returns Theme tokens with mode applied
 */
export declare function applyThemeMode(tokens: ThemeTokens, mode: ThemeMode): ThemeTokens;
/**
 * Generate CSS custom properties from theme tokens
 * @param tokens - Theme tokens
 * @param prefix - CSS custom property prefix
 * @returns CSS custom properties object
 */
export declare function generateCSSCustomProperties(tokens: ThemeTokens, prefix?: string): Record<string, string>;
/**
 * Apply CSS custom properties to a DOM element
 * @param element - DOM element to apply properties to
 * @param properties - CSS custom properties
 */
export declare function applyCSSCustomProperties(element: HTMLElement, properties: Record<string, string>): void;
/**
 * Get a color value from a color scale
 * @param scale - Color scale object
 * @param shade - Shade number (50-950)
 * @returns Color value or undefined
 */
export declare function getColorFromScale(scale: Record<string, string>, shade: number | string): string | undefined;
/**
 * Validate if a color is a valid hex color
 * @param color - Color string to validate
 * @returns True if valid hex color
 */
export declare function isValidHexColor(color: string): boolean;
/**
 * Convert hex color to RGB values
 * @param hex - Hex color string
 * @returns RGB values object or null if invalid
 */
export declare function hexToRgb(hex: string): {
    r: number;
    g: number;
    b: number;
} | null;
/**
 * Convert RGB values to hex color
 * @param r - Red value (0-255)
 * @param g - Green value (0-255)
 * @param b - Blue value (0-255)
 * @returns Hex color string
 */
export declare function rgbToHex(r: number, g: number, b: number): string;
