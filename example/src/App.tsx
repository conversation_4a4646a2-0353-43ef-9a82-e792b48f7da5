import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';
import { G<PERSON><PERSON><PERSON><PERSON>, PageRenderer, createTheme, PageData } from '../src';
import type { GutiBuilderConfig } from '../src/types';

// Create a custom theme
const theme = createTheme({
  colors: {
    primary: {
      50: '#eff6ff',
      100: '#dbeafe',
      200: '#bfdbfe',
      300: '#93c5fd',
      400: '#60a5fa',
      500: '#3b82f6',
      600: '#2563eb',
      700: '#1d4ed8',
      800: '#1e40af',
      900: '#1e3a8a',
      950: '#172554',
    },
    secondary: {
      50: '#f8fafc',
      100: '#f1f5f9',
      200: '#e2e8f0',
      300: '#cbd5e1',
      400: '#94a3b8',
      500: '#64748b',
      600: '#475569',
      700: '#334155',
      800: '#1e293b',
      900: '#0f172a',
      950: '#020617',
    },
  },
  typography: {
    fontFamily: {
      sans: 'Inter, system-ui, sans-serif',
      serif: 'Georgia, serif',
      mono: 'Monaco, monospace',
    },
  },
});

// Sample page data for demonstration
const samplePageData: PageData = {
  id: 'sample-page',
  title: 'Sample Page',
  description: 'A sample page created with Guti Builder',
  blocks: [
    {
      id: 'heading-1',
      type: 'heading',
      props: {
        content: 'Welcome to Guti Builder',
        level: 1,
      },
    },
    {
      id: 'paragraph-1',
      type: 'paragraph',
      props: {
        content: 'This is a sample page built with the Guti Builder. You can edit this content by switching to edit mode.',
      },
    },
    {
      id: 'container-1',
      type: 'container',
      props: {
        direction: 'row',
        gap: '24px',
        alignItems: 'stretch',
      },
      children: [
        {
          id: 'text-1',
          type: 'text',
          props: {
            content: 'This is a text block inside a container.',
            tag: 'p',
          },
        },
        {
          id: 'text-2',
          type: 'text',
          props: {
            content: 'This is another text block in the same container.',
            tag: 'p',
          },
        },
      ],
    },
  ],
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
};

function App() {
  const [pageData, setPageData] = useState<PageData | null>(null);
  const [mode, setMode] = useState<'edit' | 'preview'>('edit');

  // Load sample data on mount
  useEffect(() => {
    const saved = localStorage.getItem('guti-example-page');
    if (saved) {
      try {
        setPageData(JSON.parse(saved));
      } catch (error) {
        console.error('Failed to load saved page:', error);
        setPageData(samplePageData);
      }
    } else {
      setPageData(samplePageData);
    }
  }, []);

  // Save data when it changes
  const handlePageChange = (data: PageData) => {
    setPageData(data);
    localStorage.setItem('guti-example-page', JSON.stringify(data));
  };

  const handleBlockAdd = (block: any) => {
    console.log('Block added:', block);
  };

  const handleBlockUpdate = (block: any) => {
    console.log('Block updated:', block);
  };

  const handleBlockDelete = (blockId: string) => {
    console.log('Block deleted:', blockId);
  };

  const builderConfig: Partial<GutiBuilderConfig> = {
    theme,
    settings: {
      dragAndDrop: true,
      inlineEditing: true,
      responsivePreview: true,
      maxNestingDepth: 5,
    },
    onPageChange: handlePageChange,
    onBlockAdd: handleBlockAdd,
    onBlockUpdate: handleBlockUpdate,
    onBlockDelete: handleBlockDelete,
  };

  return (
    <Router>
      <div className="min-h-screen bg-gray-50">
        {/* Navigation */}
        <nav className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between h-16">
              <div className="flex items-center">
                <h1 className="text-xl font-semibold text-gray-900">
                  Guti Builder Example
                </h1>
              </div>
              <div className="flex items-center space-x-4">
                <Link
                  to="/builder"
                  className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
                >
                  Builder
                </Link>
                <Link
                  to="/preview"
                  className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
                >
                  Preview
                </Link>
                <button
                  onClick={() => {
                    localStorage.removeItem('guti-example-page');
                    setPageData(samplePageData);
                  }}
                  className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-2 rounded-md text-sm font-medium"
                >
                  Reset
                </button>
              </div>
            </div>
          </div>
        </nav>

        {/* Main Content */}
        <main className="flex-1">
          <Routes>
            <Route
              path="/"
              element={
                <div className="max-w-4xl mx-auto py-8 px-4">
                  <div className="text-center">
                    <h2 className="text-3xl font-bold text-gray-900 mb-4">
                      Welcome to Guti Builder
                    </h2>
                    <p className="text-lg text-gray-600 mb-8">
                      An advanced, API-first page builder library for React applications.
                    </p>
                    <div className="space-x-4">
                      <Link
                        to="/builder"
                        className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                      >
                        Try the Builder
                      </Link>
                      <Link
                        to="/preview"
                        className="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                      >
                        View Preview
                      </Link>
                    </div>
                  </div>
                </div>
              }
            />
            <Route
              path="/builder"
              element={
                <div className="h-screen">
                  {pageData && (
                    <GutiBuilder
                      {...builderConfig}
                      pageData={pageData}
                    />
                  )}
                </div>
              }
            />
            <Route
              path="/preview"
              element={
                <div className="max-w-4xl mx-auto py-8 px-4">
                  {pageData && (
                    <PageRenderer
                      pageData={pageData}
                      theme={theme}
                      mode="preview"
                    />
                  )}
                </div>
              }
            />
          </Routes>
        </main>
      </div>
    </Router>
  );
}

export default App;
