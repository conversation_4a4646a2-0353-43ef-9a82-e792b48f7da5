{"name": "guti-builder", "version": "0.1.0", "type": "module", "description": "An advanced, API-first page builder library for React", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist", "README.md"], "scripts": {"dev": "vite", "build": "tsc && vite build", "build:lib": "vite build --mode lib", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,json,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,json,md}\"", "type-check": "tsc --noEmit", "docs:dev": "vitepress dev docs", "docs:build": "vitepress build docs", "docs:preview": "vitepress preview docs"}, "keywords": ["page-builder", "react", "typescript", "json-schema", "api-first", "component-library", "drag-and-drop", "visual-editor"], "author": "Your Name", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/yourusername/guti-builder.git"}, "bugs": {"url": "https://github.com/yourusername/guti-builder/issues"}, "homepage": "https://github.com/yourusername/guti-builder#readme", "peerDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0"}, "dependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "ajv": "^8.12.0", "ajv-formats": "^3.0.1", "clsx": "^2.0.0", "immer": "^10.0.3", "nanoid": "^5.0.4", "zustand": "^4.4.7"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "@vitest/coverage-v8": "^1.0.4", "@vitest/ui": "^1.0.4", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "jsdom": "^23.0.1", "postcss": "^8.4.32", "prettier": "^3.1.1", "tailwindcss": "^4.0.0-alpha.4", "typescript": "^5.2.2", "vite": "^5.0.8", "vite-plugin-dts": "^3.6.4", "vitepress": "^1.0.0-rc.31", "vitest": "^1.0.4"}, "engines": {"node": ">=18.0.0"}}