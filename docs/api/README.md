# API Reference

This section provides comprehensive API documentation for Guti Builder.

## Core Components

### GutiBuilder

The main builder component that provides the editing interface.

```tsx
import { GutiBuilder } from 'guti-builder';

<GutiBuilder
  theme={theme}
  pageData={pageData}
  onPageChange={handlePageChange}
  customBlocks={customBlocks}
  settings={settings}
/>
```

#### Props

| Prop | Type | Required | Description |
|------|------|----------|-------------|
| `theme` | `ThemeConfig` | Yes | Theme configuration object |
| `pageData` | `PageData \| null` | Yes | Current page data |
| `onPageChange` | `(data: PageData) => void` | Yes | Callback when page data changes |
| `customBlocks` | `Record<string, BlockDefinition>` | No | Custom block definitions |
| `settings` | `GutiBuilderSettings` | No | Builder configuration |
| `onBlockAdd` | `(block: BlockData) => void` | No | Callback when block is added |
| `onBlockUpdate` | `(block: BlockData) => void` | No | Callback when block is updated |
| `onBlockDelete` | `(blockId: string) => void` | No | Callback when block is deleted |

### PageRenderer

Renders a page in preview or edit mode.

```tsx
import { PageRenderer } from 'guti-builder';

<PageRenderer
  pageData={pageData}
  theme={theme}
  mode="preview"
/>
```

#### Props

| Prop | Type | Required | Description |
|------|------|----------|-------------|
| `pageData` | `PageData` | Yes | Page data to render |
| `theme` | `ThemeConfig` | Yes | Theme configuration |
| `mode` | `'edit' \| 'preview'` | No | Rendering mode (default: 'preview') |
| `customBlocks` | `Record<string, BlockDefinition>` | No | Custom block definitions |

### BlockRenderer

Renders individual blocks.

```tsx
import { BlockRenderer } from 'guti-builder';

<BlockRenderer
  block={blockData}
  theme={theme}
  isEditing={false}
/>
```

#### Props

| Prop | Type | Required | Description |
|------|------|----------|-------------|
| `block` | `BlockData` | Yes | Block data to render |
| `theme` | `ThemeConfig` | Yes | Theme configuration |
| `isEditing` | `boolean` | No | Whether in edit mode |
| `isSelected` | `boolean` | No | Whether block is selected |
| `onUpdate` | `(props: Record<string, any>) => void` | No | Update callback |
| `onSelect` | `() => void` | No | Selection callback |
| `onDelete` | `() => void` | No | Deletion callback |

## Theme System

### createTheme

Creates a theme configuration object.

```tsx
import { createTheme } from 'guti-builder';

const theme = createTheme({
  colors: {
    primary: { 500: '#3b82f6' },
    secondary: { 500: '#64748b' },
  },
  typography: {
    fontFamily: { sans: 'Inter, sans-serif' },
  },
});
```

#### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `config` | `Partial<ThemeTokens>` | Yes | Theme configuration |

#### Returns

`ThemeConfig` - Complete theme configuration with defaults

### ThemeProvider

React context provider for theme.

```tsx
import { ThemeProvider } from 'guti-builder';

<ThemeProvider theme={theme}>
  <App />
</ThemeProvider>
```

### useTheme

Hook to access theme context.

```tsx
import { useTheme } from 'guti-builder';

function MyComponent() {
  const { theme, mode, setMode } = useTheme();
  // ...
}
```

#### Returns

| Property | Type | Description |
|----------|------|-------------|
| `theme` | `ThemeTokens` | Current theme tokens |
| `mode` | `ThemeMode` | Current theme mode |
| `setMode` | `(mode: ThemeMode) => void` | Set theme mode |
| `toggleMode` | `() => void` | Toggle between light/dark |

## Data Types

### PageData

```typescript
interface PageData {
  id: string;
  title: string;
  description?: string;
  meta?: Record<string, any>;
  blocks: BlockData[];
  theme?: Partial<ThemeConfig>;
  settings?: PageSettings;
  createdAt: string;
  updatedAt: string;
}
```

### BlockData

```typescript
interface BlockData {
  id: string;
  type: string;
  props: Record<string, any>;
  children?: BlockData[];
  style?: BlockStyle;
  settings?: BlockSettings;
}
```

### BlockDefinition

```typescript
interface BlockDefinition {
  type: string;
  name: string;
  description?: string;
  category: 'content' | 'layout' | 'media' | 'form' | 'custom';
  icon?: ReactNode | string;
  schema: JSONSchema7;
  defaultProps: Record<string, any>;
  component: ComponentType<BlockComponentProps>;
  editor?: ComponentType<BlockEditorProps>;
  canHaveChildren?: boolean;
  allowedChildren?: string[];
  canBeNested?: boolean;
  settings?: {
    supportsStyling?: boolean;
    supportsResponsive?: boolean;
    supportsAnimations?: boolean;
  };
}
```

## Utility Functions

### validatePageData

Validates page data against schema.

```tsx
import { validatePageData } from 'guti-builder';

const result = validatePageData(pageData);
if (!result.valid) {
  console.error('Validation errors:', result.errors);
}
```

#### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `data` | `any` | Yes | Data to validate |

#### Returns

`ValidationResult` - Validation result with errors

### generateId

Generates unique IDs for blocks and pages.

```tsx
import { generateId } from 'guti-builder';

const blockId = generateId('block');
const pageId = generateId('page');
```

#### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `prefix` | `string` | No | ID prefix |

#### Returns

`string` - Unique identifier

### cloneBlock

Deep clones a block with new IDs.

```tsx
import { cloneBlock } from 'guti-builder';

const clonedBlock = cloneBlock(originalBlock);
```

#### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `block` | `BlockData` | Yes | Block to clone |

#### Returns

`BlockData` - Cloned block with new IDs

## Event System

### EventEmitter

Central event system for builder communication.

```tsx
import { useEventEmitter } from 'guti-builder';

function MyComponent() {
  const eventEmitter = useEventEmitter();
  
  useEffect(() => {
    const unsubscribe = eventEmitter.on('block:added', (event) => {
      console.log('Block added:', event.payload.block);
    });
    
    return unsubscribe;
  }, []);
}
```

#### Methods

| Method | Parameters | Description |
|--------|------------|-------------|
| `on` | `(type: string, handler: Function)` | Subscribe to events |
| `emit` | `(event: GutiBuilderEvent)` | Emit an event |
| `removeAllListeners` | `()` | Remove all listeners |

## Built-in Blocks

### Text Block

```typescript
interface TextBlockProps {
  content: string;
  tag?: 'span' | 'p' | 'div';
  className?: string;
}
```

### Image Block

```typescript
interface ImageBlockProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  objectFit?: 'cover' | 'contain' | 'fill' | 'none' | 'scale-down';
  loading?: 'lazy' | 'eager';
  className?: string;
}
```

### Video Block

```typescript
interface VideoBlockProps {
  src?: string;
  poster?: string;
  width?: number;
  height?: number;
  autoplay?: boolean;
  loop?: boolean;
  muted?: boolean;
  controls?: boolean;
  embedUrl?: string;
  embedType?: 'youtube' | 'vimeo' | 'custom';
  className?: string;
}
```

### Heading Block

```typescript
interface HeadingBlockProps {
  content: string;
  level: 1 | 2 | 3 | 4 | 5 | 6;
  className?: string;
}
```

### Container Block

```typescript
interface ContainerBlockProps {
  direction?: 'row' | 'column';
  gap?: string;
  alignItems?: 'flex-start' | 'center' | 'flex-end' | 'stretch';
  justifyContent?: 'flex-start' | 'center' | 'flex-end' | 'space-between' | 'space-around' | 'space-evenly';
  wrap?: boolean;
  className?: string;
}
```

### Grid Block

```typescript
interface GridBlockProps {
  columns: number;
  gap?: string;
  columnGap?: string;
  rowGap?: string;
  mobileColumns?: number;
  tabletColumns?: number;
  className?: string;
}
```

## Error Handling

### Error Boundaries

Guti Builder includes error boundaries to handle component errors gracefully.

```tsx
import { GutiErrorBoundary } from 'guti-builder';

<GutiErrorBoundary fallback={<ErrorFallback />}>
  <GutiBuilder {...props} />
</GutiErrorBoundary>
```

### Validation Errors

All validation errors include detailed information:

```typescript
interface ValidationError {
  path: string;
  message: string;
  value?: any;
  schema?: GutiSchema;
}
```

## Performance

### Optimization Hooks

```tsx
import { useMemoizedBlocks, useVirtualization } from 'guti-builder';

// Memoize expensive block calculations
const memoizedBlocks = useMemoizedBlocks(blocks, dependencies);

// Virtualize large lists
const virtualizedItems = useVirtualization(items, containerRef);
```

## TypeScript Support

Guti Builder is built with TypeScript and provides comprehensive type definitions:

```tsx
import type {
  PageData,
  BlockData,
  BlockDefinition,
  ThemeConfig,
  GutiBuilderConfig,
} from 'guti-builder';
```

All components and functions are fully typed for the best developer experience.
