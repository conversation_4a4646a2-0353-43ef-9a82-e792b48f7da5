# Getting Started

This guide will help you get up and running with Guti Builder in your React application.

## Installation

### NPM
```bash
npm install guti-builder
```

### Yarn
```bash
yarn add guti-builder
```

### PNPM
```bash
pnpm add guti-builder
```

## Peer Dependencies

Guti Builder requires React 19 or later:

```bash
npm install react@^19.0.0 react-dom@^19.0.0
```

## Basic Setup

### 1. Import Styles

Import the CSS file in your main application file:

```tsx
import 'guti-builder/dist/style.css';
```

### 2. Basic Usage

```tsx
import React, { useState } from 'react';
import { GutiBuilder, createTheme, PageData } from 'guti-builder';

const theme = createTheme({
  colors: {
    primary: { 500: '#3b82f6' },
    secondary: { 500: '#64748b' },
  },
});

function App() {
  const [pageData, setPageData] = useState<PageData | null>(null);

  return (
    <div className="h-screen">
      <GutiBuilder
        theme={theme}
        pageData={pageData}
        onPageChange={setPageData}
      />
    </div>
  );
}

export default App;
```

## Configuration Options

### GutiBuilder Props

```tsx
interface GutiBuilderProps {
  // Required
  theme: ThemeConfig;
  pageData: PageData | null;
  onPageChange: (data: PageData) => void;
  
  // Optional
  customBlocks?: Record<string, BlockDefinition>;
  settings?: {
    dragAndDrop?: boolean;
    inlineEditing?: boolean;
    responsivePreview?: boolean;
    maxNestingDepth?: number;
  };
  
  // Event handlers
  onBlockAdd?: (block: BlockData) => void;
  onBlockUpdate?: (block: BlockData) => void;
  onBlockDelete?: (blockId: string) => void;
}
```

### Example with Custom Configuration

```tsx
import { GutiBuilder, createTheme } from 'guti-builder';

const customTheme = createTheme({
  colors: {
    primary: {
      50: '#eff6ff',
      500: '#3b82f6',
      900: '#1e3a8a',
    },
    secondary: {
      50: '#f8fafc',
      500: '#64748b',
      900: '#0f172a',
    },
  },
  typography: {
    fontFamily: {
      sans: 'Inter, system-ui, sans-serif',
    },
  },
});

function App() {
  const [pageData, setPageData] = useState(null);

  const handlePageChange = (data) => {
    setPageData(data);
    // Save to your backend or localStorage
    localStorage.setItem('pageData', JSON.stringify(data));
  };

  const handleBlockAdd = (block) => {
    console.log('Block added:', block);
    // Analytics or logging
  };

  return (
    <GutiBuilder
      theme={customTheme}
      pageData={pageData}
      onPageChange={handlePageChange}
      onBlockAdd={handleBlockAdd}
      settings={{
        dragAndDrop: true,
        inlineEditing: true,
        responsivePreview: true,
        maxNestingDepth: 5,
      }}
    />
  );
}
```

## Rendering Pages

To render a page created with Guti Builder, use the `PageRenderer` component:

```tsx
import { PageRenderer, createTheme } from 'guti-builder';

const theme = createTheme(/* your theme config */);

function PublicPage({ pageData }) {
  return (
    <PageRenderer
      pageData={pageData}
      theme={theme}
      mode="preview" // or "edit"
    />
  );
}
```

## Data Persistence

### Local Storage Example

```tsx
import { useState, useEffect } from 'react';
import { GutiBuilder, PageData } from 'guti-builder';

function App() {
  const [pageData, setPageData] = useState<PageData | null>(null);

  // Load data on mount
  useEffect(() => {
    const saved = localStorage.getItem('guti-page-data');
    if (saved) {
      try {
        setPageData(JSON.parse(saved));
      } catch (error) {
        console.error('Failed to load page data:', error);
      }
    }
  }, []);

  // Save data when it changes
  const handlePageChange = (data: PageData) => {
    setPageData(data);
    localStorage.setItem('guti-page-data', JSON.stringify(data));
  };

  return (
    <GutiBuilder
      theme={theme}
      pageData={pageData}
      onPageChange={handlePageChange}
    />
  );
}
```

### API Integration Example

```tsx
import { useState, useEffect } from 'react';
import { GutiBuilder, PageData } from 'guti-builder';

function App() {
  const [pageData, setPageData] = useState<PageData | null>(null);
  const [loading, setLoading] = useState(true);

  // Load page data from API
  useEffect(() => {
    async function loadPage() {
      try {
        const response = await fetch('/api/pages/homepage');
        const data = await response.json();
        setPageData(data);
      } catch (error) {
        console.error('Failed to load page:', error);
      } finally {
        setLoading(false);
      }
    }
    
    loadPage();
  }, []);

  // Save page data to API
  const handlePageChange = async (data: PageData) => {
    setPageData(data);
    
    try {
      await fetch('/api/pages/homepage', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });
    } catch (error) {
      console.error('Failed to save page:', error);
    }
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <GutiBuilder
      theme={theme}
      pageData={pageData}
      onPageChange={handlePageChange}
    />
  );
}
```

## Styling and Theming

### Custom Theme

```tsx
import { createTheme } from 'guti-builder';

const customTheme = createTheme({
  colors: {
    primary: {
      50: '#fef2f2',
      100: '#fee2e2',
      200: '#fecaca',
      300: '#fca5a5',
      400: '#f87171',
      500: '#ef4444', // Base color
      600: '#dc2626',
      700: '#b91c1c',
      800: '#991b1b',
      900: '#7f1d1d',
      950: '#450a0a',
    },
    // ... other colors
  },
  typography: {
    fontFamily: {
      sans: 'Roboto, system-ui, sans-serif',
      serif: 'Georgia, serif',
      mono: 'Monaco, monospace',
    },
    fontSize: {
      xs: '0.75rem',
      sm: '0.875rem',
      base: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
      '2xl': '1.5rem',
      // ... more sizes
    },
  },
  spacing: {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem',
    // ... more spacing
  },
  borderRadius: {
    none: '0',
    sm: '0.125rem',
    base: '0.25rem',
    lg: '0.5rem',
    full: '9999px',
  },
  shadows: {
    sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
    base: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
    lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
  },
  breakpoints: {
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px',
  },
});
```

### CSS Custom Properties

Guti Builder automatically generates CSS custom properties from your theme:

```css
:root {
  --guti-color-primary-500: #ef4444;
  --guti-color-secondary-500: #64748b;
  --guti-spacing-md: 1rem;
  --guti-radius-base: 0.25rem;
  /* ... more properties */
}
```

You can use these in your custom CSS:

```css
.my-custom-block {
  background-color: var(--guti-color-primary-500);
  padding: var(--guti-spacing-md);
  border-radius: var(--guti-radius-base);
}
```

## Next Steps

- [Learn about the data model](./data-model.md)
- [Explore built-in blocks](./built-in-blocks.md)
- [Create custom blocks](./custom-blocks.md)
- [Customize the theme system](./theme-system.md)
- [Check out examples](../examples/README.md)

## Common Issues

### TypeScript Errors

Make sure you have the correct TypeScript configuration:

```json
{
  "compilerOptions": {
    "jsx": "react-jsx",
    "moduleResolution": "bundler",
    "allowSyntheticDefaultImports": true
  }
}
```

### Styling Issues

Ensure you've imported the CSS file and that Tailwind CSS is properly configured if you're using custom styles.

### Performance Issues

For large pages, consider:
- Using the `PageRenderer` in preview mode for public pages
- Implementing virtualization for long lists of blocks
- Optimizing images and media content

## Support

- [GitHub Issues](https://github.com/yourusername/guti-builder/issues)
- [Documentation](../README.md)
- [Examples](../examples/README.md)
