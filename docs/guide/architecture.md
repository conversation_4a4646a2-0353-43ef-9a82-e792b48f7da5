# Architecture Overview

Guti Builder is designed with a modular, API-first architecture that prioritizes flexibility, maintainability, and extensibility. This document outlines the core architectural decisions and design patterns used throughout the library.

## Core Principles

### 1. API-First Design
- **JSON-based data model**: All page and block data is represented as JSON, making it easy to store, transfer, and manipulate
- **Schema-driven validation**: JSON Schema ensures data integrity and enables automatic form generation
- **Framework agnostic core**: The data model and validation logic can be used with any frontend framework

### 2. Component-Based Architecture
- **Block system**: Everything is a block - from simple text to complex layouts
- **Composition over inheritance**: Blocks are composed together to create complex layouts
- **Separation of concerns**: Data, presentation, and behavior are clearly separated

### 3. Extensibility by Design
- **Plugin architecture**: Custom blocks can be easily added without modifying core code
- **Theme system**: Comprehensive design token system for consistent styling
- **Event system**: Decoupled communication between components

## System Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Guti Builder Core                        │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │   Builder   │  │   Renderer  │  │    Block Registry   │  │
│  │  Component  │  │  Component  │  │                     │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │    State    │  │   Events    │  │       Theme         │  │
│  │ Management  │  │   System    │  │      System         │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │   Schema    │  │    Utils    │  │    Built-in         │  │
│  │ Validation  │  │             │  │     Blocks          │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

## Data Flow

### 1. Page Data Structure
```typescript
PageData {
  id: string
  title: string
  blocks: BlockData[]
  theme?: ThemeConfig
  settings?: PageSettings
}
```

### 2. Block Data Structure
```typescript
BlockData {
  id: string
  type: string
  props: Record<string, any>
  children?: BlockData[]
  style?: BlockStyle
  settings?: BlockSettings
}
```

### 3. Data Flow Pattern
1. **Input**: User interactions (drag, drop, edit)
2. **Validation**: JSON Schema validation
3. **State Update**: Immutable state updates using Immer
4. **Re-render**: React re-renders affected components
5. **Output**: Updated page data emitted via events

## Core Components

### GutiBuilder
The main builder component that provides the editing interface.

**Responsibilities:**
- Manages page state
- Handles user interactions
- Provides editing tools and panels
- Coordinates between sub-components

### BlockRenderer
Renders blocks in both edit and preview modes.

**Responsibilities:**
- Renders individual blocks
- Handles block selection and highlighting
- Manages drag and drop interactions
- Applies theme and styling

### BlockRegistry
Manages available block types and their definitions.

**Responsibilities:**
- Registers built-in and custom blocks
- Provides block metadata and schemas
- Validates block configurations
- Enables dynamic block loading

## State Management

### Zustand Store
We use Zustand for lightweight, flexible state management:

```typescript
interface BuilderState {
  // Page data
  pageData: PageData | null;
  
  // Editor state
  selectedBlockId: string | null;
  editMode: 'edit' | 'preview';
  
  // UI state
  showGrid: boolean;
  showOutlines: boolean;
  
  // Actions
  updatePage: (data: Partial<PageData>) => void;
  addBlock: (block: BlockData, parentId?: string) => void;
  updateBlock: (id: string, updates: Partial<BlockData>) => void;
  deleteBlock: (id: string) => void;
}
```

### Immutable Updates
All state updates use Immer for immutable updates:

```typescript
const updateBlock = (id: string, updates: Partial<BlockData>) =>
  set(produce((state) => {
    const block = findBlockById(state.pageData.blocks, id);
    if (block) {
      Object.assign(block, updates);
    }
  }));
```

## Event System

### Event-Driven Architecture
Components communicate through a centralized event system:

```typescript
// Event types
type GutiBuilderEvent = 
  | BlockAddedEvent
  | BlockUpdatedEvent
  | BlockDeletedEvent
  | PageUpdatedEvent;

// Event emitter
interface EventEmitter {
  on<T extends GutiBuilderEvent>(type: T['type'], handler: (event: T) => void): void;
  emit<T extends GutiBuilderEvent>(event: T): void;
}
```

### Benefits
- **Decoupling**: Components don't need direct references to each other
- **Extensibility**: Plugins can listen to events without modifying core code
- **Debugging**: All state changes are traceable through events

## Theme System

### Design Token Architecture
The theme system is built on design tokens:

```typescript
interface ThemeTokens {
  colors: ColorTokens;
  typography: TypographyTokens;
  spacing: SpacingTokens;
  borderRadius: BorderRadiusTokens;
  shadows: ShadowTokens;
  breakpoints: BreakpointTokens;
}
```

### CSS Custom Properties
Themes are applied using CSS custom properties:

```css
:root {
  --guti-color-primary-500: #3b82f6;
  --guti-color-secondary-500: #64748b;
  --guti-spacing-md: 16px;
  --guti-radius-base: 8px;
}
```

## Validation System

### JSON Schema Integration
All data is validated using JSON Schema:

```typescript
interface SchemaRegistry {
  register(blockType: string, schema: JSONSchema7): void;
  validate(blockType: string, data: any): ValidationResult;
}
```

### Benefits
- **Type Safety**: Runtime validation ensures data integrity
- **Form Generation**: Schemas automatically generate editing forms
- **Documentation**: Schemas serve as living documentation

## Performance Considerations

### Optimization Strategies
1. **Memoization**: React.memo and useMemo for expensive computations
2. **Virtualization**: Large lists of blocks are virtualized
3. **Lazy Loading**: Block components are loaded on demand
4. **Debouncing**: User input is debounced to prevent excessive updates

### Bundle Splitting
```typescript
// Dynamic imports for block components
const TextBlock = lazy(() => import('./blocks/TextBlock'));
const ImageBlock = lazy(() => import('./blocks/ImageBlock'));
```

## Testing Strategy

### Test Pyramid
1. **Unit Tests**: Individual functions and components
2. **Integration Tests**: Component interactions
3. **E2E Tests**: Full user workflows

### Testing Tools
- **Vitest**: Fast unit testing
- **React Testing Library**: Component testing
- **Playwright**: End-to-end testing

## Security Considerations

### Input Sanitization
- All user input is sanitized before rendering
- HTML content is processed through DOMPurify
- File uploads are validated and scanned

### Schema Validation
- All data is validated against JSON schemas
- Invalid data is rejected at the API boundary
- Type safety prevents many runtime errors

## Deployment Architecture

### Library Distribution
```
guti-builder/
├── dist/
│   ├── index.js          # UMD bundle
│   ├── index.esm.js      # ES modules
│   ├── index.d.ts        # TypeScript definitions
│   └── style.css         # Compiled styles
```

### CDN Support
The library can be loaded from CDN for quick prototyping:

```html
<script src="https://unpkg.com/guti-builder@latest/dist/index.js"></script>
<link rel="stylesheet" href="https://unpkg.com/guti-builder@latest/dist/style.css">
```

## Future Considerations

### Planned Enhancements
1. **Server-Side Rendering**: Full SSR support
2. **Real-time Collaboration**: Multi-user editing
3. **Version Control**: Built-in versioning system
4. **AI Integration**: AI-powered content suggestions
5. **Mobile Editor**: Touch-optimized editing interface

### Scalability
The architecture is designed to scale:
- **Micro-frontends**: Core can be split into smaller packages
- **Plugin ecosystem**: Third-party blocks and extensions
- **Cloud integration**: Built-in cloud storage adapters
