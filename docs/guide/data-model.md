# Data Model Specification

Guti Builder uses a JSON-based data model that is both human-readable and machine-processable. This document provides a comprehensive specification of the data structures used throughout the system.

## Overview

The data model is built around two primary concepts:
- **Pages**: Top-level containers that hold blocks and metadata
- **Blocks**: Individual components that make up the page content

All data structures are validated using JSON Schema and include full TypeScript definitions.

## Page Data Structure

### PageData Interface

```typescript
interface PageData {
  id: string;                    // Unique identifier (nanoid)
  title: string;                 // Human-readable page title
  description?: string;          // Optional page description
  meta?: Record<string, any>;    // Custom metadata
  blocks: BlockData[];           // Root-level blocks
  theme?: Partial<ThemeConfig>;  // Page-specific theme overrides
  settings?: PageSettings;       // Page configuration
  createdAt: string;            // ISO 8601 timestamp
  updatedAt: string;            // ISO 8601 timestamp
}
```

### Example Page Data

```json
{
  "id": "page_abc123",
  "title": "Homepage",
  "description": "Main landing page for the website",
  "meta": {
    "seoTitle": "Welcome to Our Site",
    "seoDescription": "Discover amazing content...",
    "ogImage": "https://example.com/og-image.jpg"
  },
  "blocks": [
    {
      "id": "block_def456",
      "type": "heading",
      "props": {
        "content": "Welcome to Our Site",
        "level": 1
      }
    },
    {
      "id": "block_ghi789",
      "type": "paragraph",
      "props": {
        "content": "This is a sample paragraph with <strong>bold text</strong>."
      }
    }
  ],
  "theme": {
    "colors": {
      "primary": "#3b82f6"
    }
  },
  "settings": {
    "layout": "contained",
    "background": "#ffffff"
  },
  "createdAt": "2024-01-15T10:30:00Z",
  "updatedAt": "2024-01-15T14:45:00Z"
}
```

### Page Settings

```typescript
interface PageSettings {
  layout?: 'full-width' | 'contained' | 'centered';
  background?: string;           // Color, gradient, or image URL
  className?: string;            // Custom CSS classes
  maxWidth?: string;            // Maximum page width
  padding?: SpacingConfig;      // Page padding
}
```

## Block Data Structure

### BlockData Interface

```typescript
interface BlockData {
  id: string;                    // Unique identifier (nanoid)
  type: string;                  // Block type identifier
  props: Record<string, any>;    // Block-specific properties
  children?: BlockData[];        // Child blocks (for containers)
  style?: BlockStyle;           // Visual styling
  settings?: BlockSettings;     // Block configuration
}
```

### Block Properties by Type

#### Text Block
```typescript
interface TextBlockProps {
  content: string;               // HTML content
  tag?: 'span' | 'p' | 'div';   // HTML tag to render
  className?: string;            // Custom CSS classes
}
```

#### Image Block
```typescript
interface ImageBlockProps {
  src: string;                   // Image URL
  alt: string;                   // Alternative text
  width?: number;                // Image width in pixels
  height?: number;               // Image height in pixels
  objectFit?: 'cover' | 'contain' | 'fill' | 'none' | 'scale-down';
  loading?: 'lazy' | 'eager';    // Loading strategy
  className?: string;
}
```

#### Video Block
```typescript
interface VideoBlockProps {
  src?: string;                  // Video file URL
  poster?: string;               // Poster image URL
  width?: number;
  height?: number;
  autoplay?: boolean;
  loop?: boolean;
  muted?: boolean;
  controls?: boolean;
  embedUrl?: string;             // YouTube/Vimeo URL
  embedType?: 'youtube' | 'vimeo' | 'custom';
  className?: string;
}
```

#### Heading Block
```typescript
interface HeadingBlockProps {
  content: string;               // Heading text
  level: 1 | 2 | 3 | 4 | 5 | 6; // Heading level (h1-h6)
  className?: string;
}
```

#### Container Block
```typescript
interface ContainerBlockProps {
  direction?: 'row' | 'column';  // Flex direction
  gap?: string;                  // Gap between items
  alignItems?: 'flex-start' | 'center' | 'flex-end' | 'stretch';
  justifyContent?: 'flex-start' | 'center' | 'flex-end' | 'space-between' | 'space-around' | 'space-evenly';
  wrap?: boolean;                // Allow wrapping
  className?: string;
}
```

#### Grid Block
```typescript
interface GridBlockProps {
  columns: number;               // Number of columns
  gap?: string;                  // Grid gap
  columnGap?: string;            // Column-specific gap
  rowGap?: string;               // Row-specific gap
  mobileColumns?: number;        // Columns on mobile
  tabletColumns?: number;        // Columns on tablet
  className?: string;
}
```

### Block Styling

```typescript
interface BlockStyle {
  // Spacing
  margin?: SpacingConfig;
  padding?: SpacingConfig;
  
  // Dimensions
  width?: string;
  height?: string;
  minWidth?: string;
  minHeight?: string;
  maxWidth?: string;
  maxHeight?: string;
  
  // Background
  backgroundColor?: string;
  backgroundImage?: string;
  backgroundSize?: string;
  backgroundPosition?: string;
  backgroundRepeat?: string;
  
  // Border
  border?: string;
  borderRadius?: string;
  
  // Typography
  fontSize?: string;
  fontWeight?: string;
  fontFamily?: string;
  color?: string;
  textAlign?: 'left' | 'center' | 'right' | 'justify';
  lineHeight?: string;
  
  // Effects
  boxShadow?: string;
  opacity?: number;
  transform?: string;
  
  // Custom properties
  custom?: Record<string, string>;
}

interface SpacingConfig {
  top?: string;
  right?: string;
  bottom?: string;
  left?: string;
}
```

### Block Settings

```typescript
interface BlockSettings {
  className?: string;            // Custom CSS classes
  responsive?: {                 // Responsive visibility
    mobile?: boolean;
    tablet?: boolean;
    desktop?: boolean;
  };
  animation?: {                  // Animation settings
    type?: string;
    duration?: number;
    delay?: number;
  };
  accessibility?: {              // Accessibility settings
    ariaLabel?: string;
    ariaDescribedBy?: string;
    role?: string;
  };
}
```

## Theme Configuration

### ThemeConfig Interface

```typescript
interface ThemeConfig {
  colors: ColorTokens;
  typography: TypographyTokens;
  spacing: SpacingTokens;
  borderRadius: BorderRadiusTokens;
  shadows: ShadowTokens;
  breakpoints: BreakpointTokens;
}
```

### Color Tokens

```typescript
interface ColorTokens {
  primary: ColorScale;
  secondary: ColorScale;
  accent: ColorScale;
  gray: ColorScale;
  success: ColorScale;
  warning: ColorScale;
  error: ColorScale;
  info: ColorScale;
  background: string;
  surface: string;
  text: string;
  textMuted: string;
  border: string;
}

interface ColorScale {
  50: string;
  100: string;
  200: string;
  300: string;
  400: string;
  500: string;  // Base color
  600: string;
  700: string;
  800: string;
  900: string;
  950: string;
}
```

## JSON Schema Definitions

### Page Schema

```json
{
  "type": "object",
  "required": ["id", "title", "blocks", "createdAt", "updatedAt"],
  "properties": {
    "id": {
      "type": "string",
      "pattern": "^[a-zA-Z0-9_-]+$"
    },
    "title": {
      "type": "string",
      "minLength": 1,
      "maxLength": 200
    },
    "description": {
      "type": "string",
      "maxLength": 500
    },
    "blocks": {
      "type": "array",
      "items": { "$ref": "#/definitions/blockData" }
    },
    "createdAt": {
      "type": "string",
      "format": "date-time"
    },
    "updatedAt": {
      "type": "string",
      "format": "date-time"
    }
  }
}
```

### Block Schema

```json
{
  "type": "object",
  "required": ["id", "type", "props"],
  "properties": {
    "id": {
      "type": "string",
      "pattern": "^[a-zA-Z0-9_-]+$"
    },
    "type": {
      "type": "string",
      "minLength": 1
    },
    "props": {
      "type": "object"
    },
    "children": {
      "type": "array",
      "items": { "$ref": "#/definitions/blockData" }
    },
    "style": {
      "type": "object",
      "additionalProperties": { "type": "string" }
    }
  }
}
```

## Data Validation

### Validation Rules

1. **IDs must be unique** within a page
2. **Block types must be registered** in the block registry
3. **Props must match** the block's JSON schema
4. **Circular references** are not allowed in children
5. **Maximum nesting depth** is configurable (default: 10)

### Validation Example

```typescript
import { validatePageData } from 'guti-builder';

const result = validatePageData(pageData);

if (!result.valid) {
  console.error('Validation errors:', result.errors);
  // Handle validation errors
}
```

## Data Migrations

### Version Compatibility

The data model includes version information for handling migrations:

```typescript
interface PageData {
  version?: string;  // Data model version
  // ... other properties
}
```

### Migration Strategy

```typescript
interface Migration {
  from: string;      // Source version
  to: string;        // Target version
  migrate: (data: any) => any;  // Migration function
}

const migrations: Migration[] = [
  {
    from: '1.0.0',
    to: '1.1.0',
    migrate: (data) => {
      // Transform data from v1.0.0 to v1.1.0
      return transformedData;
    }
  }
];
```

## Best Practices

### Data Structure Guidelines

1. **Keep props flat** - Avoid deeply nested objects in block props
2. **Use semantic naming** - Property names should be descriptive
3. **Provide defaults** - All optional properties should have sensible defaults
4. **Validate early** - Validate data at the API boundary
5. **Immutable updates** - Always create new objects when updating data

### Performance Considerations

1. **Minimize data size** - Remove unnecessary properties
2. **Use efficient IDs** - Short, unique identifiers (nanoid)
3. **Lazy load content** - Load images and videos on demand
4. **Cache validation** - Cache schema validation results

### Security Guidelines

1. **Sanitize HTML** - Clean all HTML content before rendering
2. **Validate URLs** - Ensure all URLs are safe and accessible
3. **Limit file sizes** - Restrict upload sizes for images and videos
4. **Escape user input** - Prevent XSS attacks through proper escaping
