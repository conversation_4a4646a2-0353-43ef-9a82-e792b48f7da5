{"version": 3, "file": "index.js", "sources": ["../../src/icons/ControlKeyIcon.tsx", "../../src/icons/SearchIcon.tsx", "../../src/DocSearchButton.tsx", "../../../../node_modules/@algolia/autocomplete-shared/dist/esm/debounce.js", "../../../../node_modules/@algolia/autocomplete-shared/dist/esm/flatten.js", "../../../../node_modules/@algolia/autocomplete-shared/dist/esm/generateAutocompleteId.js", "../../../../node_modules/@algolia/autocomplete-shared/dist/esm/getItemsCount.js", "../../../../node_modules/@algolia/autocomplete-shared/dist/esm/isEqual.js", "../../../../node_modules/@algolia/autocomplete-shared/dist/esm/noop.js", "../../../../node_modules/@algolia/autocomplete-shared/dist/esm/version.js", "../../../../node_modules/@algolia/autocomplete-shared/dist/esm/userAgents.js", "../../../../node_modules/@algolia/autocomplete-plugin-algolia-insights/dist/esm/createClickedEvent.js", "../../../../node_modules/@algolia/autocomplete-plugin-algolia-insights/dist/esm/isModernInsightsClient.js", "../../../../node_modules/@algolia/autocomplete-plugin-algolia-insights/dist/esm/createSearchInsightsApi.js", "../../../../node_modules/@algolia/autocomplete-plugin-algolia-insights/dist/esm/createViewedEvents.js", "../../../../node_modules/@algolia/autocomplete-plugin-algolia-insights/dist/esm/isAlgoliaInsightsHit.js", "../../../../node_modules/@algolia/autocomplete-plugin-algolia-insights/dist/esm/createAlgoliaInsightsPlugin.js", "../../../../node_modules/@algolia/autocomplete-shared/dist/esm/safelyRunOnBrowser.js", "../../../../node_modules/@algolia/autocomplete-shared/dist/esm/createRef.js", "../../../../node_modules/@algolia/autocomplete-core/dist/esm/utils/createCancelablePromise.js", "../../../../node_modules/@algolia/autocomplete-core/dist/esm/utils/getNextActiveItemId.js", "../../../../node_modules/@algolia/autocomplete-core/dist/esm/utils/getNormalizedSources.js", "../../../../node_modules/@algolia/autocomplete-core/dist/esm/utils/getActiveItem.js", "../../../../node_modules/@algolia/autocomplete-core/dist/esm/utils/getAutocompleteElementId.js", "../../../../node_modules/@algolia/autocomplete-core/dist/esm/utils/isSamsung.js", "../../../../node_modules/@algolia/autocomplete-core/dist/esm/utils/getNativeEvent.js", "../../../../node_modules/@algolia/autocomplete-core/dist/esm/createStore.js", "../../../../node_modules/@algolia/autocomplete-core/dist/esm/utils/createCancelablePromiseList.js", "../../../../node_modules/@algolia/autocomplete-core/dist/esm/getAutocompleteSetters.js", "../../../../node_modules/@algolia/autocomplete-core/dist/esm/getDefaultProps.js", "../../../../node_modules/@algolia/autocomplete-core/dist/esm/reshape.js", "../../../../node_modules/@algolia/autocomplete-core/dist/esm/resolve.js", "../../../../node_modules/@algolia/autocomplete-core/dist/esm/utils/mapToAlgoliaResponse.js", "../../../../node_modules/@algolia/autocomplete-core/dist/esm/onInput.js", "../../../../node_modules/@algolia/autocomplete-core/dist/esm/utils/createConcurrentSafePromise.js", "../../../../node_modules/@algolia/autocomplete-core/dist/esm/onKeyDown.js", "../../../../node_modules/@algolia/autocomplete-core/dist/esm/getPropGetters.js", "../../../../node_modules/@algolia/autocomplete-core/dist/esm/utils/isOrContainsNode.js", "../../../../node_modules/@algolia/autocomplete-core/dist/esm/metadata.js", "../../../../node_modules/@algolia/autocomplete-core/dist/esm/getCompletion.js", "../../../../node_modules/@algolia/autocomplete-core/dist/esm/stateReducer.js", "../../../../node_modules/@algolia/autocomplete-core/dist/esm/createAutocomplete.js", "../../src/AlgoliaLogo.tsx", "../../src/Footer.tsx", "../../src/Hit.tsx", "../../src/icons/LoadingIcon.tsx", "../../src/icons/RecentIcon.tsx", "../../src/icons/ResetIcon.tsx", "../../src/icons/SelectIcon.tsx", "../../src/icons/SourceIcon.tsx", "../../src/icons/StarIcon.tsx", "../../src/icons/ErrorIcon.tsx", "../../src/icons/NoResultsIcon.tsx", "../../src/ErrorScreen.tsx", "../../src/NoResultsScreen.tsx", "../../src/Snippet.tsx", "../../src/Results.tsx", "../../src/utils/groupBy.ts", "../../src/utils/identity.ts", "../../src/utils/isModifierEvent.ts", "../../src/utils/noop.ts", "../../src/utils/removeHighlightTags.ts", "../../src/ResultsScreen.tsx", "../../src/StartScreen.tsx", "../../src/ScreenState.tsx", "../../src/SearchBox.tsx", "../../src/constants.ts", "../../src/stored-searches.ts", "../../../../node_modules/@algolia/client-common/dist/common.js", "../../src/version.ts", "../../src/useSearchClient.ts", "../../../../node_modules/algoliasearch/dist/lite/builds/browser.js", "../../../../node_modules/@algolia/requester-browser-xhr/dist/requester.xhr.js", "../../src/DocSearchModal.tsx", "../../src/useTouchEvents.ts", "../../src/useTrapFocus.ts", "../../src/useDocSearchKeyboardEvents.ts", "../../src/DocSearch.tsx"], "sourcesContent": ["import React from 'react';\n\nexport function ControlKeyIcon(): JSX.Element {\n  return (\n    <svg width=\"15\" height=\"15\" className=\"DocSearch-Control-Key-Icon\">\n      <path\n        d=\"M4.505 4.496h2M5.505 5.496v5M8.216 4.496l.055 5.993M10 7.5c.333.333.5.667.5 1v2M12.326 4.5v5.996M8.384 4.496c1.674 0 2.116 0 2.116 1.5s-.442 1.5-2.116 1.5M3.205 9.303c-.09.448-.277 1.21-1.241 1.203C1 10.5.5 9.513.5 8V7c0-1.57.5-2.5 1.464-2.494.964.006 1.134.598 1.24 1.342M12.553 10.5h1.953\"\n        strokeWidth=\"1.2\"\n        stroke=\"currentColor\"\n        fill=\"none\"\n        strokeLinecap=\"square\"\n      />\n    </svg>\n  );\n}\n", "import React from 'react';\n\nexport function SearchIcon(): JSX.Element {\n  return (\n    <svg width=\"20\" height=\"20\" className=\"DocSearch-Search-Icon\" viewBox=\"0 0 20 20\" aria-hidden=\"true\">\n      <path\n        d=\"M14.386 14.386l4.0877 4.0877-4.0877-4.0877c-2.9418 2.9419-7.7115 2.9419-10.6533 0-2.9419-2.9418-2.9419-7.7115 0-10.6533 2.9418-2.9419 7.7115-2.9419 10.6533 0 2.9419 2.9418 2.9419 7.7115 0 10.6533z\"\n        stroke=\"currentColor\"\n        fill=\"none\"\n        fillRule=\"evenodd\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n      />\n    </svg>\n  );\n}\n", "import React, { useEffect, useState } from 'react';\n\nimport { ControlKeyIcon } from './icons/ControlKeyIcon';\nimport { SearchIcon } from './icons/SearchIcon';\n\nexport type ButtonTranslations = Partial<{\n  buttonText: string;\n  buttonAriaLabel: string;\n}>;\n\nexport type DocSearchButtonProps = React.ComponentProps<'button'> & {\n  translations?: ButtonTranslations;\n};\n\nconst ACTION_KEY_DEFAULT = 'Ctrl' as const;\nconst ACTION_KEY_APPLE = '⌘' as const;\n\nfunction isAppleDevice(): boolean {\n  return /(Mac|iPhone|iPod|iPad)/i.test(navigator.platform);\n}\n\nexport const DocSearchButton = React.forwardRef<HTMLButtonElement, DocSearchButtonProps>(\n  ({ translations = {}, ...props }, ref) => {\n    const { buttonText = 'Search', buttonAriaLabel = 'Search' } = translations;\n\n    const [key, setKey] = useState<typeof ACTION_KEY_APPLE | typeof ACTION_KEY_DEFAULT | null>(null);\n\n    useEffect(() => {\n      if (typeof navigator !== 'undefined') {\n        isAppleDevice() ? setKey(ACTION_KEY_APPLE) : setKey(ACTION_KEY_DEFAULT);\n      }\n    }, []);\n\n    const [actionKeyReactsTo, actionKeyAltText, actionKeyChild] =\n      key === ACTION_KEY_DEFAULT\n        ? // eslint-disable-next-line react/jsx-key -- false flag\n          ([ACTION_KEY_DEFAULT, 'Ctrl', <ControlKeyIcon />] as const)\n        : (['Meta', 'Command', key] as const);\n\n    return (\n      <button\n        type=\"button\"\n        className=\"DocSearch DocSearch-Button\"\n        aria-label={`${buttonAriaLabel} (${actionKeyAltText}+K)`}\n        {...props}\n        ref={ref}\n      >\n        <span className=\"DocSearch-Button-Container\">\n          <SearchIcon />\n          <span className=\"DocSearch-Button-Placeholder\">{buttonText}</span>\n        </span>\n\n        <span className=\"DocSearch-Button-Keys\">\n          {key !== null && (\n            <>\n              <DocSearchButtonKey reactsToKey={actionKeyReactsTo}>{actionKeyChild}</DocSearchButtonKey>\n              <DocSearchButtonKey reactsToKey=\"k\">K</DocSearchButtonKey>\n            </>\n          )}\n        </span>\n      </button>\n    );\n  },\n);\n\ntype DocSearchButtonKeyProps = {\n  reactsToKey?: string;\n};\n\nfunction DocSearchButtonKey({ reactsToKey, children }: React.PropsWithChildren<DocSearchButtonKeyProps>): JSX.Element {\n  const [isKeyDown, setIsKeyDown] = useState(false);\n\n  useEffect(() => {\n    if (!reactsToKey) {\n      return undefined;\n    }\n\n    function handleKeyDown(e: KeyboardEvent): void {\n      if (e.key === reactsToKey) {\n        setIsKeyDown(true);\n      }\n    }\n\n    function handleKeyUp(e: KeyboardEvent): void {\n      if (\n        e.key === reactsToKey ||\n        // keyup doesn't fire when Command is held down,\n        // workaround is to mark key as also released when Command is released\n        // See https://stackoverflow.com/a/73419500\n        e.key === 'Meta'\n      ) {\n        setIsKeyDown(false);\n      }\n    }\n\n    window.addEventListener('keydown', handleKeyDown);\n    window.addEventListener('keyup', handleKeyUp);\n\n    return (): void => {\n      window.removeEventListener('keydown', handleKeyDown);\n      window.removeEventListener('keyup', handleKeyUp);\n    };\n  }, [reactsToKey]);\n\n  return (\n    <kbd className={isKeyDown ? 'DocSearch-Button-Key DocSearch-Button-Key--pressed' : 'DocSearch-Button-Key'}>\n      {children}\n    </kbd>\n  );\n}\n", "export function debounce(fn, time) {\n  var timerId = undefined;\n  return function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    if (timerId) {\n      clearTimeout(timerId);\n    }\n    timerId = setTimeout(function () {\n      return fn.apply(void 0, args);\n    }, time);\n  };\n}", "export function flatten(values) {\n  return values.reduce(function (a, b) {\n    return a.concat(b);\n  }, []);\n}", "var autocompleteId = 0;\nexport function generateAutocompleteId() {\n  return \"autocomplete-\".concat(autocompleteId++);\n}", "export function getItemsCount(state) {\n  if (state.collections.length === 0) {\n    return 0;\n  }\n  return state.collections.reduce(function (sum, collection) {\n    return sum + collection.items.length;\n  }, 0);\n}", "function isPrimitive(obj) {\n  return obj !== Object(obj);\n}\nexport function isEqual(first, second) {\n  if (first === second) {\n    return true;\n  }\n  if (isPrimitive(first) || isPrimitive(second) || typeof first === 'function' || typeof second === 'function') {\n    return first === second;\n  }\n  if (Object.keys(first).length !== Object.keys(second).length) {\n    return false;\n  }\n  for (var _i = 0, _Object$keys = Object.keys(first); _i < _Object$keys.length; _i++) {\n    var key = _Object$keys[_i];\n    if (!(key in second)) {\n      return false;\n    }\n    if (!isEqual(first[key], second[key])) {\n      return false;\n    }\n  }\n  return true;\n}", "export var noop = function noop() {};", "export var version = '1.17.7';", "import { version } from './version';\nexport var userAgents = [{\n  segment: 'autocomplete-core',\n  version: version\n}];", "export function createClickedEvent(_ref) {\n  var item = _ref.item,\n    _ref$items = _ref.items,\n    items = _ref$items === void 0 ? [] : _ref$items;\n  return {\n    index: item.__autocomplete_indexName,\n    items: [item],\n    positions: [1 + items.findIndex(function (x) {\n      return x.objectID === item.objectID;\n    })],\n    queryID: item.__autocomplete_queryID,\n    algoliaSource: ['autocomplete']\n  };\n}", "function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : \"undefined\" != typeof Symbol && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\n/**\n * Determines if a given insights `client` supports the optional call to `init`\n * and the ability to set credentials via extra parameters when sending events.\n */\nexport function isModernInsightsClient(client) {\n  var _split$map = (client.version || '').split('.').map(Number),\n    _split$map2 = _slicedToArray(_split$map, 2),\n    major = _split$map2[0],\n    minor = _split$map2[1];\n\n  /* eslint-disable @typescript-eslint/camelcase */\n  var v3 = major >= 3;\n  var v2_4 = major === 2 && minor >= 4;\n  var v1_10 = major === 1 && minor >= 10;\n  return v3 || v2_4 || v1_10;\n  /* eslint-enable @typescript-eslint/camelcase */\n}", "var _excluded = [\"items\"],\n  _excluded2 = [\"items\"];\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nimport { isModernInsightsClient } from './isModernInsightsClient';\nfunction chunk(item) {\n  var chunkSize = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 20;\n  var chunks = [];\n  for (var i = 0; i < item.objectIDs.length; i += chunkSize) {\n    chunks.push(_objectSpread(_objectSpread({}, item), {}, {\n      objectIDs: item.objectIDs.slice(i, i + chunkSize)\n    }));\n  }\n  return chunks;\n}\nfunction mapToInsightsParamsApi(params) {\n  return params.map(function (_ref) {\n    var items = _ref.items,\n      param = _objectWithoutProperties(_ref, _excluded);\n    return _objectSpread(_objectSpread({}, param), {}, {\n      objectIDs: (items === null || items === void 0 ? void 0 : items.map(function (_ref2) {\n        var objectID = _ref2.objectID;\n        return objectID;\n      })) || param.objectIDs\n    });\n  });\n}\nexport function createSearchInsightsApi(searchInsights) {\n  var canSendHeaders = isModernInsightsClient(searchInsights);\n  function sendToInsights(method, payloads, items) {\n    if (canSendHeaders && typeof items !== 'undefined') {\n      var _items$0$__autocomple = items[0].__autocomplete_algoliaCredentials,\n        appId = _items$0$__autocomple.appId,\n        apiKey = _items$0$__autocomple.apiKey;\n      var headers = {\n        'X-Algolia-Application-Id': appId,\n        'X-Algolia-API-Key': apiKey\n      };\n      searchInsights.apply(void 0, [method].concat(_toConsumableArray(payloads), [{\n        headers: headers\n      }]));\n    } else {\n      searchInsights.apply(void 0, [method].concat(_toConsumableArray(payloads)));\n    }\n  }\n  return {\n    /**\n     * Initializes Insights with Algolia credentials.\n     */\n    init: function init(appId, apiKey) {\n      searchInsights('init', {\n        appId: appId,\n        apiKey: apiKey\n      });\n    },\n    /**\n     * Sets the authenticated user token to attach to events.\n     * Unsets the authenticated token by passing `undefined`.\n     *\n     * @link https://www.algolia.com/doc/api-reference/api-methods/set-authenticated-user-token/\n     */\n    setAuthenticatedUserToken: function setAuthenticatedUserToken(authenticatedUserToken) {\n      searchInsights('setAuthenticatedUserToken', authenticatedUserToken);\n    },\n    /**\n     * Sets the user token to attach to events.\n     */\n    setUserToken: function setUserToken(userToken) {\n      searchInsights('setUserToken', userToken);\n    },\n    /**\n     * Sends click events to capture a query and its clicked items and positions.\n     *\n     * @link https://www.algolia.com/doc/api-reference/api-methods/clicked-object-ids-after-search/\n     */\n    clickedObjectIDsAfterSearch: function clickedObjectIDsAfterSearch() {\n      for (var _len = arguments.length, params = new Array(_len), _key = 0; _key < _len; _key++) {\n        params[_key] = arguments[_key];\n      }\n      if (params.length > 0) {\n        sendToInsights('clickedObjectIDsAfterSearch', mapToInsightsParamsApi(params), params[0].items);\n      }\n    },\n    /**\n     * Sends click events to capture clicked items.\n     *\n     * @link https://www.algolia.com/doc/api-reference/api-methods/clicked-object-ids/\n     */\n    clickedObjectIDs: function clickedObjectIDs() {\n      for (var _len2 = arguments.length, params = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        params[_key2] = arguments[_key2];\n      }\n      if (params.length > 0) {\n        sendToInsights('clickedObjectIDs', mapToInsightsParamsApi(params), params[0].items);\n      }\n    },\n    /**\n     * Sends click events to capture the filters a user clicks on.\n     *\n     * @link https://www.algolia.com/doc/api-reference/api-methods/clicked-filters/\n     */\n    clickedFilters: function clickedFilters() {\n      for (var _len3 = arguments.length, params = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n        params[_key3] = arguments[_key3];\n      }\n      if (params.length > 0) {\n        searchInsights.apply(void 0, ['clickedFilters'].concat(params));\n      }\n    },\n    /**\n     * Sends conversion events to capture a query and its clicked items.\n     *\n     * @link https://www.algolia.com/doc/api-reference/api-methods/converted-object-ids-after-search/\n     */\n    convertedObjectIDsAfterSearch: function convertedObjectIDsAfterSearch() {\n      for (var _len4 = arguments.length, params = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n        params[_key4] = arguments[_key4];\n      }\n      if (params.length > 0) {\n        sendToInsights('convertedObjectIDsAfterSearch', mapToInsightsParamsApi(params), params[0].items);\n      }\n    },\n    /**\n     * Sends conversion events to capture clicked items.\n     *\n     * @link https://www.algolia.com/doc/api-reference/api-methods/converted-object-ids/\n     */\n    convertedObjectIDs: function convertedObjectIDs() {\n      for (var _len5 = arguments.length, params = new Array(_len5), _key5 = 0; _key5 < _len5; _key5++) {\n        params[_key5] = arguments[_key5];\n      }\n      if (params.length > 0) {\n        sendToInsights('convertedObjectIDs', mapToInsightsParamsApi(params), params[0].items);\n      }\n    },\n    /**\n     * Sends conversion events to capture the filters a user uses when converting.\n     *\n     * @link https://www.algolia.com/doc/api-reference/api-methods/converted-filters/\n     */\n    convertedFilters: function convertedFilters() {\n      for (var _len6 = arguments.length, params = new Array(_len6), _key6 = 0; _key6 < _len6; _key6++) {\n        params[_key6] = arguments[_key6];\n      }\n      if (params.length > 0) {\n        searchInsights.apply(void 0, ['convertedFilters'].concat(params));\n      }\n    },\n    /**\n     * Sends view events to capture clicked items.\n     *\n     * @link https://www.algolia.com/doc/api-reference/api-methods/viewed-object-ids/\n     */\n    viewedObjectIDs: function viewedObjectIDs() {\n      for (var _len7 = arguments.length, params = new Array(_len7), _key7 = 0; _key7 < _len7; _key7++) {\n        params[_key7] = arguments[_key7];\n      }\n      if (params.length > 0) {\n        params.reduce(function (acc, _ref3) {\n          var items = _ref3.items,\n            param = _objectWithoutProperties(_ref3, _excluded2);\n          return [].concat(_toConsumableArray(acc), _toConsumableArray(chunk(_objectSpread(_objectSpread({}, param), {}, {\n            objectIDs: (items === null || items === void 0 ? void 0 : items.map(function (_ref4) {\n              var objectID = _ref4.objectID;\n              return objectID;\n            })) || param.objectIDs\n          })).map(function (payload) {\n            return {\n              items: items,\n              payload: payload\n            };\n          })));\n        }, []).forEach(function (_ref5) {\n          var items = _ref5.items,\n            payload = _ref5.payload;\n          return sendToInsights('viewedObjectIDs', [payload], items);\n        });\n      }\n    },\n    /**\n     * Sends view events to capture the filters a user uses when viewing.\n     *\n     * @link https://www.algolia.com/doc/api-reference/api-methods/viewed-filters/\n     */\n    viewedFilters: function viewedFilters() {\n      for (var _len8 = arguments.length, params = new Array(_len8), _key8 = 0; _key8 < _len8; _key8++) {\n        params[_key8] = arguments[_key8];\n      }\n      if (params.length > 0) {\n        searchInsights.apply(void 0, ['viewedFilters'].concat(params));\n      }\n    }\n  };\n}", "export function createViewedEvents(_ref) {\n  var items = _ref.items;\n  var itemsByIndexName = items.reduce(function (acc, current) {\n    var _acc$current$__autoco;\n    acc[current.__autocomplete_indexName] = ((_acc$current$__autoco = acc[current.__autocomplete_indexName]) !== null && _acc$current$__autoco !== void 0 ? _acc$current$__autoco : []).concat(current);\n    return acc;\n  }, {});\n  return Object.keys(itemsByIndexName).map(function (indexName) {\n    var items = itemsByIndexName[indexName];\n    return {\n      index: indexName,\n      items: items,\n      algoliaSource: ['autocomplete']\n    };\n  });\n}", "export function isAlgoliaInsightsHit(hit) {\n  return hit.objectID && hit.__autocomplete_indexName && hit.__autocomplete_queryID;\n}", "function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nimport { createRef, debounce, isEqual, noop, safelyRunOnBrowser } from '@algolia/autocomplete-shared';\nimport { createClickedEvent } from './createClickedEvent';\nimport { createSearchInsightsApi } from './createSearchInsightsApi';\nimport { createViewedEvents } from './createViewedEvents';\nimport { isAlgoliaInsightsHit } from './isAlgoliaInsightsHit';\nvar VIEW_EVENT_DELAY = 400;\nvar ALGOLIA_INSIGHTS_VERSION = '2.15.0';\nvar ALGOLIA_INSIGHTS_SRC = \"https://cdn.jsdelivr.net/npm/search-insights@\".concat(ALGOLIA_INSIGHTS_VERSION, \"/dist/search-insights.min.js\");\nvar sendViewedObjectIDs = debounce(function (_ref) {\n  var onItemsChange = _ref.onItemsChange,\n    items = _ref.items,\n    insights = _ref.insights,\n    state = _ref.state;\n  onItemsChange({\n    insights: insights,\n    insightsEvents: createViewedEvents({\n      items: items\n    }).map(function (event) {\n      return _objectSpread({\n        eventName: 'Items Viewed'\n      }, event);\n    }),\n    state: state\n  });\n}, VIEW_EVENT_DELAY);\nexport function createAlgoliaInsightsPlugin(options) {\n  var _getOptions = getOptions(options),\n    providedInsightsClient = _getOptions.insightsClient,\n    insightsInitParams = _getOptions.insightsInitParams,\n    onItemsChange = _getOptions.onItemsChange,\n    onSelectEvent = _getOptions.onSelect,\n    onActiveEvent = _getOptions.onActive,\n    __autocomplete_clickAnalytics = _getOptions.__autocomplete_clickAnalytics;\n  var insightsClient = providedInsightsClient;\n  if (!providedInsightsClient) {\n    safelyRunOnBrowser(function (_ref2) {\n      var window = _ref2.window;\n      var pointer = window.AlgoliaAnalyticsObject || 'aa';\n      if (typeof pointer === 'string') {\n        insightsClient = window[pointer];\n      }\n      if (!insightsClient) {\n        window.AlgoliaAnalyticsObject = pointer;\n        if (!window[pointer]) {\n          window[pointer] = function () {\n            if (!window[pointer].queue) {\n              window[pointer].queue = [];\n            }\n            for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n              args[_key] = arguments[_key];\n            }\n            window[pointer].queue.push(args);\n          };\n        }\n        window[pointer].version = ALGOLIA_INSIGHTS_VERSION;\n        insightsClient = window[pointer];\n        loadInsights(window);\n      }\n    });\n  }\n\n  // We return an empty plugin if `insightsClient` is still undefined at\n  // this stage, which can happen in server environments.\n  if (!insightsClient) {\n    return {};\n  }\n  if (insightsInitParams) {\n    insightsClient('init', _objectSpread({\n      partial: true\n    }, insightsInitParams));\n  }\n  var insights = createSearchInsightsApi(insightsClient);\n  var previousItems = createRef([]);\n  var debouncedOnStateChange = debounce(function (_ref3) {\n    var state = _ref3.state;\n    if (!state.isOpen) {\n      return;\n    }\n    var items = state.collections.reduce(function (acc, current) {\n      return [].concat(_toConsumableArray(acc), _toConsumableArray(current.items));\n    }, []).filter(isAlgoliaInsightsHit);\n    if (!isEqual(previousItems.current.map(function (x) {\n      return x.objectID;\n    }), items.map(function (x) {\n      return x.objectID;\n    }))) {\n      previousItems.current = items;\n      if (items.length > 0) {\n        sendViewedObjectIDs({\n          onItemsChange: onItemsChange,\n          items: items,\n          insights: insights,\n          state: state\n        });\n      }\n    }\n  }, 0);\n  return {\n    name: 'aa.algoliaInsightsPlugin',\n    subscribe: function subscribe(_ref4) {\n      var setContext = _ref4.setContext,\n        onSelect = _ref4.onSelect,\n        onActive = _ref4.onActive;\n      var isAuthenticatedToken = false;\n      function setInsightsContext(userToken) {\n        setContext({\n          algoliaInsightsPlugin: {\n            __algoliaSearchParameters: _objectSpread(_objectSpread({}, __autocomplete_clickAnalytics ? {\n              clickAnalytics: true\n            } : {}), userToken ? {\n              userToken: normalizeUserToken(userToken)\n            } : {}),\n            insights: insights\n          }\n        });\n      }\n      insightsClient('addAlgoliaAgent', 'insights-plugin');\n      setInsightsContext();\n\n      // Handles user token changes\n      insightsClient('onUserTokenChange', function (userToken) {\n        if (!isAuthenticatedToken) {\n          setInsightsContext(userToken);\n        }\n      });\n      insightsClient('getUserToken', null, function (_error, userToken) {\n        if (!isAuthenticatedToken) {\n          setInsightsContext(userToken);\n        }\n      });\n\n      // Handles authenticated user token changes\n      insightsClient('onAuthenticatedUserTokenChange', function (authenticatedUserToken) {\n        if (authenticatedUserToken) {\n          isAuthenticatedToken = true;\n          setInsightsContext(authenticatedUserToken);\n        } else {\n          isAuthenticatedToken = false;\n          insightsClient('getUserToken', null, function (_error, userToken) {\n            return setInsightsContext(userToken);\n          });\n        }\n      });\n      insightsClient('getAuthenticatedUserToken', null, function (_error, authenticatedUserToken) {\n        if (authenticatedUserToken) {\n          isAuthenticatedToken = true;\n          setInsightsContext(authenticatedUserToken);\n        }\n      });\n      onSelect(function (_ref5) {\n        var item = _ref5.item,\n          state = _ref5.state,\n          event = _ref5.event,\n          source = _ref5.source;\n        if (!isAlgoliaInsightsHit(item)) {\n          return;\n        }\n        onSelectEvent({\n          state: state,\n          event: event,\n          insights: insights,\n          item: item,\n          insightsEvents: [_objectSpread({\n            eventName: 'Item Selected'\n          }, createClickedEvent({\n            item: item,\n            items: source.getItems().filter(isAlgoliaInsightsHit)\n          }))]\n        });\n      });\n      onActive(function (_ref6) {\n        var item = _ref6.item,\n          source = _ref6.source,\n          state = _ref6.state,\n          event = _ref6.event;\n        if (!isAlgoliaInsightsHit(item)) {\n          return;\n        }\n        onActiveEvent({\n          state: state,\n          event: event,\n          insights: insights,\n          item: item,\n          insightsEvents: [_objectSpread({\n            eventName: 'Item Active'\n          }, createClickedEvent({\n            item: item,\n            items: source.getItems().filter(isAlgoliaInsightsHit)\n          }))]\n        });\n      });\n    },\n    onStateChange: function onStateChange(_ref7) {\n      var state = _ref7.state;\n      debouncedOnStateChange({\n        state: state\n      });\n    },\n    __autocomplete_pluginOptions: options\n  };\n}\nfunction getAlgoliaSources() {\n  var _context$algoliaInsig;\n  var algoliaSourceBase = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  var context = arguments.length > 1 ? arguments[1] : undefined;\n  return [].concat(_toConsumableArray(algoliaSourceBase), ['autocomplete-internal'], _toConsumableArray((_context$algoliaInsig = context.algoliaInsightsPlugin) !== null && _context$algoliaInsig !== void 0 && _context$algoliaInsig.__automaticInsights ? ['autocomplete-automatic'] : []));\n}\nfunction getOptions(options) {\n  return _objectSpread({\n    onItemsChange: function onItemsChange(_ref8) {\n      var insights = _ref8.insights,\n        insightsEvents = _ref8.insightsEvents,\n        state = _ref8.state;\n      insights.viewedObjectIDs.apply(insights, _toConsumableArray(insightsEvents.map(function (event) {\n        return _objectSpread(_objectSpread({}, event), {}, {\n          algoliaSource: getAlgoliaSources(event.algoliaSource, state.context)\n        });\n      })));\n    },\n    onSelect: function onSelect(_ref9) {\n      var insights = _ref9.insights,\n        insightsEvents = _ref9.insightsEvents,\n        state = _ref9.state;\n      insights.clickedObjectIDsAfterSearch.apply(insights, _toConsumableArray(insightsEvents.map(function (event) {\n        return _objectSpread(_objectSpread({}, event), {}, {\n          algoliaSource: getAlgoliaSources(event.algoliaSource, state.context)\n        });\n      })));\n    },\n    onActive: noop,\n    __autocomplete_clickAnalytics: true\n  }, options);\n}\nfunction loadInsights(environment) {\n  var errorMessage = \"[Autocomplete]: Could not load search-insights.js. Please load it manually following https://alg.li/insights-autocomplete\";\n  try {\n    var script = environment.document.createElement('script');\n    script.async = true;\n    script.src = ALGOLIA_INSIGHTS_SRC;\n    script.onerror = function () {\n      // eslint-disable-next-line no-console\n      console.error(errorMessage);\n    };\n    document.body.appendChild(script);\n  } catch (cause) {\n    // eslint-disable-next-line no-console\n    console.error(errorMessage);\n  }\n}\n\n/**\n * While `search-insights` supports both string and number user tokens,\n * the Search API only accepts strings. This function normalizes the user token.\n */\nfunction normalizeUserToken(userToken) {\n  return typeof userToken === 'number' ? userToken.toString() : userToken;\n}", "/**\n * Safely runs code meant for browser environments only.\n */\nexport function safelyRunOnBrowser(callback) {\n  if (typeof window !== 'undefined') {\n    return callback({\n      window: window\n    });\n  }\n  return undefined;\n}", "export function createRef(initialValue) {\n  return {\n    current: initialValue\n  };\n}", "function createInternalCancelablePromise(promise, initialState) {\n  var state = initialState;\n  return {\n    then: function then(onfulfilled, onrejected) {\n      return createInternalCancelablePromise(promise.then(createCallback(onfulfilled, state, promise), createCallback(onrejected, state, promise)), state);\n    },\n    catch: function _catch(onrejected) {\n      return createInternalCancelablePromise(promise.catch(createCallback(onrejected, state, promise)), state);\n    },\n    finally: function _finally(onfinally) {\n      if (onfinally) {\n        state.onCancelList.push(onfinally);\n      }\n      return createInternalCancelablePromise(promise.finally(createCallback(onfinally && function () {\n        state.onCancelList = [];\n        return onfinally();\n      }, state, promise)), state);\n    },\n    cancel: function cancel() {\n      state.isCanceled = true;\n      var callbacks = state.onCancelList;\n      state.onCancelList = [];\n      callbacks.forEach(function (callback) {\n        callback();\n      });\n    },\n    isCanceled: function isCanceled() {\n      return state.isCanceled === true;\n    }\n  };\n}\nexport function createCancelablePromise(executor) {\n  return createInternalCancelablePromise(new Promise(function (resolve, reject) {\n    return executor(resolve, reject);\n  }), {\n    isCanceled: false,\n    onCancelList: []\n  });\n}\ncreateCancelablePromise.resolve = function (value) {\n  return cancelable(Promise.resolve(value));\n};\ncreateCancelablePromise.reject = function (reason) {\n  return cancelable(Promise.reject(reason));\n};\nexport function cancelable(promise) {\n  return createInternalCancelablePromise(promise, {\n    isCanceled: false,\n    onCancelList: []\n  });\n}\nfunction createCallback(onResult, state, fallback) {\n  if (!onResult) {\n    return fallback;\n  }\n  return function callback(arg) {\n    if (state.isCanceled) {\n      return arg;\n    }\n    return onResult(arg);\n  };\n}", "/**\n * Returns the next active item ID from the current state.\n *\n * We allow circular keyboard navigation from the base index.\n * The base index can either be `null` (nothing is highlighted) or `0`\n * (the first item is highlighted).\n * The base index is allowed to get assigned `null` only if\n * `props.defaultActiveItemId` is `null`. This pattern allows to \"stop\"\n * by the actual query before navigating to other suggestions as seen on\n * Google or Amazon.\n *\n * @param moveAmount The offset to increment (or decrement) the last index\n * @param baseIndex The current index to compute the next index from\n * @param itemCount The number of items\n * @param defaultActiveItemId The default active index to fallback to\n */\nexport function getNextActiveItemId(moveAmount, baseIndex, itemCount, defaultActiveItemId) {\n  if (!itemCount) {\n    return null;\n  }\n  if (moveAmount < 0 && (baseIndex === null || defaultActiveItemId !== null && baseIndex === 0)) {\n    return itemCount + moveAmount;\n  }\n  var numericIndex = (baseIndex === null ? -1 : baseIndex) + moveAmount;\n  if (numericIndex <= -1 || numericIndex >= itemCount) {\n    return defaultActiveItemId === null ? null : 0;\n  }\n  return numericIndex;\n}", "function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nimport { invariant, decycle, noop } from '@algolia/autocomplete-shared';\nexport function getNormalizedSources(getSources, params) {\n  var seenSourceIds = [];\n  return Promise.resolve(getSources(params)).then(function (sources) {\n    invariant(Array.isArray(sources), function () {\n      return \"The `getSources` function must return an array of sources but returned type \".concat(JSON.stringify(_typeof(sources)), \":\\n\\n\").concat(JSON.stringify(decycle(sources), null, 2));\n    });\n    return Promise.all(sources\n    // We allow `undefined` and `false` sources to allow users to use\n    // `Boolean(query) && source` (=> `false`).\n    // We need to remove these values at this point.\n    .filter(function (maybeSource) {\n      return Boolean(maybeSource);\n    }).map(function (source) {\n      invariant(typeof source.sourceId === 'string', 'A source must provide a `sourceId` string.');\n      if (seenSourceIds.includes(source.sourceId)) {\n        throw new Error(\"[Autocomplete] The `sourceId` \".concat(JSON.stringify(source.sourceId), \" is not unique.\"));\n      }\n      seenSourceIds.push(source.sourceId);\n      var defaultSource = {\n        getItemInputValue: function getItemInputValue(_ref) {\n          var state = _ref.state;\n          return state.query;\n        },\n        getItemUrl: function getItemUrl() {\n          return undefined;\n        },\n        onSelect: function onSelect(_ref2) {\n          var setIsOpen = _ref2.setIsOpen;\n          setIsOpen(false);\n        },\n        onActive: noop,\n        onResolve: noop\n      };\n      Object.keys(defaultSource).forEach(function (key) {\n        defaultSource[key].__default = true;\n      });\n      var normalizedSource = _objectSpread(_objectSpread({}, defaultSource), source);\n      return Promise.resolve(normalizedSource);\n    }));\n  });\n}", "// We don't have access to the autocomplete source when we call `onKeyDown`\n// or `onClick` because those are native browser events.\n// However, we can get the source from the suggestion index.\nfunction getCollectionFromActiveItemId(state) {\n  // Given 3 sources with respectively 1, 2 and 3 suggestions: [1, 2, 3]\n  // We want to get the accumulated counts:\n  // [1, 1 + 2, 1 + 2 + 3] = [1, 3, 3 + 3] = [1, 3, 6]\n  var accumulatedCollectionsCount = state.collections.map(function (collections) {\n    return collections.items.length;\n  }).reduce(function (acc, collectionsCount, index) {\n    var previousValue = acc[index - 1] || 0;\n    var nextValue = previousValue + collectionsCount;\n    acc.push(nextValue);\n    return acc;\n  }, []);\n\n  // Based on the accumulated counts, we can infer the index of the suggestion.\n  var collectionIndex = accumulatedCollectionsCount.reduce(function (acc, current) {\n    if (current <= state.activeItemId) {\n      return acc + 1;\n    }\n    return acc;\n  }, 0);\n  return state.collections[collectionIndex];\n}\n\n/**\n * Gets the highlighted index relative to a suggestion object (not the absolute\n * highlighted index).\n *\n * Example:\n *  [['a', 'b'], ['c', 'd', 'e'], ['f']]\n *                      ↑\n *         (absolute: 3, relative: 1)\n */\nfunction getRelativeActiveItemId(_ref) {\n  var state = _ref.state,\n    collection = _ref.collection;\n  var isOffsetFound = false;\n  var counter = 0;\n  var previousItemsOffset = 0;\n  while (isOffsetFound === false) {\n    var currentCollection = state.collections[counter];\n    if (currentCollection === collection) {\n      isOffsetFound = true;\n      break;\n    }\n    previousItemsOffset += currentCollection.items.length;\n    counter++;\n  }\n  return state.activeItemId - previousItemsOffset;\n}\nexport function getActiveItem(state) {\n  var collection = getCollectionFromActiveItemId(state);\n  if (!collection) {\n    return null;\n  }\n  var item = collection.items[getRelativeActiveItemId({\n    state: state,\n    collection: collection\n  })];\n  var source = collection.source;\n  var itemInputValue = source.getItemInputValue({\n    item: item,\n    state: state\n  });\n  var itemUrl = source.getItemUrl({\n    item: item,\n    state: state\n  });\n  return {\n    item: item,\n    itemInputValue: itemInputValue,\n    itemUrl: itemUrl,\n    source: source\n  };\n}", "/**\n * Returns a full element id for an autocomplete element.\n *\n * @param autocompleteInstanceId The id of the autocomplete instance\n * @param elementId The specific element id\n * @param source The source of the element, when it needs to be scoped\n */\nexport function getAutocompleteElementId(autocompleteInstanceId, elementId, source) {\n  return [autocompleteInstanceId, source === null || source === void 0 ? void 0 : source.sourceId, elementId].filter(Boolean).join('-').replace(/\\s/g, '');\n}", "var regex = /((gt|sm)-|galaxy nexus)|samsung[- ]|samsungbrowser/i;\nexport function isSamsung(userAgent) {\n  return Boolean(userAgent && userAgent.match(regex));\n}", "export function getNativeEvent(event) {\n  return event.nativeEvent || event;\n}", "function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nimport { createCancelablePromiseList } from './utils';\nexport function createStore(reducer, props, onStoreStateChange) {\n  var state = props.initialState;\n  return {\n    getState: function getState() {\n      return state;\n    },\n    dispatch: function dispatch(action, payload) {\n      var prevState = _objectSpread({}, state);\n      state = reducer(state, {\n        type: action,\n        props: props,\n        payload: payload\n      });\n      onStoreStateChange({\n        state: state,\n        prevState: prevState\n      });\n    },\n    pendingRequests: createCancelablePromiseList()\n  };\n}", "export function createCancelablePromiseList() {\n  var list = [];\n  return {\n    add: function add(cancelablePromise) {\n      list.push(cancelablePromise);\n      return cancelablePromise.finally(function () {\n        list = list.filter(function (item) {\n          return item !== cancelablePromise;\n        });\n      });\n    },\n    cancelAll: function cancelAll() {\n      list.forEach(function (promise) {\n        return promise.cancel();\n      });\n    },\n    isEmpty: function isEmpty() {\n      return list.length === 0;\n    }\n  };\n}", "function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nimport { flatten } from '@algolia/autocomplete-shared';\nexport function getAutocompleteSetters(_ref) {\n  var store = _ref.store;\n  var setActiveItemId = function setActiveItemId(value) {\n    store.dispatch('setActiveItemId', value);\n  };\n  var setQuery = function setQuery(value) {\n    store.dispatch('setQuery', value);\n  };\n  var setCollections = function setCollections(rawValue) {\n    var baseItemId = 0;\n    var value = rawValue.map(function (collection) {\n      return _objectSpread(_objectSpread({}, collection), {}, {\n        // We flatten the stored items to support calling `getAlgoliaResults`\n        // from the source itself.\n        items: flatten(collection.items).map(function (item) {\n          return _objectSpread(_objectSpread({}, item), {}, {\n            __autocomplete_id: baseItemId++\n          });\n        })\n      });\n    });\n    store.dispatch('setCollections', value);\n  };\n  var setIsOpen = function setIsOpen(value) {\n    store.dispatch('setIsOpen', value);\n  };\n  var setStatus = function setStatus(value) {\n    store.dispatch('setStatus', value);\n  };\n  var setContext = function setContext(value) {\n    store.dispatch('setContext', value);\n  };\n  return {\n    setActiveItemId: setActiveItemId,\n    setQuery: setQuery,\n    setCollections: setCollections,\n    setIsOpen: setIsOpen,\n    setStatus: setStatus,\n    setContext: setContext\n  };\n}", "function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nimport { getItemsCount, generateAutocompleteId, flatten } from '@algolia/autocomplete-shared';\nimport { getNormalizedSources } from './utils';\nexport function getDefaultProps(props, pluginSubscribers) {\n  var _props$id;\n  /* eslint-disable no-restricted-globals */\n  var environment = typeof window !== 'undefined' ? window : {};\n  /* eslint-enable no-restricted-globals */\n  var plugins = props.plugins || [];\n  return _objectSpread(_objectSpread({\n    debug: false,\n    openOnFocus: false,\n    enterKeyHint: undefined,\n    ignoreCompositionEvents: false,\n    placeholder: '',\n    autoFocus: false,\n    defaultActiveItemId: null,\n    stallThreshold: 300,\n    insights: undefined,\n    environment: environment,\n    shouldPanelOpen: function shouldPanelOpen(_ref) {\n      var state = _ref.state;\n      return getItemsCount(state) > 0;\n    },\n    reshape: function reshape(_ref2) {\n      var sources = _ref2.sources;\n      return sources;\n    }\n  }, props), {}, {\n    // Since `generateAutocompleteId` triggers a side effect (it increments\n    // an internal counter), we don't want to execute it if unnecessary.\n    id: (_props$id = props.id) !== null && _props$id !== void 0 ? _props$id : generateAutocompleteId(),\n    plugins: plugins,\n    // The following props need to be deeply defaulted.\n    initialState: _objectSpread({\n      activeItemId: null,\n      query: '',\n      completion: null,\n      collections: [],\n      isOpen: false,\n      status: 'idle',\n      context: {}\n    }, props.initialState),\n    onStateChange: function onStateChange(params) {\n      var _props$onStateChange;\n      (_props$onStateChange = props.onStateChange) === null || _props$onStateChange === void 0 ? void 0 : _props$onStateChange.call(props, params);\n      plugins.forEach(function (x) {\n        var _x$onStateChange;\n        return (_x$onStateChange = x.onStateChange) === null || _x$onStateChange === void 0 ? void 0 : _x$onStateChange.call(x, params);\n      });\n    },\n    onSubmit: function onSubmit(params) {\n      var _props$onSubmit;\n      (_props$onSubmit = props.onSubmit) === null || _props$onSubmit === void 0 ? void 0 : _props$onSubmit.call(props, params);\n      plugins.forEach(function (x) {\n        var _x$onSubmit;\n        return (_x$onSubmit = x.onSubmit) === null || _x$onSubmit === void 0 ? void 0 : _x$onSubmit.call(x, params);\n      });\n    },\n    onReset: function onReset(params) {\n      var _props$onReset;\n      (_props$onReset = props.onReset) === null || _props$onReset === void 0 ? void 0 : _props$onReset.call(props, params);\n      plugins.forEach(function (x) {\n        var _x$onReset;\n        return (_x$onReset = x.onReset) === null || _x$onReset === void 0 ? void 0 : _x$onReset.call(x, params);\n      });\n    },\n    getSources: function getSources(params) {\n      return Promise.all([].concat(_toConsumableArray(plugins.map(function (plugin) {\n        return plugin.getSources;\n      })), [props.getSources]).filter(Boolean).map(function (getSources) {\n        return getNormalizedSources(getSources, params);\n      })).then(function (nested) {\n        return flatten(nested);\n      }).then(function (sources) {\n        return sources.map(function (source) {\n          return _objectSpread(_objectSpread({}, source), {}, {\n            onSelect: function onSelect(params) {\n              source.onSelect(params);\n              pluginSubscribers.forEach(function (x) {\n                var _x$onSelect;\n                return (_x$onSelect = x.onSelect) === null || _x$onSelect === void 0 ? void 0 : _x$onSelect.call(x, params);\n              });\n            },\n            onActive: function onActive(params) {\n              source.onActive(params);\n              pluginSubscribers.forEach(function (x) {\n                var _x$onActive;\n                return (_x$onActive = x.onActive) === null || _x$onActive === void 0 ? void 0 : _x$onActive.call(x, params);\n              });\n            },\n            onResolve: function onResolve(params) {\n              source.onResolve(params);\n              pluginSubscribers.forEach(function (x) {\n                var _x$onResolve;\n                return (_x$onResolve = x.onResolve) === null || _x$onResolve === void 0 ? void 0 : _x$onResolve.call(x, params);\n              });\n            }\n          });\n        });\n      });\n    },\n    navigator: _objectSpread({\n      navigate: function navigate(_ref3) {\n        var itemUrl = _ref3.itemUrl;\n        environment.location.assign(itemUrl);\n      },\n      navigateNewTab: function navigateNewTab(_ref4) {\n        var itemUrl = _ref4.itemUrl;\n        var windowReference = environment.open(itemUrl, '_blank', 'noopener');\n        windowReference === null || windowReference === void 0 ? void 0 : windowReference.focus();\n      },\n      navigateNewWindow: function navigateNewWindow(_ref5) {\n        var itemUrl = _ref5.itemUrl;\n        environment.open(itemUrl, '_blank', 'noopener');\n      }\n    }, props.navigator)\n  });\n}", "function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nimport { flatten } from '@algolia/autocomplete-shared';\nexport function reshape(_ref) {\n  var collections = _ref.collections,\n    props = _ref.props,\n    state = _ref.state;\n  // Sources are grouped by `sourceId` to conveniently pick them via destructuring.\n  // Example: `const { recentSearchesPlugin } = sourcesBySourceId`\n  var originalSourcesBySourceId = collections.reduce(function (acc, collection) {\n    return _objectSpread(_objectSpread({}, acc), {}, _defineProperty({}, collection.source.sourceId, _objectSpread(_objectSpread({}, collection.source), {}, {\n      getItems: function getItems() {\n        // We provide the resolved items from the collection to the `reshape` prop.\n        return flatten(collection.items);\n      }\n    })));\n  }, {});\n  var _props$plugins$reduce = props.plugins.reduce(function (acc, plugin) {\n      if (plugin.reshape) {\n        return plugin.reshape(acc);\n      }\n      return acc;\n    }, {\n      sourcesBySourceId: originalSourcesBySourceId,\n      state: state\n    }),\n    sourcesBySourceId = _props$plugins$reduce.sourcesBySourceId;\n  var reshapeSources = props.reshape({\n    sourcesBySourceId: sourcesBySourceId,\n    sources: Object.values(sourcesBySourceId),\n    state: state\n  });\n\n  // We reconstruct the collections with the items modified by the `reshape` prop.\n  return flatten(reshapeSources).filter(Boolean).map(function (source) {\n    return {\n      source: source,\n      items: source.getItems()\n    };\n  });\n}", "function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nimport { decycle, flatten, invariant } from '@algolia/autocomplete-shared';\nimport { mapToAlgoliaResponse } from './utils';\nfunction isDescription(item) {\n  return Boolean(item.execute);\n}\nfunction isRequesterDescription(description) {\n  return Boolean(description === null || description === void 0 ? void 0 : description.execute);\n}\nexport function preResolve(itemsOrDescription, sourceId, state) {\n  if (isRequesterDescription(itemsOrDescription)) {\n    var contextParameters = itemsOrDescription.requesterId === 'algolia' ? Object.assign.apply(Object, [{}].concat(_toConsumableArray(Object.keys(state.context).map(function (key) {\n      var _state$context$key;\n      return (_state$context$key = state.context[key]) === null || _state$context$key === void 0 ? void 0 : _state$context$key.__algoliaSearchParameters;\n    })))) : {};\n    return _objectSpread(_objectSpread({}, itemsOrDescription), {}, {\n      requests: itemsOrDescription.queries.map(function (query) {\n        return {\n          query: itemsOrDescription.requesterId === 'algolia' ? _objectSpread(_objectSpread({}, query), {}, {\n            params: _objectSpread(_objectSpread({}, contextParameters), query.params)\n          }) : query,\n          sourceId: sourceId,\n          transformResponse: itemsOrDescription.transformResponse\n        };\n      })\n    });\n  }\n  return {\n    items: itemsOrDescription,\n    sourceId: sourceId\n  };\n}\nexport function resolve(items) {\n  var packed = items.reduce(function (acc, current) {\n    if (!isDescription(current)) {\n      acc.push(current);\n      return acc;\n    }\n    var searchClient = current.searchClient,\n      execute = current.execute,\n      requesterId = current.requesterId,\n      requests = current.requests;\n    var container = acc.find(function (item) {\n      return isDescription(current) && isDescription(item) && item.searchClient === searchClient && Boolean(requesterId) && item.requesterId === requesterId;\n    });\n    if (container) {\n      var _container$items;\n      (_container$items = container.items).push.apply(_container$items, _toConsumableArray(requests));\n    } else {\n      var request = {\n        execute: execute,\n        requesterId: requesterId,\n        items: requests,\n        searchClient: searchClient\n      };\n      acc.push(request);\n    }\n    return acc;\n  }, []);\n  var values = packed.map(function (maybeDescription) {\n    if (!isDescription(maybeDescription)) {\n      return Promise.resolve(maybeDescription);\n    }\n    var _ref = maybeDescription,\n      execute = _ref.execute,\n      items = _ref.items,\n      searchClient = _ref.searchClient;\n    return execute({\n      searchClient: searchClient,\n      requests: items\n    });\n  });\n  return Promise.all(values).then(function (responses) {\n    return flatten(responses);\n  });\n}\nexport function postResolve(responses, sources, store) {\n  return sources.map(function (source) {\n    var matches = responses.filter(function (response) {\n      return response.sourceId === source.sourceId;\n    });\n    var results = matches.map(function (_ref2) {\n      var items = _ref2.items;\n      return items;\n    });\n    var transform = matches[0].transformResponse;\n    var items = transform ? transform(mapToAlgoliaResponse(results)) : results;\n    source.onResolve({\n      source: source,\n      results: results,\n      items: items,\n      state: store.getState()\n    });\n    invariant(Array.isArray(items), function () {\n      return \"The `getItems` function from source \\\"\".concat(source.sourceId, \"\\\" must return an array of items but returned type \").concat(JSON.stringify(_typeof(items)), \":\\n\\n\").concat(JSON.stringify(decycle(items), null, 2), \".\\n\\nSee: https://www.algolia.com/doc/ui-libraries/autocomplete/core-concepts/sources/#param-getitems\");\n    });\n    invariant(items.every(Boolean), \"The `getItems` function from source \\\"\".concat(source.sourceId, \"\\\" must return an array of items but returned \").concat(JSON.stringify(undefined), \".\\n\\nDid you forget to return items?\\n\\nSee: https://www.algolia.com/doc/ui-libraries/autocomplete/core-concepts/sources/#param-getitems\"));\n    return {\n      source: source,\n      items: items\n    };\n  });\n}", "export function mapToAlgoliaResponse(rawResults) {\n  return {\n    results: rawResults,\n    hits: rawResults.map(function (result) {\n      return result.hits;\n    }).filter(Boolean),\n    facetHits: rawResults.map(function (result) {\n      var _facetHits;\n      return (_facetHits = result.facetHits) === null || _facetHits === void 0 ? void 0 : _facetHits.map(function (facetHit) {\n        // Bring support for the highlighting components.\n        return {\n          label: facetHit.value,\n          count: facetHit.count,\n          _highlightResult: {\n            label: {\n              value: facetHit.highlighted\n            }\n          }\n        };\n      });\n    }).filter(Boolean)\n  };\n}", "function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nvar _excluded = [\"event\", \"nextState\", \"props\", \"query\", \"refresh\", \"store\"];\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\nimport { reshape } from './reshape';\nimport { preResolve, resolve, postResolve } from './resolve';\nimport { cancelable, createConcurrentSafePromise, getActiveItem } from './utils';\nvar lastStalledId = null;\nvar runConcurrentSafePromise = createConcurrentSafePromise();\nexport function onInput(_ref) {\n  var event = _ref.event,\n    _ref$nextState = _ref.nextState,\n    nextState = _ref$nextState === void 0 ? {} : _ref$nextState,\n    props = _ref.props,\n    query = _ref.query,\n    refresh = _ref.refresh,\n    store = _ref.store,\n    setters = _objectWithoutProperties(_ref, _excluded);\n  if (lastStalledId) {\n    props.environment.clearTimeout(lastStalledId);\n  }\n  var setCollections = setters.setCollections,\n    setIsOpen = setters.setIsOpen,\n    setQuery = setters.setQuery,\n    setActiveItemId = setters.setActiveItemId,\n    setStatus = setters.setStatus,\n    setContext = setters.setContext;\n  setQuery(query);\n  setActiveItemId(props.defaultActiveItemId);\n  if (!query && props.openOnFocus === false) {\n    var _nextState$isOpen;\n    var collections = store.getState().collections.map(function (collection) {\n      return _objectSpread(_objectSpread({}, collection), {}, {\n        items: []\n      });\n    });\n    setStatus('idle');\n    setCollections(collections);\n    setIsOpen((_nextState$isOpen = nextState.isOpen) !== null && _nextState$isOpen !== void 0 ? _nextState$isOpen : props.shouldPanelOpen({\n      state: store.getState()\n    }));\n\n    // We make sure to update the latest resolved value of the tracked\n    // promises to keep late resolving promises from \"cancelling\" the state\n    // updates performed in this code path.\n    // We chain with a void promise to respect `onInput`'s expected return type.\n    var _request = cancelable(runConcurrentSafePromise(collections).then(function () {\n      return Promise.resolve();\n    }));\n    return store.pendingRequests.add(_request);\n  }\n  setStatus('loading');\n  lastStalledId = props.environment.setTimeout(function () {\n    setStatus('stalled');\n  }, props.stallThreshold);\n\n  // We track the entire promise chain triggered by `onInput` before mutating\n  // the Autocomplete state to make sure that any state manipulation is based on\n  // fresh data regardless of when promises individually resolve.\n  // We don't track nested promises and only rely on the full chain resolution,\n  // meaning we should only ever manipulate the state once this concurrent-safe\n  // promise is resolved.\n  var request = cancelable(runConcurrentSafePromise(props.getSources(_objectSpread({\n    query: query,\n    refresh: refresh,\n    state: store.getState()\n  }, setters)).then(function (sources) {\n    return Promise.all(sources.map(function (source) {\n      return Promise.resolve(source.getItems(_objectSpread({\n        query: query,\n        refresh: refresh,\n        state: store.getState()\n      }, setters))).then(function (itemsOrDescription) {\n        return preResolve(itemsOrDescription, source.sourceId, store.getState());\n      });\n    })).then(resolve).then(function (responses) {\n      var __automaticInsights = responses.some(function (_ref2) {\n        var items = _ref2.items;\n        return isSearchResponseWithAutomaticInsightsFlag(items);\n      });\n\n      // No need to pollute the context if `__automaticInsights=false`\n      if (__automaticInsights) {\n        var _store$getState$conte;\n        setContext({\n          algoliaInsightsPlugin: _objectSpread(_objectSpread({}, ((_store$getState$conte = store.getState().context) === null || _store$getState$conte === void 0 ? void 0 : _store$getState$conte.algoliaInsightsPlugin) || {}), {}, {\n            __automaticInsights: __automaticInsights\n          })\n        });\n      }\n      return postResolve(responses, sources, store);\n    }).then(function (collections) {\n      return reshape({\n        collections: collections,\n        props: props,\n        state: store.getState()\n      });\n    });\n  }))).then(function (collections) {\n    var _nextState$isOpen2;\n    // Parameters passed to `onInput` could be stale when the following code\n    // executes, because `onInput` calls may not resolve in order.\n    // If it becomes a problem we'll need to save the last passed parameters.\n    // See: https://codesandbox.io/s/agitated-cookies-y290z\n\n    setStatus('idle');\n    setCollections(collections);\n    var isPanelOpen = props.shouldPanelOpen({\n      state: store.getState()\n    });\n    setIsOpen((_nextState$isOpen2 = nextState.isOpen) !== null && _nextState$isOpen2 !== void 0 ? _nextState$isOpen2 : props.openOnFocus && !query && isPanelOpen || isPanelOpen);\n    var highlightedItem = getActiveItem(store.getState());\n    if (store.getState().activeItemId !== null && highlightedItem) {\n      var item = highlightedItem.item,\n        itemInputValue = highlightedItem.itemInputValue,\n        itemUrl = highlightedItem.itemUrl,\n        source = highlightedItem.source;\n      source.onActive(_objectSpread({\n        event: event,\n        item: item,\n        itemInputValue: itemInputValue,\n        itemUrl: itemUrl,\n        refresh: refresh,\n        source: source,\n        state: store.getState()\n      }, setters));\n    }\n  }).finally(function () {\n    setStatus('idle');\n    if (lastStalledId) {\n      props.environment.clearTimeout(lastStalledId);\n    }\n  });\n  return store.pendingRequests.add(request);\n}\nfunction isSearchResponseWithAutomaticInsightsFlag(items) {\n  return !Array.isArray(items) && Boolean(items === null || items === void 0 ? void 0 : items._automaticInsights);\n}", "/**\n * Creates a runner that executes promises in a concurrent-safe way.\n *\n * This is useful to prevent older promises to resolve after a newer promise,\n * otherwise resulting in stale resolved values.\n */\nexport function createConcurrentSafePromise() {\n  var basePromiseId = -1;\n  var latestResolvedId = -1;\n  var latestResolvedValue = undefined;\n  return function runConcurrentSafePromise(promise) {\n    basePromiseId++;\n    var currentPromiseId = basePromiseId;\n    return Promise.resolve(promise).then(function (x) {\n      // The promise might take too long to resolve and get outdated. This would\n      // result in resolving stale values.\n      // When this happens, we ignore the promise value and return the one\n      // coming from the latest resolved value.\n      //\n      // +----------------------------------+\n      // |        100ms                     |\n      // | run(1) +--->  R1                 |\n      // |        300ms                     |\n      // | run(2) +-------------> R2 (SKIP) |\n      // |        200ms                     |\n      // | run(3) +--------> R3             |\n      // +----------------------------------+\n      if (latestResolvedValue && currentPromiseId < latestResolvedId) {\n        return latestResolvedValue;\n      }\n      latestResolvedId = currentPromiseId;\n      latestResolvedValue = x;\n      return x;\n    });\n  };\n}", "function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nvar _excluded = [\"event\", \"props\", \"refresh\", \"store\"];\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\nimport { onInput } from './onInput';\nimport { getActiveItem, getAutocompleteElementId } from './utils';\nexport function onKeyDown(_ref) {\n  var event = _ref.event,\n    props = _ref.props,\n    refresh = _ref.refresh,\n    store = _ref.store,\n    setters = _objectWithoutProperties(_ref, _excluded);\n  if (event.key === 'ArrowUp' || event.key === 'ArrowDown') {\n    // eslint-disable-next-line no-inner-declarations\n    var triggerScrollIntoView = function triggerScrollIntoView() {\n      var highlightedItem = getActiveItem(store.getState());\n      var nodeItem = props.environment.document.getElementById(getAutocompleteElementId(props.id, \"item-\".concat(store.getState().activeItemId), highlightedItem === null || highlightedItem === void 0 ? void 0 : highlightedItem.source));\n      if (nodeItem) {\n        if (nodeItem.scrollIntoViewIfNeeded) {\n          nodeItem.scrollIntoViewIfNeeded(false);\n        } else {\n          nodeItem.scrollIntoView(false);\n        }\n      }\n    }; // eslint-disable-next-line no-inner-declarations\n    var triggerOnActive = function triggerOnActive() {\n      var highlightedItem = getActiveItem(store.getState());\n      if (store.getState().activeItemId !== null && highlightedItem) {\n        var item = highlightedItem.item,\n          itemInputValue = highlightedItem.itemInputValue,\n          itemUrl = highlightedItem.itemUrl,\n          source = highlightedItem.source;\n        source.onActive(_objectSpread({\n          event: event,\n          item: item,\n          itemInputValue: itemInputValue,\n          itemUrl: itemUrl,\n          refresh: refresh,\n          source: source,\n          state: store.getState()\n        }, setters));\n      }\n    }; // Default browser behavior changes the caret placement on ArrowUp and\n    // ArrowDown.\n    event.preventDefault();\n\n    // When re-opening the panel, we need to split the logic to keep the actions\n    // synchronized as `onInput` returns a promise.\n    if (store.getState().isOpen === false && (props.openOnFocus || Boolean(store.getState().query))) {\n      onInput(_objectSpread({\n        event: event,\n        props: props,\n        query: store.getState().query,\n        refresh: refresh,\n        store: store\n      }, setters)).then(function () {\n        store.dispatch(event.key, {\n          nextActiveItemId: props.defaultActiveItemId\n        });\n        triggerOnActive();\n        // Since we rely on the DOM, we need to wait for all the micro tasks to\n        // finish (which include re-opening the panel) to make sure all the\n        // elements are available.\n        setTimeout(triggerScrollIntoView, 0);\n      });\n    } else {\n      store.dispatch(event.key, {});\n      triggerOnActive();\n      triggerScrollIntoView();\n    }\n  } else if (event.key === 'Escape') {\n    // This prevents the default browser behavior on `input[type=\"search\"]`\n    // from removing the query right away because we first want to close the\n    // panel.\n    event.preventDefault();\n    store.dispatch(event.key, null);\n\n    // Hitting the `Escape` key signals the end of a user interaction with the\n    // autocomplete. At this point, we should ignore any requests that are still\n    // pending and could reopen the panel once they resolve, because that would\n    // result in an unsolicited UI behavior.\n    store.pendingRequests.cancelAll();\n  } else if (event.key === 'Tab') {\n    store.dispatch('blur', null);\n\n    // Hitting the `Tab` key signals the end of a user interaction with the\n    // autocomplete. At this point, we should ignore any requests that are still\n    // pending and could reopen the panel once they resolve, because that would\n    // result in an unsolicited UI behavior.\n    store.pendingRequests.cancelAll();\n  } else if (event.key === 'Enter') {\n    // No active item, so we let the browser handle the native `onSubmit` form\n    // event.\n    if (store.getState().activeItemId === null || store.getState().collections.every(function (collection) {\n      return collection.items.length === 0;\n    })) {\n      // If requests are still pending when the panel closes, they could reopen\n      // the panel once they resolve.\n      // We want to prevent any subsequent query from reopening the panel\n      // because it would result in an unsolicited UI behavior.\n      if (!props.debug) {\n        store.pendingRequests.cancelAll();\n      }\n      return;\n    }\n\n    // This prevents the `onSubmit` event to be sent because an item is\n    // highlighted.\n    event.preventDefault();\n    var _ref2 = getActiveItem(store.getState()),\n      item = _ref2.item,\n      itemInputValue = _ref2.itemInputValue,\n      itemUrl = _ref2.itemUrl,\n      source = _ref2.source;\n    if (event.metaKey || event.ctrlKey) {\n      if (itemUrl !== undefined) {\n        source.onSelect(_objectSpread({\n          event: event,\n          item: item,\n          itemInputValue: itemInputValue,\n          itemUrl: itemUrl,\n          refresh: refresh,\n          source: source,\n          state: store.getState()\n        }, setters));\n        props.navigator.navigateNewTab({\n          itemUrl: itemUrl,\n          item: item,\n          state: store.getState()\n        });\n      }\n    } else if (event.shiftKey) {\n      if (itemUrl !== undefined) {\n        source.onSelect(_objectSpread({\n          event: event,\n          item: item,\n          itemInputValue: itemInputValue,\n          itemUrl: itemUrl,\n          refresh: refresh,\n          source: source,\n          state: store.getState()\n        }, setters));\n        props.navigator.navigateNewWindow({\n          itemUrl: itemUrl,\n          item: item,\n          state: store.getState()\n        });\n      }\n    } else if (event.altKey) {\n      // Keep native browser behavior\n    } else {\n      if (itemUrl !== undefined) {\n        source.onSelect(_objectSpread({\n          event: event,\n          item: item,\n          itemInputValue: itemInputValue,\n          itemUrl: itemUrl,\n          refresh: refresh,\n          source: source,\n          state: store.getState()\n        }, setters));\n        props.navigator.navigate({\n          itemUrl: itemUrl,\n          item: item,\n          state: store.getState()\n        });\n        return;\n      }\n      onInput(_objectSpread({\n        event: event,\n        nextState: {\n          isOpen: false\n        },\n        props: props,\n        query: itemInputValue,\n        refresh: refresh,\n        store: store\n      }, setters)).then(function () {\n        source.onSelect(_objectSpread({\n          event: event,\n          item: item,\n          itemInputValue: itemInputValue,\n          itemUrl: itemUrl,\n          refresh: refresh,\n          source: source,\n          state: store.getState()\n        }, setters));\n      });\n    }\n  }\n}", "function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nvar _excluded = [\"props\", \"refresh\", \"store\"],\n  _excluded2 = [\"inputElement\", \"formElement\", \"panelElement\"],\n  _excluded3 = [\"inputElement\"],\n  _excluded4 = [\"inputElement\", \"maxLength\"],\n  _excluded5 = [\"source\"],\n  _excluded6 = [\"item\", \"source\"];\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\nimport { noop } from '@algolia/autocomplete-shared';\nimport { onInput } from './onInput';\nimport { onKeyDown as _onKeyDown } from './onKeyDown';\nimport { getActiveItem, getAutocompleteElementId, isOrContainsNode, isSamsung, getNativeEvent } from './utils';\nexport function getPropGetters(_ref) {\n  var props = _ref.props,\n    refresh = _ref.refresh,\n    store = _ref.store,\n    setters = _objectWithoutProperties(_ref, _excluded);\n  var getEnvironmentProps = function getEnvironmentProps(providedProps) {\n    var inputElement = providedProps.inputElement,\n      formElement = providedProps.formElement,\n      panelElement = providedProps.panelElement,\n      rest = _objectWithoutProperties(providedProps, _excluded2);\n    function onMouseDownOrTouchStart(event) {\n      // The `onTouchStart`/`onMouseDown` events shouldn't trigger the `blur`\n      // handler when it's not an interaction with Autocomplete.\n      // We detect it with the following heuristics:\n      // - the panel is closed AND there are no pending requests\n      //   (no interaction with the autocomplete, no future state updates)\n      // - OR the touched target is the input element (should open the panel)\n      var isAutocompleteInteraction = store.getState().isOpen || !store.pendingRequests.isEmpty();\n      if (!isAutocompleteInteraction || event.target === inputElement) {\n        return;\n      }\n\n      // @TODO: support cases where there are multiple Autocomplete instances.\n      // Right now, a second instance makes this computation return false.\n      var isTargetWithinAutocomplete = [formElement, panelElement].some(function (contextNode) {\n        return isOrContainsNode(contextNode, event.target);\n      });\n      if (isTargetWithinAutocomplete === false) {\n        store.dispatch('blur', null);\n\n        // If requests are still pending when the user closes the panel, they\n        // could reopen the panel once they resolve.\n        // We want to prevent any subsequent query from reopening the panel\n        // because it would result in an unsolicited UI behavior.\n        if (!props.debug) {\n          store.pendingRequests.cancelAll();\n        }\n      }\n    }\n    return _objectSpread({\n      // We do not rely on the native `blur` event of the input to close the\n      // panel, but rather on a custom `touchstart`/`mousedown` event outside\n      // of the autocomplete elements.\n      // This ensures we don't mistakenly interpret interactions within the\n      // autocomplete (but outside of the input) as a signal to close the panel.\n      // For example, clicking reset button causes an input blur, but if\n      // `openOnFocus=true`, it shouldn't close the panel.\n      // On touch devices, scrolling results (`touchmove`) causes an input blur\n      // but shouldn't close the panel.\n      onTouchStart: onMouseDownOrTouchStart,\n      onMouseDown: onMouseDownOrTouchStart,\n      // When scrolling on touch devices (mobiles, tablets, etc.), we want to\n      // mimic the native platform behavior where the input is blurred to\n      // hide the virtual keyboard. This gives more vertical space to\n      // discover all the suggestions showing up in the panel.\n      onTouchMove: function onTouchMove(event) {\n        if (store.getState().isOpen === false || inputElement !== props.environment.document.activeElement || event.target === inputElement) {\n          return;\n        }\n        inputElement.blur();\n      }\n    }, rest);\n  };\n  var getRootProps = function getRootProps(rest) {\n    return _objectSpread({\n      role: 'combobox',\n      'aria-expanded': store.getState().isOpen,\n      'aria-haspopup': 'listbox',\n      'aria-controls': store.getState().isOpen ? store.getState().collections.map(function (_ref2) {\n        var source = _ref2.source;\n        return getAutocompleteElementId(props.id, 'list', source);\n      }).join(' ') : undefined,\n      'aria-labelledby': getAutocompleteElementId(props.id, 'label')\n    }, rest);\n  };\n  var getFormProps = function getFormProps(providedProps) {\n    var inputElement = providedProps.inputElement,\n      rest = _objectWithoutProperties(providedProps, _excluded3);\n    return _objectSpread({\n      action: '',\n      noValidate: true,\n      role: 'search',\n      onSubmit: function onSubmit(event) {\n        var _providedProps$inputE;\n        event.preventDefault();\n        props.onSubmit(_objectSpread({\n          event: event,\n          refresh: refresh,\n          state: store.getState()\n        }, setters));\n        store.dispatch('submit', null);\n        (_providedProps$inputE = providedProps.inputElement) === null || _providedProps$inputE === void 0 ? void 0 : _providedProps$inputE.blur();\n      },\n      onReset: function onReset(event) {\n        var _providedProps$inputE2;\n        event.preventDefault();\n        props.onReset(_objectSpread({\n          event: event,\n          refresh: refresh,\n          state: store.getState()\n        }, setters));\n        store.dispatch('reset', null);\n        (_providedProps$inputE2 = providedProps.inputElement) === null || _providedProps$inputE2 === void 0 ? void 0 : _providedProps$inputE2.focus();\n      }\n    }, rest);\n  };\n  var getInputProps = function getInputProps(providedProps) {\n    var _props$environment$na;\n    function onFocus(event) {\n      // We want to trigger a query when `openOnFocus` is true\n      // because the panel should open with the current query.\n      if (props.openOnFocus || Boolean(store.getState().query)) {\n        onInput(_objectSpread({\n          event: event,\n          props: props,\n          query: store.getState().completion || store.getState().query,\n          refresh: refresh,\n          store: store\n        }, setters));\n      }\n      store.dispatch('focus', null);\n    }\n    var _ref3 = providedProps || {},\n      inputElement = _ref3.inputElement,\n      _ref3$maxLength = _ref3.maxLength,\n      maxLength = _ref3$maxLength === void 0 ? 512 : _ref3$maxLength,\n      rest = _objectWithoutProperties(_ref3, _excluded4);\n    var activeItem = getActiveItem(store.getState());\n    var userAgent = ((_props$environment$na = props.environment.navigator) === null || _props$environment$na === void 0 ? void 0 : _props$environment$na.userAgent) || '';\n    var shouldFallbackKeyHint = isSamsung(userAgent);\n    var enterKeyHint = props.enterKeyHint || (activeItem !== null && activeItem !== void 0 && activeItem.itemUrl && !shouldFallbackKeyHint ? 'go' : 'search');\n    return _objectSpread({\n      'aria-autocomplete': 'both',\n      'aria-activedescendant': store.getState().isOpen && store.getState().activeItemId !== null ? getAutocompleteElementId(props.id, \"item-\".concat(store.getState().activeItemId), activeItem === null || activeItem === void 0 ? void 0 : activeItem.source) : undefined,\n      'aria-controls': store.getState().isOpen ? store.getState().collections.map(function (_ref4) {\n        var source = _ref4.source;\n        return getAutocompleteElementId(props.id, 'list', source);\n      }).join(' ') : undefined,\n      'aria-labelledby': getAutocompleteElementId(props.id, 'label'),\n      value: store.getState().completion || store.getState().query,\n      id: getAutocompleteElementId(props.id, 'input'),\n      autoComplete: 'off',\n      autoCorrect: 'off',\n      autoCapitalize: 'off',\n      enterKeyHint: enterKeyHint,\n      spellCheck: 'false',\n      autoFocus: props.autoFocus,\n      placeholder: props.placeholder,\n      maxLength: maxLength,\n      type: 'search',\n      onChange: function onChange(event) {\n        var value = event.currentTarget.value;\n        if (props.ignoreCompositionEvents && getNativeEvent(event).isComposing) {\n          setters.setQuery(value);\n          return;\n        }\n        onInput(_objectSpread({\n          event: event,\n          props: props,\n          query: value.slice(0, maxLength),\n          refresh: refresh,\n          store: store\n        }, setters));\n      },\n      onCompositionEnd: function onCompositionEnd(event) {\n        onInput(_objectSpread({\n          event: event,\n          props: props,\n          query: event.currentTarget.value.slice(0, maxLength),\n          refresh: refresh,\n          store: store\n        }, setters));\n      },\n      onKeyDown: function onKeyDown(event) {\n        if (getNativeEvent(event).isComposing) {\n          return;\n        }\n        _onKeyDown(_objectSpread({\n          event: event,\n          props: props,\n          refresh: refresh,\n          store: store\n        }, setters));\n      },\n      onFocus: onFocus,\n      // We don't rely on the `blur` event.\n      // See explanation in `onTouchStart`/`onMouseDown`.\n      // @MAJOR See if we need to keep this handler.\n      onBlur: noop,\n      onClick: function onClick(event) {\n        // When the panel is closed and you click on the input while\n        // the input is focused, the `onFocus` event is not triggered\n        // (default browser behavior).\n        // In an autocomplete context, it makes sense to open the panel in this\n        // case.\n        // We mimic this event by catching the `onClick` event which\n        // triggers the `onFocus` for the panel to open.\n        if (providedProps.inputElement === props.environment.document.activeElement && !store.getState().isOpen) {\n          onFocus(event);\n        }\n      }\n    }, rest);\n  };\n  var getLabelProps = function getLabelProps(rest) {\n    return _objectSpread({\n      htmlFor: getAutocompleteElementId(props.id, 'input'),\n      id: getAutocompleteElementId(props.id, 'label')\n    }, rest);\n  };\n  var getListProps = function getListProps(providedProps) {\n    var _ref5 = providedProps || {},\n      source = _ref5.source,\n      rest = _objectWithoutProperties(_ref5, _excluded5);\n    return _objectSpread({\n      role: 'listbox',\n      'aria-labelledby': getAutocompleteElementId(props.id, 'label'),\n      id: getAutocompleteElementId(props.id, 'list', source)\n    }, rest);\n  };\n  var getPanelProps = function getPanelProps(rest) {\n    return _objectSpread({\n      onMouseDown: function onMouseDown(event) {\n        // Prevents the `activeElement` from being changed to the panel so\n        // that the blur event is not triggered, otherwise it closes the\n        // panel.\n        event.preventDefault();\n      },\n      onMouseLeave: function onMouseLeave() {\n        store.dispatch('mouseleave', null);\n      }\n    }, rest);\n  };\n  var getItemProps = function getItemProps(providedProps) {\n    var item = providedProps.item,\n      source = providedProps.source,\n      rest = _objectWithoutProperties(providedProps, _excluded6);\n    return _objectSpread({\n      id: getAutocompleteElementId(props.id, \"item-\".concat(item.__autocomplete_id), source),\n      role: 'option',\n      'aria-selected': store.getState().activeItemId === item.__autocomplete_id,\n      onMouseMove: function onMouseMove(event) {\n        if (item.__autocomplete_id === store.getState().activeItemId) {\n          return;\n        }\n        store.dispatch('mousemove', item.__autocomplete_id);\n        var activeItem = getActiveItem(store.getState());\n        if (store.getState().activeItemId !== null && activeItem) {\n          var _item = activeItem.item,\n            itemInputValue = activeItem.itemInputValue,\n            itemUrl = activeItem.itemUrl,\n            _source = activeItem.source;\n          _source.onActive(_objectSpread({\n            event: event,\n            item: _item,\n            itemInputValue: itemInputValue,\n            itemUrl: itemUrl,\n            refresh: refresh,\n            source: _source,\n            state: store.getState()\n          }, setters));\n        }\n      },\n      onMouseDown: function onMouseDown(event) {\n        // Prevents the `activeElement` from being changed to the item so it\n        // can remain with the current `activeElement`.\n        event.preventDefault();\n      },\n      onClick: function onClick(event) {\n        var itemInputValue = source.getItemInputValue({\n          item: item,\n          state: store.getState()\n        });\n        var itemUrl = source.getItemUrl({\n          item: item,\n          state: store.getState()\n        });\n\n        // If `getItemUrl` is provided, it means that the suggestion\n        // is a link, not plain text that aims at updating the query.\n        // We can therefore skip the state change because it will update\n        // the `activeItemId`, resulting in a UI flash, especially\n        // noticeable on mobile.\n        var runPreCommand = itemUrl ? Promise.resolve() : onInput(_objectSpread({\n          event: event,\n          nextState: {\n            isOpen: false\n          },\n          props: props,\n          query: itemInputValue,\n          refresh: refresh,\n          store: store\n        }, setters));\n        runPreCommand.then(function () {\n          source.onSelect(_objectSpread({\n            event: event,\n            item: item,\n            itemInputValue: itemInputValue,\n            itemUrl: itemUrl,\n            refresh: refresh,\n            source: source,\n            state: store.getState()\n          }, setters));\n        });\n      }\n    }, rest);\n  };\n  return {\n    getEnvironmentProps: getEnvironmentProps,\n    getRootProps: getRootProps,\n    getFormProps: getFormProps,\n    getLabelProps: getLabelProps,\n    getInputProps: getInputProps,\n    getPanelProps: getPanelProps,\n    getListProps: getListProps,\n    getItemProps: getItemProps\n  };\n}", "export function isOrContainsNode(parent, child) {\n  return parent === child || parent.contains(child);\n}", "function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nimport { userAgents } from '@algolia/autocomplete-shared';\nexport function getMetadata(_ref) {\n  var _, _options$__autocomple, _options$__autocomple2, _options$__autocomple3;\n  var plugins = _ref.plugins,\n    options = _ref.options;\n  var optionsKey = (_ = (((_options$__autocomple = options.__autocomplete_metadata) === null || _options$__autocomple === void 0 ? void 0 : _options$__autocomple.userAgents) || [])[0]) === null || _ === void 0 ? void 0 : _.segment;\n  var extraOptions = optionsKey ? _defineProperty({}, optionsKey, Object.keys(((_options$__autocomple2 = options.__autocomplete_metadata) === null || _options$__autocomple2 === void 0 ? void 0 : _options$__autocomple2.options) || {})) : {};\n  return {\n    plugins: plugins.map(function (plugin) {\n      return {\n        name: plugin.name,\n        options: Object.keys(plugin.__autocomplete_pluginOptions || [])\n      };\n    }),\n    options: _objectSpread({\n      'autocomplete-core': Object.keys(options)\n    }, extraOptions),\n    ua: userAgents.concat(((_options$__autocomple3 = options.__autocomplete_metadata) === null || _options$__autocomple3 === void 0 ? void 0 : _options$__autocomple3.userAgents) || [])\n  };\n}\nexport function injectMetadata(_ref3) {\n  var _environment$navigato, _environment$navigato2;\n  var metadata = _ref3.metadata,\n    environment = _ref3.environment;\n  var isMetadataEnabled = (_environment$navigato = environment.navigator) === null || _environment$navigato === void 0 ? void 0 : (_environment$navigato2 = _environment$navigato.userAgent) === null || _environment$navigato2 === void 0 ? void 0 : _environment$navigato2.includes('Algolia Crawler');\n  if (isMetadataEnabled) {\n    var metadataContainer = environment.document.createElement('meta');\n    var headRef = environment.document.querySelector('head');\n    metadataContainer.name = 'algolia:metadata';\n    setTimeout(function () {\n      metadataContainer.content = JSON.stringify(metadata);\n      headRef.appendChild(metadataContainer);\n    }, 0);\n  }\n}", "import { getActiveItem } from './utils';\nexport function getCompletion(_ref) {\n  var _getActiveItem;\n  var state = _ref.state;\n  if (state.isOpen === false || state.activeItemId === null) {\n    return null;\n  }\n  return ((_getActiveItem = getActiveItem(state)) === null || _getActiveItem === void 0 ? void 0 : _getActiveItem.itemInputValue) || null;\n}", "function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nimport { getItemsCount, invariant } from '@algolia/autocomplete-shared';\nimport { getCompletion } from './getCompletion';\nimport { getNextActiveItemId } from './utils';\nexport var stateReducer = function stateReducer(state, action) {\n  switch (action.type) {\n    case 'setActiveItemId':\n      {\n        return _objectSpread(_objectSpread({}, state), {}, {\n          activeItemId: action.payload\n        });\n      }\n    case 'setQuery':\n      {\n        return _objectSpread(_objectSpread({}, state), {}, {\n          query: action.payload,\n          completion: null\n        });\n      }\n    case 'setCollections':\n      {\n        return _objectSpread(_objectSpread({}, state), {}, {\n          collections: action.payload\n        });\n      }\n    case 'setIsOpen':\n      {\n        return _objectSpread(_objectSpread({}, state), {}, {\n          isOpen: action.payload\n        });\n      }\n    case 'setStatus':\n      {\n        return _objectSpread(_objectSpread({}, state), {}, {\n          status: action.payload\n        });\n      }\n    case 'setContext':\n      {\n        return _objectSpread(_objectSpread({}, state), {}, {\n          context: _objectSpread(_objectSpread({}, state.context), action.payload)\n        });\n      }\n    case 'ArrowDown':\n      {\n        var nextState = _objectSpread(_objectSpread({}, state), {}, {\n          activeItemId: action.payload.hasOwnProperty('nextActiveItemId') ? action.payload.nextActiveItemId : getNextActiveItemId(1, state.activeItemId, getItemsCount(state), action.props.defaultActiveItemId)\n        });\n        return _objectSpread(_objectSpread({}, nextState), {}, {\n          completion: getCompletion({\n            state: nextState\n          })\n        });\n      }\n    case 'ArrowUp':\n      {\n        var _nextState = _objectSpread(_objectSpread({}, state), {}, {\n          activeItemId: getNextActiveItemId(-1, state.activeItemId, getItemsCount(state), action.props.defaultActiveItemId)\n        });\n        return _objectSpread(_objectSpread({}, _nextState), {}, {\n          completion: getCompletion({\n            state: _nextState\n          })\n        });\n      }\n    case 'Escape':\n      {\n        if (state.isOpen) {\n          return _objectSpread(_objectSpread({}, state), {}, {\n            activeItemId: null,\n            isOpen: false,\n            completion: null\n          });\n        }\n        return _objectSpread(_objectSpread({}, state), {}, {\n          activeItemId: null,\n          query: '',\n          status: 'idle',\n          collections: []\n        });\n      }\n    case 'submit':\n      {\n        return _objectSpread(_objectSpread({}, state), {}, {\n          activeItemId: null,\n          isOpen: false,\n          status: 'idle'\n        });\n      }\n    case 'reset':\n      {\n        return _objectSpread(_objectSpread({}, state), {}, {\n          activeItemId:\n          // Since we open the panel on reset when openOnFocus=true\n          // we need to restore the highlighted index to the defaultActiveItemId. (DocSearch use-case)\n\n          // Since we close the panel when openOnFocus=false\n          // we lose track of the highlighted index. (Query-suggestions use-case)\n          action.props.openOnFocus === true ? action.props.defaultActiveItemId : null,\n          status: 'idle',\n          completion: null,\n          query: ''\n        });\n      }\n    case 'focus':\n      {\n        return _objectSpread(_objectSpread({}, state), {}, {\n          activeItemId: action.props.defaultActiveItemId,\n          isOpen: (action.props.openOnFocus || Boolean(state.query)) && action.props.shouldPanelOpen({\n            state: state\n          })\n        });\n      }\n    case 'blur':\n      {\n        if (action.props.debug) {\n          return state;\n        }\n        return _objectSpread(_objectSpread({}, state), {}, {\n          isOpen: false,\n          activeItemId: null\n        });\n      }\n    case 'mousemove':\n      {\n        return _objectSpread(_objectSpread({}, state), {}, {\n          activeItemId: action.payload\n        });\n      }\n    case 'mouseleave':\n      {\n        return _objectSpread(_objectSpread({}, state), {}, {\n          activeItemId: action.props.defaultActiveItemId\n        });\n      }\n    default:\n      invariant(false, \"The reducer action \".concat(JSON.stringify(action.type), \" is not supported.\"));\n      return state;\n  }\n};", "function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nimport { createAlgoliaInsightsPlugin } from '@algolia/autocomplete-plugin-algolia-insights';\nimport { checkOptions } from './checkOptions';\nimport { createStore } from './createStore';\nimport { getAutocompleteSetters } from './getAutocompleteSetters';\nimport { getDefaultProps } from './getDefaultProps';\nimport { getPropGetters } from './getPropGetters';\nimport { getMetadata, injectMetadata } from './metadata';\nimport { onInput } from './onInput';\nimport { stateReducer } from './stateReducer';\nexport function createAutocomplete(options) {\n  checkOptions(options);\n  var subscribers = [];\n  var props = getDefaultProps(options, subscribers);\n  var store = createStore(stateReducer, props, onStoreStateChange);\n  var setters = getAutocompleteSetters({\n    store: store\n  });\n  var propGetters = getPropGetters(_objectSpread({\n    props: props,\n    refresh: refresh,\n    store: store,\n    navigator: props.navigator\n  }, setters));\n  function onStoreStateChange(_ref) {\n    var _state$context, _state$context$algoli;\n    var prevState = _ref.prevState,\n      state = _ref.state;\n    props.onStateChange(_objectSpread({\n      prevState: prevState,\n      state: state,\n      refresh: refresh,\n      navigator: props.navigator\n    }, setters));\n    if (!isAlgoliaInsightsPluginEnabled() && (_state$context = state.context) !== null && _state$context !== void 0 && (_state$context$algoli = _state$context.algoliaInsightsPlugin) !== null && _state$context$algoli !== void 0 && _state$context$algoli.__automaticInsights && props.insights !== false) {\n      var plugin = createAlgoliaInsightsPlugin({\n        __autocomplete_clickAnalytics: false\n      });\n      props.plugins.push(plugin);\n      subscribePlugins([plugin]);\n    }\n  }\n  function refresh() {\n    return onInput(_objectSpread({\n      event: new Event('input'),\n      nextState: {\n        isOpen: store.getState().isOpen\n      },\n      props: props,\n      navigator: props.navigator,\n      query: store.getState().query,\n      refresh: refresh,\n      store: store\n    }, setters));\n  }\n  function subscribePlugins(plugins) {\n    plugins.forEach(function (plugin) {\n      var _plugin$subscribe;\n      return (_plugin$subscribe = plugin.subscribe) === null || _plugin$subscribe === void 0 ? void 0 : _plugin$subscribe.call(plugin, _objectSpread(_objectSpread({}, setters), {}, {\n        navigator: props.navigator,\n        refresh: refresh,\n        onSelect: function onSelect(fn) {\n          subscribers.push({\n            onSelect: fn\n          });\n        },\n        onActive: function onActive(fn) {\n          subscribers.push({\n            onActive: fn\n          });\n        },\n        onResolve: function onResolve(fn) {\n          subscribers.push({\n            onResolve: fn\n          });\n        }\n      }));\n    });\n  }\n  function isAlgoliaInsightsPluginEnabled() {\n    return props.plugins.some(function (plugin) {\n      return plugin.name === 'aa.algoliaInsightsPlugin';\n    });\n  }\n  if (props.insights && !isAlgoliaInsightsPluginEnabled()) {\n    var insightsParams = typeof props.insights === 'boolean' ? {} : props.insights;\n    props.plugins.push(createAlgoliaInsightsPlugin(insightsParams));\n  }\n  subscribePlugins(props.plugins);\n  injectMetadata({\n    metadata: getMetadata({\n      plugins: props.plugins,\n      options: options\n    }),\n    environment: props.environment\n  });\n  return _objectSpread(_objectSpread({\n    refresh: refresh,\n    navigator: props.navigator\n  }, propGetters), setters);\n}", "import React from 'react';\n\ntype AlgoliaLogoTranslations = Partial<{\n  searchByText: string;\n}>;\n\ntype AlgoliaLogoProps = {\n  translations?: AlgoliaLogoTranslations;\n};\n\nexport function AlgoliaLogo({ translations = {} }: AlgoliaLogoProps): JSX.Element {\n  const { searchByText = 'Search by' } = translations;\n\n  return (\n    <a\n      href={`https://www.algolia.com/ref/docsearch/?utm_source=${window.location.hostname}&utm_medium=referral&utm_content=powered_by&utm_campaign=docsearch`}\n      target=\"_blank\"\n      rel=\"noopener noreferrer\"\n    >\n      <span className=\"DocSearch-Label\">{searchByText}</span>\n      <svg\n        width=\"77\"\n        height=\"19\"\n        aria-label=\"Algolia\"\n        role=\"img\"\n        id=\"Layer_1\"\n        xmlns=\"http://www.w3.org/2000/svg\"\n        viewBox=\"0 0 2196.2 500\"\n      >\n        <defs>\n          <style>{'.cls-1,.cls-2{fill:#003dff;}.cls-2{fill-rule:evenodd;}'}</style>\n        </defs>\n        <path\n          className=\"cls-2\"\n          d=\"M1070.38,275.3V5.91c0-3.63-3.24-6.39-6.82-5.83l-50.46,7.94c-2.87,.45-4.99,2.93-4.99,5.84l.17,273.22c0,12.92,0,92.7,95.97,95.49,3.33,.1,6.09-2.58,6.09-5.91v-40.78c0-2.96-2.19-5.51-5.12-5.84-34.85-4.01-34.85-47.57-34.85-54.72Z\"\n        />\n        <rect className=\"cls-1\" x=\"1845.88\" y=\"104.73\" width=\"62.58\" height=\"277.9\" rx=\"5.9\" ry=\"5.9\" />\n        <path\n          className=\"cls-2\"\n          d=\"M1851.78,71.38h50.77c3.26,0,5.9-2.64,5.9-5.9V5.9c0-3.62-3.24-6.39-6.82-5.83l-50.77,7.95c-2.87,.45-4.99,2.92-4.99,5.83v51.62c0,3.26,2.64,5.9,5.9,5.9Z\"\n        />\n        <path\n          className=\"cls-2\"\n          d=\"M1764.03,275.3V5.91c0-3.63-3.24-6.39-6.82-5.83l-50.46,7.94c-2.87,.45-4.99,2.93-4.99,5.84l.17,273.22c0,12.92,0,92.7,95.97,95.49,3.33,.1,6.09-2.58,6.09-5.91v-40.78c0-2.96-2.19-5.51-5.12-5.84-34.85-4.01-34.85-47.57-34.85-54.72Z\"\n        />\n        <path\n          className=\"cls-2\"\n          d=\"M1631.95,142.72c-11.14-12.25-24.83-21.65-40.78-28.31-15.92-6.53-33.26-9.85-52.07-9.85-18.78,0-36.15,3.17-51.92,9.85-15.59,6.66-29.29,16.05-40.76,28.31-11.47,12.23-20.38,26.87-26.76,44.03-6.38,17.17-9.24,37.37-9.24,58.36,0,20.99,3.19,36.87,9.55,54.21,6.38,17.32,15.14,32.11,26.45,44.36,11.29,12.23,24.83,21.62,40.6,28.46,15.77,6.83,40.12,10.33,52.4,10.48,12.25,0,36.78-3.82,52.7-10.48,15.92-6.68,29.46-16.23,40.78-28.46,11.29-12.25,20.05-27.04,26.25-44.36,6.22-17.34,9.24-33.22,9.24-54.21,0-20.99-3.34-41.19-10.03-58.36-6.38-17.17-15.14-31.8-26.43-44.03Zm-44.43,163.75c-11.47,15.75-27.56,23.7-48.09,23.7-20.55,0-36.63-7.8-48.1-23.7-11.47-15.75-17.21-34.01-17.21-61.2,0-26.89,5.59-49.14,17.06-64.87,11.45-15.75,27.54-23.52,48.07-23.52,20.55,0,36.63,7.78,48.09,23.52,11.47,15.57,17.36,37.98,17.36,64.87,0,27.19-5.72,45.3-17.19,61.2Z\"\n        />\n        <path\n          className=\"cls-2\"\n          d=\"M894.42,104.73h-49.33c-48.36,0-90.91,25.48-115.75,64.1-14.52,22.58-22.99,49.63-22.99,78.73,0,44.89,20.13,84.92,51.59,111.1,2.93,2.6,6.05,4.98,9.31,7.14,12.86,8.49,28.11,13.47,44.52,13.47,1.23,0,2.46-.03,3.68-.09,.36-.02,.71-.05,1.07-.07,.87-.05,1.75-.11,2.62-.2,.34-.03,.68-.08,1.02-.12,.91-.1,1.82-.21,2.73-.34,.21-.03,.42-.07,.63-.1,32.89-5.07,61.56-30.82,70.9-62.81v57.83c0,3.26,2.64,5.9,5.9,5.9h50.42c3.26,0,5.9-2.64,5.9-5.9V110.63c0-3.26-2.64-5.9-5.9-5.9h-56.32Zm0,206.92c-12.2,10.16-27.97,13.98-44.84,15.12-.16,.01-.33,.03-.49,.04-1.12,.07-2.24,.1-3.36,.1-42.24,0-77.12-35.89-77.12-79.37,0-10.25,1.96-20.01,5.42-28.98,11.22-29.12,38.77-49.74,71.06-49.74h49.33v142.83Z\"\n        />\n        <path\n          className=\"cls-2\"\n          d=\"M2133.97,104.73h-49.33c-48.36,0-90.91,25.48-115.75,64.1-14.52,22.58-22.99,49.63-22.99,78.73,0,44.89,20.13,84.92,51.59,111.1,2.93,2.6,6.05,4.98,9.31,7.14,12.86,8.49,28.11,13.47,44.52,13.47,1.23,0,2.46-.03,3.68-.09,.36-.02,.71-.05,1.07-.07,.87-.05,1.75-.11,2.62-.2,.34-.03,.68-.08,1.02-.12,.91-.1,1.82-.21,2.73-.34,.21-.03,.42-.07,.63-.1,32.89-5.07,61.56-30.82,70.9-62.81v57.83c0,3.26,2.64,5.9,5.9,5.9h50.42c3.26,0,5.9-2.64,5.9-5.9V110.63c0-3.26-2.64-5.9-5.9-5.9h-56.32Zm0,206.92c-12.2,10.16-27.97,13.98-44.84,15.12-.16,.01-.33,.03-.49,.04-1.12,.07-2.24,.1-3.36,.1-42.24,0-77.12-35.89-77.12-79.37,0-10.25,1.96-20.01,5.42-28.98,11.22-29.12,38.77-49.74,71.06-49.74h49.33v142.83Z\"\n        />\n        <path\n          className=\"cls-2\"\n          d=\"M1314.05,104.73h-49.33c-48.36,0-90.91,25.48-115.75,64.1-11.79,18.34-19.6,39.64-22.11,62.59-.58,5.3-.88,10.68-.88,16.14s.31,11.15,.93,16.59c4.28,38.09,23.14,71.61,50.66,94.52,2.93,2.6,6.05,4.98,9.31,7.14,12.86,8.49,28.11,13.47,44.52,13.47h0c17.99,0,34.61-5.93,48.16-15.97,16.29-11.58,28.88-28.54,34.48-47.75v50.26h-.11v11.08c0,21.84-5.71,38.27-17.34,49.36-11.61,11.08-31.04,16.63-58.25,16.63-11.12,0-28.79-.59-46.6-2.41-2.83-.29-5.46,1.5-6.27,4.22l-12.78,43.11c-1.02,3.46,1.27,7.02,4.83,7.53,21.52,3.08,42.52,4.68,54.65,4.68,48.91,0,85.16-10.75,108.89-32.21,21.48-19.41,33.15-48.89,35.2-88.52V110.63c0-3.26-2.64-5.9-5.9-5.9h-56.32Zm0,64.1s.65,139.13,0,143.36c-12.08,9.77-27.11,13.59-43.49,14.7-.16,.01-.33,.03-.49,.04-1.12,.07-2.24,.1-3.36,.1-1.32,0-2.63-.03-3.94-.1-40.41-2.11-74.52-37.26-74.52-79.38,0-10.25,1.96-20.01,5.42-28.98,11.22-29.12,38.77-49.74,71.06-49.74h49.33Z\"\n        />\n        <path\n          className=\"cls-1\"\n          d=\"M249.83,0C113.3,0,2,110.09,.03,246.16c-2,138.19,110.12,252.7,248.33,253.5,42.68,.25,83.79-10.19,120.3-30.03,3.56-1.93,4.11-6.83,1.08-9.51l-23.38-20.72c-4.75-4.21-11.51-5.4-17.36-2.92-25.48,10.84-53.17,16.38-81.71,16.03-111.68-1.37-201.91-94.29-200.13-205.96,1.76-110.26,92-199.41,202.67-199.41h202.69V407.41l-115-102.18c-3.72-3.31-9.42-2.66-12.42,1.31-18.46,24.44-48.53,39.64-81.93,37.34-46.33-3.2-83.87-40.5-87.34-86.81-4.15-55.24,39.63-101.52,94-101.52,49.18,0,89.68,37.85,93.91,85.95,.38,4.28,2.31,8.27,5.52,11.12l29.95,26.55c3.4,3.01,8.79,1.17,9.63-3.3,2.16-11.55,2.92-23.58,2.07-35.92-4.82-70.34-61.8-126.93-132.17-131.26-80.68-4.97-148.13,58.14-150.27,137.25-2.09,77.1,61.08,143.56,138.19,145.26,32.19,.71,62.03-9.41,86.14-26.95l150.26,133.2c6.44,5.71,16.61,1.14,16.61-7.47V9.48C499.66,4.25,495.42,0,490.18,0H249.83Z\"\n        />\n      </svg>\n    </a>\n  );\n}\n", "import React from 'react';\n\nimport { AlgoliaLogo } from './AlgoliaLogo';\n\nexport type FooterTranslations = Partial<{\n  selectText: string;\n  selectKeyAriaLabel: string;\n  navigateText: string;\n  navigateUpKeyAriaLabel: string;\n  navigateDownKeyAriaLabel: string;\n  closeText: string;\n  closeKeyAriaLabel: string;\n  searchByText: string;\n}>;\n\ntype FooterProps = Partial<{\n  translations: FooterTranslations;\n}>;\n\ninterface CommandIconProps {\n  children: React.ReactNode;\n  ariaLabel: string;\n}\n\nfunction CommandIcon(props: CommandIconProps): JSX.Element {\n  return (\n    <svg width=\"15\" height=\"15\" aria-label={props.ariaLabel} role=\"img\">\n      <g fill=\"none\" stroke=\"currentColor\" strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"1.2\">\n        {props.children}\n      </g>\n    </svg>\n  );\n}\n\nexport function Footer({ translations = {} }: FooterProps): JSX.Element {\n  const {\n    selectText = 'to select',\n    selectKeyAriaLabel = 'Enter key',\n    navigateText = 'to navigate',\n    navigateUpKeyAriaLabel = 'Arrow up',\n    navigateDownKeyAriaLabel = 'Arrow down',\n    closeText = 'to close',\n    closeKeyAriaLabel = 'Escape key',\n    searchByText = 'Search by',\n  } = translations;\n\n  return (\n    <>\n      <div className=\"DocSearch-Logo\">\n        <AlgoliaLogo translations={{ searchByText }} />\n      </div>\n      <ul className=\"DocSearch-Commands\">\n        <li>\n          <kbd className=\"DocSearch-Commands-Key\">\n            <CommandIcon ariaLabel={selectKeyAriaLabel}>\n              <path d=\"M12 3.53088v3c0 1-1 2-2 2H4M7 11.53088l-3-3 3-3\" />\n            </CommandIcon>\n          </kbd>\n          <span className=\"DocSearch-Label\">{selectText}</span>\n        </li>\n        <li>\n          <kbd className=\"DocSearch-Commands-Key\">\n            <CommandIcon ariaLabel={navigateDownKeyAriaLabel}>\n              <path d=\"M7.5 3.5v8M10.5 8.5l-3 3-3-3\" />\n            </CommandIcon>\n          </kbd>\n          <kbd className=\"DocSearch-Commands-Key\">\n            <CommandIcon ariaLabel={navigateUpKeyAriaLabel}>\n              <path d=\"M7.5 11.5v-8M10.5 6.5l-3-3-3 3\" />\n            </CommandIcon>\n          </kbd>\n          <span className=\"DocSearch-Label\">{navigateText}</span>\n        </li>\n        <li>\n          <kbd className=\"DocSearch-Commands-Key\">\n            <CommandIcon ariaLabel={closeKeyAriaLabel}>\n              <path d=\"M13.6167 8.936c-.1065.3583-.6883.962-1.4875.962-.7993 0-1.653-.9165-1.653-2.1258v-.5678c0-1.2548.7896-2.1016 1.653-2.1016.8634 0 1.3601.4778 1.4875 1.0724M9 6c-.1352-.4735-.7506-.9219-1.46-.8972-.7092.0246-1.344.57-1.344 1.2166s.4198.8812 1.3445.9805C8.465 7.3992 8.968 7.9337 9 8.5c.032.5663-.454 1.398-1.4595 1.398C6.6593 9.898 6 9 5.963 8.4851m-1.4748.5368c-.2635.5941-.8099.876-1.5443.876s-1.7073-.6248-1.7073-2.204v-.4603c0-1.0416.721-2.131 1.7073-2.131.9864 0 1.6425 1.031 1.5443 2.2492h-2.956\" />\n            </CommandIcon>\n          </kbd>\n          <span className=\"DocSearch-Label\">{closeText}</span>\n        </li>\n      </ul>\n    </>\n  );\n}\n", "import React from 'react';\n\nimport type { InternalDocSearchHit, StoredDocSearchHit } from './types';\n\ninterface HitProps {\n  hit: InternalDocSearchHit | StoredDocSearchHit;\n  children: React.ReactNode;\n}\n\nexport function Hit({ hit, children }: HitProps): JSX.Element {\n  return <a href={hit.url}>{children}</a>;\n}\n", "import React from 'react';\n\nexport function LoadingIcon(): JSX.Element {\n  return (\n    <svg viewBox=\"0 0 38 38\" stroke=\"currentColor\" strokeOpacity=\".5\">\n      <g fill=\"none\" fillRule=\"evenodd\">\n        <g transform=\"translate(1 1)\" strokeWidth=\"2\">\n          <circle strokeOpacity=\".3\" cx=\"18\" cy=\"18\" r=\"18\" />\n          <path d=\"M36 18c0-9.94-8.06-18-18-18\">\n            <animateTransform\n              attributeName=\"transform\"\n              type=\"rotate\"\n              from=\"0 18 18\"\n              to=\"360 18 18\"\n              dur=\"1s\"\n              repeatCount=\"indefinite\"\n            />\n          </path>\n        </g>\n      </g>\n    </svg>\n  );\n}\n", "import React from 'react';\n\nexport function RecentIcon(): JSX.Element {\n  return (\n    <svg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\">\n      <g stroke=\"currentColor\" fill=\"none\" fillRule=\"evenodd\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\n        <path d=\"M3.18 6.6a8.23 8.23 0 1112.93 9.94h0a8.23 8.23 0 01-11.63 0\" />\n        <path d=\"M6.44 7.25H2.55V3.36M10.45 6v5.6M10.45 11.6L13 13\" />\n      </g>\n    </svg>\n  );\n}\n", "import React from 'react';\n\nexport function ResetIcon(): JSX.Element {\n  return (\n    <svg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\">\n      <path\n        d=\"M10 10l5.09-5.09L10 10l5.09 5.09L10 10zm0 0L4.91 4.91 10 10l-5.09 5.09L10 10z\"\n        stroke=\"currentColor\"\n        fill=\"none\"\n        fillRule=\"evenodd\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n      />\n    </svg>\n  );\n}\n", "import React from 'react';\n\nexport function SelectIcon(): JSX.Element {\n  return (\n    <svg className=\"DocSearch-Hit-Select-Icon\" width=\"20\" height=\"20\" viewBox=\"0 0 20 20\">\n      <g stroke=\"currentColor\" fill=\"none\" fillRule=\"evenodd\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\n        <path d=\"M18 3v4c0 2-2 4-4 4H2\" />\n        <path d=\"M8 17l-6-6 6-6\" />\n      </g>\n    </svg>\n  );\n}\n", "import React from 'react';\n\nconst LvlIcon: React.FC = () => {\n  return (\n    <svg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\">\n      <path\n        d=\"M17 6v12c0 .52-.2 1-1 1H4c-.7 0-1-.33-1-1V2c0-.55.42-1 1-1h8l5 5zM14 8h-3.13c-.51 0-.87-.34-.87-.87V4\"\n        stroke=\"currentColor\"\n        fill=\"none\"\n        fillRule=\"evenodd\"\n        strokeLinejoin=\"round\"\n      />\n    </svg>\n  );\n};\n\nexport function SourceIcon(props: { type: string }): JSX.Element {\n  switch (props.type) {\n    case 'lvl1':\n      return <LvlIcon />;\n    case 'content':\n      return <ContentIcon />;\n    default:\n      return <AnchorIcon />;\n  }\n}\n\nfunction AnchorIcon(): JSX.Element {\n  return (\n    <svg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\">\n      <path\n        d=\"M13 13h4-4V8H7v5h6v4-4H7V8H3h4V3v5h6V3v5h4-4v5zm-6 0v4-4H3h4z\"\n        stroke=\"currentColor\"\n        fill=\"none\"\n        fillRule=\"evenodd\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n      />\n    </svg>\n  );\n}\n\nfunction ContentIcon(): JSX.Element {\n  return (\n    <svg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\">\n      <path\n        d=\"M17 5H3h14zm0 5H3h14zm0 5H3h14z\"\n        stroke=\"currentColor\"\n        fill=\"none\"\n        fillRule=\"evenodd\"\n        strokeLinejoin=\"round\"\n      />\n    </svg>\n  );\n}\n", "import React from 'react';\n\nexport function StarIcon(): JSX.Element {\n  return (\n    <svg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\">\n      <path\n        d=\"M10 14.2L5 17l1-5.6-4-4 5.5-.7 2.5-5 2.5 5 5.6.8-4 4 .9 5.5z\"\n        stroke=\"currentColor\"\n        fill=\"none\"\n        fillRule=\"evenodd\"\n        strokeLinejoin=\"round\"\n      />\n    </svg>\n  );\n}\n", "import React from 'react';\n\nexport function ErrorIcon(): JSX.Element {\n  return (\n    <svg\n      width=\"40\"\n      height=\"40\"\n      viewBox=\"0 0 20 20\"\n      fill=\"none\"\n      fillRule=\"evenodd\"\n      stroke=\"currentColor\"\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n    >\n      <path d=\"M19 4.8a16 16 0 00-2-1.2m-3.3-1.2A16 16 0 001.1 4.7M16.7 8a12 12 0 00-2.8-1.4M10 6a12 12 0 00-6.7 2M12.3 14.7a4 4 0 00-4.5 0M14.5 11.4A8 8 0 0010 10M3 16L18 2M10 18h0\"></path>\n    </svg>\n  );\n}\n", "import React from 'react';\n\nexport function NoResultsIcon(): JSX.Element {\n  return (\n    <svg\n      width=\"40\"\n      height=\"40\"\n      viewBox=\"0 0 20 20\"\n      fill=\"none\"\n      fillRule=\"evenodd\"\n      stroke=\"currentColor\"\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n    >\n      <path d=\"M15.5 4.8c2 3 1.7 7-1 9.7h0l4.3 4.3-4.3-4.3a7.8 7.8 0 01-9.8 1m-2.2-2.2A7.8 7.8 0 0113.2 2.4M2 18L18 2\"></path>\n    </svg>\n  );\n}\n", "import React from 'react';\n\nimport { ErrorIcon } from './icons';\n\nexport type ErrorScreenTranslations = Partial<{\n  titleText: string;\n  helpText: string;\n}>;\n\ntype ErrorScreenProps = {\n  translations?: ErrorScreenTranslations;\n};\n\nexport function ErrorScreen({ translations = {} }: ErrorScreenProps): JSX.Element {\n  const { titleText = 'Unable to fetch results', helpText = 'You might want to check your network connection.' } =\n    translations;\n  return (\n    <div className=\"DocSearch-ErrorScreen\">\n      <div className=\"DocSearch-Screen-Icon\">\n        <ErrorIcon />\n      </div>\n      <p className=\"DocSearch-Title\">{titleText}</p>\n      <p className=\"DocSearch-Help\">{helpText}</p>\n    </div>\n  );\n}\n", "import React from 'react';\n\nimport { NoResultsIcon } from './icons';\nimport type { ScreenStateProps } from './ScreenState';\nimport type { InternalDocSearchHit } from './types';\n\nexport type NoResultsScreenTranslations = Partial<{\n  noResultsText: string;\n  suggestedQueryText: string;\n  reportMissingResultsText: string;\n  reportMissingResultsLinkText: string;\n}>;\n\ntype NoResultsScreenProps = Omit<ScreenStateProps<InternalDocSearchHit>, 'translations'> & {\n  translations?: NoResultsScreenTranslations;\n};\n\nexport function NoResultsScreen({ translations = {}, ...props }: NoResultsScreenProps): JSX.Element {\n  const {\n    noResultsText = 'No results for',\n    suggestedQueryText = 'Try searching for',\n    reportMissingResultsText = 'Believe this query should return results?',\n    reportMissingResultsLinkText = 'Let us know.',\n  } = translations;\n  const searchSuggestions: string[] | undefined = props.state.context.searchSuggestions as string[];\n\n  return (\n    <div className=\"DocSearch-NoResults\">\n      <div className=\"DocSearch-Screen-Icon\">\n        <NoResultsIcon />\n      </div>\n      <p className=\"DocSearch-Title\">\n        {noResultsText} \"<strong>{props.state.query}</strong>\"\n      </p>\n\n      {searchSuggestions && searchSuggestions.length > 0 && (\n        <div className=\"DocSearch-NoResults-Prefill-List\">\n          <p className=\"DocSearch-Help\">{suggestedQueryText}:</p>\n          <ul>\n            {searchSuggestions.slice(0, 3).reduce<React.ReactNode[]>(\n              (acc, search) => [\n                ...acc,\n                <li key={search}>\n                  <button\n                    className=\"DocSearch-Prefill\"\n                    key={search}\n                    type=\"button\"\n                    onClick={() => {\n                      props.setQuery(search.toLowerCase() + ' ');\n                      props.refresh();\n                      props.inputRef.current!.focus();\n                    }}\n                  >\n                    {search}\n                  </button>\n                </li>,\n              ],\n              [],\n            )}\n          </ul>\n        </div>\n      )}\n\n      {props.getMissingResultsUrl && (\n        <p className=\"DocSearch-Help\">\n          {`${reportMissingResultsText} `}\n          <a href={props.getMissingResultsUrl({ query: props.state.query })} target=\"_blank\" rel=\"noopener noreferrer\">\n            {reportMissingResultsLinkText}\n          </a>\n        </p>\n      )}\n    </div>\n  );\n}\n", "import { createElement } from 'react';\n\nimport type { StoredDocSearchHit } from './types';\n\nfunction getPropertyByPath(object: Record<string, any>, path: string): any {\n  const parts = path.split('.');\n\n  return parts.reduce((prev, current) => {\n    if (prev?.[current]) return prev[current];\n    return null;\n  }, object);\n}\n\ninterface SnippetProps<TItem> {\n  hit: TItem;\n  attribute: string;\n  tagName?: string;\n  [prop: string]: unknown;\n}\n\nexport function Snippet<TItem extends StoredDocSearchHit>({\n  hit,\n  attribute,\n  tagName = 'span',\n  ...rest\n}: SnippetProps<TItem>): JSX.Element {\n  return createElement(tagName, {\n    ...rest,\n    dangerouslySetInnerHTML: {\n      __html: getPropertyByPath(hit, `_snippetResult.${attribute}.value`) || getPropertyByPath(hit, attribute),\n    },\n  });\n}\n", "import type { Autocomplete<PERSON>pi, AutocompleteState, BaseItem } from '@algolia/autocomplete-core';\nimport React from 'react';\n\nimport type { DocSearchProps } from './DocSearch';\nimport { Snippet } from './Snippet';\nimport type { InternalDocSearchHit, StoredDocSearchHit } from './types';\n\ninterface ResultsProps<TItem extends BaseItem>\n  extends AutocompleteApi<TItem, React.FormEvent, React.MouseEvent, React.KeyboardEvent> {\n  title: string;\n  collection: AutocompleteState<TItem>['collections'][0];\n  renderIcon: (props: { item: TItem; index: number }) => React.ReactNode;\n  renderAction: (props: {\n    item: TItem;\n    runDeleteTransition: (cb: () => void) => void;\n    runFavoriteTransition: (cb: () => void) => void;\n  }) => React.ReactNode;\n  onItemClick: (item: TItem, event: KeyboardEvent | MouseEvent) => void;\n  hitComponent: DocSearchProps['hitComponent'];\n}\n\nexport function Results<TItem extends StoredDocSearchHit>(props: ResultsProps<TItem>): JSX.Element | null {\n  if (!props.collection || props.collection.items.length === 0) {\n    return null;\n  }\n\n  return (\n    <section className=\"DocSearch-Hits\">\n      <div className=\"DocSearch-Hit-source\">{props.title}</div>\n\n      <ul {...props.getListProps()}>\n        {props.collection.items.map((item, index) => {\n          return <Result key={[props.title, item.objectID].join(':')} item={item} index={index} {...props} />;\n        })}\n      </ul>\n    </section>\n  );\n}\n\ninterface ResultProps<TItem extends BaseItem> extends ResultsProps<TItem> {\n  item: TItem;\n  index: number;\n}\n\nfunction Result<TItem extends StoredDocSearchHit>({\n  item,\n  index,\n  renderIcon,\n  renderAction,\n  getItemProps,\n  onItemClick,\n  collection,\n  hitComponent,\n}: ResultProps<TItem>): JSX.Element {\n  const [isDeleting, setIsDeleting] = React.useState(false);\n  const [isFavoriting, setIsFavoriting] = React.useState(false);\n  const action = React.useRef<(() => void) | null>(null);\n  const Hit = hitComponent!;\n\n  function runDeleteTransition(cb: () => void): void {\n    setIsDeleting(true);\n    action.current = cb;\n  }\n\n  function runFavoriteTransition(cb: () => void): void {\n    setIsFavoriting(true);\n    action.current = cb;\n  }\n\n  return (\n    <li\n      className={[\n        'DocSearch-Hit',\n        (item as unknown as InternalDocSearchHit).__docsearch_parent && 'DocSearch-Hit--Child',\n        isDeleting && 'DocSearch-Hit--deleting',\n        isFavoriting && 'DocSearch-Hit--favoriting',\n      ]\n        .filter(Boolean)\n        .join(' ')}\n      onTransitionEnd={() => {\n        if (action.current) {\n          action.current();\n        }\n      }}\n      {...getItemProps({\n        item,\n        source: collection.source,\n        onClick(event) {\n          onItemClick(item, event);\n        },\n      })}\n    >\n      <Hit hit={item}>\n        <div className=\"DocSearch-Hit-Container\">\n          {renderIcon({ item, index })}\n\n          {item.hierarchy[item.type] && item.type === 'lvl1' && (\n            <div className=\"DocSearch-Hit-content-wrapper\">\n              <Snippet className=\"DocSearch-Hit-title\" hit={item} attribute=\"hierarchy.lvl1\" />\n              {item.content && <Snippet className=\"DocSearch-Hit-path\" hit={item} attribute=\"content\" />}\n            </div>\n          )}\n\n          {item.hierarchy[item.type] &&\n            (item.type === 'lvl2' ||\n              item.type === 'lvl3' ||\n              item.type === 'lvl4' ||\n              item.type === 'lvl5' ||\n              item.type === 'lvl6') && (\n              <div className=\"DocSearch-Hit-content-wrapper\">\n                <Snippet className=\"DocSearch-Hit-title\" hit={item} attribute={`hierarchy.${item.type}`} />\n                <Snippet className=\"DocSearch-Hit-path\" hit={item} attribute=\"hierarchy.lvl1\" />\n              </div>\n            )}\n\n          {item.type === 'content' && (\n            <div className=\"DocSearch-Hit-content-wrapper\">\n              <Snippet className=\"DocSearch-Hit-title\" hit={item} attribute=\"content\" />\n              <Snippet className=\"DocSearch-Hit-path\" hit={item} attribute=\"hierarchy.lvl1\" />\n            </div>\n          )}\n\n          {renderAction({ item, runDeleteTransition, runFavoriteTransition })}\n        </div>\n      </Hit>\n    </li>\n  );\n}\n", "export function groupBy<TValue extends Record<string, unknown>>(\n  values: TValue[],\n  predicate: (value: TValue) => string,\n  maxResultsPerGroup?: number,\n): Record<string, TValue[]> {\n  return values.reduce<Record<string, TValue[]>>((acc, item) => {\n    const key = predicate(item);\n\n    if (!acc.hasOwnProperty(key)) {\n      acc[key] = [];\n    }\n\n    // We limit each section to show 5 hits maximum.\n    // This acts as a frontend alternative to `distinct`.\n    if (acc[key].length < (maxResultsPerGroup || 5)) {\n      acc[key].push(item);\n    }\n\n    return acc;\n  }, {});\n}\n", "export function identity<TParam>(x: TParam): TParam {\n  return x;\n}\n", "/**\n * Detect when an event is modified with a special key to let the browser\n * trigger its default behavior.\n */\nexport function isModifierEvent<TEvent extends KeyboardEvent | MouseEvent>(event: TEvent): boolean {\n  const isMiddleClick = (event as MouseEvent).button === 1;\n\n  return isMiddleClick || event.altKey || event.ctrlKey || event.metaKey || event.shiftKey;\n}\n", "export function noop(..._args: any[]): void {}\n", "import type { DocSearchHit, InternalDocSearchHit } from '../types';\n\nconst regexHighlightTags = /(<mark>|<\\/mark>)/g;\nconst regexHasHighlightTags = RegExp(regexHighlightTags.source);\n\nexport function removeHighlightTags(hit: DocSearchHit | InternalDocSearchHit): string {\n  const internalDocSearchHit = hit as InternalDocSearchHit;\n\n  if (!internalDocSearchHit.__docsearch_parent && !hit._highlightResult) {\n    return hit.hierarchy.lvl0;\n  }\n\n  const lvl0 = internalDocSearchHit.__docsearch_parent\n    ? internalDocSearchHit.__docsearch_parent?._highlightResult?.hierarchy?.lvl0\n    : hit._highlightResult?.hierarchy?.lvl0;\n\n  if (!lvl0) {\n    return hit.hierarchy.lvl0;\n  }\n\n  return lvl0.value && regexHasHighlightTags.test(lvl0.value) ? lvl0.value.replace(regexHighlightTags, '') : lvl0.value;\n}\n", "import React from 'react';\n\nimport { SelectIcon, SourceIcon } from './icons';\nimport { Results } from './Results';\nimport type { ScreenStateProps } from './ScreenState';\nimport type { InternalDocSearchHit } from './types';\nimport { removeHighlightTags } from './utils';\n\ntype ResultsScreenProps = Omit<ScreenStateProps<InternalDocSearchHit>, 'translations'>;\n\nexport function ResultsScreen(props: ResultsScreenProps): JSX.Element {\n  return (\n    <div className=\"DocSearch-Dropdown-Container\">\n      {props.state.collections.map((collection) => {\n        if (collection.items.length === 0) {\n          return null;\n        }\n\n        const title = removeHighlightTags(collection.items[0]);\n\n        return (\n          <Results\n            {...props}\n            key={collection.source.sourceId}\n            title={title}\n            collection={collection}\n            renderIcon={({ item, index }) => (\n              <>\n                {item.__docsearch_parent && (\n                  <svg className=\"DocSearch-Hit-Tree\" viewBox=\"0 0 24 54\">\n                    <g\n                      stroke=\"currentColor\"\n                      fill=\"none\"\n                      fillRule=\"evenodd\"\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                    >\n                      {item.__docsearch_parent !== collection.items[index + 1]?.__docsearch_parent ? (\n                        <path d=\"M8 6v21M20 27H8.3\" />\n                      ) : (\n                        <path d=\"M8 6v42M20 27H8.3\" />\n                      )}\n                    </g>\n                  </svg>\n                )}\n\n                <div className=\"DocSearch-Hit-icon\">\n                  <SourceIcon type={item.type} />\n                </div>\n              </>\n            )}\n            renderAction={() => (\n              <div className=\"DocSearch-Hit-action\">\n                <SelectIcon />\n              </div>\n            )}\n          />\n        );\n      })}\n\n      {props.resultsFooterComponent && (\n        <section className=\"DocSearch-HitsFooter\">\n          <props.resultsFooterComponent state={props.state} />\n        </section>\n      )}\n    </div>\n  );\n}\n", "import React from 'react';\n\nimport { RecentIcon, ResetIcon, StarIcon } from './icons';\nimport { Results } from './Results';\nimport type { ScreenStateProps } from './ScreenState';\nimport type { InternalDocSearchHit } from './types';\n\nexport type StartScreenTranslations = Partial<{\n  recentSearchesTitle: string;\n  noRecentSearchesText: string;\n  saveRecentSearchButtonTitle: string;\n  removeRecentSearchButtonTitle: string;\n  favoriteSearchesTitle: string;\n  removeFavoriteSearchButtonTitle: string;\n}>;\n\ntype StartScreenProps = Omit<ScreenStateProps<InternalDocSearchHit>, 'translations'> & {\n  hasCollections: boolean;\n  translations?: StartScreenTranslations;\n};\n\nexport function StartScreen({ translations = {}, ...props }: StartScreenProps): JSX.Element | null {\n  const {\n    recentSearchesTitle = 'Recent',\n    noRecentSearchesText = 'No recent searches',\n    saveRecentSearchButtonTitle = 'Save this search',\n    removeRecentSearchButtonTitle = 'Remove this search from history',\n    favoriteSearchesTitle = 'Favorite',\n    removeFavoriteSearchButtonTitle = 'Remove this search from favorites',\n  } = translations;\n  if (props.state.status === 'idle' && props.hasCollections === false) {\n    if (props.disableUserPersonalization) {\n      return null;\n    }\n\n    return (\n      <div className=\"DocSearch-StartScreen\">\n        <p className=\"DocSearch-Help\">{noRecentSearchesText}</p>\n      </div>\n    );\n  }\n\n  if (props.hasCollections === false) {\n    return null;\n  }\n\n  return (\n    <div className=\"DocSearch-Dropdown-Container\">\n      <Results\n        {...props}\n        title={recentSearchesTitle}\n        collection={props.state.collections[0]}\n        renderIcon={() => (\n          <div className=\"DocSearch-Hit-icon\">\n            <RecentIcon />\n          </div>\n        )}\n        renderAction={({ item, runFavoriteTransition, runDeleteTransition }) => (\n          <>\n            <div className=\"DocSearch-Hit-action\">\n              <button\n                className=\"DocSearch-Hit-action-button\"\n                title={saveRecentSearchButtonTitle}\n                type=\"submit\"\n                onClick={(event) => {\n                  event.preventDefault();\n                  event.stopPropagation();\n                  runFavoriteTransition(() => {\n                    props.favoriteSearches.add(item);\n                    props.recentSearches.remove(item);\n                    props.refresh();\n                  });\n                }}\n              >\n                <StarIcon />\n              </button>\n            </div>\n            <div className=\"DocSearch-Hit-action\">\n              <button\n                className=\"DocSearch-Hit-action-button\"\n                title={removeRecentSearchButtonTitle}\n                type=\"submit\"\n                onClick={(event) => {\n                  event.preventDefault();\n                  event.stopPropagation();\n                  runDeleteTransition(() => {\n                    props.recentSearches.remove(item);\n                    props.refresh();\n                  });\n                }}\n              >\n                <ResetIcon />\n              </button>\n            </div>\n          </>\n        )}\n      />\n\n      <Results\n        {...props}\n        title={favoriteSearchesTitle}\n        collection={props.state.collections[1]}\n        renderIcon={() => (\n          <div className=\"DocSearch-Hit-icon\">\n            <StarIcon />\n          </div>\n        )}\n        renderAction={({ item, runDeleteTransition }) => (\n          <div className=\"DocSearch-Hit-action\">\n            <button\n              className=\"DocSearch-Hit-action-button\"\n              title={removeFavoriteSearchButtonTitle}\n              type=\"submit\"\n              onClick={(event) => {\n                event.preventDefault();\n                event.stopPropagation();\n                runDeleteTransition(() => {\n                  props.favoriteSearches.remove(item);\n                  props.refresh();\n                });\n              }}\n            >\n              <ResetIcon />\n            </button>\n          </div>\n        )}\n      />\n    </div>\n  );\n}\n", "import type { Autocomplete<PERSON>pi, AutocompleteState, BaseItem } from '@algolia/autocomplete-core';\nimport React from 'react';\n\nimport type { DocSearchProps } from './DocSearch';\nimport type { ErrorScreenTranslations } from './ErrorScreen';\nimport { ErrorScreen } from './ErrorScreen';\nimport type { NoResultsScreenTranslations } from './NoResultsScreen';\nimport { NoResultsScreen } from './NoResultsScreen';\nimport { ResultsScreen } from './ResultsScreen';\nimport type { StartScreenTranslations } from './StartScreen';\nimport { StartScreen } from './StartScreen';\nimport type { StoredSearchPlugin } from './stored-searches';\nimport type { InternalDocSearchHit, StoredDocSearchHit } from './types';\n\nexport type ScreenStateTranslations = Partial<{\n  errorScreen: ErrorScreenTranslations;\n  startScreen: StartScreenTranslations;\n  noResultsScreen: NoResultsScreenTranslations;\n}>;\n\nexport interface ScreenStateProps<TItem extends BaseItem>\n  extends AutocompleteApi<TItem, React.FormEvent, React.MouseEvent, React.KeyboardEvent> {\n  state: AutocompleteState<TItem>;\n  recentSearches: StoredSearchPlugin<StoredDocSearchHit>;\n  favoriteSearches: StoredSearchPlugin<StoredDocSearchHit>;\n  onItemClick: (item: InternalDocSearchHit, event: KeyboardEvent | MouseEvent) => void;\n  inputRef: React.MutableRefObject<HTMLInputElement | null>;\n  hitComponent: DocSearchProps['hitComponent'];\n  indexName: DocSearchProps['indexName'];\n  disableUserPersonalization: boolean;\n  resultsFooterComponent: DocSearchProps['resultsFooterComponent'];\n  translations: ScreenStateTranslations;\n  getMissingResultsUrl?: DocSearchProps['getMissingResultsUrl'];\n}\n\nexport const ScreenState = React.memo(\n  ({ translations = {}, ...props }: ScreenStateProps<InternalDocSearchHit>) => {\n    if (props.state.status === 'error') {\n      return <ErrorScreen translations={translations?.errorScreen} />;\n    }\n\n    const hasCollections = props.state.collections.some((collection) => collection.items.length > 0);\n\n    if (!props.state.query) {\n      return <StartScreen {...props} hasCollections={hasCollections} translations={translations?.startScreen} />;\n    }\n\n    if (hasCollections === false) {\n      return <NoResultsScreen {...props} translations={translations?.noResultsScreen} />;\n    }\n\n    return <ResultsScreen {...props} />;\n  },\n  function areEqual(_prevProps, nextProps) {\n    // We don't update the screen when Autocomplete is loading or stalled to\n    // avoid UI flashes:\n    //  - Empty screen → Results screen\n    //  - NoResults screen → NoResults screen with another query\n    return nextProps.state.status === 'loading' || nextProps.state.status === 'stalled';\n  },\n);\n", "import type { Autocomplete<PERSON><PERSON>, AutocompleteState } from '@algolia/autocomplete-core';\nimport type { MutableRefObject } from 'react';\nimport React from 'react';\n\nimport { MAX_QUERY_SIZE } from './constants';\nimport { LoadingIcon } from './icons/LoadingIcon';\nimport { ResetIcon } from './icons/ResetIcon';\nimport { SearchIcon } from './icons/SearchIcon';\nimport type { InternalDocSearchHit } from './types';\n\nexport type SearchBoxTranslations = Partial<{\n  resetButtonTitle: string;\n  resetButtonAriaLabel: string;\n  cancelButtonText: string;\n  cancelButtonAriaLabel: string;\n  searchInputLabel: string;\n}>;\n\ninterface SearchBoxProps\n  extends AutocompleteApi<InternalDocSearchHit, React.FormEvent, React.MouseEvent, React.KeyboardEvent> {\n  state: AutocompleteState<InternalDocSearchHit>;\n  autoFocus: boolean;\n  inputRef: MutableRefObject<HTMLInputElement | null>;\n  onClose: () => void;\n  isFromSelection: boolean;\n  translations?: SearchBoxTranslations;\n}\n\nexport function SearchBox({ translations = {}, ...props }: SearchBoxProps): JSX.Element {\n  const {\n    resetButtonTitle = 'Clear the query',\n    resetButtonAriaLabel = 'Clear the query',\n    cancelButtonText = 'Cancel',\n    cancelButtonAriaLabel = 'Cancel',\n    searchInputLabel = 'Search',\n  } = translations;\n  const { onReset } = props.getFormProps({\n    inputElement: props.inputRef.current,\n  });\n\n  React.useEffect(() => {\n    if (props.autoFocus && props.inputRef.current) {\n      props.inputRef.current.focus();\n    }\n  }, [props.autoFocus, props.inputRef]);\n\n  React.useEffect(() => {\n    if (props.isFromSelection && props.inputRef.current) {\n      props.inputRef.current.select();\n    }\n  }, [props.isFromSelection, props.inputRef]);\n\n  return (\n    <>\n      <form\n        className=\"DocSearch-Form\"\n        onSubmit={(event) => {\n          event.preventDefault();\n        }}\n        onReset={onReset}\n      >\n        <label className=\"DocSearch-MagnifierLabel\" {...props.getLabelProps()}>\n          <SearchIcon />\n          <span className=\"DocSearch-VisuallyHiddenForAccessibility\">{searchInputLabel}</span>\n        </label>\n\n        <div className=\"DocSearch-LoadingIndicator\">\n          <LoadingIcon />\n        </div>\n\n        <input\n          className=\"DocSearch-Input\"\n          ref={props.inputRef}\n          {...props.getInputProps({\n            inputElement: props.inputRef.current!,\n            autoFocus: props.autoFocus,\n            maxLength: MAX_QUERY_SIZE,\n          })}\n        />\n\n        <button\n          type=\"reset\"\n          title={resetButtonTitle}\n          className=\"DocSearch-Reset\"\n          aria-label={resetButtonAriaLabel}\n          hidden={!props.state.query}\n        >\n          <ResetIcon />\n        </button>\n      </form>\n\n      <button className=\"DocSearch-Cancel\" type=\"reset\" aria-label={cancelButtonAriaLabel} onClick={props.onClose}>\n        {cancelButtonText}\n      </button>\n    </>\n  );\n}\n", "export const MAX_QUERY_SIZE = 64;\n", "import type { DocSearchHit, StoredDocSearchHit } from './types';\n\nfunction isLocalStorageSupported(): boolean {\n  const key = '__TEST_KEY__';\n\n  try {\n    localStorage.setItem(key, '');\n    localStorage.removeItem(key);\n\n    return true;\n  } catch {\n    return false;\n  }\n}\n\n// eslint-disable-next-line @typescript-eslint/explicit-function-return-type\nfunction createStorage<TItem>(key: string) {\n  if (isLocalStorageSupported() === false) {\n    return {\n      setItem(): void {},\n      getItem(): TItem[] {\n        return [];\n      },\n    };\n  }\n\n  return {\n    setItem(item: TItem[]): void {\n      return window.localStorage.setItem(key, JSON.stringify(item));\n    },\n    getItem(): TItem[] {\n      const item = window.localStorage.getItem(key);\n\n      return item ? JSON.parse(item) : [];\n    },\n  };\n}\n\ntype CreateStoredSearchesOptions = {\n  key: string;\n  limit?: number;\n};\n\nexport type StoredSearchPlugin<TItem> = {\n  add: (item: TItem) => void;\n  remove: (item: TItem) => void;\n  getAll: () => TItem[];\n};\n\nexport function createStoredSearches<TItem extends StoredDocSearchHit>({\n  key,\n  limit = 5,\n}: CreateStoredSearchesOptions): StoredSearchPlugin<TItem> {\n  const storage = createStorage<TItem>(key);\n  let items = storage.getItem().slice(0, limit);\n\n  return {\n    add(item: TItem): void {\n      const { _highlightResult, _snippetResult, ...hit } = item as unknown as DocSearchHit;\n\n      const isQueryAlreadySaved = items.findIndex((x) => x.objectID === hit.objectID);\n\n      if (isQueryAlreadySaved > -1) {\n        items.splice(isQueryAlreadySaved, 1);\n      }\n\n      items.unshift(hit as TItem);\n      items = items.slice(0, limit);\n\n      storage.setItem(items);\n    },\n    remove(item: TItem): void {\n      items = items.filter((x) => x.objectID !== item.objectID);\n\n      storage.setItem(items);\n    },\n    getAll(): TItem[] {\n      return items;\n    },\n  };\n}\n", "// src/cache/createBrowserLocalStorageCache.ts\nfunction createBrowserLocalStorageCache(options) {\n  let storage;\n  const namespaceKey = `algolia-client-js-${options.key}`;\n  function getStorage() {\n    if (storage === void 0) {\n      storage = options.localStorage || window.localStorage;\n    }\n    return storage;\n  }\n  function getNamespace() {\n    return JSON.parse(getStorage().getItem(namespaceKey) || \"{}\");\n  }\n  function setNamespace(namespace) {\n    getStorage().setItem(namespaceKey, JSON.stringify(namespace));\n  }\n  function removeOutdatedCacheItems() {\n    const timeToLive = options.timeToLive ? options.timeToLive * 1e3 : null;\n    const namespace = getNamespace();\n    const filteredNamespaceWithoutOldFormattedCacheItems = Object.fromEntries(\n      Object.entries(namespace).filter(([, cacheItem]) => {\n        return cacheItem.timestamp !== void 0;\n      })\n    );\n    setNamespace(filteredNamespaceWithoutOldFormattedCacheItems);\n    if (!timeToLive) {\n      return;\n    }\n    const filteredNamespaceWithoutExpiredItems = Object.fromEntries(\n      Object.entries(filteredNamespaceWithoutOldFormattedCacheItems).filter(([, cacheItem]) => {\n        const currentTimestamp = (/* @__PURE__ */ new Date()).getTime();\n        const isExpired = cacheItem.timestamp + timeToLive < currentTimestamp;\n        return !isExpired;\n      })\n    );\n    setNamespace(filteredNamespaceWithoutExpiredItems);\n  }\n  return {\n    get(key, defaultValue, events = {\n      miss: () => Promise.resolve()\n    }) {\n      return Promise.resolve().then(() => {\n        removeOutdatedCacheItems();\n        return getNamespace()[JSON.stringify(key)];\n      }).then((value) => {\n        return Promise.all([value ? value.value : defaultValue(), value !== void 0]);\n      }).then(([value, exists]) => {\n        return Promise.all([value, exists || events.miss(value)]);\n      }).then(([value]) => value);\n    },\n    set(key, value) {\n      return Promise.resolve().then(() => {\n        const namespace = getNamespace();\n        namespace[JSON.stringify(key)] = {\n          timestamp: (/* @__PURE__ */ new Date()).getTime(),\n          value\n        };\n        getStorage().setItem(namespaceKey, JSON.stringify(namespace));\n        return value;\n      });\n    },\n    delete(key) {\n      return Promise.resolve().then(() => {\n        const namespace = getNamespace();\n        delete namespace[JSON.stringify(key)];\n        getStorage().setItem(namespaceKey, JSON.stringify(namespace));\n      });\n    },\n    clear() {\n      return Promise.resolve().then(() => {\n        getStorage().removeItem(namespaceKey);\n      });\n    }\n  };\n}\n\n// src/cache/createNullCache.ts\nfunction createNullCache() {\n  return {\n    get(_key, defaultValue, events = {\n      miss: () => Promise.resolve()\n    }) {\n      const value = defaultValue();\n      return value.then((result) => Promise.all([result, events.miss(result)])).then(([result]) => result);\n    },\n    set(_key, value) {\n      return Promise.resolve(value);\n    },\n    delete(_key) {\n      return Promise.resolve();\n    },\n    clear() {\n      return Promise.resolve();\n    }\n  };\n}\n\n// src/cache/createFallbackableCache.ts\nfunction createFallbackableCache(options) {\n  const caches = [...options.caches];\n  const current = caches.shift();\n  if (current === void 0) {\n    return createNullCache();\n  }\n  return {\n    get(key, defaultValue, events = {\n      miss: () => Promise.resolve()\n    }) {\n      return current.get(key, defaultValue, events).catch(() => {\n        return createFallbackableCache({ caches }).get(key, defaultValue, events);\n      });\n    },\n    set(key, value) {\n      return current.set(key, value).catch(() => {\n        return createFallbackableCache({ caches }).set(key, value);\n      });\n    },\n    delete(key) {\n      return current.delete(key).catch(() => {\n        return createFallbackableCache({ caches }).delete(key);\n      });\n    },\n    clear() {\n      return current.clear().catch(() => {\n        return createFallbackableCache({ caches }).clear();\n      });\n    }\n  };\n}\n\n// src/cache/createMemoryCache.ts\nfunction createMemoryCache(options = { serializable: true }) {\n  let cache = {};\n  return {\n    get(key, defaultValue, events = {\n      miss: () => Promise.resolve()\n    }) {\n      const keyAsString = JSON.stringify(key);\n      if (keyAsString in cache) {\n        return Promise.resolve(options.serializable ? JSON.parse(cache[keyAsString]) : cache[keyAsString]);\n      }\n      const promise = defaultValue();\n      return promise.then((value) => events.miss(value)).then(() => promise);\n    },\n    set(key, value) {\n      cache[JSON.stringify(key)] = options.serializable ? JSON.stringify(value) : value;\n      return Promise.resolve(value);\n    },\n    delete(key) {\n      delete cache[JSON.stringify(key)];\n      return Promise.resolve();\n    },\n    clear() {\n      cache = {};\n      return Promise.resolve();\n    }\n  };\n}\n\n// src/constants.ts\nvar DEFAULT_CONNECT_TIMEOUT_BROWSER = 1e3;\nvar DEFAULT_READ_TIMEOUT_BROWSER = 2e3;\nvar DEFAULT_WRITE_TIMEOUT_BROWSER = 3e4;\nvar DEFAULT_CONNECT_TIMEOUT_NODE = 2e3;\nvar DEFAULT_READ_TIMEOUT_NODE = 5e3;\nvar DEFAULT_WRITE_TIMEOUT_NODE = 3e4;\n\n// src/createAlgoliaAgent.ts\nfunction createAlgoliaAgent(version) {\n  const algoliaAgent = {\n    value: `Algolia for JavaScript (${version})`,\n    add(options) {\n      const addedAlgoliaAgent = `; ${options.segment}${options.version !== void 0 ? ` (${options.version})` : \"\"}`;\n      if (algoliaAgent.value.indexOf(addedAlgoliaAgent) === -1) {\n        algoliaAgent.value = `${algoliaAgent.value}${addedAlgoliaAgent}`;\n      }\n      return algoliaAgent;\n    }\n  };\n  return algoliaAgent;\n}\n\n// src/createAuth.ts\nfunction createAuth(appId, apiKey, authMode = \"WithinHeaders\") {\n  const credentials = {\n    \"x-algolia-api-key\": apiKey,\n    \"x-algolia-application-id\": appId\n  };\n  return {\n    headers() {\n      return authMode === \"WithinHeaders\" ? credentials : {};\n    },\n    queryParameters() {\n      return authMode === \"WithinQueryParameters\" ? credentials : {};\n    }\n  };\n}\n\n// src/createIterablePromise.ts\nfunction createIterablePromise({\n  func,\n  validate,\n  aggregator,\n  error,\n  timeout = () => 0\n}) {\n  const retry = (previousResponse) => {\n    return new Promise((resolve, reject) => {\n      func(previousResponse).then((response) => {\n        if (aggregator) {\n          aggregator(response);\n        }\n        if (validate(response)) {\n          return resolve(response);\n        }\n        if (error && error.validate(response)) {\n          return reject(new Error(error.message(response)));\n        }\n        return setTimeout(() => {\n          retry(response).then(resolve).catch(reject);\n        }, timeout());\n      }).catch((err) => {\n        reject(err);\n      });\n    });\n  };\n  return retry();\n}\n\n// src/getAlgoliaAgent.ts\nfunction getAlgoliaAgent({ algoliaAgents, client, version }) {\n  const defaultAlgoliaAgent = createAlgoliaAgent(version).add({\n    segment: client,\n    version\n  });\n  algoliaAgents.forEach((algoliaAgent) => defaultAlgoliaAgent.add(algoliaAgent));\n  return defaultAlgoliaAgent;\n}\n\n// src/logger/createNullLogger.ts\nfunction createNullLogger() {\n  return {\n    debug(_message, _args) {\n      return Promise.resolve();\n    },\n    info(_message, _args) {\n      return Promise.resolve();\n    },\n    error(_message, _args) {\n      return Promise.resolve();\n    }\n  };\n}\n\n// src/transporter/createStatefulHost.ts\nvar EXPIRATION_DELAY = 2 * 60 * 1e3;\nfunction createStatefulHost(host, status = \"up\") {\n  const lastUpdate = Date.now();\n  function isUp() {\n    return status === \"up\" || Date.now() - lastUpdate > EXPIRATION_DELAY;\n  }\n  function isTimedOut() {\n    return status === \"timed out\" && Date.now() - lastUpdate <= EXPIRATION_DELAY;\n  }\n  return { ...host, status, lastUpdate, isUp, isTimedOut };\n}\n\n// src/transporter/errors.ts\nvar AlgoliaError = class extends Error {\n  name = \"AlgoliaError\";\n  constructor(message, name) {\n    super(message);\n    if (name) {\n      this.name = name;\n    }\n  }\n};\nvar ErrorWithStackTrace = class extends AlgoliaError {\n  stackTrace;\n  constructor(message, stackTrace, name) {\n    super(message, name);\n    this.stackTrace = stackTrace;\n  }\n};\nvar RetryError = class extends ErrorWithStackTrace {\n  constructor(stackTrace) {\n    super(\n      \"Unreachable hosts - your application id may be incorrect. If the error persists, please reach out to the Algolia Support team: https://alg.li/support.\",\n      stackTrace,\n      \"RetryError\"\n    );\n  }\n};\nvar ApiError = class extends ErrorWithStackTrace {\n  status;\n  constructor(message, status, stackTrace, name = \"ApiError\") {\n    super(message, stackTrace, name);\n    this.status = status;\n  }\n};\nvar DeserializationError = class extends AlgoliaError {\n  response;\n  constructor(message, response) {\n    super(message, \"DeserializationError\");\n    this.response = response;\n  }\n};\nvar DetailedApiError = class extends ApiError {\n  error;\n  constructor(message, status, error, stackTrace) {\n    super(message, status, stackTrace, \"DetailedApiError\");\n    this.error = error;\n  }\n};\n\n// src/transporter/helpers.ts\nfunction shuffle(array) {\n  const shuffledArray = array;\n  for (let c = array.length - 1; c > 0; c--) {\n    const b = Math.floor(Math.random() * (c + 1));\n    const a = array[c];\n    shuffledArray[c] = array[b];\n    shuffledArray[b] = a;\n  }\n  return shuffledArray;\n}\nfunction serializeUrl(host, path, queryParameters) {\n  const queryParametersAsString = serializeQueryParameters(queryParameters);\n  let url = `${host.protocol}://${host.url}${host.port ? `:${host.port}` : \"\"}/${path.charAt(0) === \"/\" ? path.substring(1) : path}`;\n  if (queryParametersAsString.length) {\n    url += `?${queryParametersAsString}`;\n  }\n  return url;\n}\nfunction serializeQueryParameters(parameters) {\n  return Object.keys(parameters).filter((key) => parameters[key] !== void 0).sort().map(\n    (key) => `${key}=${encodeURIComponent(\n      Object.prototype.toString.call(parameters[key]) === \"[object Array]\" ? parameters[key].join(\",\") : parameters[key]\n    ).replace(/\\+/g, \"%20\")}`\n  ).join(\"&\");\n}\nfunction serializeData(request, requestOptions) {\n  if (request.method === \"GET\" || request.data === void 0 && requestOptions.data === void 0) {\n    return void 0;\n  }\n  const data = Array.isArray(request.data) ? request.data : { ...request.data, ...requestOptions.data };\n  return JSON.stringify(data);\n}\nfunction serializeHeaders(baseHeaders, requestHeaders, requestOptionsHeaders) {\n  const headers = {\n    Accept: \"application/json\",\n    ...baseHeaders,\n    ...requestHeaders,\n    ...requestOptionsHeaders\n  };\n  const serializedHeaders = {};\n  Object.keys(headers).forEach((header) => {\n    const value = headers[header];\n    serializedHeaders[header.toLowerCase()] = value;\n  });\n  return serializedHeaders;\n}\nfunction deserializeSuccess(response) {\n  try {\n    return JSON.parse(response.content);\n  } catch (e) {\n    throw new DeserializationError(e.message, response);\n  }\n}\nfunction deserializeFailure({ content, status }, stackFrame) {\n  try {\n    const parsed = JSON.parse(content);\n    if (\"error\" in parsed) {\n      return new DetailedApiError(parsed.message, status, parsed.error, stackFrame);\n    }\n    return new ApiError(parsed.message, status, stackFrame);\n  } catch {\n  }\n  return new ApiError(content, status, stackFrame);\n}\n\n// src/transporter/responses.ts\nfunction isNetworkError({ isTimedOut, status }) {\n  return !isTimedOut && ~~status === 0;\n}\nfunction isRetryable({ isTimedOut, status }) {\n  return isTimedOut || isNetworkError({ isTimedOut, status }) || ~~(status / 100) !== 2 && ~~(status / 100) !== 4;\n}\nfunction isSuccess({ status }) {\n  return ~~(status / 100) === 2;\n}\n\n// src/transporter/stackTrace.ts\nfunction stackTraceWithoutCredentials(stackTrace) {\n  return stackTrace.map((stackFrame) => stackFrameWithoutCredentials(stackFrame));\n}\nfunction stackFrameWithoutCredentials(stackFrame) {\n  const modifiedHeaders = stackFrame.request.headers[\"x-algolia-api-key\"] ? { \"x-algolia-api-key\": \"*****\" } : {};\n  return {\n    ...stackFrame,\n    request: {\n      ...stackFrame.request,\n      headers: {\n        ...stackFrame.request.headers,\n        ...modifiedHeaders\n      }\n    }\n  };\n}\n\n// src/transporter/createTransporter.ts\nfunction createTransporter({\n  hosts,\n  hostsCache,\n  baseHeaders,\n  logger,\n  baseQueryParameters,\n  algoliaAgent,\n  timeouts,\n  requester,\n  requestsCache,\n  responsesCache\n}) {\n  async function createRetryableOptions(compatibleHosts) {\n    const statefulHosts = await Promise.all(\n      compatibleHosts.map((compatibleHost) => {\n        return hostsCache.get(compatibleHost, () => {\n          return Promise.resolve(createStatefulHost(compatibleHost));\n        });\n      })\n    );\n    const hostsUp = statefulHosts.filter((host) => host.isUp());\n    const hostsTimedOut = statefulHosts.filter((host) => host.isTimedOut());\n    const hostsAvailable = [...hostsUp, ...hostsTimedOut];\n    const compatibleHostsAvailable = hostsAvailable.length > 0 ? hostsAvailable : compatibleHosts;\n    return {\n      hosts: compatibleHostsAvailable,\n      getTimeout(timeoutsCount, baseTimeout) {\n        const timeoutMultiplier = hostsTimedOut.length === 0 && timeoutsCount === 0 ? 1 : hostsTimedOut.length + 3 + timeoutsCount;\n        return timeoutMultiplier * baseTimeout;\n      }\n    };\n  }\n  async function retryableRequest(request, requestOptions, isRead = true) {\n    const stackTrace = [];\n    const data = serializeData(request, requestOptions);\n    const headers = serializeHeaders(baseHeaders, request.headers, requestOptions.headers);\n    const dataQueryParameters = request.method === \"GET\" ? {\n      ...request.data,\n      ...requestOptions.data\n    } : {};\n    const queryParameters = {\n      ...baseQueryParameters,\n      ...request.queryParameters,\n      ...dataQueryParameters\n    };\n    if (algoliaAgent.value) {\n      queryParameters[\"x-algolia-agent\"] = algoliaAgent.value;\n    }\n    if (requestOptions && requestOptions.queryParameters) {\n      for (const key of Object.keys(requestOptions.queryParameters)) {\n        if (!requestOptions.queryParameters[key] || Object.prototype.toString.call(requestOptions.queryParameters[key]) === \"[object Object]\") {\n          queryParameters[key] = requestOptions.queryParameters[key];\n        } else {\n          queryParameters[key] = requestOptions.queryParameters[key].toString();\n        }\n      }\n    }\n    let timeoutsCount = 0;\n    const retry = async (retryableHosts, getTimeout) => {\n      const host = retryableHosts.pop();\n      if (host === void 0) {\n        throw new RetryError(stackTraceWithoutCredentials(stackTrace));\n      }\n      const timeout = { ...timeouts, ...requestOptions.timeouts };\n      const payload = {\n        data,\n        headers,\n        method: request.method,\n        url: serializeUrl(host, request.path, queryParameters),\n        connectTimeout: getTimeout(timeoutsCount, timeout.connect),\n        responseTimeout: getTimeout(timeoutsCount, isRead ? timeout.read : timeout.write)\n      };\n      const pushToStackTrace = (response2) => {\n        const stackFrame = {\n          request: payload,\n          response: response2,\n          host,\n          triesLeft: retryableHosts.length\n        };\n        stackTrace.push(stackFrame);\n        return stackFrame;\n      };\n      const response = await requester.send(payload);\n      if (isRetryable(response)) {\n        const stackFrame = pushToStackTrace(response);\n        if (response.isTimedOut) {\n          timeoutsCount++;\n        }\n        logger.info(\"Retryable failure\", stackFrameWithoutCredentials(stackFrame));\n        await hostsCache.set(host, createStatefulHost(host, response.isTimedOut ? \"timed out\" : \"down\"));\n        return retry(retryableHosts, getTimeout);\n      }\n      if (isSuccess(response)) {\n        return deserializeSuccess(response);\n      }\n      pushToStackTrace(response);\n      throw deserializeFailure(response, stackTrace);\n    };\n    const compatibleHosts = hosts.filter(\n      (host) => host.accept === \"readWrite\" || (isRead ? host.accept === \"read\" : host.accept === \"write\")\n    );\n    const options = await createRetryableOptions(compatibleHosts);\n    return retry([...options.hosts].reverse(), options.getTimeout);\n  }\n  function createRequest(request, requestOptions = {}) {\n    const isRead = request.useReadTransporter || request.method === \"GET\";\n    if (!isRead) {\n      return retryableRequest(request, requestOptions, isRead);\n    }\n    const createRetryableRequest = () => {\n      return retryableRequest(request, requestOptions);\n    };\n    const cacheable = requestOptions.cacheable || request.cacheable;\n    if (cacheable !== true) {\n      return createRetryableRequest();\n    }\n    const key = {\n      request,\n      requestOptions,\n      transporter: {\n        queryParameters: baseQueryParameters,\n        headers: baseHeaders\n      }\n    };\n    return responsesCache.get(\n      key,\n      () => {\n        return requestsCache.get(\n          key,\n          () => (\n            /**\n             * Finally, if there is no request in progress with the same key,\n             * this `createRetryableRequest()` will actually trigger the\n             * retryable request.\n             */\n            requestsCache.set(key, createRetryableRequest()).then(\n              (response) => Promise.all([requestsCache.delete(key), response]),\n              (err) => Promise.all([requestsCache.delete(key), Promise.reject(err)])\n            ).then(([_, response]) => response)\n          )\n        );\n      },\n      {\n        /**\n         * Of course, once we get this response back from the server, we\n         * tell response cache to actually store the received response\n         * to be used later.\n         */\n        miss: (response) => responsesCache.set(key, response)\n      }\n    );\n  }\n  return {\n    hostsCache,\n    requester,\n    timeouts,\n    logger,\n    algoliaAgent,\n    baseHeaders,\n    baseQueryParameters,\n    hosts,\n    request: createRequest,\n    requestsCache,\n    responsesCache\n  };\n}\n\n// src/types/logger.ts\nvar LogLevelEnum = {\n  Debug: 1,\n  Info: 2,\n  Error: 3\n};\nexport {\n  AlgoliaError,\n  ApiError,\n  DEFAULT_CONNECT_TIMEOUT_BROWSER,\n  DEFAULT_CONNECT_TIMEOUT_NODE,\n  DEFAULT_READ_TIMEOUT_BROWSER,\n  DEFAULT_READ_TIMEOUT_NODE,\n  DEFAULT_WRITE_TIMEOUT_BROWSER,\n  DEFAULT_WRITE_TIMEOUT_NODE,\n  DeserializationError,\n  DetailedApiError,\n  ErrorWithStackTrace,\n  LogLevelEnum,\n  RetryError,\n  createAlgoliaAgent,\n  createAuth,\n  createBrowserLocalStorageCache,\n  createFallbackableCache,\n  createIterablePromise,\n  createMemoryCache,\n  createNullCache,\n  createNullLogger,\n  createStatefulHost,\n  createTransporter,\n  deserializeFailure,\n  deserializeSuccess,\n  getAlgoliaAgent,\n  isNetworkError,\n  isRetryable,\n  isSuccess,\n  serializeData,\n  serializeHeaders,\n  serializeQueryParameters,\n  serializeUrl,\n  shuffle,\n  stackFrameWithoutCredentials,\n  stackTraceWithoutCredentials\n};\n//# sourceMappingURL=common.js.map", "export const version = '3.8.2';\n", "import { liteClient } from 'algoliasearch/lite';\nimport React from 'react';\n\nimport type { DocSearchTransformClient } from './DocSearch';\nimport { version } from './version';\n\nexport function useSearchClient(\n  appId: string,\n  apiKey: string,\n  transformSearchClient: (searchClient: DocSearchTransformClient) => DocSearchTransformClient,\n): DocSearchTransformClient {\n  const searchClient = React.useMemo(() => {\n    const client = liteClient(appId, apiKey);\n    client.addAlgoliaAgent('docsearch', version);\n\n    // Since DocSearch.js relies on DocSearch React with an alias to Preact,\n    // we cannot add the `docsearch-react` user agent by default, otherwise\n    // it would also be sent on a DocSearch.js integration.\n    // We therefore only add the `docsearch-react` user agent if `docsearch.js`\n    // is not present.\n    if (/docsearch.js \\(.*\\)/.test(client.transporter.algoliaAgent.value) === false) {\n      client.addAlgoliaAgent('docsearch-react', version);\n    }\n\n    return transformSearchClient(client);\n  }, [appId, apiKey, transformSearchClient]);\n\n  return searchClient;\n}\n", "// lite/builds/browser.ts\nimport { createXhrRequester } from \"@algolia/requester-browser-xhr\";\nimport {\n  createBrowserLocalStorageCache,\n  createFallbackableCache,\n  createMemoryCache,\n  createNullLogger,\n  DEFAULT_CONNECT_TIMEOUT_BROWSER,\n  DEFAULT_READ_TIMEOUT_BROWSER,\n  DEFAULT_WRITE_TIMEOUT_BROWSER\n} from \"@algolia/client-common\";\n\n// lite/src/liteClient.ts\nimport { createAuth, createTransporter, getAlgoliaAgent, shuffle } from \"@algolia/client-common\";\nvar apiClientVersion = \"5.14.2\";\nfunction getDefaultHosts(appId) {\n  return [\n    {\n      url: `${appId}-dsn.algolia.net`,\n      accept: \"read\",\n      protocol: \"https\"\n    },\n    {\n      url: `${appId}.algolia.net`,\n      accept: \"write\",\n      protocol: \"https\"\n    }\n  ].concat(\n    shuffle([\n      {\n        url: `${appId}-1.algolianet.com`,\n        accept: \"readWrite\",\n        protocol: \"https\"\n      },\n      {\n        url: `${appId}-2.algolianet.com`,\n        accept: \"readWrite\",\n        protocol: \"https\"\n      },\n      {\n        url: `${appId}-3.algolianet.com`,\n        accept: \"readWrite\",\n        protocol: \"https\"\n      }\n    ])\n  );\n}\nfunction createLiteClient({\n  appId: appIdOption,\n  apiKey: apiKeyOption,\n  authMode,\n  algoliaAgents,\n  ...options\n}) {\n  const auth = createAuth(appIdOption, apiKeyOption, authMode);\n  const transporter = createTransporter({\n    hosts: getDefaultHosts(appIdOption),\n    ...options,\n    algoliaAgent: getAlgoliaAgent({\n      algoliaAgents,\n      client: \"Lite\",\n      version: apiClientVersion\n    }),\n    baseHeaders: {\n      \"content-type\": \"text/plain\",\n      ...auth.headers(),\n      ...options.baseHeaders\n    },\n    baseQueryParameters: {\n      ...auth.queryParameters(),\n      ...options.baseQueryParameters\n    }\n  });\n  return {\n    transporter,\n    /**\n     * The `appId` currently in use.\n     */\n    appId: appIdOption,\n    /**\n     * Clears the cache of the transporter for the `requestsCache` and `responsesCache` properties.\n     */\n    clearCache() {\n      return Promise.all([transporter.requestsCache.clear(), transporter.responsesCache.clear()]).then(() => void 0);\n    },\n    /**\n     * Get the value of the `algoliaAgent`, used by our libraries internally and telemetry system.\n     */\n    get _ua() {\n      return transporter.algoliaAgent.value;\n    },\n    /**\n     * Adds a `segment` to the `x-algolia-agent` sent with every requests.\n     *\n     * @param segment - The algolia agent (user-agent) segment to add.\n     * @param version - The version of the agent.\n     */\n    addAlgoliaAgent(segment, version) {\n      transporter.algoliaAgent.add({ segment, version });\n    },\n    /**\n     * Helper method to switch the API key used to authenticate the requests.\n     *\n     * @param params - Method params.\n     * @param params.apiKey - The new API Key to use.\n     */\n    setClientApiKey({ apiKey }) {\n      if (!authMode || authMode === \"WithinHeaders\") {\n        transporter.baseHeaders[\"x-algolia-api-key\"] = apiKey;\n      } else {\n        transporter.baseQueryParameters[\"x-algolia-api-key\"] = apiKey;\n      }\n    },\n    /**\n     * Helper: calls the `search` method but with certainty that we will only request Algolia records (hits) and not facets.\n     * Disclaimer: We don't assert that the parameters you pass to this method only contains `hits` requests to prevent impacting search performances, this helper is purely for typing purposes.\n     *\n     * @summary Search multiple indices for `hits`.\n     * @param searchMethodParams - Query requests and strategies. Results will be received in the same order as the queries.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    searchForHits(searchMethodParams, requestOptions) {\n      return this.search(searchMethodParams, requestOptions);\n    },\n    /**\n     * Helper: calls the `search` method but with certainty that we will only request Algolia facets and not records (hits).\n     * Disclaimer: We don't assert that the parameters you pass to this method only contains `facets` requests to prevent impacting search performances, this helper is purely for typing purposes.\n     *\n     * @summary Search multiple indices for `facets`.\n     * @param searchMethodParams - Query requests and strategies. Results will be received in the same order as the queries.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    searchForFacets(searchMethodParams, requestOptions) {\n      return this.search(searchMethodParams, requestOptions);\n    },\n    /**\n     * This method allow you to send requests to the Algolia REST API.\n     * @param customPost - The customPost object.\n     * @param customPost.path - Path of the endpoint, anything after \\\"/1\\\" must be specified.\n     * @param customPost.parameters - Query parameters to apply to the current query.\n     * @param customPost.body - Parameters to send with the custom request.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    customPost({ path, parameters, body }, requestOptions) {\n      if (!path) {\n        throw new Error(\"Parameter `path` is required when calling `customPost`.\");\n      }\n      const requestPath = \"/{path}\".replace(\"{path}\", path);\n      const headers = {};\n      const queryParameters = parameters ? parameters : {};\n      const request = {\n        method: \"POST\",\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: body ? body : {}\n      };\n      return transporter.request(request, requestOptions);\n    },\n    /**\n     * Retrieves recommendations from selected AI models.\n     *\n     * Required API Key ACLs:\n     *  - search\n     * @param getRecommendationsParams - The getRecommendationsParams object.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getRecommendations(getRecommendationsParams, requestOptions) {\n      if (getRecommendationsParams && Array.isArray(getRecommendationsParams)) {\n        const newSignatureRequest = {\n          requests: getRecommendationsParams\n        };\n        getRecommendationsParams = newSignatureRequest;\n      }\n      if (!getRecommendationsParams) {\n        throw new Error(\"Parameter `getRecommendationsParams` is required when calling `getRecommendations`.\");\n      }\n      if (!getRecommendationsParams.requests) {\n        throw new Error(\"Parameter `getRecommendationsParams.requests` is required when calling `getRecommendations`.\");\n      }\n      const requestPath = \"/1/indexes/*/recommendations\";\n      const headers = {};\n      const queryParameters = {};\n      const request = {\n        method: \"POST\",\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: getRecommendationsParams,\n        useReadTransporter: true,\n        cacheable: true\n      };\n      return transporter.request(request, requestOptions);\n    },\n    /**\n     * Sends multiple search requests to one or more indices.  This can be useful in these cases:  - Different indices for different purposes, such as, one index for products, another one for marketing content. - Multiple searches to the same index—for example, with different filters.\n     *\n     * Required API Key ACLs:\n     *  - search\n     * @param searchMethodParams - Muli-search request body. Results are returned in the same order as the requests.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    search(searchMethodParams, requestOptions) {\n      if (searchMethodParams && Array.isArray(searchMethodParams)) {\n        const newSignatureRequest = {\n          requests: searchMethodParams.map(({ params, ...legacyRequest }) => {\n            if (legacyRequest.type === \"facet\") {\n              return {\n                ...legacyRequest,\n                ...params,\n                type: \"facet\"\n              };\n            }\n            return {\n              ...legacyRequest,\n              ...params,\n              facet: void 0,\n              maxFacetHits: void 0,\n              facetQuery: void 0\n            };\n          })\n        };\n        searchMethodParams = newSignatureRequest;\n      }\n      if (!searchMethodParams) {\n        throw new Error(\"Parameter `searchMethodParams` is required when calling `search`.\");\n      }\n      if (!searchMethodParams.requests) {\n        throw new Error(\"Parameter `searchMethodParams.requests` is required when calling `search`.\");\n      }\n      const requestPath = \"/1/indexes/*/queries\";\n      const headers = {};\n      const queryParameters = {};\n      const request = {\n        method: \"POST\",\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: searchMethodParams,\n        useReadTransporter: true,\n        cacheable: true\n      };\n      return transporter.request(request, requestOptions);\n    }\n  };\n}\n\n// lite/builds/browser.ts\nfunction liteClient(appId, apiKey, options) {\n  if (!appId || typeof appId !== \"string\") {\n    throw new Error(\"`appId` is missing.\");\n  }\n  if (!apiKey || typeof apiKey !== \"string\") {\n    throw new Error(\"`apiKey` is missing.\");\n  }\n  return createLiteClient({\n    appId,\n    apiKey,\n    timeouts: {\n      connect: DEFAULT_CONNECT_TIMEOUT_BROWSER,\n      read: DEFAULT_READ_TIMEOUT_BROWSER,\n      write: DEFAULT_WRITE_TIMEOUT_BROWSER\n    },\n    logger: createNullLogger(),\n    requester: createXhrRequester(),\n    algoliaAgents: [{ segment: \"Browser\" }],\n    authMode: \"WithinQueryParameters\",\n    responsesCache: createMemoryCache(),\n    requestsCache: createMemoryCache({ serializable: false }),\n    hostsCache: createFallbackableCache({\n      caches: [createBrowserLocalStorageCache({ key: `${apiClientVersion}-${appId}` }), createMemoryCache()]\n    }),\n    ...options\n  });\n}\nexport {\n  apiClientVersion,\n  liteClient\n};\n//# sourceMappingURL=browser.js.map", "function m(){function r(t){return new Promise(s=>{let e=new XMLHttpRequest;e.open(t.method,t.url,!0),Object.keys(t.headers).forEach(n=>e.setRequestHeader(n,t.headers[n]));let i=(n,a)=>setTimeout(()=>{e.abort(),s({status:0,content:a,isTimedOut:!0})},n),u=i(t.connectTimeout,\"Connection timeout\"),o;e.onreadystatechange=()=>{e.readyState>e.OPENED&&o===void 0&&(clearTimeout(u),o=i(t.responseTimeout,\"Socket timeout\"))},e.onerror=()=>{e.status===0&&(clearTimeout(u),clearTimeout(o),s({content:e.responseText||\"Network request failed\",status:e.status,isTimedOut:!1}))},e.onload=()=>{clearTimeout(u),clearTimeout(o),s({content:e.responseText,status:e.status,isTimedOut:!1})},e.send(t.data)})}return{send:r}}export{m as createXhrRequester};\n//# sourceMappingURL=requester.xhr.js.map", "import { type AlgoliaInsightsHit, createAutocomplete } from '@algolia/autocomplete-core';\nimport type { SearchResponse } from 'algoliasearch/lite';\nimport React from 'react';\n\nimport { MAX_QUERY_SIZE } from './constants';\nimport type { DocSearchProps } from './DocSearch';\nimport type { FooterTranslations } from './Footer';\nimport { Footer } from './Footer';\nimport { Hit } from './Hit';\nimport type { ScreenStateTranslations } from './ScreenState';\nimport { ScreenState } from './ScreenState';\nimport type { SearchBoxTranslations } from './SearchBox';\nimport { SearchBox } from './SearchBox';\nimport { createStoredSearches } from './stored-searches';\nimport type { DocSearchHit, DocSearchState, InternalDocSearchHit, StoredDocSearchHit } from './types';\nimport { useSearchClient } from './useSearchClient';\nimport { useTouchEvents } from './useTouchEvents';\nimport { useTrapFocus } from './useTrapFocus';\nimport { groupBy, identity, noop, removeHighlightTags, isModifierEvent } from './utils';\n\nexport type ModalTranslations = Partial<{\n  searchBox: SearchBoxTranslations;\n  footer: FooterTranslations;\n}> &\n  ScreenStateTranslations;\n\nexport type DocSearchModalProps = DocSearchProps & {\n  initialScrollY: number;\n  onClose?: () => void;\n  translations?: ModalTranslations;\n};\n\nexport function DocSearchModal({\n  appId,\n  apiKey,\n  indexName,\n  placeholder = 'Search docs',\n  searchParameters,\n  maxResultsPerGroup,\n  onClose = noop,\n  transformItems = identity,\n  hitComponent = Hit,\n  resultsFooterComponent = (): JSX.Element | null => null,\n  navigator,\n  initialScrollY = 0,\n  transformSearchClient = identity,\n  disableUserPersonalization = false,\n  initialQuery: initialQueryFromProp = '',\n  translations = {},\n  getMissingResultsUrl,\n  insights = false,\n}: DocSearchModalProps): JSX.Element {\n  const { footer: footerTranslations, searchBox: searchBoxTranslations, ...screenStateTranslations } = translations;\n  const [state, setState] = React.useState<DocSearchState<InternalDocSearchHit>>({\n    query: '',\n    collections: [],\n    completion: null,\n    context: {},\n    isOpen: false,\n    activeItemId: null,\n    status: 'idle',\n  });\n\n  const containerRef = React.useRef<HTMLDivElement | null>(null);\n  const modalRef = React.useRef<HTMLDivElement | null>(null);\n  const formElementRef = React.useRef<HTMLDivElement | null>(null);\n  const dropdownRef = React.useRef<HTMLDivElement | null>(null);\n  const inputRef = React.useRef<HTMLInputElement | null>(null);\n  const snippetLength = React.useRef<number>(10);\n  const initialQueryFromSelection = React.useRef(\n    typeof window !== 'undefined' ? window.getSelection()!.toString().slice(0, MAX_QUERY_SIZE) : '',\n  ).current;\n  const initialQuery = React.useRef(initialQueryFromProp || initialQueryFromSelection).current;\n\n  const searchClient = useSearchClient(appId, apiKey, transformSearchClient);\n  const favoriteSearches = React.useRef(\n    createStoredSearches<StoredDocSearchHit>({\n      key: `__DOCSEARCH_FAVORITE_SEARCHES__${indexName}`,\n      limit: 10,\n    }),\n  ).current;\n  const recentSearches = React.useRef(\n    createStoredSearches<StoredDocSearchHit>({\n      key: `__DOCSEARCH_RECENT_SEARCHES__${indexName}`,\n      // We display 7 recent searches and there's no favorites, but only\n      // 4 when there are favorites.\n      limit: favoriteSearches.getAll().length === 0 ? 7 : 4,\n    }),\n  ).current;\n\n  const saveRecentSearch = React.useCallback(\n    function saveRecentSearch(item: InternalDocSearchHit) {\n      if (disableUserPersonalization) {\n        return;\n      }\n\n      // We don't store `content` record, but their parent if available.\n      const search = item.type === 'content' ? item.__docsearch_parent : item;\n\n      // We save the recent search only if it's not favorited.\n      if (search && favoriteSearches.getAll().findIndex((x) => x.objectID === search.objectID) === -1) {\n        recentSearches.add(search);\n      }\n    },\n    [favoriteSearches, recentSearches, disableUserPersonalization],\n  );\n\n  const sendItemClickEvent = React.useCallback(\n    (item: InternalDocSearchHit) => {\n      if (!state.context.algoliaInsightsPlugin || !item.__autocomplete_id) return;\n\n      const insightsItem = item as AlgoliaInsightsHit;\n\n      const insightsClickParams = {\n        eventName: 'Item Selected',\n        index: insightsItem.__autocomplete_indexName,\n        items: [insightsItem],\n        positions: [item.__autocomplete_id],\n        queryID: insightsItem.__autocomplete_queryID,\n      };\n\n      state.context.algoliaInsightsPlugin.insights.clickedObjectIDsAfterSearch(insightsClickParams);\n    },\n    [state.context.algoliaInsightsPlugin],\n  );\n\n  const autocomplete = React.useMemo(\n    () =>\n      createAutocomplete<InternalDocSearchHit, React.FormEvent<HTMLFormElement>, React.MouseEvent, React.KeyboardEvent>(\n        {\n          id: 'docsearch',\n          defaultActiveItemId: 0,\n          placeholder,\n          openOnFocus: true,\n          initialState: {\n            query: initialQuery,\n            context: {\n              searchSuggestions: [],\n            },\n          },\n          insights,\n          navigator,\n          onStateChange(props) {\n            setState(props.state);\n          },\n          getSources({ query, state: sourcesState, setContext, setStatus }) {\n            if (!query) {\n              if (disableUserPersonalization) {\n                return [];\n              }\n\n              return [\n                {\n                  sourceId: 'recentSearches',\n                  onSelect({ item, event }): void {\n                    saveRecentSearch(item);\n\n                    if (!isModifierEvent(event)) {\n                      onClose();\n                    }\n                  },\n                  getItemUrl({ item }): string {\n                    return item.url;\n                  },\n                  getItems(): InternalDocSearchHit[] {\n                    return recentSearches.getAll() as InternalDocSearchHit[];\n                  },\n                },\n                {\n                  sourceId: 'favoriteSearches',\n                  onSelect({ item, event }): void {\n                    saveRecentSearch(item);\n\n                    if (!isModifierEvent(event)) {\n                      onClose();\n                    }\n                  },\n                  getItemUrl({ item }): string {\n                    return item.url;\n                  },\n                  getItems(): InternalDocSearchHit[] {\n                    return favoriteSearches.getAll() as InternalDocSearchHit[];\n                  },\n                },\n              ];\n            }\n\n            const insightsActive = Boolean(insights);\n\n            return searchClient\n              .search<DocSearchHit>({\n                requests: [\n                  {\n                    query,\n                    indexName,\n                    attributesToRetrieve: [\n                      'hierarchy.lvl0',\n                      'hierarchy.lvl1',\n                      'hierarchy.lvl2',\n                      'hierarchy.lvl3',\n                      'hierarchy.lvl4',\n                      'hierarchy.lvl5',\n                      'hierarchy.lvl6',\n                      'content',\n                      'type',\n                      'url',\n                    ],\n                    attributesToSnippet: [\n                      `hierarchy.lvl1:${snippetLength.current}`,\n                      `hierarchy.lvl2:${snippetLength.current}`,\n                      `hierarchy.lvl3:${snippetLength.current}`,\n                      `hierarchy.lvl4:${snippetLength.current}`,\n                      `hierarchy.lvl5:${snippetLength.current}`,\n                      `hierarchy.lvl6:${snippetLength.current}`,\n                      `content:${snippetLength.current}`,\n                    ],\n                    snippetEllipsisText: '…',\n                    highlightPreTag: '<mark>',\n                    highlightPostTag: '</mark>',\n                    hitsPerPage: 20,\n                    clickAnalytics: insightsActive,\n                    ...searchParameters,\n                  },\n                ],\n              })\n              .catch((error) => {\n                // The Algolia `RetryError` happens when all the servers have\n                // failed, meaning that there's no chance the response comes\n                // back. This is the right time to display an error.\n                // See https://github.com/algolia/algoliasearch-client-javascript/blob/2ffddf59bc765cd1b664ee0346b28f00229d6e12/packages/transporter/src/errors/createRetryError.ts#L5\n                if (error.name === 'RetryError') {\n                  setStatus('error');\n                }\n\n                throw error;\n              })\n              .then(({ results }) => {\n                const firstResult = results[0] as SearchResponse<DocSearchHit>;\n                const { hits, nbHits } = firstResult;\n                const sources = groupBy<DocSearchHit>(hits, (hit) => removeHighlightTags(hit), maxResultsPerGroup);\n\n                // We store the `lvl0`s to display them as search suggestions\n                // in the \"no results\" screen.\n                if ((sourcesState.context.searchSuggestions as any[]).length < Object.keys(sources).length) {\n                  setContext({\n                    searchSuggestions: Object.keys(sources),\n                  });\n                }\n\n                setContext({ nbHits });\n\n                let insightsParams = {};\n\n                if (insightsActive) {\n                  insightsParams = {\n                    __autocomplete_indexName: indexName,\n                    __autocomplete_queryID: firstResult.queryID,\n                    __autocomplete_algoliaCredentials: {\n                      appId,\n                      apiKey,\n                    },\n                  };\n                }\n\n                return Object.values<DocSearchHit[]>(sources).map((items, index) => {\n                  return {\n                    sourceId: `hits${index}`,\n                    onSelect({ item, event }): void {\n                      saveRecentSearch(item);\n\n                      if (!isModifierEvent(event)) {\n                        onClose();\n                      }\n                    },\n                    getItemUrl({ item }): string {\n                      return item.url;\n                    },\n                    getItems(): InternalDocSearchHit[] {\n                      return Object.values(groupBy(items, (item) => item.hierarchy.lvl1, maxResultsPerGroup))\n                        .map(transformItems)\n                        .map((groupedHits) =>\n                          groupedHits.map((item) => {\n                            let parent: InternalDocSearchHit | null = null;\n\n                            const potentialParent = groupedHits.find(\n                              (siblingItem) =>\n                                siblingItem.type === 'lvl1' && siblingItem.hierarchy.lvl1 === item.hierarchy.lvl1,\n                            ) as InternalDocSearchHit | undefined;\n\n                            if (item.type !== 'lvl1' && potentialParent) {\n                              parent = potentialParent;\n                            }\n\n                            return {\n                              ...item,\n                              __docsearch_parent: parent,\n                              ...insightsParams,\n                            };\n                          }),\n                        )\n                        .flat();\n                    },\n                  };\n                });\n              });\n          },\n        },\n      ),\n    [\n      indexName,\n      searchParameters,\n      maxResultsPerGroup,\n      searchClient,\n      onClose,\n      recentSearches,\n      favoriteSearches,\n      saveRecentSearch,\n      initialQuery,\n      placeholder,\n      navigator,\n      transformItems,\n      disableUserPersonalization,\n      insights,\n      appId,\n      apiKey,\n    ],\n  );\n\n  const { getEnvironmentProps, getRootProps, refresh } = autocomplete;\n\n  useTouchEvents({\n    getEnvironmentProps,\n    panelElement: dropdownRef.current,\n    formElement: formElementRef.current,\n    inputElement: inputRef.current,\n  });\n  useTrapFocus({ container: containerRef.current });\n\n  React.useEffect(() => {\n    document.body.classList.add('DocSearch--active');\n\n    return (): void => {\n      document.body.classList.remove('DocSearch--active');\n\n      // IE11 doesn't support `scrollTo` so we check that the method exists\n      // first.\n      window.scrollTo?.(0, initialScrollY);\n    };\n\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  React.useEffect(() => {\n    const isMobileMediaQuery = window.matchMedia('(max-width: 768px)');\n\n    if (isMobileMediaQuery.matches) {\n      snippetLength.current = 5;\n    }\n  }, []);\n\n  React.useEffect(() => {\n    if (dropdownRef.current) {\n      dropdownRef.current.scrollTop = 0;\n    }\n  }, [state.query]);\n\n  // We don't focus the input when there's an initial query (i.e. Selection\n  // Search) because users rather want to see the results directly, without the\n  // keyboard appearing.\n  // We therefore need to refresh the autocomplete instance to load all the\n  // results, which is usually triggered on focus.\n  React.useEffect(() => {\n    if (initialQuery.length > 0) {\n      refresh();\n\n      if (inputRef.current) {\n        inputRef.current.focus();\n      }\n    }\n  }, [initialQuery, refresh]);\n\n  // We rely on a CSS property to set the modal height to the full viewport height\n  // because all mobile browsers don't compute their height the same way.\n  // See https://css-tricks.com/the-trick-to-viewport-units-on-mobile/\n  React.useEffect(() => {\n    function setFullViewportHeight(): void {\n      if (modalRef.current) {\n        const vh = window.innerHeight * 0.01;\n        modalRef.current.style.setProperty('--docsearch-vh', `${vh}px`);\n      }\n    }\n\n    setFullViewportHeight();\n\n    window.addEventListener('resize', setFullViewportHeight);\n\n    return (): void => {\n      window.removeEventListener('resize', setFullViewportHeight);\n    };\n  }, []);\n\n  return (\n    <div\n      ref={containerRef}\n      {...getRootProps({\n        'aria-expanded': true,\n      })}\n      className={[\n        'DocSearch',\n        'DocSearch-Container',\n        state.status === 'stalled' && 'DocSearch-Container--Stalled',\n        state.status === 'error' && 'DocSearch-Container--Errored',\n      ]\n        .filter(Boolean)\n        .join(' ')}\n      role=\"button\"\n      tabIndex={0}\n      onMouseDown={(event) => {\n        if (event.target === event.currentTarget) {\n          onClose();\n        }\n      }}\n    >\n      <div className=\"DocSearch-Modal\" ref={modalRef}>\n        <header className=\"DocSearch-SearchBar\" ref={formElementRef}>\n          <SearchBox\n            {...autocomplete}\n            state={state}\n            autoFocus={initialQuery.length === 0}\n            inputRef={inputRef}\n            isFromSelection={Boolean(initialQuery) && initialQuery === initialQueryFromSelection}\n            translations={searchBoxTranslations}\n            onClose={onClose}\n          />\n        </header>\n\n        <div className=\"DocSearch-Dropdown\" ref={dropdownRef}>\n          <ScreenState\n            {...autocomplete}\n            indexName={indexName}\n            state={state}\n            hitComponent={hitComponent}\n            resultsFooterComponent={resultsFooterComponent}\n            disableUserPersonalization={disableUserPersonalization}\n            recentSearches={recentSearches}\n            favoriteSearches={favoriteSearches}\n            inputRef={inputRef}\n            translations={screenStateTranslations}\n            getMissingResultsUrl={getMissingResultsUrl}\n            onItemClick={(item, event) => {\n              // If insights is active, send insights click event\n              sendItemClickEvent(item);\n\n              saveRecentSearch(item);\n              if (!isModifierEvent(event)) {\n                onClose();\n              }\n            }}\n          />\n        </div>\n\n        <footer className=\"DocSearch-Footer\">\n          <Footer translations={footerTranslations} />\n        </footer>\n      </div>\n    </div>\n  );\n}\n", "import type { AutocompleteApi } from '@algolia/autocomplete-core';\nimport React from 'react';\n\ninterface UseTouchEventsProps {\n  getEnvironmentProps: AutocompleteApi<any>['getEnvironmentProps'];\n  panelElement: HTMLDivElement | null;\n  formElement: HTMLDivElement | null;\n  inputElement: HTMLInputElement | null;\n}\n\nexport function useTouchEvents({\n  getEnvironmentProps,\n  panelElement,\n  formElement,\n  inputElement,\n}: UseTouchEventsProps): void {\n  React.useEffect(() => {\n    if (!(panelElement && formElement && inputElement)) {\n      return undefined;\n    }\n\n    const { onTouchStart, onTouchMove } = getEnvironmentProps({\n      panelElement,\n      formElement,\n      inputElement,\n    });\n\n    window.addEventListener('touchstart', onTouchStart);\n    window.addEventListener('touchmove', onTouchMove);\n\n    return (): void => {\n      window.removeEventListener('touchstart', onTouchStart);\n      window.removeEventListener('touchmove', onTouchMove);\n    };\n  }, [getEnvironmentProps, panelElement, formElement, inputElement]);\n}\n", "import React from 'react';\n\ninterface UseTrapFocusProps {\n  container: HTMLElement | null;\n}\n\nexport function useTrapFocus({ container }: UseTrapFocusProps): void {\n  React.useEffect(() => {\n    if (!container) {\n      return undefined;\n    }\n\n    const focusableElements = container.querySelectorAll<HTMLElement>(\n      'a[href]:not([disabled]), button:not([disabled]), input:not([disabled])',\n    );\n    const firstElement = focusableElements[0];\n    const lastElement = focusableElements[focusableElements.length - 1];\n\n    function trapFocus(event: KeyboardEvent): void {\n      if (event.key !== 'Tab') {\n        return;\n      }\n\n      if (event.shiftKey) {\n        if (document.activeElement === firstElement) {\n          event.preventDefault();\n          lastElement.focus();\n        }\n      } else if (document.activeElement === lastElement) {\n        event.preventDefault();\n        firstElement.focus();\n      }\n    }\n\n    container.addEventListener('keydown', trapFocus);\n\n    return (): void => {\n      container.removeEventListener('keydown', trapFocus);\n    };\n  }, [container]);\n}\n", "import React from 'react';\n\nexport interface UseDocSearchKeyboardEventsProps {\n  isOpen: boolean;\n  onOpen: () => void;\n  onClose: () => void;\n  onInput?: (event: KeyboardEvent) => void;\n  searchButtonRef?: React.RefObject<HTMLButtonElement>;\n}\n\nfunction isEditingContent(event: KeyboardEvent): boolean {\n  const element = event.target as HTMLElement;\n  const tagName = element.tagName;\n\n  return element.isContentEditable || tagName === 'INPUT' || tagName === 'SELECT' || tagName === 'TEXTAREA';\n}\n\nexport function useDocSearchKeyboardEvents({\n  isOpen,\n  onOpen,\n  onClose,\n  onInput,\n  searchButtonRef,\n}: UseDocSearchKeyboardEventsProps): void {\n  React.useEffect(() => {\n    function onKeyDown(event: KeyboardEvent): void {\n      if (\n        (event.code === 'Escape' && isOpen) ||\n        // The `Cmd+K` shortcut both opens and closes the modal.\n        // We need to check for `event.key` because it can be `undefined` with\n        // Chrome's autofill feature.\n        // See https://github.com/paperjs/paper.js/issues/1398\n        (event.key?.toLowerCase() === 'k' && (event.metaKey || event.ctrlKey)) ||\n        // The `/` shortcut opens but doesn't close the modal because it's\n        // a character.\n        (!isEditingContent(event) && event.key === '/' && !isOpen)\n      ) {\n        event.preventDefault();\n\n        if (isOpen) {\n          onClose();\n        } else if (!document.body.classList.contains('DocSearch--active')) {\n          // We check that no other DocSearch modal is showing before opening\n          // another one.\n          onOpen();\n        }\n\n        return;\n      }\n\n      if (searchButtonRef && searchButtonRef.current === document.activeElement && onInput) {\n        if (/[a-zA-Z0-9]/.test(String.fromCharCode(event.keyCode))) {\n          onInput(event);\n        }\n      }\n    }\n\n    window.addEventListener('keydown', onKeyDown);\n\n    return (): void => {\n      window.removeEventListener('keydown', onKeyDown);\n    };\n  }, [isOpen, onOpen, onClose, onInput, searchButtonRef]);\n}\n", "import type { AutocompleteState, AutocompleteOptions } from '@algolia/autocomplete-core';\nimport type { LiteClient, SearchParamsObject } from 'algoliasearch/lite';\nimport React from 'react';\nimport { createPortal } from 'react-dom';\n\nimport { DocSearchButton } from './DocSearchButton';\nimport { DocSearchModal } from './DocSearchModal';\nimport type { DocSearchHit, InternalDocSearchHit, StoredDocSearchHit } from './types';\nimport { useDocSearchKeyboardEvents } from './useDocSearchKeyboardEvents';\n\nimport type { ButtonTranslations, ModalTranslations } from '.';\n\nexport type DocSearchTranslations = Partial<{\n  button: ButtonTranslations;\n  modal: ModalTranslations;\n}>;\n\n// The interface that describes the minimal implementation required for the algoliasearch client, when using the [`transformSearchClient`](https://docsearch.algolia.com/docs/api/#transformsearchclient) option.\nexport type DocSearchTransformClient = {\n  search: LiteClient['search'];\n  addAlgoliaAgent: LiteClient['addAlgoliaAgent'];\n  transporter: Pick<LiteClient['transporter'], 'algoliaAgent'>;\n};\n\nexport interface DocSearchProps {\n  appId: string;\n  apiKey: string;\n  indexName: string;\n  placeholder?: string;\n  searchParameters?: SearchParamsObject;\n  maxResultsPerGroup?: number;\n  transformItems?: (items: DocSearchHit[]) => DocSearchHit[];\n  hitComponent?: (props: { hit: InternalDocSearchHit | StoredDocSearchHit; children: React.ReactNode }) => JSX.Element;\n  resultsFooterComponent?: (props: { state: AutocompleteState<InternalDocSearchHit> }) => JSX.Element | null;\n  transformSearchClient?: (searchClient: DocSearchTransformClient) => DocSearchTransformClient;\n  disableUserPersonalization?: boolean;\n  initialQuery?: string;\n  navigator?: AutocompleteOptions<InternalDocSearchHit>['navigator'];\n  translations?: DocSearchTranslations;\n  getMissingResultsUrl?: ({ query }: { query: string }) => string;\n  insights?: AutocompleteOptions<InternalDocSearchHit>['insights'];\n}\n\nexport function DocSearch(props: DocSearchProps): JSX.Element {\n  const searchButtonRef = React.useRef<HTMLButtonElement>(null);\n  const [isOpen, setIsOpen] = React.useState(false);\n  const [initialQuery, setInitialQuery] = React.useState<string | undefined>(props?.initialQuery || undefined);\n\n  const onOpen = React.useCallback(() => {\n    setIsOpen(true);\n  }, [setIsOpen]);\n\n  const onClose = React.useCallback(() => {\n    setIsOpen(false);\n    setInitialQuery(props?.initialQuery);\n  }, [setIsOpen, props.initialQuery]);\n\n  const onInput = React.useCallback(\n    (event: KeyboardEvent) => {\n      setIsOpen(true);\n      setInitialQuery(event.key);\n    },\n    [setIsOpen, setInitialQuery],\n  );\n\n  useDocSearchKeyboardEvents({\n    isOpen,\n    onOpen,\n    onClose,\n    onInput,\n    searchButtonRef,\n  });\n\n  return (\n    <>\n      <DocSearchButton ref={searchButtonRef} translations={props?.translations?.button} onClick={onOpen} />\n\n      {isOpen &&\n        createPortal(\n          <DocSearchModal\n            {...props}\n            initialScrollY={window.scrollY}\n            initialQuery={initialQuery}\n            translations={props?.translations?.modal}\n            onClose={onClose}\n          />,\n          document.body,\n        )}\n    </>\n  );\n}\n"], "names": ["ControlKeyIcon", "React", "createElement", "width", "height", "className", "d", "strokeWidth", "stroke", "fill", "strokeLinecap", "SearchIcon", "viewBox", "fillRule", "strokeLinejoin", "ACTION_KEY_DEFAULT", "DocSearchButton", "forwardRef", "_ref", "ref", "_ref$translations", "translations", "props", "_objectWithoutProperties", "_excluded", "_translations$buttonT", "buttonText", "_translations$buttonA", "buttonAriaLabel", "_useState2", "_slicedToArray", "useState", "key", "<PERSON><PERSON><PERSON>", "useEffect", "navigator", "test", "platform", "_ref3", "actionKeyReactsTo", "actionKeyAltText", "action<PERSON>eyChild", "_extends", "type", "concat", "Fragment", "DocSearchButtonKey", "reactsToKey", "_ref4", "children", "_useState4", "isKeyDown", "setIsKeyDown", "window", "addEventListener", "handleKeyDown", "handleKeyUp", "removeEventListener", "e", "debounce", "fn", "time", "timerId", "undefined", "_len", "arguments", "length", "args", "Array", "_key", "clearTimeout", "setTimeout", "apply", "flatten", "values", "reduce", "a", "b", "autocompleteId", "getItemsCount", "state", "collections", "sum", "collection", "items", "isPrimitive", "obj", "Object", "isEqual", "first", "second", "keys", "_i", "_Object$keys", "noop", "userAgents", "segment", "version", "createClickedEvent", "item", "_ref$items", "index", "__autocomplete_indexName", "positions", "findIndex", "x", "objectID", "queryID", "__autocomplete_queryID", "algoliaSource", "arr", "i", "isArray", "_arrayWithHoles", "Symbol", "iterator", "_s", "_e", "_x", "_r", "_arr", "_n", "_d", "call", "next", "done", "push", "value", "err", "return", "_iterableToArrayLimit", "o", "minLen", "_arrayLikeToArray", "n", "prototype", "toString", "slice", "constructor", "name", "from", "_unsupportedIterableToArray", "TypeError", "_nonIterableRest", "len", "arr2", "_excluded2", "_typeof", "_toConsumableArray", "_arrayWithoutHoles", "iter", "_iterableToArray", "_nonIterableSpread", "source", "excluded", "target", "sourceKeys", "indexOf", "_objectWithoutPropertiesLoose", "getOwnPropertySymbols", "sourceSymbolKeys", "propertyIsEnumerable", "ownKeys", "object", "enumerableOnly", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "enumerable", "_objectSpread", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "arg", "input", "hint", "prim", "toPrimitive", "res", "String", "Number", "_toPrimitive", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "configurable", "writable", "mapToInsightsParamsApi", "params", "map", "param", "objectIDs", "_ref2", "createSearchInsightsApi", "searchInsights", "_split$map2", "major", "minor", "canSendHeaders", "split", "sendToInsights", "method", "payloads", "_items$0$__autocomple", "__autocomplete_algoliaCredentials", "headers", "appId", "<PERSON><PERSON><PERSON><PERSON>", "init", "setAuthenticatedUserToken", "authenticatedUserToken", "setUserToken", "userToken", "clickedObjectIDsAfterSearch", "clickedObjectIDs", "_len2", "_key2", "clickedFilters", "_len3", "_key3", "convertedObjectIDsAfterSearch", "_len4", "_key4", "convertedObjectIDs", "_len5", "_key5", "convertedFilters", "_len6", "_key6", "viewedObjectIDs", "_len7", "_key7", "acc", "chunkSize", "chunks", "chunk", "payload", "_ref5", "viewedFilters", "_len8", "_key8", "createViewedEvents", "itemsByIndexName", "current", "_acc$current$__autoco", "indexName", "isAlgoliaInsightsHit", "hit", "ALGOLIA_INSIGHTS_VERSION", "ALGOLIA_INSIGHTS_SRC", "sendViewedObjectIDs", "onItemsChange", "insights", "insightsEvents", "event", "eventName", "createAlgoliaInsightsPlugin", "options", "_getOptions", "_ref8", "getAlgoliaSources", "context", "onSelect", "_ref9", "onActive", "__autocomplete_clickAnalytics", "getOptions", "providedInsightsClient", "insightsClient", "insightsInitParams", "onSelectEvent", "onActiveEvent", "callback", "safelyRunOnBrowser", "pointer", "AlgoliaAnalyticsObject", "queue", "environment", "errorMessage", "script", "document", "async", "src", "onerror", "console", "error", "body", "append<PERSON><PERSON><PERSON>", "cause", "loadInsights", "partial", "previousItems", "debouncedOnStateChange", "isOpen", "subscribe", "setContext", "isAuthenticatedToken", "setInsightsContext", "algoliaInsightsPlugin", "__algoliaSearchParameters", "clickAnalytics", "normalizeUserToken", "_error", "getItems", "_ref6", "onStateChange", "_ref7", "__autocomplete_pluginOptions", "_context$algoliaInsig", "__automaticInsights", "createInternalCancelablePromise", "promise", "initialState", "then", "onfulfilled", "onrejected", "createCallback", "catch", "finally", "onfinally", "onCancelList", "cancel", "isCanceled", "callbacks", "cancelable", "onResult", "fallback", "getNextActiveItemId", "moveAmount", "baseIndex", "itemCount", "defaultActiveItemId", "numericIndex", "getActiveItem", "collectionIndex", "collectionsCount", "nextValue", "activeItemId", "getCollectionFromActiveItemId", "isOffsetFound", "counter", "previousItemsOffset", "currentCollection", "getRelativeActiveItemId", "itemInputValue", "getItemInputValue", "itemUrl", "getItemUrl", "getAutocompleteElementId", "autocompleteInstanceId", "elementId", "sourceId", "Boolean", "join", "replace", "regex", "getNativeEvent", "nativeEvent", "createStore", "reducer", "onStoreStateChange", "list", "getState", "dispatch", "action", "prevState", "pendingRequests", "add", "cancelablePromise", "cancelAll", "isEmpty", "getDefaultProps", "pluginSubscribers", "_props$id", "plugins", "debug", "openOnFocus", "enterKeyHint", "ignoreCompositionEvents", "placeholder", "autoFocus", "stallThreshold", "shouldPanelOpen", "reshape", "sources", "id", "query", "completion", "status", "_props$onStateChange", "_x$onStateChange", "onSubmit", "_props$onSubmit", "_x$onSubmit", "onReset", "_props$onReset", "_x$onReset", "getSources", "Promise", "all", "plugin", "seenSourceIds", "resolve", "maybeSource", "includes", "Error", "JSON", "stringify", "defaultSource", "setIsOpen", "onResolve", "__default", "normalizedSource", "getNormalizedSources", "nested", "_x$onSelect", "_x$onActive", "_x$onResolve", "navigate", "location", "assign", "navigateNewTab", "windowReference", "open", "focus", "navigateNewWindow", "isDescription", "execute", "preResolve", "itemsOrDescription", "description", "contextParameters", "requesterId", "_state$context$key", "requests", "queries", "transformResponse", "searchClient", "container", "find", "_container$items", "request", "maybeDescription", "responses", "postResolve", "store", "rawResults", "matches", "response", "results", "transform", "hits", "result", "facetHits", "_facetHits", "facetHit", "label", "count", "_highlightResult", "highlighted", "every", "basePromiseId", "latestResolvedId", "latestResolvedValue", "lastStalledId", "runConcurrentSafePromise", "currentPromiseId", "onInput", "_ref$nextState", "nextState", "refresh", "setters", "setCollections", "<PERSON><PERSON><PERSON><PERSON>", "setActiveItemId", "setStatus", "_nextState$isOpen", "_request", "_store$getState$conte", "some", "_automaticInsights", "isSearchResponseWithAutomaticInsightsFlag", "originalSourcesBySourceId", "sourcesBySourceId", "_nextState$isOpen2", "isPanelOpen", "highlightedItem", "_excluded3", "_excluded4", "_excluded5", "_excluded6", "getPropGetters", "getEnvironmentProps", "providedProps", "inputElement", "formElement", "panelElement", "onMouseDownOrTouchStart", "contextNode", "parent", "child", "contains", "onTouchStart", "onMouseDown", "onTouchMove", "activeElement", "blur", "getRootProps", "rest", "role", "getFormProps", "noValidate", "_providedProps$inputE", "preventDefault", "_providedProps$inputE2", "getLabelProps", "htmlFor", "getInputProps", "_props$environment$na", "onFocus", "_ref3$maxLength", "max<PERSON><PERSON><PERSON>", "activeItem", "shouldFallbackKeyHint", "userAgent", "match", "isSamsung", "autoComplete", "autoCorrect", "autoCapitalize", "spell<PERSON>heck", "onChange", "currentTarget", "isComposing", "onCompositionEnd", "onKeyDown", "triggerScrollIntoView", "nodeItem", "getElementById", "scrollIntoViewIfNeeded", "scrollIntoView", "triggerOnActive", "nextActiveItemId", "metaKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "_onKeyDown", "onBlur", "onClick", "getPanelProps", "onMouseLeave", "getListProps", "getItemProps", "__autocomplete_id", "onMouseMove", "_item", "_source", "getMetadata", "_", "_options$__autocomple", "_options$__autocomple2", "_options$__autocomple3", "optionsKey", "__autocomplete_metadata", "extraOptions", "ua", "getCompletion", "_getActiveItem", "stateReducer", "hasOwnProperty", "_nextState", "createAutocomplete", "subscribers", "_state$context", "_state$context$algoli", "isAlgoliaInsightsPluginEnabled", "subscribePlugins", "rawValue", "baseItemId", "getAutocompleteSetters", "propGetters", "Event", "_plugin$subscribe", "insightsParams", "_environment$navigato", "_environment$navigato2", "metadata", "metadataContainer", "headRef", "querySelector", "content", "injectMetadata", "AlgoliaLogo", "_translations$searchB", "searchByText", "href", "hostname", "rel", "xmlns", "y", "rx", "ry", "CommandIcon", "aria<PERSON><PERSON><PERSON>", "Footer", "_translations$selectT", "selectText", "_translations$selectK", "selectKeyAriaLabel", "_translations$navigat", "navigateText", "_translations$navigat2", "navigateUpKeyAriaLabel", "_translations$navigat3", "navigateDownKeyAriaLabel", "_translations$closeTe", "closeText", "_translations$closeKe", "closeKeyAriaLabel", "Hit", "url", "LoadingIcon", "strokeOpacity", "cx", "cy", "r", "attributeName", "to", "dur", "repeatCount", "RecentIcon", "ResetIcon", "SelectIcon", "LvlIcon", "SourceIcon", "ContentIcon", "AnchorIcon", "StarIcon", "ErrorIcon", "NoResultsIcon", "ErrorScreen", "_translations$titleTe", "titleText", "_translations$helpTex", "helpText", "NoResultsScreen", "_translations$noResul", "noResultsText", "_translations$suggest", "suggestedQueryText", "_translations$reportM", "reportMissingResultsText", "_translations$reportM2", "reportMissingResultsLinkText", "searchSuggestions", "search", "toLowerCase", "inputRef", "getMissingResultsUrl", "getPropertyByPath", "path", "prev", "Snippet", "attribute", "_ref$tagName", "tagName", "dangerouslySetInnerHTML", "__html", "Results", "title", "Result", "renderIcon", "renderAction", "onItemClick", "hitComponent", "_React$useState2", "isDeleting", "setIsDeleting", "_React$useState4", "isFavoriting", "setIsFavoriting", "useRef", "__docsearch_parent", "onTransitionEnd", "hierarchy", "runDeleteTransition", "cb", "runFavoriteTransition", "groupBy", "predicate", "maxResultsPerGroup", "identity", "isModifierEvent", "button", "regexHighlightTags", "regexHasHighlightTags", "RegExp", "removeHighlightTags", "_internalDocSearchHit", "_hit$_highlightResult", "internalDocSearchHit", "lvl0", "ResultsScreen", "_collection$items", "resultsFooterComponent", "StartScreen", "_translations$recentS", "recentSearchesTitle", "_translations$noRecen", "noRecentSearchesText", "_translations$saveRec", "saveRecentSearchButtonTitle", "_translations$removeR", "removeRecentSearchButtonTitle", "_translations$favorit", "favoriteSearchesTitle", "_translations$removeF", "removeFavoriteSearchButtonTitle", "hasCollections", "disableUserPersonalization", "stopPropagation", "favoriteSearches", "recentSearches", "remove", "ScreenState", "memo", "errorScreen", "noResultsScreen", "startScreen", "_prevProps", "nextProps", "SearchBox", "_translations$resetBu", "resetButtonTitle", "_translations$resetBu2", "resetButtonAriaLabel", "_translations$cancelB", "cancelButtonText", "_translations$cancelB2", "cancelButtonAriaLabel", "_translations$searchI", "searchInputLabel", "isFromSelection", "select", "hidden", "onClose", "createStorage", "localStorage", "setItem", "removeItem", "_unused", "isLocalStorageSupported", "getItem", "parse", "createStoredSearches", "_ref$limit", "limit", "storage", "_snippetResult", "isQueryAlreadySaved", "splice", "unshift", "getAll", "createBrowserLocalStorageCache", "namespaceKey", "getStorage", "getNamespace", "setNamespace", "namespace", "get", "defaultValue", "events", "miss", "timeToLive", "filteredNamespaceWithoutOldFormattedCacheItems", "fromEntries", "entries", "timestamp", "cacheItem", "currentTimestamp", "Date", "getTime", "exists", "set", "delete", "clear", "useSearchClient", "transformSearchClient", "useMemo", "client", "createLiteClient", "timeouts", "connect", "read", "write", "logger", "requester", "send", "t", "s", "XMLHttpRequest", "setRequestHeader", "abort", "isTimedOut", "u", "connectTimeout", "onreadystatechange", "readyState", "OPENED", "responseTimeout", "responseText", "onload", "data", "algoliaAgents", "authMode", "responsesCache", "createMemoryCache", "requestsCache", "serializable", "hostsCache", "createFallbackableCache", "caches", "apiClientVersion", "liteClient", "addAlgoliaAgent", "transporter", "algoliaAgent", "DocSearchModal", "_ref$placeholder", "searchParameters", "_ref$onClose", "_ref$transformItems", "transformItems", "_ref$hitComponent", "_ref$resultsFooterCom", "_ref$initialScrollY", "initialScrollY", "_ref$transformSearchC", "_ref$disableUserPerso", "_ref$initialQuery", "initialQuery", "initialQueryFromProp", "_ref$insights", "footerTranslations", "footer", "searchBoxTranslations", "searchBox", "screenStateTranslations", "setState", "containerRef", "modalRef", "formElementRef", "dropdownRef", "snippet<PERSON>ength", "initialQueryFromSelection", "getSelection", "saveRecentSearch", "useCallback", "sendItemClickEvent", "insightsItem", "insightsClickParams", "autocomplete", "sourcesState", "insightsActive", "attributesToRetrieve", "attributesToSnippet", "snippetEllipsisText", "highlightPreTag", "highlightPostTag", "hitsPerPage", "firstResult", "nbHits", "lvl1", "groupedHits", "potentialParent", "siblingItem", "flat", "_getEnvironmentProps", "useTouchEvents", "focusableElements", "querySelectorAll", "firstElement", "lastElement", "trapFocus", "useTrapFocus", "classList", "_window$scrollTo", "_window", "scrollTo", "matchMedia", "scrollTop", "setFullViewportHeight", "vh", "innerHeight", "style", "setProperty", "tabIndex", "useDocSearchKeyboardEvents", "onOpen", "searchButtonRef", "_event$key", "code", "element", "isContentEditable", "isEditingContent", "fromCharCode", "keyCode", "_props$translations", "_props$translations2", "setInitialQuery", "createPortal", "scrollY", "modal"], "mappings": ";+zXAEO,SAASA,IACd,OACEC,EAAAC,cAAA,MAAA,CAAKC,MAAM,KAAKC,OAAO,KAAKC,UAAU,8BACpCJ,EAAAC,cAAA,OAAA,CACEI,EAAE,qSACFC,YAAY,MACZC,OAAO,eACPC,KAAK,OACLC,cAAc,WAItB,CCZO,SAASC,IACd,OACEV,EAAAC,cAAA,MAAA,CAAKC,MAAM,KAAKC,OAAO,KAAKC,UAAU,wBAAwBO,QAAQ,YAAY,cAAY,QAC5FX,EAAAC,cAAA,OAAA,CACEI,EAAE,uMACFE,OAAO,eACPC,KAAK,OACLI,SAAS,UACTH,cAAc,QACdI,eAAe,UAIvB,wBCDMC,EAAqB,OAOdC,IAAAA,EAAkBf,EAAMgB,YACnC,SAAAC,EAAkCC,GAAQ,IAAAC,EAAAF,EAAvCG,aAAAA,OAAY,IAAAD,EAAG,CAAE,EAAAA,EAAKE,EAAKC,EAAAL,EAAAM,GAC5BC,EAA8DJ,EAAtDK,WAAAA,OAAa,IAAHD,EAAG,SAAQA,EAAAE,EAAiCN,EAA/BO,gBAAAA,OAAkB,IAAHD,EAAG,SAAQA,EAEuCE,EAAAC,EAA1EC,EAAQA,SAA6D,MAAK,GAAzFC,EAAGH,EAAA,GAAEI,EAAMJ,EAAA,GAElBK,EAAAA,WAAU,WACiB,oBAAdC,YAVR,0BAA0BC,KAAKD,UAAUE,UAWxBJ,EAdD,KAc4BA,EAAOlB,GAEvD,GAAE,IAEH,IAIyCuB,EAAAR,EAHvCE,IAAQjB,EAEH,CAACA,EAAoB,OAAQd,EAAAC,cAACF,SAC9B,CAAC,OAAQ,UAAWgC,GAAc,GAJlCO,EAAiBD,EAAA,GAAEE,EAAgBF,EAAA,GAAEG,EAAcH,EAAA,GAM1D,OACErC,EAAAC,cAAA,SAAAwC,EAAA,CACEC,KAAK,SACLtC,UAAU,6BACV,aAAA,GAAAuC,OAAehB,EAAegB,MAAAA,OAAKJ,EAAgB,QAC/ClB,EAAK,CACTH,IAAKA,IAELlB,EAAAC,cAAA,OAAA,CAAMG,UAAU,8BACdJ,EAAAC,cAACS,QACDV,EAAAC,cAAA,OAAA,CAAMG,UAAU,gCAAgCqB,IAGlDzB,EAAAC,cAAA,OAAA,CAAMG,UAAU,yBACL,OAAR2B,GACC/B,EAAAC,cAAAD,EAAA4C,SACE5C,KAAAA,EAAAC,cAAC4C,EAAkB,CAACC,YAAaR,GAAoBE,GACrDxC,EAAAC,cAAC4C,EAAkB,CAACC,YAAY,KAAI,OAMhD,IAOF,SAASD,EAAkBE,GAA2F,IAAxFD,EAAWC,EAAXD,YAAaE,EAAQD,EAARC,SACQC,EAAApB,EAAfC,EAAQA,UAAC,GAAM,GAA1CoB,EAASD,EAAA,GAAEE,EAAYF,EAAA,GAkC9B,OAhCAhB,EAAAA,WAAU,WACR,GAAKa,EAyBL,OAHAM,OAAOC,iBAAiB,UAAWC,GACnCF,OAAOC,iBAAiB,QAASE,GAE1B,WACLH,OAAOI,oBAAoB,UAAWF,GACtCF,OAAOI,oBAAoB,QAASD,EACrC,EAxBD,SAASD,EAAcG,GACjBA,EAAE1B,MAAQe,GACZK,GAAa,EAEjB,CAEA,SAASI,EAAYE,GAEjBA,EAAE1B,MAAQe,GAIA,SAAVW,EAAE1B,KAEFoB,GAAa,EAEjB,CASF,GAAG,CAACL,IAGF9C,EAAAC,cAAA,MAAA,CAAKG,UAAW8C,EAAY,qDAAuD,wBAChFF,EAGP,CC7GO,SAASU,EAASC,EAAIC,GAC3B,IAAIC,OAAUC,EACd,OAAO,WACL,IAAK,IAAIC,EAAOC,UAAUC,OAAQC,EAAO,IAAIC,MAAMJ,GAAOK,EAAO,EAAGA,EAAOL,EAAMK,IAC/EF,EAAKE,GAAQJ,UAAUI,GAErBP,GACFQ,aAAaR,GAEfA,EAAUS,YAAW,WACnB,OAAOX,EAAGY,WAAM,EAAQL,EACzB,GAAEN,EACJ,CACH,CCbO,SAASY,EAAQC,GACtB,OAAOA,EAAOC,QAAO,SAAUC,EAAGC,GAChC,OAAOD,EAAEhC,OAAOiC,EACjB,GAAE,GACL,CCJA,IAAIC,EAAiB,ECAd,SAASC,EAAcC,GAC5B,OAAiC,IAA7BA,EAAMC,YAAYf,OACb,EAEFc,EAAMC,YAAYN,QAAO,SAAUO,EAAKC,GAC7C,OAAOD,EAAMC,EAAWC,MAAMlB,MAC/B,GAAE,EACL,CCPA,SAASmB,EAAYC,GACnB,OAAOA,IAAQC,OAAOD,EACxB,CACO,SAASE,EAAQC,EAAOC,GAC7B,GAAID,IAAUC,EACZ,OAAO,EAET,GAAIL,EAAYI,IAAUJ,EAAYK,IAA4B,mBAAVD,GAA0C,mBAAXC,EACrF,OAAOD,IAAUC,EAEnB,GAAIH,OAAOI,KAAKF,GAAOvB,SAAWqB,OAAOI,KAAKD,GAAQxB,OACpD,OAAO,EAET,IAAK,IAAI0B,EAAK,EAAGC,EAAeN,OAAOI,KAAKF,GAAQG,EAAKC,EAAa3B,OAAQ0B,IAAM,CAClF,IAAI5D,EAAM6D,EAAaD,GACvB,KAAM5D,KAAO0D,GACX,OAAO,EAET,IAAKF,EAAQC,EAAMzD,GAAM0D,EAAO1D,IAC9B,OAAO,CAEX,CACA,OAAO,CACT,CCvBO,IAAI8D,EAAO,WAAkB,ECA7B,ICCIC,EAAa,CAAC,CACvBC,QAAS,oBACTC,QDHmB,WEAd,SAASC,EAAmBhF,GACjC,IAAIiF,EAAOjF,EAAKiF,KACdC,EAAalF,EAAKkE,MAClBA,OAAuB,IAAfgB,EAAwB,GAAKA,EACvC,MAAO,CACLC,MAAOF,EAAKG,yBACZlB,MAAO,CAACe,GACRI,UAAW,CAAC,EAAInB,EAAMoB,WAAU,SAAUC,GACxC,OAAOA,EAAEC,WAAaP,EAAKO,QAC7B,KACAC,QAASR,EAAKS,uBACdC,cAAe,CAAC,gBAEpB,CCbA,SAAS/E,EAAegF,EAAKC,GAAK,OAKlC,SAAyBD,GAAO,GAAI1C,MAAM4C,QAAQF,GAAM,OAAOA,CAAK,CAL3BG,CAAgBH,IAIzD,SAA+BA,EAAKC,GAAK,IAAInB,EAAK,MAAQkB,EAAM,KAAO,oBAAsBI,QAAUJ,EAAII,OAAOC,WAAaL,EAAI,cAAe,GAAI,MAAQlB,EAAI,CAAE,IAAIwB,EAAIC,EAAIC,EAAIC,EAAIC,EAAO,GAAIC,GAAK,EAAIC,GAAK,EAAI,IAAM,GAAIJ,GAAM1B,EAAKA,EAAG+B,KAAKb,IAAMc,KAAM,IAAMb,QAAoD,OAASU,GAAML,EAAKE,EAAGK,KAAK/B,IAAKiC,QAAUL,EAAKM,KAAKV,EAAGW,OAAQP,EAAKtD,SAAW6C,GAAIU,GAAK,GAAM,CAAC,MAAOO,GAAON,GAAK,EAAIL,EAAKW,CAAK,CAAU,QAAE,IAAM,IAAKP,GAAM,MAAQ7B,EAAGqC,SAAWV,EAAK3B,EAAGqC,SAAU1C,OAAOgC,KAAQA,GAAK,MAAQ,CAAU,QAAE,GAAIG,EAAI,MAAML,CAAI,CAAE,CAAE,OAAOG,CAAM,CAAE,CAJ1gBU,CAAsBpB,EAAKC,IAE5F,SAAqCoB,EAAGC,GAAU,IAAKD,EAAG,OAAQ,GAAiB,iBAANA,EAAgB,OAAOE,EAAkBF,EAAGC,GAAS,IAAIE,EAAI/C,OAAOgD,UAAUC,SAASb,KAAKQ,GAAGM,MAAM,GAAI,GAAc,WAANH,GAAkBH,EAAEO,cAAaJ,EAAIH,EAAEO,YAAYC,MAAM,GAAU,QAANL,GAAqB,QAANA,EAAa,OAAOlE,MAAMwE,KAAKT,GAAI,GAAU,cAANG,GAAqB,2CAA2ClG,KAAKkG,GAAI,OAAOD,EAAkBF,EAAGC,EAAS,CAF7TS,CAA4B/B,EAAKC,IACnI,WAA8B,MAAM,IAAI+B,UAAU,4IAA8I,CADvDC,EAAoB,CAG7J,SAASV,EAAkBvB,EAAKkC,IAAkB,MAAPA,GAAeA,EAAMlC,EAAI5C,UAAQ8E,EAAMlC,EAAI5C,QAAQ,IAAK,IAAI6C,EAAI,EAAGkC,EAAO,IAAI7E,MAAM4E,GAAMjC,EAAIiC,EAAKjC,IAAKkC,EAAKlC,GAAKD,EAAIC,GAAI,OAAOkC,CAAM,CCHlL,IAAIzH,EAAY,CAAC,SACf0H,EAAa,CAAC,SAChB,SAASC,EAAQ7D,GAAkC,OAAO6D,EAAU,mBAAqBjC,QAAU,iBAAmBA,OAAOC,SAAW,SAAU7B,GAAO,cAAcA,CAAM,EAAG,SAAUA,GAAO,OAAOA,GAAO,mBAAqB4B,QAAU5B,EAAIoD,cAAgBxB,QAAU5B,IAAQ4B,OAAOqB,UAAY,gBAAkBjD,CAAK,EAAG6D,EAAQ7D,EAAM,CAC/U,SAAS8D,EAAmBtC,GAAO,OAInC,SAA4BA,GAAO,GAAI1C,MAAM4C,QAAQF,GAAM,OAAOuB,EAAkBvB,EAAM,CAJhDuC,CAAmBvC,IAG7D,SAA0BwC,GAAQ,GAAsB,oBAAXpC,QAAmD,MAAzBoC,EAAKpC,OAAOC,WAA2C,MAAtBmC,EAAK,cAAuB,OAAOlF,MAAMwE,KAAKU,EAAO,CAHxFC,CAAiBzC,IAEtF,SAAqCqB,EAAGC,GAAU,IAAKD,EAAG,OAAQ,GAAiB,iBAANA,EAAgB,OAAOE,EAAkBF,EAAGC,GAAS,IAAIE,EAAI/C,OAAOgD,UAAUC,SAASb,KAAKQ,GAAGM,MAAM,GAAI,GAAc,WAANH,GAAkBH,EAAEO,cAAaJ,EAAIH,EAAEO,YAAYC,MAAM,GAAU,QAANL,GAAqB,QAANA,EAAa,OAAOlE,MAAMwE,KAAKT,GAAI,GAAU,cAANG,GAAqB,2CAA2ClG,KAAKkG,GAAI,OAAOD,EAAkBF,EAAGC,EAAS,CAFjUS,CAA4B/B,IAC1H,WAAgC,MAAM,IAAIgC,UAAU,uIAAyI,CAD3DU,EAAsB,CAKxJ,SAASnB,EAAkBvB,EAAKkC,IAAkB,MAAPA,GAAeA,EAAMlC,EAAI5C,UAAQ8E,EAAMlC,EAAI5C,QAAQ,IAAK,IAAI6C,EAAI,EAAGkC,EAAO,IAAI7E,MAAM4E,GAAMjC,EAAIiC,EAAKjC,IAAKkC,EAAKlC,GAAKD,EAAIC,GAAI,OAAOkC,CAAM,CAClL,SAAS1H,EAAyBkI,EAAQC,GAAY,GAAc,MAAVD,EAAgB,MAAO,CAAE,EAAE,IAAkEzH,EAAK+E,EAAnE4C,EACzF,SAAuCF,EAAQC,GAAY,GAAc,MAAVD,EAAgB,MAAO,CAAE,EAAE,IAA2DzH,EAAK+E,EAA5D4C,EAAS,CAAE,EAAMC,EAAarE,OAAOI,KAAK8D,GAAqB,IAAK1C,EAAI,EAAGA,EAAI6C,EAAW1F,OAAQ6C,IAAO/E,EAAM4H,EAAW7C,GAAQ2C,EAASG,QAAQ7H,IAAQ,IAAa2H,EAAO3H,GAAOyH,EAAOzH,IAAQ,OAAO2H,CAAQ,CADhNG,CAA8BL,EAAQC,GAAuB,GAAInE,OAAOwE,sBAAuB,CAAE,IAAIC,EAAmBzE,OAAOwE,sBAAsBN,GAAS,IAAK1C,EAAI,EAAGA,EAAIiD,EAAiB9F,OAAQ6C,IAAO/E,EAAMgI,EAAiBjD,GAAQ2C,EAASG,QAAQ7H,IAAQ,GAAkBuD,OAAOgD,UAAU0B,qBAAqBtC,KAAK8B,EAAQzH,KAAgB2H,EAAO3H,GAAOyH,EAAOzH,GAAQ,CAAE,OAAO2H,CAAQ,CAE3e,SAASO,EAAQC,EAAQC,GAAkB,IAAIzE,EAAOJ,OAAOI,KAAKwE,GAAS,GAAI5E,OAAOwE,sBAAuB,CAAE,IAAIM,EAAU9E,OAAOwE,sBAAsBI,GAASC,IAAmBC,EAAUA,EAAQC,QAAO,SAAUC,GAAO,OAAOhF,OAAOiF,yBAAyBL,EAAQI,GAAKE,UAAY,KAAK9E,EAAKmC,KAAKtD,MAAMmB,EAAM0E,EAAU,CAAE,OAAO1E,CAAM,CACpV,SAAS+E,EAAcf,GAAU,IAAK,IAAI5C,EAAI,EAAGA,EAAI9C,UAAUC,OAAQ6C,IAAK,CAAE,IAAI0C,EAAS,MAAQxF,UAAU8C,GAAK9C,UAAU8C,GAAK,CAAE,EAAEA,EAAI,EAAImD,EAAQ3E,OAAOkE,IAAS,GAAIkB,SAAQ,SAAU3I,GAAO4I,EAAgBjB,EAAQ3H,EAAKyH,EAAOzH,GAAO,IAAKuD,OAAOsF,0BAA4BtF,OAAOuF,iBAAiBnB,EAAQpE,OAAOsF,0BAA0BpB,IAAWS,EAAQ3E,OAAOkE,IAASkB,SAAQ,SAAU3I,GAAOuD,OAAOwF,eAAepB,EAAQ3H,EAAKuD,OAAOiF,yBAAyBf,EAAQzH,GAAO,GAAI,CAAE,OAAO2H,CAAQ,CACzf,SAASiB,EAAgBtF,EAAKtD,EAAK+F,GAA4L,OAAnL/F,EAC5C,SAAwBgJ,GAAO,IAAIhJ,EACnC,SAAsBiJ,EAAOC,GAAQ,GAAuB,WAAnB/B,EAAQ8B,IAAiC,OAAVA,EAAgB,OAAOA,EAAO,IAAIE,EAAOF,EAAM/D,OAAOkE,aAAc,QAAarH,IAAToH,EAAoB,CAAE,IAAIE,EAAMF,EAAKxD,KAAKsD,EAAOC,GAAQ,WAAY,GAAqB,WAAjB/B,EAAQkC,GAAmB,OAAOA,EAAK,MAAM,IAAIvC,UAAU,+CAAiD,CAAE,OAAiB,WAAToC,EAAoBI,OAASC,QAAQN,EAAQ,CADnVO,CAAaR,EAAK,UAAW,MAAwB,WAAjB7B,EAAQnH,GAAoBA,EAAMsJ,OAAOtJ,EAAM,CAD1EyJ,CAAezJ,MAAiBsD,EAAOC,OAAOwF,eAAezF,EAAKtD,EAAK,CAAE+F,MAAOA,EAAO0C,YAAY,EAAMiB,cAAc,EAAMC,UAAU,IAAkBrG,EAAItD,GAAO+F,EAAgBzC,CAAK,CAc3O,SAASsG,EAAuBC,GAC9B,OAAOA,EAAOC,KAAI,SAAU5K,GAC1B,IAAIkE,EAAQlE,EAAKkE,MACf2G,EAAQxK,EAAyBL,EAAMM,GACzC,OAAOkJ,EAAcA,EAAc,CAAE,EAAEqB,GAAQ,CAAA,EAAI,CACjDC,WAAY5G,aAAqC,EAASA,EAAM0G,KAAI,SAAUG,GAE5E,OADeA,EAAMvF,QAEtB,MAAMqF,EAAMC,WAEjB,GACF,CACO,SAASE,EAAwBC,GACtC,ID5BEC,EACAC,EACAC,EC0BEC,GD5BFH,EAActK,GC4B4BqK,ED7BnBlG,SAAW,IAAIuG,MAAM,KAAKV,IAAIP,QACZ,GACzCc,EAAQD,EAAY,GACpBE,EAAQF,EAAY,GAGbC,GAAS,GACG,IAAVA,GAAeC,GAAS,GACb,IAAVD,GAAeC,GAAS,ICsBpC,SAASG,EAAeC,EAAQC,EAAUvH,GACxC,GAAImH,QAAmC,IAAVnH,EAAuB,CAClD,IAAIwH,EAAwBxH,EAAM,GAAGyH,kCAGjCC,EAAU,CACZ,2BAHQF,EAAsBG,MAI9B,oBAHSH,EAAsBI,QAKjCb,EAAe3H,WAAM,EAAQ,CAACkI,GAAQ9J,OAAOwG,EAAmBuD,GAAW,CAAC,CAC1EG,QAASA,KAEb,MACEX,EAAe3H,WAAM,EAAQ,CAACkI,GAAQ9J,OAAOwG,EAAmBuD,IAEpE,CACA,MAAO,CAILM,KAAM,SAAcF,EAAOC,GACzBb,EAAe,OAAQ,CACrBY,MAAOA,EACPC,OAAQA,GAEX,EAODE,0BAA2B,SAAmCC,GAC5DhB,EAAe,4BAA6BgB,EAC7C,EAIDC,aAAc,SAAsBC,GAClClB,EAAe,eAAgBkB,EAChC,EAMDC,4BAA6B,WAC3B,IAAK,IAAItJ,EAAOC,UAAUC,OAAQ2H,EAAS,IAAIzH,MAAMJ,GAAOK,EAAO,EAAGA,EAAOL,EAAMK,IACjFwH,EAAOxH,GAAQJ,UAAUI,GAEvBwH,EAAO3H,OAAS,GAClBuI,EAAe,8BAA+Bb,EAAuBC,GAASA,EAAO,GAAGzG,MAE3F,EAMDmI,iBAAkB,WAChB,IAAK,IAAIC,EAAQvJ,UAAUC,OAAQ2H,EAAS,IAAIzH,MAAMoJ,GAAQC,EAAQ,EAAGA,EAAQD,EAAOC,IACtF5B,EAAO4B,GAASxJ,UAAUwJ,GAExB5B,EAAO3H,OAAS,GAClBuI,EAAe,mBAAoBb,EAAuBC,GAASA,EAAO,GAAGzG,MAEhF,EAMDsI,eAAgB,WACd,IAAK,IAAIC,EAAQ1J,UAAUC,OAAQ2H,EAAS,IAAIzH,MAAMuJ,GAAQC,EAAQ,EAAGA,EAAQD,EAAOC,IACtF/B,EAAO+B,GAAS3J,UAAU2J,GAExB/B,EAAO3H,OAAS,GAClBiI,EAAe3H,WAAM,EAAQ,CAAC,kBAAkB5B,OAAOiJ,GAE1D,EAMDgC,8BAA+B,WAC7B,IAAK,IAAIC,EAAQ7J,UAAUC,OAAQ2H,EAAS,IAAIzH,MAAM0J,GAAQC,EAAQ,EAAGA,EAAQD,EAAOC,IACtFlC,EAAOkC,GAAS9J,UAAU8J,GAExBlC,EAAO3H,OAAS,GAClBuI,EAAe,gCAAiCb,EAAuBC,GAASA,EAAO,GAAGzG,MAE7F,EAMD4I,mBAAoB,WAClB,IAAK,IAAIC,EAAQhK,UAAUC,OAAQ2H,EAAS,IAAIzH,MAAM6J,GAAQC,EAAQ,EAAGA,EAAQD,EAAOC,IACtFrC,EAAOqC,GAASjK,UAAUiK,GAExBrC,EAAO3H,OAAS,GAClBuI,EAAe,qBAAsBb,EAAuBC,GAASA,EAAO,GAAGzG,MAElF,EAMD+I,iBAAkB,WAChB,IAAK,IAAIC,EAAQnK,UAAUC,OAAQ2H,EAAS,IAAIzH,MAAMgK,GAAQC,EAAQ,EAAGA,EAAQD,EAAOC,IACtFxC,EAAOwC,GAASpK,UAAUoK,GAExBxC,EAAO3H,OAAS,GAClBiI,EAAe3H,WAAM,EAAQ,CAAC,oBAAoB5B,OAAOiJ,GAE5D,EAMDyC,gBAAiB,WACf,IAAK,IAAIC,EAAQtK,UAAUC,OAAQ2H,EAAS,IAAIzH,MAAMmK,GAAQC,EAAQ,EAAGA,EAAQD,EAAOC,IACtF3C,EAAO2C,GAASvK,UAAUuK,GAExB3C,EAAO3H,OAAS,GAClB2H,EAAOlH,QAAO,SAAU8J,EAAKnM,GAC3B,IAAI8C,EAAQ9C,EAAM8C,MAChB2G,EAAQxK,EAAyBe,EAAO4G,GAC1C,MAAO,GAAGtG,OAAOwG,EAAmBqF,GAAMrF,EA5JpD,SAAejD,GAGb,IAFA,IAAIuI,EAAYzK,UAAUC,OAAS,QAAsBH,IAAjBE,UAAU,GAAmBA,UAAU,GAAK,GAChF0K,EAAS,GACJ5H,EAAI,EAAGA,EAAIZ,EAAK6F,UAAU9H,OAAQ6C,GAAK2H,EAC9CC,EAAO7G,KAAK4C,EAAcA,EAAc,CAAA,EAAIvE,GAAO,GAAI,CACrD6F,UAAW7F,EAAK6F,UAAUvD,MAAM1B,EAAGA,EAAI2H,MAG3C,OAAOC,CACT,CAmJuEC,CAAMlE,EAAcA,EAAc,CAAA,EAAIqB,GAAQ,CAAA,EAAI,CAC7GC,WAAY5G,aAAqC,EAASA,EAAM0G,KAAI,SAAU9I,GAE5E,OADeA,EAAM0D,QAEtB,MAAMqF,EAAMC,aACXF,KAAI,SAAU+C,GAChB,MAAO,CACLzJ,MAAOA,EACPyJ,QAASA,EAEZ,KACF,GAAE,IAAIlE,SAAQ,SAAUmE,GACvB,IAAI1J,EAAQ0J,EAAM1J,MAElB,OAAOqH,EAAe,kBAAmB,CAD7BqC,EAAMD,SACkCzJ,EACtD,GAEH,EAMD2J,cAAe,WACb,IAAK,IAAIC,EAAQ/K,UAAUC,OAAQ2H,EAAS,IAAIzH,MAAM4K,GAAQC,EAAQ,EAAGA,EAAQD,EAAOC,IACtFpD,EAAOoD,GAAShL,UAAUgL,GAExBpD,EAAO3H,OAAS,GAClBiI,EAAe3H,WAAM,EAAQ,CAAC,iBAAiB5B,OAAOiJ,GAE1D,EAEJ,CC7MO,SAASqD,EAAmBhO,GACjC,IACIiO,EADQjO,EAAKkE,MACYT,QAAO,SAAU8J,EAAKW,GACjD,IAAIC,EAEJ,OADAZ,EAAIW,EAAQ9I,2BAAiG,QAAnE+I,EAAwBZ,EAAIW,EAAQ9I,iCAAiE,IAA1B+I,EAAmCA,EAAwB,IAAIzM,OAAOwM,GACpLX,CACR,GAAE,IACH,OAAOlJ,OAAOI,KAAKwJ,GAAkBrD,KAAI,SAAUwD,GAEjD,MAAO,CACLjJ,MAAOiJ,EACPlK,MAHU+J,EAAiBG,GAI3BzI,cAAe,CAAC,gBAEpB,GACF,CCfO,SAAS0I,EAAqBC,GACnC,OAAOA,EAAI9I,UAAY8I,EAAIlJ,0BAA4BkJ,EAAI5I,sBAC7D,CCFA,SAASuC,EAAQ7D,GAAkC,OAAO6D,EAAU,mBAAqBjC,QAAU,iBAAmBA,OAAOC,SAAW,SAAU7B,GAAO,cAAcA,CAAM,EAAG,SAAUA,GAAO,OAAOA,GAAO,mBAAqB4B,QAAU5B,EAAIoD,cAAgBxB,QAAU5B,IAAQ4B,OAAOqB,UAAY,gBAAkBjD,CAAK,EAAG6D,EAAQ7D,EAAM,CAC/U,SAAS8D,GAAmBtC,GAAO,OAInC,SAA4BA,GAAO,GAAI1C,MAAM4C,QAAQF,GAAM,OAAOuB,GAAkBvB,EAAM,CAJhDuC,CAAmBvC,IAG7D,SAA0BwC,GAAQ,GAAsB,oBAAXpC,QAAmD,MAAzBoC,EAAKpC,OAAOC,WAA2C,MAAtBmC,EAAK,cAAuB,OAAOlF,MAAMwE,KAAKU,EAAO,CAHxFC,CAAiBzC,IAEtF,SAAqCqB,EAAGC,GAAU,IAAKD,EAAG,OAAQ,GAAiB,iBAANA,EAAgB,OAAOE,GAAkBF,EAAGC,GAAS,IAAIE,EAAI/C,OAAOgD,UAAUC,SAASb,KAAKQ,GAAGM,MAAM,GAAI,GAAc,WAANH,GAAkBH,EAAEO,cAAaJ,EAAIH,EAAEO,YAAYC,MAAM,GAAU,QAANL,GAAqB,QAANA,EAAa,OAAOlE,MAAMwE,KAAKT,GAAI,GAAU,cAANG,GAAqB,2CAA2ClG,KAAKkG,GAAI,OAAOD,GAAkBF,EAAGC,EAAS,CAFjUS,CAA4B/B,IAC1H,WAAgC,MAAM,IAAIgC,UAAU,uIAAyI,CAD3DU,EAAsB,CAKxJ,SAASnB,GAAkBvB,EAAKkC,IAAkB,MAAPA,GAAeA,EAAMlC,EAAI5C,UAAQ8E,EAAMlC,EAAI5C,QAAQ,IAAK,IAAI6C,EAAI,EAAGkC,EAAO,IAAI7E,MAAM4E,GAAMjC,EAAIiC,EAAKjC,IAAKkC,EAAKlC,GAAKD,EAAIC,GAAI,OAAOkC,CAAM,CAClL,SAASiB,GAAQC,EAAQC,GAAkB,IAAIzE,EAAOJ,OAAOI,KAAKwE,GAAS,GAAI5E,OAAOwE,sBAAuB,CAAE,IAAIM,EAAU9E,OAAOwE,sBAAsBI,GAASC,IAAmBC,EAAUA,EAAQC,QAAO,SAAUC,GAAO,OAAOhF,OAAOiF,yBAAyBL,EAAQI,GAAKE,UAAY,KAAK9E,EAAKmC,KAAKtD,MAAMmB,EAAM0E,EAAU,CAAE,OAAO1E,CAAM,CACpV,SAAS+E,GAAcf,GAAU,IAAK,IAAI5C,EAAI,EAAGA,EAAI9C,UAAUC,OAAQ6C,IAAK,CAAE,IAAI0C,EAAS,MAAQxF,UAAU8C,GAAK9C,UAAU8C,GAAK,CAAE,EAAEA,EAAI,EAAImD,GAAQ3E,OAAOkE,IAAS,GAAIkB,SAAQ,SAAU3I,GAAO4I,GAAgBjB,EAAQ3H,EAAKyH,EAAOzH,GAAO,IAAKuD,OAAOsF,0BAA4BtF,OAAOuF,iBAAiBnB,EAAQpE,OAAOsF,0BAA0BpB,IAAWS,GAAQ3E,OAAOkE,IAASkB,SAAQ,SAAU3I,GAAOuD,OAAOwF,eAAepB,EAAQ3H,EAAKuD,OAAOiF,yBAAyBf,EAAQzH,GAAO,GAAI,CAAE,OAAO2H,CAAQ,CACzf,SAASiB,GAAgBtF,EAAKtD,EAAK+F,GAA4L,OAAnL/F,EAC5C,SAAwBgJ,GAAO,IAAIhJ,EACnC,SAAsBiJ,EAAOC,GAAQ,GAAuB,WAAnB/B,EAAQ8B,IAAiC,OAAVA,EAAgB,OAAOA,EAAO,IAAIE,EAAOF,EAAM/D,OAAOkE,aAAc,QAAarH,IAAToH,EAAoB,CAAE,IAAIE,EAAMF,EAAKxD,KAAKsD,EAAOC,GAAQ,WAAY,GAAqB,WAAjB/B,EAAQkC,GAAmB,OAAOA,EAAK,MAAM,IAAIvC,UAAU,+CAAiD,CAAE,OAAiB,WAAToC,EAAoBI,OAASC,QAAQN,EAAQ,CADnVO,CAAaR,EAAK,UAAW,MAAwB,WAAjB7B,EAAQnH,GAAoBA,EAAMsJ,OAAOtJ,EAAM,CAD1EyJ,CAAezJ,MAAiBsD,EAAOC,OAAOwF,eAAezF,EAAKtD,EAAK,CAAE+F,MAAOA,EAAO0C,YAAY,EAAMiB,cAAc,EAAMC,UAAU,IAAkBrG,EAAItD,GAAO+F,EAAgBzC,CAAK,CAQ3O,IACImK,GAA2B,SAC3BC,GAAuB,gDAAgD9M,OAAO6M,GAA0B,gCACxGE,GAAsBhM,GAAS,SAAUzC,GAC3C,IAAI0O,EAAgB1O,EAAK0O,cACvBxK,EAAQlE,EAAKkE,MACbyK,EAAW3O,EAAK2O,SAChB7K,EAAQ9D,EAAK8D,MACf4K,EAAc,CACZC,SAAUA,EACVC,eAAgBZ,EAAmB,CACjC9J,MAAOA,IACN0G,KAAI,SAAUiE,GACf,OAAOrF,GAAc,CACnBsF,UAAW,gBACVD,EACL,IACA/K,MAAOA,GAEX,GAnBuB,KAoBhB,SAASiL,GAA4BC,GAC1C,IAAIC,EAqLN,SAAoBD,GAClB,OAAOxF,GAAc,CACnBkF,cAAe,SAAuBQ,GACpC,IAAIP,EAAWO,EAAMP,SACnBC,EAAiBM,EAAMN,eACvB9K,EAAQoL,EAAMpL,MAChB6K,EAASvB,gBAAgB9J,MAAMqL,EAAUzG,GAAmB0G,EAAehE,KAAI,SAAUiE,GACvF,OAAOrF,GAAcA,GAAc,CAAE,EAAEqF,GAAQ,CAAA,EAAI,CACjDlJ,cAAewJ,GAAkBN,EAAMlJ,cAAe7B,EAAMsL,UAE/D,KACF,EACDC,SAAU,SAAkBC,GAC1B,IAAIX,EAAWW,EAAMX,SACnBC,EAAiBU,EAAMV,eACvB9K,EAAQwL,EAAMxL,MAChB6K,EAASvC,4BAA4B9I,MAAMqL,EAAUzG,GAAmB0G,EAAehE,KAAI,SAAUiE,GACnG,OAAOrF,GAAcA,GAAc,CAAE,EAAEqF,GAAQ,CAAA,EAAI,CACjDlJ,cAAewJ,GAAkBN,EAAMlJ,cAAe7B,EAAMsL,UAE/D,KACF,EACDG,SAAU3K,EACV4K,+BAA+B,GAC9BR,EACL,CA9MoBS,CAAWT,GAC3BU,EAAyBT,EAAYU,eACrCC,EAAqBX,EAAYW,mBACjClB,EAAgBO,EAAYP,cAC5BmB,EAAgBZ,EAAYI,SAC5BS,EAAgBb,EAAYM,SAC5BC,EAAgCP,EAAYO,8BAC1CG,EAAiBD,EA8BrB,GA7BKA,GC3CA,SAA4BK,GACjC,GAAsB,oBAAX5N,OACF4N,EAAS,CACd5N,OAAQA,QAId,CDqCI6N,EAAmB,SAAUjF,GAC3B,IAAI5I,EAAS4I,EAAM5I,OACf8N,EAAU9N,EAAO+N,wBAA0B,KACxB,iBAAZD,IACTN,EAAiBxN,EAAO8N,IAErBN,IACHxN,EAAO+N,uBAAyBD,EAC3B9N,EAAO8N,KACV9N,EAAO8N,GAAW,WACX9N,EAAO8N,GAASE,QACnBhO,EAAO8N,GAASE,MAAQ,IAE1B,IAAK,IAAIrN,EAAOC,UAAUC,OAAQC,EAAO,IAAIC,MAAMJ,GAAOK,EAAO,EAAGA,EAAOL,EAAMK,IAC/EF,EAAKE,GAAQJ,UAAUI,GAEzBhB,EAAO8N,GAASE,MAAMvJ,KAAK3D,EAC5B,GAEHd,EAAO8N,GAASlL,QAAUwJ,GAC1BoB,EAAiBxN,EAAO8N,GAkLhC,SAAsBG,GACpB,IAAIC,EAAe,4HACnB,IACE,IAAIC,EAASF,EAAYG,SAASvR,cAAc,UAChDsR,EAAOE,OAAQ,EACfF,EAAOG,IAAMjC,GACb8B,EAAOI,QAAU,WAEfC,QAAQC,MAAMP,EACf,EACDE,SAASM,KAAKC,YAAYR,EAC3B,CAAC,MAAOS,GAEPJ,QAAQC,MAAMP,EAChB,CACF,CAhMQW,CAAa7O,GAEjB,KAKGwN,EACH,MAAO,CAAE,EAEPC,GACFD,EAAe,OAAQnG,GAAc,CACnCyH,SAAS,GACRrB,IAEL,IAAIjB,EAAW3D,EAAwB2E,GACnCuB,EEnFG,CACLhD,QFkF4B,IAC1BiD,EAAyB1O,GAAS,SAAUrB,GAC9C,IAAI0C,EAAQ1C,EAAM0C,MAClB,GAAKA,EAAMsN,OAAX,CAGA,IAAIlN,EAAQJ,EAAMC,YAAYN,QAAO,SAAU8J,EAAKW,GAClD,MAAO,GAAGxM,OAAOwG,GAAmBqF,GAAMrF,GAAmBgG,EAAQhK,OACvE,GAAG,IAAIkF,OAAOiF,GACT/J,EAAQ4M,EAAchD,QAAQtD,KAAI,SAAUrF,GAC/C,OAAOA,EAAEC,QACV,IAAGtB,EAAM0G,KAAI,SAAUrF,GACtB,OAAOA,EAAEC,QACV,OACC0L,EAAchD,QAAUhK,EACpBA,EAAMlB,OAAS,GACjByL,GAAoB,CAClBC,cAAeA,EACfxK,MAAOA,EACPyK,SAAUA,EACV7K,MAAOA,IAfb,CAmBD,GAAE,GACH,MAAO,CACL2D,KAAM,2BACN4J,UAAW,SAAmBvP,GAC5B,IAAIwP,EAAaxP,EAAMwP,WACrBjC,EAAWvN,EAAMuN,SACjBE,EAAWzN,EAAMyN,SACfgC,GAAuB,EAC3B,SAASC,EAAmBrF,GAC1BmF,EAAW,CACTG,sBAAuB,CACrBC,0BAA2BlI,GAAcA,GAAc,CAAA,EAAIgG,EAAgC,CACzFmC,gBAAgB,GACd,CAAA,GAAKxF,EAAY,CACnBA,UAAWyF,GAAmBzF,IAC5B,IACJwC,SAAUA,IAGhB,CACAgB,EAAe,kBAAmB,mBAClC6B,IAGA7B,EAAe,qBAAqB,SAAUxD,GACvCoF,GACHC,EAAmBrF,EAEvB,IACAwD,EAAe,eAAgB,MAAM,SAAUkC,EAAQ1F,GAChDoF,GACHC,EAAmBrF,EAEvB,IAGAwD,EAAe,kCAAkC,SAAU1D,GACrDA,GACFsF,GAAuB,EACvBC,EAAmBvF,KAEnBsF,GAAuB,EACvB5B,EAAe,eAAgB,MAAM,SAAUkC,EAAQ1F,GACrD,OAAOqF,EAAmBrF,EAC5B,IAEJ,IACAwD,EAAe,4BAA6B,MAAM,SAAUkC,EAAQ5F,GAC9DA,IACFsF,GAAuB,EACvBC,EAAmBvF,GAEvB,IACAoD,GAAS,SAAUzB,GACjB,IAAI3I,EAAO2I,EAAM3I,KACfnB,EAAQ8J,EAAM9J,MACd+K,EAAQjB,EAAMiB,MACdtG,EAASqF,EAAMrF,OACZ8F,EAAqBpJ,IAG1B4K,EAAc,CACZ/L,MAAOA,EACP+K,MAAOA,EACPF,SAAUA,EACV1J,KAAMA,EACN2J,eAAgB,CAACpF,GAAc,CAC7BsF,UAAW,iBACV9J,EAAmB,CACpBC,KAAMA,EACNf,MAAOqE,EAAOuJ,WAAW1I,OAAOiF,QAGtC,IACAkB,GAAS,SAAUwC,GACjB,IAAI9M,EAAO8M,EAAM9M,KACfsD,EAASwJ,EAAMxJ,OACfzE,EAAQiO,EAAMjO,MACd+K,EAAQkD,EAAMlD,MACXR,EAAqBpJ,IAG1B6K,EAAc,CACZhM,MAAOA,EACP+K,MAAOA,EACPF,SAAUA,EACV1J,KAAMA,EACN2J,eAAgB,CAACpF,GAAc,CAC7BsF,UAAW,eACV9J,EAAmB,CACpBC,KAAMA,EACNf,MAAOqE,EAAOuJ,WAAW1I,OAAOiF,QAGtC,GACD,EACD2D,cAAe,SAAuBC,GACpC,IAAInO,EAAQmO,EAAMnO,MAClBqN,EAAuB,CACrBrN,MAAOA,GAEV,EACDoO,6BAA8BlD,EAElC,CACA,SAASG,KACP,IAAIgD,EAEA/C,EAAUrM,UAAUC,OAAS,EAAID,UAAU,QAAKF,EACpD,MAAO,GAAGnB,OAAOwG,GAFOnF,UAAUC,OAAS,QAAsBH,IAAjBE,UAAU,GAAmBA,UAAU,GAAK,IAEpC,CAAC,yBAA0BmF,GAA+E,QAA3DiK,EAAwB/C,EAAQqC,6BAA6D,IAA1BU,GAAoCA,EAAsBC,oBAAsB,CAAC,0BAA4B,IACzR,CAgDA,SAASR,GAAmBzF,GAC1B,MAA4B,iBAAdA,EAAyBA,EAAU7E,WAAa6E,CAChE,CG5QA,SAASkG,GAAgCC,EAASC,GAChD,IAAIzO,EAAQyO,EACZ,MAAO,CACLC,KAAM,SAAcC,EAAaC,GAC/B,OAAOL,GAAgCC,EAAQE,KAAKG,GAAeF,EAAa3O,EAAOwO,GAAUK,GAAeD,EAAY5O,EAAOwO,IAAWxO,EAC/I,EACD8O,MAAO,SAAgBF,GACrB,OAAOL,GAAgCC,EAAQM,MAAMD,GAAeD,EAAY5O,EAAOwO,IAAWxO,EACnG,EACD+O,QAAS,SAAkBC,GAIzB,OAHIA,GACFhP,EAAMiP,aAAanM,KAAKkM,GAEnBT,GAAgCC,EAAQO,QAAQF,GAAeG,GAAa,WAEjF,OADAhP,EAAMiP,aAAe,GACdD,GACR,EAAEhP,EAAOwO,IAAWxO,EACtB,EACDkP,OAAQ,WACNlP,EAAMmP,YAAa,EACnB,IAAIC,EAAYpP,EAAMiP,aACtBjP,EAAMiP,aAAe,GACrBG,EAAUzJ,SAAQ,SAAUsG,GAC1BA,GACF,GACD,EACDkD,WAAY,WACV,OAA4B,IAArBnP,EAAMmP,UACf,EAEJ,CAeO,SAASE,GAAWb,GACzB,OAAOD,GAAgCC,EAAS,CAC9CW,YAAY,EACZF,aAAc,IAElB,CACA,SAASJ,GAAeS,EAAUtP,EAAOuP,GACvC,OAAKD,EAGE,SAAkBtJ,GACvB,OAAIhG,EAAMmP,WACDnJ,EAEFsJ,EAAStJ,EACjB,EAPQuJ,CAQX,CC7CO,SAASC,GAAoBC,EAAYC,EAAWC,EAAWC,GACpE,IAAKD,EACH,OAAO,KAET,GAAIF,EAAa,IAAoB,OAAdC,GAA8C,OAAxBE,GAA8C,IAAdF,GAC3E,OAAOC,EAAYF,EAErB,IAAII,GAA8B,OAAdH,GAAsB,EAAIA,GAAaD,EAC3D,OAAII,IAAiB,GAAKA,GAAgBF,EACT,OAAxBC,EAA+B,KAAO,EAExCC,CACT,CC5BA,SAAS3K,GAAQC,EAAQC,GAAkB,IAAIzE,EAAOJ,OAAOI,KAAKwE,GAAS,GAAI5E,OAAOwE,sBAAuB,CAAE,IAAIM,EAAU9E,OAAOwE,sBAAsBI,GAASC,IAAmBC,EAAUA,EAAQC,QAAO,SAAUC,GAAO,OAAOhF,OAAOiF,yBAAyBL,EAAQI,GAAKE,UAAY,KAAK9E,EAAKmC,KAAKtD,MAAMmB,EAAM0E,EAAU,CAAE,OAAO1E,CAAM,CACpV,SAAS+E,GAAcf,GAAU,IAAK,IAAI5C,EAAI,EAAGA,EAAI9C,UAAUC,OAAQ6C,IAAK,CAAE,IAAI0C,EAAS,MAAQxF,UAAU8C,GAAK9C,UAAU8C,GAAK,CAAE,EAAEA,EAAI,EAAImD,GAAQ3E,OAAOkE,IAAS,GAAIkB,SAAQ,SAAU3I,GAAO4I,GAAgBjB,EAAQ3H,EAAKyH,EAAOzH,GAAO,IAAKuD,OAAOsF,0BAA4BtF,OAAOuF,iBAAiBnB,EAAQpE,OAAOsF,0BAA0BpB,IAAWS,GAAQ3E,OAAOkE,IAASkB,SAAQ,SAAU3I,GAAOuD,OAAOwF,eAAepB,EAAQ3H,EAAKuD,OAAOiF,yBAAyBf,EAAQzH,GAAO,GAAI,CAAE,OAAO2H,CAAQ,CACzf,SAASiB,GAAgBtF,EAAKtD,EAAK+F,GAA4L,OAAnL/F,EAC5C,SAAwBgJ,GAAO,IAAIhJ,EACnC,SAAsBiJ,EAAOC,GAAQ,GAAuB,WAAnB/B,GAAQ8B,IAAiC,OAAVA,EAAgB,OAAOA,EAAO,IAAIE,EAAOF,EAAM/D,OAAOkE,aAAc,QAAarH,IAAToH,EAAoB,CAAE,IAAIE,EAAMF,EAAKxD,KAAKsD,EAAOC,GAAQ,WAAY,GAAqB,WAAjB/B,GAAQkC,GAAmB,OAAOA,EAAK,MAAM,IAAIvC,UAAU,+CAAiD,CAAE,OAAiB,WAAToC,EAAoBI,OAASC,QAAQN,EAAQ,CADnVO,CAAaR,EAAK,UAAW,MAAwB,WAAjB7B,GAAQnH,GAAoBA,EAAMsJ,OAAOtJ,EAAM,CAD1EyJ,CAAezJ,MAAiBsD,EAAOC,OAAOwF,eAAezF,EAAKtD,EAAK,CAAE+F,MAAOA,EAAO0C,YAAY,EAAMiB,cAAc,EAAMC,UAAU,IAAkBrG,EAAItD,GAAO+F,EAAgBzC,CAAK,CAG3O,SAAS6D,GAAQ7D,GAAkC,OAAO6D,GAAU,mBAAqBjC,QAAU,iBAAmBA,OAAOC,SAAW,SAAU7B,GAAO,cAAcA,CAAM,EAAG,SAAUA,GAAO,OAAOA,GAAO,mBAAqB4B,QAAU5B,EAAIoD,cAAgBxB,QAAU5B,IAAQ4B,OAAOqB,UAAY,gBAAkBjD,CAAK,EAAG6D,GAAQ7D,EAAM,CC+CxU,SAASwP,GAAc9P,GAC5B,IAAIG,EAlDN,SAAuCH,GAIrC,IAUI+P,EAV8B/P,EAAMC,YAAY6G,KAAI,SAAU7G,GAChE,OAAOA,EAAYG,MAAMlB,MAC1B,IAAES,QAAO,SAAU8J,EAAKuG,EAAkB3O,GACzC,IACI4O,GADgBxG,EAAIpI,EAAQ,IAAM,GACN2O,EAEhC,OADAvG,EAAI3G,KAAKmN,GACFxG,CACR,GAAE,IAG+C9J,QAAO,SAAU8J,EAAKW,GACtE,OAAIA,GAAWpK,EAAMkQ,aACZzG,EAAM,EAERA,CACR,GAAE,GACH,OAAOzJ,EAAMC,YAAY8P,EAC3B,CA6BmBI,CAA8BnQ,GAC/C,IAAKG,EACH,OAAO,KAET,IAAIgB,EAAOhB,EAAWC,MAtBxB,SAAiClE,GAM/B,IALA,IAAI8D,EAAQ9D,EAAK8D,MACfG,EAAajE,EAAKiE,WAChBiQ,GAAgB,EAChBC,EAAU,EACVC,EAAsB,GACD,IAAlBF,GAAyB,CAC9B,IAAIG,EAAoBvQ,EAAMC,YAAYoQ,GAC1C,GAAIE,IAAsBpQ,EAAY,CACpCiQ,GAAgB,EAChB,KACF,CACAE,GAAuBC,EAAkBnQ,MAAMlB,OAC/CmR,GACF,CACA,OAAOrQ,EAAMkQ,aAAeI,CAC9B,CAM8BE,CAAwB,CAClDxQ,MAAOA,EACPG,WAAYA,KAEVsE,EAAStE,EAAWsE,OASxB,MAAO,CACLtD,KAAMA,EACNsP,eAVmBhM,EAAOiM,kBAAkB,CAC5CvP,KAAMA,EACNnB,MAAOA,IASP2Q,QAPYlM,EAAOmM,WAAW,CAC9BzP,KAAMA,EACNnB,MAAOA,IAMPyE,OAAQA,EAEZ,CCrEO,SAASoM,GAAyBC,EAAwBC,EAAWtM,GAC1E,MAAO,CAACqM,EAAwBrM,aAAuC,EAASA,EAAOuM,SAAUD,GAAWzL,OAAO2L,SAASC,KAAK,KAAKC,QAAQ,MAAO,GACvJ,CCTA,IAAIC,GAAQ,sDCAL,SAASC,GAAetG,GAC7B,OAAOA,EAAMuG,aAAevG,CAC9B,CCFA,SAAS5G,GAAQ7D,GAAkC,OAAO6D,GAAU,mBAAqBjC,QAAU,iBAAmBA,OAAOC,SAAW,SAAU7B,GAAO,cAAcA,CAAM,EAAG,SAAUA,GAAO,OAAOA,GAAO,mBAAqB4B,QAAU5B,EAAIoD,cAAgBxB,QAAU5B,IAAQ4B,OAAOqB,UAAY,gBAAkBjD,CAAK,EAAG6D,GAAQ7D,EAAM,CAC/U,SAAS4E,GAAQC,EAAQC,GAAkB,IAAIzE,EAAOJ,OAAOI,KAAKwE,GAAS,GAAI5E,OAAOwE,sBAAuB,CAAE,IAAIM,EAAU9E,OAAOwE,sBAAsBI,GAASC,IAAmBC,EAAUA,EAAQC,QAAO,SAAUC,GAAO,OAAOhF,OAAOiF,yBAAyBL,EAAQI,GAAKE,UAAY,KAAK9E,EAAKmC,KAAKtD,MAAMmB,EAAM0E,EAAU,CAAE,OAAO1E,CAAM,CAEpV,SAASiF,GAAgBtF,EAAKtD,EAAK+F,GAA4L,OAAnL/F,EAC5C,SAAwBgJ,GAAO,IAAIhJ,EACnC,SAAsBiJ,EAAOC,GAAQ,GAAuB,WAAnB/B,GAAQ8B,IAAiC,OAAVA,EAAgB,OAAOA,EAAO,IAAIE,EAAOF,EAAM/D,OAAOkE,aAAc,QAAarH,IAAToH,EAAoB,CAAE,IAAIE,EAAMF,EAAKxD,KAAKsD,EAAOC,GAAQ,WAAY,GAAqB,WAAjB/B,GAAQkC,GAAmB,OAAOA,EAAK,MAAM,IAAIvC,UAAU,+CAAiD,CAAE,OAAiB,WAAToC,EAAoBI,OAASC,QAAQN,EAAQ,CADnVO,CAAaR,EAAK,UAAW,MAAwB,WAAjB7B,GAAQnH,GAAoBA,EAAMsJ,OAAOtJ,EAAM,CAD1EyJ,CAAezJ,MAAiBsD,EAAOC,OAAOwF,eAAezF,EAAKtD,EAAK,CAAE+F,MAAOA,EAAO0C,YAAY,EAAMiB,cAAc,EAAMC,UAAU,IAAkBrG,EAAItD,GAAO+F,EAAgBzC,CAAK,CAIpO,SAASiR,GAAYC,EAASlV,EAAOmV,GAC1C,ICPIC,EDOA1R,EAAQ1D,EAAMmS,aAClB,MAAO,CACLkD,SAAU,WACR,OAAO3R,CACR,EACD4R,SAAU,SAAkBC,EAAQhI,GAClC,IAAIiI,EAZV,SAAuBnN,GAAU,IAAK,IAAI5C,EAAI,EAAGA,EAAI9C,UAAUC,OAAQ6C,IAAK,CAAE,IAAI0C,EAAS,MAAQxF,UAAU8C,GAAK9C,UAAU8C,GAAK,CAAE,EAAEA,EAAI,EAAImD,GAAQ3E,OAAOkE,IAAS,GAAIkB,SAAQ,SAAU3I,GAAO4I,GAAgBjB,EAAQ3H,EAAKyH,EAAOzH,GAAO,IAAKuD,OAAOsF,0BAA4BtF,OAAOuF,iBAAiBnB,EAAQpE,OAAOsF,0BAA0BpB,IAAWS,GAAQ3E,OAAOkE,IAASkB,SAAQ,SAAU3I,GAAOuD,OAAOwF,eAAepB,EAAQ3H,EAAKuD,OAAOiF,yBAAyBf,EAAQzH,GAAO,GAAI,CAAE,OAAO2H,CAAQ,CAYnee,CAAc,CAAE,EAAE1F,GAClCA,EAAQwR,EAAQxR,EAAO,CACrBrC,KAAMkU,EACNvV,MAAOA,EACPuN,QAASA,IAEX4H,EAAmB,CACjBzR,MAAOA,EACP8R,UAAWA,GAEd,EACDC,iBCxBEL,EAAO,GACJ,CACLM,IAAK,SAAaC,GAEhB,OADAP,EAAK5O,KAAKmP,GACHA,EAAkBlD,SAAQ,WAC/B2C,EAAOA,EAAKpM,QAAO,SAAUnE,GAC3B,OAAOA,IAAS8Q,CAClB,GACF,GACD,EACDC,UAAW,WACTR,EAAK/L,SAAQ,SAAU6I,GACrB,OAAOA,EAAQU,QACjB,GACD,EACDiD,QAAS,WACP,OAAuB,IAAhBT,EAAKxS,MACd,IDSJ,CE3BA,SAASiF,GAAQ7D,GAAkC,OAAO6D,GAAU,mBAAqBjC,QAAU,iBAAmBA,OAAOC,SAAW,SAAU7B,GAAO,cAAcA,CAAM,EAAG,SAAUA,GAAO,OAAOA,GAAO,mBAAqB4B,QAAU5B,EAAIoD,cAAgBxB,QAAU5B,IAAQ4B,OAAOqB,UAAY,gBAAkBjD,CAAK,EAAG6D,GAAQ7D,EAAM,CAC/U,SAAS4E,GAAQC,EAAQC,GAAkB,IAAIzE,EAAOJ,OAAOI,KAAKwE,GAAS,GAAI5E,OAAOwE,sBAAuB,CAAE,IAAIM,EAAU9E,OAAOwE,sBAAsBI,GAASC,IAAmBC,EAAUA,EAAQC,QAAO,SAAUC,GAAO,OAAOhF,OAAOiF,yBAAyBL,EAAQI,GAAKE,UAAY,KAAK9E,EAAKmC,KAAKtD,MAAMmB,EAAM0E,EAAU,CAAE,OAAO1E,CAAM,CACpV,SAAS+E,GAAcf,GAAU,IAAK,IAAI5C,EAAI,EAAGA,EAAI9C,UAAUC,OAAQ6C,IAAK,CAAE,IAAI0C,EAAS,MAAQxF,UAAU8C,GAAK9C,UAAU8C,GAAK,CAAE,EAAEA,EAAI,EAAImD,GAAQ3E,OAAOkE,IAAS,GAAIkB,SAAQ,SAAU3I,GAAO4I,GAAgBjB,EAAQ3H,EAAKyH,EAAOzH,GAAO,IAAKuD,OAAOsF,0BAA4BtF,OAAOuF,iBAAiBnB,EAAQpE,OAAOsF,0BAA0BpB,IAAWS,GAAQ3E,OAAOkE,IAASkB,SAAQ,SAAU3I,GAAOuD,OAAOwF,eAAepB,EAAQ3H,EAAKuD,OAAOiF,yBAAyBf,EAAQzH,GAAO,GAAI,CAAE,OAAO2H,CAAQ,CACzf,SAASiB,GAAgBtF,EAAKtD,EAAK+F,GAA4L,OAAnL/F,EAC5C,SAAwBgJ,GAAO,IAAIhJ,EACnC,SAAsBiJ,EAAOC,GAAQ,GAAuB,WAAnB/B,GAAQ8B,IAAiC,OAAVA,EAAgB,OAAOA,EAAO,IAAIE,EAAOF,EAAM/D,OAAOkE,aAAc,QAAarH,IAAToH,EAAoB,CAAE,IAAIE,EAAMF,EAAKxD,KAAKsD,EAAOC,GAAQ,WAAY,GAAqB,WAAjB/B,GAAQkC,GAAmB,OAAOA,EAAK,MAAM,IAAIvC,UAAU,+CAAiD,CAAE,OAAiB,WAAToC,EAAoBI,OAASC,QAAQN,EAAQ,CADnVO,CAAaR,EAAK,UAAW,MAAwB,WAAjB7B,GAAQnH,GAAoBA,EAAMsJ,OAAOtJ,EAAM,CAD1EyJ,CAAezJ,MAAiBsD,EAAOC,OAAOwF,eAAezF,EAAKtD,EAAK,CAAE+F,MAAOA,EAAO0C,YAAY,EAAMiB,cAAc,EAAMC,UAAU,IAAkBrG,EAAItD,GAAO+F,EAAgBzC,CAAK,CCH3O,SAAS6D,GAAQ7D,GAAkC,OAAO6D,GAAU,mBAAqBjC,QAAU,iBAAmBA,OAAOC,SAAW,SAAU7B,GAAO,cAAcA,CAAM,EAAG,SAAUA,GAAO,OAAOA,GAAO,mBAAqB4B,QAAU5B,EAAIoD,cAAgBxB,QAAU5B,IAAQ4B,OAAOqB,UAAY,gBAAkBjD,CAAK,EAAG6D,GAAQ7D,EAAM,CAC/U,SAAS8D,GAAmBtC,GAAO,OAInC,SAA4BA,GAAO,GAAI1C,MAAM4C,QAAQF,GAAM,OAAOuB,GAAkBvB,EAAM,CAJhDuC,CAAmBvC,IAG7D,SAA0BwC,GAAQ,GAAsB,oBAAXpC,QAAmD,MAAzBoC,EAAKpC,OAAOC,WAA2C,MAAtBmC,EAAK,cAAuB,OAAOlF,MAAMwE,KAAKU,EAAO,CAHxFC,CAAiBzC,IAEtF,SAAqCqB,EAAGC,GAAU,IAAKD,EAAG,OAAQ,GAAiB,iBAANA,EAAgB,OAAOE,GAAkBF,EAAGC,GAAS,IAAIE,EAAI/C,OAAOgD,UAAUC,SAASb,KAAKQ,GAAGM,MAAM,GAAI,GAAc,WAANH,GAAkBH,EAAEO,cAAaJ,EAAIH,EAAEO,YAAYC,MAAM,GAAU,QAANL,GAAqB,QAANA,EAAa,OAAOlE,MAAMwE,KAAKT,GAAI,GAAU,cAANG,GAAqB,2CAA2ClG,KAAKkG,GAAI,OAAOD,GAAkBF,EAAGC,EAAS,CAFjUS,CAA4B/B,IAC1H,WAAgC,MAAM,IAAIgC,UAAU,uIAAyI,CAD3DU,EAAsB,CAKxJ,SAASnB,GAAkBvB,EAAKkC,IAAkB,MAAPA,GAAeA,EAAMlC,EAAI5C,UAAQ8E,EAAMlC,EAAI5C,QAAQ,IAAK,IAAI6C,EAAI,EAAGkC,EAAO,IAAI7E,MAAM4E,GAAMjC,EAAIiC,EAAKjC,IAAKkC,EAAKlC,GAAKD,EAAIC,GAAI,OAAOkC,CAAM,CAClL,SAASiB,GAAQC,EAAQC,GAAkB,IAAIzE,EAAOJ,OAAOI,KAAKwE,GAAS,GAAI5E,OAAOwE,sBAAuB,CAAE,IAAIM,EAAU9E,OAAOwE,sBAAsBI,GAASC,IAAmBC,EAAUA,EAAQC,QAAO,SAAUC,GAAO,OAAOhF,OAAOiF,yBAAyBL,EAAQI,GAAKE,UAAY,KAAK9E,EAAKmC,KAAKtD,MAAMmB,EAAM0E,EAAU,CAAE,OAAO1E,CAAM,CACpV,SAAS+E,GAAcf,GAAU,IAAK,IAAI5C,EAAI,EAAGA,EAAI9C,UAAUC,OAAQ6C,IAAK,CAAE,IAAI0C,EAAS,MAAQxF,UAAU8C,GAAK9C,UAAU8C,GAAK,CAAE,EAAEA,EAAI,EAAImD,GAAQ3E,OAAOkE,IAAS,GAAIkB,SAAQ,SAAU3I,GAAO4I,GAAgBjB,EAAQ3H,EAAKyH,EAAOzH,GAAO,IAAKuD,OAAOsF,0BAA4BtF,OAAOuF,iBAAiBnB,EAAQpE,OAAOsF,0BAA0BpB,IAAWS,GAAQ3E,OAAOkE,IAASkB,SAAQ,SAAU3I,GAAOuD,OAAOwF,eAAepB,EAAQ3H,EAAKuD,OAAOiF,yBAAyBf,EAAQzH,GAAO,GAAI,CAAE,OAAO2H,CAAQ,CACzf,SAASiB,GAAgBtF,EAAKtD,EAAK+F,GAA4L,OAAnL/F,EAC5C,SAAwBgJ,GAAO,IAAIhJ,EACnC,SAAsBiJ,EAAOC,GAAQ,GAAuB,WAAnB/B,GAAQ8B,IAAiC,OAAVA,EAAgB,OAAOA,EAAO,IAAIE,EAAOF,EAAM/D,OAAOkE,aAAc,QAAarH,IAAToH,EAAoB,CAAE,IAAIE,EAAMF,EAAKxD,KAAKsD,EAAOC,GAAQ,WAAY,GAAqB,WAAjB/B,GAAQkC,GAAmB,OAAOA,EAAK,MAAM,IAAIvC,UAAU,+CAAiD,CAAE,OAAiB,WAAToC,EAAoBI,OAASC,QAAQN,EAAQ,CADnVO,CAAaR,EAAK,UAAW,MAAwB,WAAjB7B,GAAQnH,GAAoBA,EAAMsJ,OAAOtJ,EAAM,CAD1EyJ,CAAezJ,MAAiBsD,EAAOC,OAAOwF,eAAezF,EAAKtD,EAAK,CAAE+F,MAAOA,EAAO0C,YAAY,EAAMiB,cAAc,EAAMC,UAAU,IAAkBrG,EAAItD,GAAO+F,EAAgBzC,CAAK,CAKpO,SAAS8R,GAAgB9V,EAAO+V,GACrC,IAAIC,EAEAhG,EAAgC,oBAAXjO,OAAyBA,OAAS,CAAE,EAEzDkU,EAAUjW,EAAMiW,SAAW,GAC/B,OAAO7M,GAAcA,GAAc,CACjC8M,OAAO,EACPC,aAAa,EACbC,kBAAc3T,EACd4T,yBAAyB,EACzBC,YAAa,GACbC,WAAW,EACXjD,oBAAqB,KACrBkD,eAAgB,IAChBjI,cAAU9L,EACVuN,YAAaA,EACbyG,gBAAiB,SAAyB7W,GAExC,OAAO6D,EADK7D,EAAK8D,OACa,CAC/B,EACDgT,QAAS,SAAiB/L,GAExB,OADcA,EAAMgM,OAEtB,GACC3W,GAAQ,GAAI,CAGb4W,GAA+B,QAA1BZ,EAAYhW,EAAM4W,UAA8B,IAAdZ,EAAuBA,ExBxCzD,gBAAgB1U,OAAOkC,KwByC5ByS,QAASA,EAET9D,aAAc/I,GAAc,CAC1BwK,aAAc,KACdiD,MAAO,GACPC,WAAY,KACZnT,YAAa,GACbqN,QAAQ,EACR+F,OAAQ,OACR/H,QAAS,CAAA,GACRhP,EAAMmS,cACTP,cAAe,SAAuBrH,GACpC,IAAIyM,EAC6C,QAAhDA,EAAuBhX,EAAM4R,qBAAoD,IAAzBoF,GAA2CA,EAAqB3Q,KAAKrG,EAAOuK,GACrI0L,EAAQ5M,SAAQ,SAAUlE,GACxB,IAAI8R,EACJ,OAAgD,QAAxCA,EAAmB9R,EAAEyM,qBAAgD,IAArBqF,OAA8B,EAASA,EAAiB5Q,KAAKlB,EAAGoF,EAC1H,GACD,EACD2M,SAAU,SAAkB3M,GAC1B,IAAI4M,EACmC,QAAtCA,EAAkBnX,EAAMkX,gBAA0C,IAApBC,GAAsCA,EAAgB9Q,KAAKrG,EAAOuK,GACjH0L,EAAQ5M,SAAQ,SAAUlE,GACxB,IAAIiS,EACJ,OAAsC,QAA9BA,EAAcjS,EAAE+R,gBAAsC,IAAhBE,OAAyB,EAASA,EAAY/Q,KAAKlB,EAAGoF,EACtG,GACD,EACD8M,QAAS,SAAiB9M,GACxB,IAAI+M,EACiC,QAApCA,EAAiBtX,EAAMqX,eAAwC,IAAnBC,GAAqCA,EAAejR,KAAKrG,EAAOuK,GAC7G0L,EAAQ5M,SAAQ,SAAUlE,GACxB,IAAIoS,EACJ,OAAoC,QAA5BA,EAAapS,EAAEkS,eAAoC,IAAfE,OAAwB,EAASA,EAAWlR,KAAKlB,EAAGoF,EAClG,GACD,EACDiN,WAAY,SAAoBjN,GAC9B,OAAOkN,QAAQC,IAAI,GAAGpW,OAAOwG,GAAmBmO,EAAQzL,KAAI,SAAUmN,GACpE,OAAOA,EAAOH,UAChB,KAAK,CAACxX,EAAMwX,aAAaxO,OAAO2L,SAASnK,KAAI,SAAUgN,GACrD,OR3ED,SAA8BA,EAAYjN,GAC/C,IAAIqN,EAAgB,GACpB,OAAOH,QAAQI,QAAQL,EAAWjN,IAAS6H,MAAK,SAAUuE,GAIxD,OAAOc,QAAQC,IAAIf,EAIlB3N,QAAO,SAAU8O,GAChB,OAAOnD,QAAQmD,EACjB,IAAGtN,KAAI,SAAUrC,GAEf,GADiBA,EAAOuM,SACpBkD,EAAcG,SAAS5P,EAAOuM,UAChC,MAAM,IAAIsD,MAAM,iCAAiC1W,OAAO2W,KAAKC,UAAU/P,EAAOuM,UAAW,oBAE3FkD,EAAcpR,KAAK2B,EAAOuM,UAC1B,IAAIyD,EAAgB,CAClB/D,kBAAmB,SAA2BxU,GAE5C,OADYA,EAAK8D,MACJmT,KACd,EACDvC,WAAY,WAEX,EACDrF,SAAU,SAAkBtE,IAE1ByN,EADgBzN,EAAMyN,YACZ,EACX,EACDjJ,SAAU3K,EACV6T,UAAW7T,GAEbP,OAAOI,KAAK8T,GAAe9O,SAAQ,SAAU3I,GAC3CyX,EAAczX,GAAK4X,WAAY,CACjC,IACA,IAAIC,EAAmBnP,GAAcA,GAAc,CAAA,EAAI+O,GAAgBhQ,GACvE,OAAOsP,QAAQI,QAAQU,EACzB,IACF,GACF,CQmCeC,CAAqBhB,EAAYjN,EAC1C,KAAI6H,MAAK,SAAUqG,GACjB,OAAOtV,EAAQsV,EACjB,IAAGrG,MAAK,SAAUuE,GAChB,OAAOA,EAAQnM,KAAI,SAAUrC,GAC3B,OAAOiB,GAAcA,GAAc,CAAE,EAAEjB,GAAS,CAAA,EAAI,CAClD8G,SAAU,SAAkB1E,GAC1BpC,EAAO8G,SAAS1E,GAChBwL,EAAkB1M,SAAQ,SAAUlE,GAClC,IAAIuT,EACJ,OAAsC,QAA9BA,EAAcvT,EAAE8J,gBAAsC,IAAhByJ,OAAyB,EAASA,EAAYrS,KAAKlB,EAAGoF,EACtG,GACD,EACD4E,SAAU,SAAkB5E,GAC1BpC,EAAOgH,SAAS5E,GAChBwL,EAAkB1M,SAAQ,SAAUlE,GAClC,IAAIwT,EACJ,OAAsC,QAA9BA,EAAcxT,EAAEgK,gBAAsC,IAAhBwJ,OAAyB,EAASA,EAAYtS,KAAKlB,EAAGoF,EACtG,GACD,EACD8N,UAAW,SAAmB9N,GAC5BpC,EAAOkQ,UAAU9N,GACjBwL,EAAkB1M,SAAQ,SAAUlE,GAClC,IAAIyT,EACJ,OAAwC,QAAhCA,EAAezT,EAAEkT,iBAAwC,IAAjBO,OAA0B,EAASA,EAAavS,KAAKlB,EAAGoF,EAC1G,GACF,GAEJ,GACF,GACD,EACD1J,UAAWuI,GAAc,CACvByP,SAAU,SAAkB7X,GAC1B,IAAIqT,EAAUrT,EAAMqT,QACpBrE,EAAY8I,SAASC,OAAO1E,EAC7B,EACD2E,eAAgB,SAAwBtX,GACtC,IAAI2S,EAAU3S,EAAM2S,QAChB4E,EAAkBjJ,EAAYkJ,KAAK7E,EAAS,SAAU,YAC1D4E,SAAkEA,EAAgBE,OACnF,EACDC,kBAAmB,SAA2B5L,GAC5C,IAAI6G,EAAU7G,EAAM6G,QACpBrE,EAAYkJ,KAAK7E,EAAS,SAAU,WACtC,GACCrU,EAAMa,YAEb,CCjIA,SAASgH,GAAQ7D,GAAkC,OAAO6D,GAAU,mBAAqBjC,QAAU,iBAAmBA,OAAOC,SAAW,SAAU7B,GAAO,cAAcA,CAAM,EAAG,SAAUA,GAAO,OAAOA,GAAO,mBAAqB4B,QAAU5B,EAAIoD,cAAgBxB,QAAU5B,IAAQ4B,OAAOqB,UAAY,gBAAkBjD,CAAK,EAAG6D,GAAQ7D,EAAM,CAC/U,SAAS4E,GAAQC,EAAQC,GAAkB,IAAIzE,EAAOJ,OAAOI,KAAKwE,GAAS,GAAI5E,OAAOwE,sBAAuB,CAAE,IAAIM,EAAU9E,OAAOwE,sBAAsBI,GAASC,IAAmBC,EAAUA,EAAQC,QAAO,SAAUC,GAAO,OAAOhF,OAAOiF,yBAAyBL,EAAQI,GAAKE,UAAY,KAAK9E,EAAKmC,KAAKtD,MAAMmB,EAAM0E,EAAU,CAAE,OAAO1E,CAAM,CACpV,SAAS+E,GAAcf,GAAU,IAAK,IAAI5C,EAAI,EAAGA,EAAI9C,UAAUC,OAAQ6C,IAAK,CAAE,IAAI0C,EAAS,MAAQxF,UAAU8C,GAAK9C,UAAU8C,GAAK,CAAE,EAAEA,EAAI,EAAImD,GAAQ3E,OAAOkE,IAAS,GAAIkB,SAAQ,SAAU3I,GAAO4I,GAAgBjB,EAAQ3H,EAAKyH,EAAOzH,GAAO,IAAKuD,OAAOsF,0BAA4BtF,OAAOuF,iBAAiBnB,EAAQpE,OAAOsF,0BAA0BpB,IAAWS,GAAQ3E,OAAOkE,IAASkB,SAAQ,SAAU3I,GAAOuD,OAAOwF,eAAepB,EAAQ3H,EAAKuD,OAAOiF,yBAAyBf,EAAQzH,GAAO,GAAI,CAAE,OAAO2H,CAAQ,CACzf,SAASiB,GAAgBtF,EAAKtD,EAAK+F,GAA4L,OAAnL/F,EAC5C,SAAwBgJ,GAAO,IAAIhJ,EACnC,SAAsBiJ,EAAOC,GAAQ,GAAuB,WAAnB/B,GAAQ8B,IAAiC,OAAVA,EAAgB,OAAOA,EAAO,IAAIE,EAAOF,EAAM/D,OAAOkE,aAAc,QAAarH,IAAToH,EAAoB,CAAE,IAAIE,EAAMF,EAAKxD,KAAKsD,EAAOC,GAAQ,WAAY,GAAqB,WAAjB/B,GAAQkC,GAAmB,OAAOA,EAAK,MAAM,IAAIvC,UAAU,+CAAiD,CAAE,OAAiB,WAAToC,EAAoBI,OAASC,QAAQN,EAAQ,CADnVO,CAAaR,EAAK,UAAW,MAAwB,WAAjB7B,GAAQnH,GAAoBA,EAAMsJ,OAAOtJ,EAAM,CAD1EyJ,CAAezJ,MAAiBsD,EAAOC,OAAOwF,eAAezF,EAAKtD,EAAK,CAAE+F,MAAOA,EAAO0C,YAAY,EAAMiB,cAAc,EAAMC,UAAU,IAAkBrG,EAAItD,GAAO+F,EAAgBzC,CAAK,CCH3O,SAAS6D,GAAQ7D,GAAkC,OAAO6D,GAAU,mBAAqBjC,QAAU,iBAAmBA,OAAOC,SAAW,SAAU7B,GAAO,cAAcA,CAAM,EAAG,SAAUA,GAAO,OAAOA,GAAO,mBAAqB4B,QAAU5B,EAAIoD,cAAgBxB,QAAU5B,IAAQ4B,OAAOqB,UAAY,gBAAkBjD,CAAK,EAAG6D,GAAQ7D,EAAM,CAC/U,SAAS4E,GAAQC,EAAQC,GAAkB,IAAIzE,EAAOJ,OAAOI,KAAKwE,GAAS,GAAI5E,OAAOwE,sBAAuB,CAAE,IAAIM,EAAU9E,OAAOwE,sBAAsBI,GAASC,IAAmBC,EAAUA,EAAQC,QAAO,SAAUC,GAAO,OAAOhF,OAAOiF,yBAAyBL,EAAQI,GAAKE,UAAY,KAAK9E,EAAKmC,KAAKtD,MAAMmB,EAAM0E,EAAU,CAAE,OAAO1E,CAAM,CACpV,SAAS+E,GAAcf,GAAU,IAAK,IAAI5C,EAAI,EAAGA,EAAI9C,UAAUC,OAAQ6C,IAAK,CAAE,IAAI0C,EAAS,MAAQxF,UAAU8C,GAAK9C,UAAU8C,GAAK,CAAE,EAAEA,EAAI,EAAImD,GAAQ3E,OAAOkE,IAAS,GAAIkB,SAAQ,SAAU3I,GAAO4I,GAAgBjB,EAAQ3H,EAAKyH,EAAOzH,GAAO,IAAKuD,OAAOsF,0BAA4BtF,OAAOuF,iBAAiBnB,EAAQpE,OAAOsF,0BAA0BpB,IAAWS,GAAQ3E,OAAOkE,IAASkB,SAAQ,SAAU3I,GAAOuD,OAAOwF,eAAepB,EAAQ3H,EAAKuD,OAAOiF,yBAAyBf,EAAQzH,GAAO,GAAI,CAAE,OAAO2H,CAAQ,CACzf,SAASiB,GAAgBtF,EAAKtD,EAAK+F,GAA4L,OAAnL/F,EAC5C,SAAwBgJ,GAAO,IAAIhJ,EACnC,SAAsBiJ,EAAOC,GAAQ,GAAuB,WAAnB/B,GAAQ8B,IAAiC,OAAVA,EAAgB,OAAOA,EAAO,IAAIE,EAAOF,EAAM/D,OAAOkE,aAAc,QAAarH,IAAToH,EAAoB,CAAE,IAAIE,EAAMF,EAAKxD,KAAKsD,EAAOC,GAAQ,WAAY,GAAqB,WAAjB/B,GAAQkC,GAAmB,OAAOA,EAAK,MAAM,IAAIvC,UAAU,+CAAiD,CAAE,OAAiB,WAAToC,EAAoBI,OAASC,QAAQN,EAAQ,CADnVO,CAAaR,EAAK,UAAW,MAAwB,WAAjB7B,GAAQnH,GAAoBA,EAAMsJ,OAAOtJ,EAAM,CAD1EyJ,CAAezJ,MAAiBsD,EAAOC,OAAOwF,eAAezF,EAAKtD,EAAK,CAAE+F,MAAOA,EAAO0C,YAAY,EAAMiB,cAAc,EAAMC,UAAU,IAAkBrG,EAAItD,GAAO+F,EAAgBzC,CAAK,CAG3O,SAAS8D,GAAmBtC,GAAO,OAInC,SAA4BA,GAAO,GAAI1C,MAAM4C,QAAQF,GAAM,OAAOuB,GAAkBvB,EAAM,CAJhDuC,CAAmBvC,IAG7D,SAA0BwC,GAAQ,GAAsB,oBAAXpC,QAAmD,MAAzBoC,EAAKpC,OAAOC,WAA2C,MAAtBmC,EAAK,cAAuB,OAAOlF,MAAMwE,KAAKU,EAAO,CAHxFC,CAAiBzC,IAEtF,SAAqCqB,EAAGC,GAAU,IAAKD,EAAG,OAAQ,GAAiB,iBAANA,EAAgB,OAAOE,GAAkBF,EAAGC,GAAS,IAAIE,EAAI/C,OAAOgD,UAAUC,SAASb,KAAKQ,GAAGM,MAAM,GAAI,GAAc,WAANH,GAAkBH,EAAEO,cAAaJ,EAAIH,EAAEO,YAAYC,MAAM,GAAU,QAANL,GAAqB,QAANA,EAAa,OAAOlE,MAAMwE,KAAKT,GAAI,GAAU,cAANG,GAAqB,2CAA2ClG,KAAKkG,GAAI,OAAOD,GAAkBF,EAAGC,EAAS,CAFjUS,CAA4B/B,IAC1H,WAAgC,MAAM,IAAIgC,UAAU,uIAAyI,CAD3DU,EAAsB,CAKxJ,SAASnB,GAAkBvB,EAAKkC,IAAkB,MAAPA,GAAeA,EAAMlC,EAAI5C,UAAQ8E,EAAMlC,EAAI5C,QAAQ,IAAK,IAAI6C,EAAI,EAAGkC,EAAO,IAAI7E,MAAM4E,GAAMjC,EAAIiC,EAAKjC,IAAKkC,EAAKlC,GAAKD,EAAIC,GAAI,OAAOkC,CAAM,CAGlL,SAAS0R,GAAcxU,GACrB,OAAO8P,QAAQ9P,EAAKyU,QACtB,CAIO,SAASC,GAAWC,EAAoB9E,EAAUhR,GACvD,GAJ8B+V,EAIHD,EAHpB7E,QAAQ8E,aAAiD,EAASA,EAAYH,SAGrC,CAC9C,IAAII,EAAuD,YAAnCF,EAAmBG,YAA4B1V,OAAO8U,OAAO7V,MAAMe,OAAQ,CAAC,CAAA,GAAI3C,OAAOwG,GAAmB7D,OAAOI,KAAKX,EAAMsL,SAASxE,KAAI,SAAU9J,GACzK,IAAIkZ,EACJ,OAAqD,QAA7CA,EAAqBlW,EAAMsL,QAAQtO,UAAyC,IAAvBkZ,OAAgC,EAASA,EAAmBtI,yBAC3H,OAAQ,CAAE,EACV,OAAOlI,GAAcA,GAAc,CAAE,EAAEoQ,GAAqB,CAAA,EAAI,CAC9DK,SAAUL,EAAmBM,QAAQtP,KAAI,SAAUqM,GACjD,MAAO,CACLA,MAA0C,YAAnC2C,EAAmBG,YAA4BvQ,GAAcA,GAAc,CAAE,EAAEyN,GAAQ,GAAI,CAChGtM,OAAQnB,GAAcA,GAAc,CAAE,EAAEsQ,GAAoB7C,EAAMtM,UAC/DsM,EACLnC,SAAUA,EACVqF,kBAAmBP,EAAmBO,kBAEzC,KAEL,CApBF,IAAgCN,EAqB9B,MAAO,CACL3V,MAAO0V,EACP9E,SAAUA,EAEd,CACO,SAASmD,GAAQ/T,GACtB,IA0BIV,EA1BSU,EAAMT,QAAO,SAAU8J,EAAKW,GACvC,IAAKuL,GAAcvL,GAEjB,OADAX,EAAI3G,KAAKsH,GACFX,EAET,IAAI6M,EAAelM,EAAQkM,aACzBV,EAAUxL,EAAQwL,QAClBK,EAAc7L,EAAQ6L,YACtBE,EAAW/L,EAAQ+L,SACjBI,EAAY9M,EAAI+M,MAAK,SAAUrV,GACjC,OAAOwU,GAAcvL,IAAYuL,GAAcxU,IAASA,EAAKmV,eAAiBA,GAAgBrF,QAAQgF,IAAgB9U,EAAK8U,cAAgBA,CAC7I,IACA,GAAIM,EAAW,CACb,IAAIE,GACHA,EAAmBF,EAAUnW,OAAO0C,KAAKtD,MAAMiX,EAAkBrS,GAAmB+R,GACvF,KAAO,CACL,IAAIO,EAAU,CACZd,QAASA,EACTK,YAAaA,EACb7V,MAAO+V,EACPG,aAAcA,GAEhB7M,EAAI3G,KAAK4T,EACX,CACA,OAAOjN,CACR,GAAE,IACiB3C,KAAI,SAAU6P,GAChC,IAAKhB,GAAcgB,GACjB,OAAO5C,QAAQI,QAAQwC,GAEzB,IAAIza,EAAOya,EACTf,EAAU1Z,EAAK0Z,QACfxV,EAAQlE,EAAKkE,MAEf,OAAOwV,EAAQ,CACbU,aAFepa,EAAKoa,aAGpBH,SAAU/V,GAEd,IACA,OAAO2T,QAAQC,IAAItU,GAAQgP,MAAK,SAAUkI,GACxC,OAAOnX,EAAQmX,EACjB,GACF,CACO,SAASC,GAAYD,EAAW3D,EAAS6D,GAC9C,OAAO7D,EAAQnM,KAAI,SAAUrC,GAC3B,ICzFiCsS,EDyF7BC,EAAUJ,EAAUtR,QAAO,SAAU2R,GACvC,OAAOA,EAASjG,WAAavM,EAAOuM,QACtC,IACIkG,EAAUF,EAAQlQ,KAAI,SAAUG,GAElC,OADYA,EAAM7G,KAEpB,IACI+W,EAAYH,EAAQ,GAAGX,kBACvBjW,EAAQ+W,EAAYA,EChGnB,CACLD,QAFiCH,EDiGsBG,EC9FvDE,KAAML,EAAWjQ,KAAI,SAAUuQ,GAC7B,OAAOA,EAAOD,IAChB,IAAG9R,OAAO2L,SACVqG,UAAWP,EAAWjQ,KAAI,SAAUuQ,GAClC,IAAIE,EACJ,OAA2C,QAAnCA,EAAaF,EAAOC,iBAAsC,IAAfC,OAAwB,EAASA,EAAWzQ,KAAI,SAAU0Q,GAE3G,MAAO,CACLC,MAAOD,EAASzU,MAChB2U,MAAOF,EAASE,MAChBC,iBAAkB,CAChBF,MAAO,CACL1U,MAAOyU,EAASI,cAIxB,GACF,IAAGtS,OAAO2L,WD6EyDiG,EAWnE,OAVAzS,EAAOkQ,UAAU,CACflQ,OAAQA,EACRyS,QAASA,EACT9W,MAAOA,EACPJ,MAAO8W,EAAMnF,aAKLvR,EAAMyX,MAAM5G,SAAU,wCAAyCrT,OAAO6G,EAAOuM,SAAU,iDAAkDpT,OAAO2W,KAAKC,eAAUzV,GAAY,4IAC9K,CACL0F,OAAQA,EACRrE,MAAOA,EAEX,GACF,CEjHA,SAAS+D,GAAQ7D,GAAkC,OAAO6D,GAAU,mBAAqBjC,QAAU,iBAAmBA,OAAOC,SAAW,SAAU7B,GAAO,cAAcA,CAAM,EAAG,SAAUA,GAAO,OAAOA,GAAO,mBAAqB4B,QAAU5B,EAAIoD,cAAgBxB,QAAU5B,IAAQ4B,OAAOqB,UAAY,gBAAkBjD,CAAK,EAAG6D,GAAQ7D,EAAM,CAC/U,IAAI9D,GAAY,CAAC,QAAS,YAAa,QAAS,QAAS,UAAW,SACpE,SAAS0I,GAAQC,EAAQC,GAAkB,IAAIzE,EAAOJ,OAAOI,KAAKwE,GAAS,GAAI5E,OAAOwE,sBAAuB,CAAE,IAAIM,EAAU9E,OAAOwE,sBAAsBI,GAASC,IAAmBC,EAAUA,EAAQC,QAAO,SAAUC,GAAO,OAAOhF,OAAOiF,yBAAyBL,EAAQI,GAAKE,UAAY,KAAK9E,EAAKmC,KAAKtD,MAAMmB,EAAM0E,EAAU,CAAE,OAAO1E,CAAM,CACpV,SAAS+E,GAAcf,GAAU,IAAK,IAAI5C,EAAI,EAAGA,EAAI9C,UAAUC,OAAQ6C,IAAK,CAAE,IAAI0C,EAAS,MAAQxF,UAAU8C,GAAK9C,UAAU8C,GAAK,CAAE,EAAEA,EAAI,EAAImD,GAAQ3E,OAAOkE,IAAS,GAAIkB,SAAQ,SAAU3I,GAAO4I,GAAgBjB,EAAQ3H,EAAKyH,EAAOzH,GAAO,IAAKuD,OAAOsF,0BAA4BtF,OAAOuF,iBAAiBnB,EAAQpE,OAAOsF,0BAA0BpB,IAAWS,GAAQ3E,OAAOkE,IAASkB,SAAQ,SAAU3I,GAAOuD,OAAOwF,eAAepB,EAAQ3H,EAAKuD,OAAOiF,yBAAyBf,EAAQzH,GAAO,GAAI,CAAE,OAAO2H,CAAQ,CACzf,SAASiB,GAAgBtF,EAAKtD,EAAK+F,GAA4L,OAAnL/F,EAC5C,SAAwBgJ,GAAO,IAAIhJ,EACnC,SAAsBiJ,EAAOC,GAAQ,GAAuB,WAAnB/B,GAAQ8B,IAAiC,OAAVA,EAAgB,OAAOA,EAAO,IAAIE,EAAOF,EAAM/D,OAAOkE,aAAc,QAAarH,IAAToH,EAAoB,CAAE,IAAIE,EAAMF,EAAKxD,KAAKsD,EAAOC,GAAQ,WAAY,GAAqB,WAAjB/B,GAAQkC,GAAmB,OAAOA,EAAK,MAAM,IAAIvC,UAAU,+CAAiD,CAAE,OAAiB,WAAToC,EAAoBI,OAASC,QAAQN,EAAQ,CADnVO,CAAaR,EAAK,UAAW,MAAwB,WAAjB7B,GAAQnH,GAAoBA,EAAMsJ,OAAOtJ,EAAM,CAD1EyJ,CAAezJ,MAAiBsD,EAAOC,OAAOwF,eAAezF,EAAKtD,EAAK,CAAE+F,MAAOA,EAAO0C,YAAY,EAAMiB,cAAc,EAAMC,UAAU,IAAkBrG,EAAItD,GAAO+F,EAAgBzC,CAAK,CAG3O,SAAS/D,GAAyBkI,EAAQC,GAAY,GAAc,MAAVD,EAAgB,MAAO,CAAE,EAAE,IAAkEzH,EAAK+E,EAAnE4C,EACzF,SAAuCF,EAAQC,GAAY,GAAc,MAAVD,EAAgB,MAAO,CAAE,EAAE,IAA2DzH,EAAK+E,EAA5D4C,EAAS,CAAE,EAAMC,EAAarE,OAAOI,KAAK8D,GAAqB,IAAK1C,EAAI,EAAGA,EAAI6C,EAAW1F,OAAQ6C,IAAO/E,EAAM4H,EAAW7C,GAAQ2C,EAASG,QAAQ7H,IAAQ,IAAa2H,EAAO3H,GAAOyH,EAAOzH,IAAQ,OAAO2H,CAAQ,CADhNG,CAA8BL,EAAQC,GAAuB,GAAInE,OAAOwE,sBAAuB,CAAE,IAAIC,EAAmBzE,OAAOwE,sBAAsBN,GAAS,IAAK1C,EAAI,EAAGA,EAAIiD,EAAiB9F,OAAQ6C,IAAO/E,EAAMgI,EAAiBjD,GAAQ2C,EAASG,QAAQ7H,IAAQ,GAAkBuD,OAAOgD,UAAU0B,qBAAqBtC,KAAK8B,EAAQzH,KAAgB2H,EAAO3H,GAAOyH,EAAOzH,GAAQ,CAAE,OAAO2H,CAAQ,CAK3e,ICLMmT,GACAC,GACAC,GDGFC,GAAgB,KAChBC,ICNEJ,IAAiB,EACjBC,IAAoB,EACpBC,QAAsBjZ,EACnB,SAAkCyP,GAEvC,IAAI2J,IADJL,GAEA,OAAO/D,QAAQI,QAAQ3F,GAASE,MAAK,SAAUjN,GAc7C,OAAIuW,IAAuBG,EAAmBJ,GACrCC,IAETD,GAAmBI,EACnBH,GAAsBvW,EACfA,EACT,GACD,GDpBI,SAAS2W,GAAQlc,GACtB,IAAI6O,EAAQ7O,EAAK6O,MACfsN,EAAiBnc,EAAKoc,UACtBA,OAA+B,IAAnBD,EAA4B,CAAE,EAAGA,EAC7C/b,EAAQJ,EAAKI,MACb6W,EAAQjX,EAAKiX,MACboF,EAAUrc,EAAKqc,QACfzB,EAAQ5a,EAAK4a,MACb0B,EAAUjc,GAAyBL,EAAMM,IACvCyb,IACF3b,EAAMgQ,YAAYhN,aAAa2Y,IAEjC,IAAIQ,EAAiBD,EAAQC,eAC3B/D,EAAY8D,EAAQ9D,UACpBgE,EAAWF,EAAQE,SACnBC,EAAkBH,EAAQG,gBAC1BC,EAAYJ,EAAQI,UACpBpL,EAAagL,EAAQhL,WAGvB,GAFAkL,EAASvF,GACTwF,EAAgBrc,EAAMsT,sBACjBuD,IAA+B,IAAtB7W,EAAMmW,YAAuB,CACzC,IAAIoG,EACA5Y,EAAc6W,EAAMnF,WAAW1R,YAAY6G,KAAI,SAAU3G,GAC3D,OAAOuF,GAAcA,GAAc,CAAE,EAAEvF,GAAa,CAAA,EAAI,CACtDC,MAAO,IAEX,IACAwY,EAAU,QACVH,EAAexY,GACfyU,EAAqD,QAA1CmE,EAAoBP,EAAUhL,cAA0C,IAAtBuL,EAA+BA,EAAoBvc,EAAMyW,gBAAgB,CACpI/S,MAAO8W,EAAMnF,cAOf,IAAImH,EAAWzJ,GAAW6I,GAAyBjY,GAAayO,MAAK,WACnE,OAAOqF,QAAQI,SACjB,KACA,OAAO2C,EAAM/E,gBAAgBC,IAAI8G,EACnC,CACAF,EAAU,WACVX,GAAgB3b,EAAMgQ,YAAY/M,YAAW,WAC3CqZ,EAAU,UACZ,GAAGtc,EAAMwW,gBAQT,IAAI4D,EAAUrH,GAAW6I,GAAyB5b,EAAMwX,WAAWpO,GAAc,CAC/EyN,MAAOA,EACPoF,QAASA,EACTvY,MAAO8W,EAAMnF,YACZ6G,IAAU9J,MAAK,SAAUuE,GAC1B,OAAOc,QAAQC,IAAIf,EAAQnM,KAAI,SAAUrC,GACvC,OAAOsP,QAAQI,QAAQ1P,EAAOuJ,SAAStI,GAAc,CACnDyN,MAAOA,EACPoF,QAASA,EACTvY,MAAO8W,EAAMnF,YACZ6G,KAAW9J,MAAK,SAAUoH,GAC3B,OAAOD,GAAWC,EAAoBrR,EAAOuM,SAAU8F,EAAMnF,WAC/D,GACF,KAAIjD,KAAKyF,IAASzF,MAAK,SAAUkI,GAC/B,IAOMmC,EAPFzK,EAAsBsI,EAAUoC,MAAK,SAAU/R,GAEjD,OAyDR,SAAmD7G,GACjD,OAAQhB,MAAM4C,QAAQ5B,IAAU6Q,QAAQ7Q,aAAqC,EAASA,EAAM6Y,mBAC9F,CA3DeC,CADKjS,EAAM7G,MAEpB,IAGIkO,GAEFd,EAAW,CACTG,sBAAuBjI,GAAcA,GAAc,CAAE,GAA0D,QAAtDqT,EAAwBjC,EAAMnF,WAAWrG,eAA+C,IAA1ByN,OAAmC,EAASA,EAAsBpL,wBAA0B,IAAK,GAAI,CAC1NW,oBAAqBA,MAI3B,OAAOuI,GAAYD,EAAW3D,EAAS6D,EACzC,IAAGpI,MAAK,SAAUzO,GAChB,OH1FC,SAAiB/D,GACtB,IAAI+D,EAAc/D,EAAK+D,YACrB3D,EAAQJ,EAAKI,MACb0D,EAAQ9D,EAAK8D,MAGXmZ,EAA4BlZ,EAAYN,QAAO,SAAU8J,EAAKtJ,GAChE,OAAOuF,GAAcA,GAAc,CAAE,EAAE+D,GAAM,CAAE,EAAE7D,GAAgB,GAAIzF,EAAWsE,OAAOuM,SAAUtL,GAAcA,GAAc,CAAE,EAAEvF,EAAWsE,QAAS,GAAI,CACvJuJ,SAAU,WAER,OAAOvO,EAAQU,EAAWC,MAC5B,KAEH,GAAE,IAUDgZ,EAT0B9c,EAAMiW,QAAQ5S,QAAO,SAAU8J,EAAKwK,GAC5D,OAAIA,EAAOjB,QACFiB,EAAOjB,QAAQvJ,GAEjBA,CACT,GAAG,CACD2P,kBAAmBD,EACnBnZ,MAAOA,IAEiCoZ,kBAQ5C,OAAO3Z,EAPcnD,EAAM0W,QAAQ,CACjCoG,kBAAmBA,EACnBnG,QAAS1S,OAAOb,OAAO0Z,GACvBpZ,MAAOA,KAIsBsF,OAAO2L,SAASnK,KAAI,SAAUrC,GAC3D,MAAO,CACLA,OAAQA,EACRrE,MAAOqE,EAAOuJ,WAElB,GACF,CGqDagF,CAAQ,CACb/S,YAAaA,EACb3D,MAAOA,EACP0D,MAAO8W,EAAMnF,YAEjB,GACD,MAAIjD,MAAK,SAAUzO,GAClB,IAAIoZ,EAMJT,EAAU,QACVH,EAAexY,GACf,IAAIqZ,EAAchd,EAAMyW,gBAAgB,CACtC/S,MAAO8W,EAAMnF,aAEf+C,EAAsD,QAA3C2E,EAAqBf,EAAUhL,cAA2C,IAAvB+L,EAAgCA,EAAqB/c,EAAMmW,cAAgBU,GAASmG,GAAeA,GACjK,IAAIC,EAAkBzJ,GAAcgH,EAAMnF,YAC1C,GAAsC,OAAlCmF,EAAMnF,WAAWzB,cAAyBqJ,EAAiB,CAC7D,IAAIpY,EAAOoY,EAAgBpY,KACzBsP,EAAiB8I,EAAgB9I,eACjCE,EAAU4I,EAAgB5I,QAC1BlM,EAAS8U,EAAgB9U,OAC3BA,EAAOgH,SAAS/F,GAAc,CAC5BqF,MAAOA,EACP5J,KAAMA,EACNsP,eAAgBA,EAChBE,QAASA,EACT4H,QAASA,EACT9T,OAAQA,EACRzE,MAAO8W,EAAMnF,YACZ6G,GACL,CACF,IAAGzJ,SAAQ,WACT6J,EAAU,QACNX,IACF3b,EAAMgQ,YAAYhN,aAAa2Y,GAEnC,IACA,OAAOnB,EAAM/E,gBAAgBC,IAAI0E,EACnC,CE3IA,SAASvS,GAAQ7D,GAAkC,OAAO6D,GAAU,mBAAqBjC,QAAU,iBAAmBA,OAAOC,SAAW,SAAU7B,GAAO,cAAcA,CAAM,EAAG,SAAUA,GAAO,OAAOA,GAAO,mBAAqB4B,QAAU5B,EAAIoD,cAAgBxB,QAAU5B,IAAQ4B,OAAOqB,UAAY,gBAAkBjD,CAAK,EAAG6D,GAAQ7D,EAAM,CAC/U,IAAI9D,GAAY,CAAC,QAAS,QAAS,UAAW,SAC9C,SAAS0I,GAAQC,EAAQC,GAAkB,IAAIzE,EAAOJ,OAAOI,KAAKwE,GAAS,GAAI5E,OAAOwE,sBAAuB,CAAE,IAAIM,EAAU9E,OAAOwE,sBAAsBI,GAASC,IAAmBC,EAAUA,EAAQC,QAAO,SAAUC,GAAO,OAAOhF,OAAOiF,yBAAyBL,EAAQI,GAAKE,UAAY,KAAK9E,EAAKmC,KAAKtD,MAAMmB,EAAM0E,EAAU,CAAE,OAAO1E,CAAM,CACpV,SAAS+E,GAAcf,GAAU,IAAK,IAAI5C,EAAI,EAAGA,EAAI9C,UAAUC,OAAQ6C,IAAK,CAAE,IAAI0C,EAAS,MAAQxF,UAAU8C,GAAK9C,UAAU8C,GAAK,CAAE,EAAEA,EAAI,EAAImD,GAAQ3E,OAAOkE,IAAS,GAAIkB,SAAQ,SAAU3I,GAAO4I,GAAgBjB,EAAQ3H,EAAKyH,EAAOzH,GAAO,IAAKuD,OAAOsF,0BAA4BtF,OAAOuF,iBAAiBnB,EAAQpE,OAAOsF,0BAA0BpB,IAAWS,GAAQ3E,OAAOkE,IAASkB,SAAQ,SAAU3I,GAAOuD,OAAOwF,eAAepB,EAAQ3H,EAAKuD,OAAOiF,yBAAyBf,EAAQzH,GAAO,GAAI,CAAE,OAAO2H,CAAQ,CACzf,SAASiB,GAAgBtF,EAAKtD,EAAK+F,GAA4L,OAAnL/F,EAC5C,SAAwBgJ,GAAO,IAAIhJ,EACnC,SAAsBiJ,EAAOC,GAAQ,GAAuB,WAAnB/B,GAAQ8B,IAAiC,OAAVA,EAAgB,OAAOA,EAAO,IAAIE,EAAOF,EAAM/D,OAAOkE,aAAc,QAAarH,IAAToH,EAAoB,CAAE,IAAIE,EAAMF,EAAKxD,KAAKsD,EAAOC,GAAQ,WAAY,GAAqB,WAAjB/B,GAAQkC,GAAmB,OAAOA,EAAK,MAAM,IAAIvC,UAAU,+CAAiD,CAAE,OAAiB,WAAToC,EAAoBI,OAASC,QAAQN,EAAQ,CADnVO,CAAaR,EAAK,UAAW,MAAwB,WAAjB7B,GAAQnH,GAAoBA,EAAMsJ,OAAOtJ,EAAM,CAD1EyJ,CAAezJ,MAAiBsD,EAAOC,OAAOwF,eAAezF,EAAKtD,EAAK,CAAE+F,MAAOA,EAAO0C,YAAY,EAAMiB,cAAc,EAAMC,UAAU,IAAkBrG,EAAItD,GAAO+F,EAAgBzC,CAAK,CAG3O,SAAS/D,GAAyBkI,EAAQC,GAAY,GAAc,MAAVD,EAAgB,MAAO,CAAE,EAAE,IAAkEzH,EAAK+E,EAAnE4C,EACzF,SAAuCF,EAAQC,GAAY,GAAc,MAAVD,EAAgB,MAAO,CAAE,EAAE,IAA2DzH,EAAK+E,EAA5D4C,EAAS,CAAE,EAAMC,EAAarE,OAAOI,KAAK8D,GAAqB,IAAK1C,EAAI,EAAGA,EAAI6C,EAAW1F,OAAQ6C,IAAO/E,EAAM4H,EAAW7C,GAAQ2C,EAASG,QAAQ7H,IAAQ,IAAa2H,EAAO3H,GAAOyH,EAAOzH,IAAQ,OAAO2H,CAAQ,CADhNG,CAA8BL,EAAQC,GAAuB,GAAInE,OAAOwE,sBAAuB,CAAE,IAAIC,EAAmBzE,OAAOwE,sBAAsBN,GAAS,IAAK1C,EAAI,EAAGA,EAAIiD,EAAiB9F,OAAQ6C,IAAO/E,EAAMgI,EAAiBjD,GAAQ2C,EAASG,QAAQ7H,IAAQ,GAAkBuD,OAAOgD,UAAU0B,qBAAqBtC,KAAK8B,EAAQzH,KAAgB2H,EAAO3H,GAAOyH,EAAOzH,GAAQ,CAAE,OAAO2H,CAAQ,CCP3e,SAASR,GAAQ7D,GAAkC,OAAO6D,GAAU,mBAAqBjC,QAAU,iBAAmBA,OAAOC,SAAW,SAAU7B,GAAO,cAAcA,CAAM,EAAG,SAAUA,GAAO,OAAOA,GAAO,mBAAqB4B,QAAU5B,EAAIoD,cAAgBxB,QAAU5B,IAAQ4B,OAAOqB,UAAY,gBAAkBjD,CAAK,EAAG6D,GAAQ7D,EAAM,CAC/U,IAAI9D,GAAY,CAAC,QAAS,UAAW,SACnC0H,GAAa,CAAC,eAAgB,cAAe,gBAC7CsV,GAAa,CAAC,gBACdC,GAAa,CAAC,eAAgB,aAC9BC,GAAa,CAAC,UACdC,GAAa,CAAC,OAAQ,UACxB,SAASzU,GAAQC,EAAQC,GAAkB,IAAIzE,EAAOJ,OAAOI,KAAKwE,GAAS,GAAI5E,OAAOwE,sBAAuB,CAAE,IAAIM,EAAU9E,OAAOwE,sBAAsBI,GAASC,IAAmBC,EAAUA,EAAQC,QAAO,SAAUC,GAAO,OAAOhF,OAAOiF,yBAAyBL,EAAQI,GAAKE,UAAY,KAAK9E,EAAKmC,KAAKtD,MAAMmB,EAAM0E,EAAU,CAAE,OAAO1E,CAAM,CACpV,SAAS+E,GAAcf,GAAU,IAAK,IAAI5C,EAAI,EAAGA,EAAI9C,UAAUC,OAAQ6C,IAAK,CAAE,IAAI0C,EAAS,MAAQxF,UAAU8C,GAAK9C,UAAU8C,GAAK,CAAE,EAAEA,EAAI,EAAImD,GAAQ3E,OAAOkE,IAAS,GAAIkB,SAAQ,SAAU3I,GAAO4I,GAAgBjB,EAAQ3H,EAAKyH,EAAOzH,GAAO,IAAKuD,OAAOsF,0BAA4BtF,OAAOuF,iBAAiBnB,EAAQpE,OAAOsF,0BAA0BpB,IAAWS,GAAQ3E,OAAOkE,IAASkB,SAAQ,SAAU3I,GAAOuD,OAAOwF,eAAepB,EAAQ3H,EAAKuD,OAAOiF,yBAAyBf,EAAQzH,GAAO,GAAI,CAAE,OAAO2H,CAAQ,CACzf,SAASiB,GAAgBtF,EAAKtD,EAAK+F,GAA4L,OAAnL/F,EAC5C,SAAwBgJ,GAAO,IAAIhJ,EACnC,SAAsBiJ,EAAOC,GAAQ,GAAuB,WAAnB/B,GAAQ8B,IAAiC,OAAVA,EAAgB,OAAOA,EAAO,IAAIE,EAAOF,EAAM/D,OAAOkE,aAAc,QAAarH,IAAToH,EAAoB,CAAE,IAAIE,EAAMF,EAAKxD,KAAKsD,EAAOC,GAAQ,WAAY,GAAqB,WAAjB/B,GAAQkC,GAAmB,OAAOA,EAAK,MAAM,IAAIvC,UAAU,+CAAiD,CAAE,OAAiB,WAAToC,EAAoBI,OAASC,QAAQN,EAAQ,CADnVO,CAAaR,EAAK,UAAW,MAAwB,WAAjB7B,GAAQnH,GAAoBA,EAAMsJ,OAAOtJ,EAAM,CAD1EyJ,CAAezJ,MAAiBsD,EAAOC,OAAOwF,eAAezF,EAAKtD,EAAK,CAAE+F,MAAOA,EAAO0C,YAAY,EAAMiB,cAAc,EAAMC,UAAU,IAAkBrG,EAAItD,GAAO+F,EAAgBzC,CAAK,CAG3O,SAAS/D,GAAyBkI,EAAQC,GAAY,GAAc,MAAVD,EAAgB,MAAO,CAAE,EAAE,IAAkEzH,EAAK+E,EAAnE4C,EACzF,SAAuCF,EAAQC,GAAY,GAAc,MAAVD,EAAgB,MAAO,CAAE,EAAE,IAA2DzH,EAAK+E,EAA5D4C,EAAS,CAAE,EAAMC,EAAarE,OAAOI,KAAK8D,GAAqB,IAAK1C,EAAI,EAAGA,EAAI6C,EAAW1F,OAAQ6C,IAAO/E,EAAM4H,EAAW7C,GAAQ2C,EAASG,QAAQ7H,IAAQ,IAAa2H,EAAO3H,GAAOyH,EAAOzH,IAAQ,OAAO2H,CAAQ,CADhNG,CAA8BL,EAAQC,GAAuB,GAAInE,OAAOwE,sBAAuB,CAAE,IAAIC,EAAmBzE,OAAOwE,sBAAsBN,GAAS,IAAK1C,EAAI,EAAGA,EAAIiD,EAAiB9F,OAAQ6C,IAAO/E,EAAMgI,EAAiBjD,GAAQ2C,EAASG,QAAQ7H,IAAQ,GAAkBuD,OAAOgD,UAAU0B,qBAAqBtC,KAAK8B,EAAQzH,KAAgB2H,EAAO3H,GAAOyH,EAAOzH,GAAQ,CAAE,OAAO2H,CAAQ,CAMpe,SAASiV,GAAe1d,GAC7B,IAAII,EAAQJ,EAAKI,MACfic,EAAUrc,EAAKqc,QACfzB,EAAQ5a,EAAK4a,MACb0B,EAAUjc,GAAyBL,EAAMM,IA8S3C,MAAO,CACLqd,oBA9SwB,SAA6BC,GACrD,IAAIC,EAAeD,EAAcC,aAC/BC,EAAcF,EAAcE,YAC5BC,EAAeH,EAAcG,aAE/B,SAASC,EAAwBnP,IAOC+L,EAAMnF,WAAWrE,QAAWwJ,EAAM/E,gBAAgBI,WAChDpH,EAAMpG,SAAWoV,IAShB,IAHF,CAACC,EAAaC,GAAcjB,MAAK,SAAUmB,GAC1E,OC3CyBC,ED2CDD,EC3CSE,ED2CItP,EAAMpG,OC1C1CyV,IAAWC,GAASD,EAAOE,SAASD,GADtC,IAA0BD,EAAQC,CD4CnC,MAEEvD,EAAMlF,SAAS,OAAQ,MAMlBtV,EAAMkW,OACTsE,EAAM/E,gBAAgBG,YAG5B,CACA,OAAOxM,GAAc,CAUnB6U,aAAcL,EACdM,YAAaN,EAKbO,YAAa,SAAqB1P,IACA,IAA5B+L,EAAMnF,WAAWrE,QAAoByM,IAAiBzd,EAAMgQ,YAAYG,SAASiO,eAAiB3P,EAAMpG,SAAWoV,GAGvHA,EAAaY,MACf,GAnDOpe,GAAyBud,EAAe5V,IAqDlD,EAsPC0W,aArPiB,SAAsBC,GACvC,OAAOnV,GAAc,CACnBoV,KAAM,WACN,gBAAiBhE,EAAMnF,WAAWrE,OAClC,gBAAiB,UACjB,gBAAiBwJ,EAAMnF,WAAWrE,OAASwJ,EAAMnF,WAAW1R,YAAY6G,KAAI,SAAUG,GACpF,IAAIxC,EAASwC,EAAMxC,OACnB,OAAOoM,GAAyBvU,EAAM4W,GAAI,OAAQzO,EACpD,IAAGyM,KAAK,UAAOnS,EACf,kBAAmB8R,GAAyBvU,EAAM4W,GAAI,UACrD2H,EACJ,EA2OCE,aA1OiB,SAAsBjB,GAGvC,OAFmBA,EAAcC,aAE1BrU,GAAc,CACnBmM,OAAQ,GACRmJ,YAAY,EACZF,KAAM,SACNtH,SAAU,SAAkBzI,GAC1B,IAAIkQ,EACJlQ,EAAMmQ,iBACN5e,EAAMkX,SAAS9N,GAAc,CAC3BqF,MAAOA,EACPwN,QAASA,EACTvY,MAAO8W,EAAMnF,YACZ6G,IACH1B,EAAMlF,SAAS,SAAU,MACgC,QAAxDqJ,EAAwBnB,EAAcC,oBAAoD,IAA1BkB,GAA4CA,EAAsBN,MACpI,EACDhH,QAAS,SAAiB5I,GACxB,IAAIoQ,EACJpQ,EAAMmQ,iBACN5e,EAAMqX,QAAQjO,GAAc,CAC1BqF,MAAOA,EACPwN,QAASA,EACTvY,MAAO8W,EAAMnF,YACZ6G,IACH1B,EAAMlF,SAAS,QAAS,MACkC,QAAzDuJ,EAAyBrB,EAAcC,oBAAqD,IAA3BoB,GAA6CA,EAAuB1F,OACxI,GA1BOlZ,GAAyBud,EAAeN,IA4BlD,EA6MC4B,cA3GkB,SAAuBP,GACzC,OAAOnV,GAAc,CACnB2V,QAASxK,GAAyBvU,EAAM4W,GAAI,SAC5CA,GAAIrC,GAAyBvU,EAAM4W,GAAI,UACtC2H,EACJ,EAuGCS,cA7MkB,SAAuBxB,GACzC,IAAIyB,EACJ,SAASC,EAAQzQ,IAGXzO,EAAMmW,aAAexB,QAAQ6F,EAAMnF,WAAWwB,SAChDiF,GAAQ1S,GAAc,CACpBqF,MAAOA,EACPzO,MAAOA,EACP6W,MAAO2D,EAAMnF,WAAWyB,YAAc0D,EAAMnF,WAAWwB,MACvDoF,QAASA,EACTzB,MAAOA,GACN0B,IAEL1B,EAAMlF,SAAS,QAAS,KAC1B,CACA,IAAItU,EAAQwc,GAAiB,CAAE,EACdxc,EAAMyc,iBACrB0B,EAAkBne,EAAMoe,UACxBA,OAAgC,IAApBD,EAA6B,IAAMA,EAC/CZ,EAAOte,GAAyBe,EAAOmc,IACrCkC,EAAa7L,GAAcgH,EAAMnF,YAEjCiK,EZlJD,SAAmBC,GACxB,OAAO5K,QAAQ4K,GAAaA,EAAUC,MAAM1K,IAC9C,CYgJgC2K,EAD+C,QAAzDR,EAAwBjf,EAAMgQ,YAAYnP,iBAAiD,IAA1Boe,OAAmC,EAASA,EAAsBM,YAAc,IAE/JnJ,EAAepW,EAAMoW,eAAiBiJ,SAAgDA,EAAWhL,UAAYiL,EAAwB,KAAO,UAChJ,OAAOlW,GAAc,CACnB,oBAAqB,OACrB,wBAAyBoR,EAAMnF,WAAWrE,QAA4C,OAAlCwJ,EAAMnF,WAAWzB,aAAwBW,GAAyBvU,EAAM4W,GAAI,QAAQtV,OAAOkZ,EAAMnF,WAAWzB,cAAeyL,aAA+C,EAASA,EAAWlX,aAAU1F,EAC5P,gBAAiB+X,EAAMnF,WAAWrE,OAASwJ,EAAMnF,WAAW1R,YAAY6G,KAAI,SAAU9I,GACpF,IAAIyG,EAASzG,EAAMyG,OACnB,OAAOoM,GAAyBvU,EAAM4W,GAAI,OAAQzO,EACpD,IAAGyM,KAAK,UAAOnS,EACf,kBAAmB8R,GAAyBvU,EAAM4W,GAAI,SACtDnQ,MAAO+T,EAAMnF,WAAWyB,YAAc0D,EAAMnF,WAAWwB,MACvDD,GAAIrC,GAAyBvU,EAAM4W,GAAI,SACvC8I,aAAc,MACdC,YAAa,MACbC,eAAgB,MAChBxJ,aAAcA,EACdyJ,WAAY,QACZtJ,UAAWvW,EAAMuW,UACjBD,YAAatW,EAAMsW,YACnB8I,UAAWA,EACX/d,KAAM,SACNye,SAAU,SAAkBrR,GAC1B,IAAIhI,EAAQgI,EAAMsR,cAActZ,MAC5BzG,EAAMqW,yBAA2BtB,GAAetG,GAAOuR,YACzD9D,EAAQE,SAAS3V,GAGnBqV,GAAQ1S,GAAc,CACpBqF,MAAOA,EACPzO,MAAOA,EACP6W,MAAOpQ,EAAMU,MAAM,EAAGiY,GACtBnD,QAASA,EACTzB,MAAOA,GACN0B,GACJ,EACD+D,iBAAkB,SAA0BxR,GAC1CqN,GAAQ1S,GAAc,CACpBqF,MAAOA,EACPzO,MAAOA,EACP6W,MAAOpI,EAAMsR,cAActZ,MAAMU,MAAM,EAAGiY,GAC1CnD,QAASA,EACTzB,MAAOA,GACN0B,GACJ,EACDgE,UAAW,SAAmBzR,GACxBsG,GAAetG,GAAOuR,aDrL3B,SAAmBpgB,GACxB,IAAI6O,EAAQ7O,EAAK6O,MACfzO,EAAQJ,EAAKI,MACbic,EAAUrc,EAAKqc,QACfzB,EAAQ5a,EAAK4a,MACb0B,EAAUjc,GAAyBL,EAAMM,IAC3C,GAAkB,YAAduO,EAAM/N,KAAmC,cAAd+N,EAAM/N,IAAqB,CAExD,IAAIyf,EAAwB,WAC1B,IAAIlD,EAAkBzJ,GAAcgH,EAAMnF,YACtC+K,EAAWpgB,EAAMgQ,YAAYG,SAASkQ,eAAe9L,GAAyBvU,EAAM4W,GAAI,QAAQtV,OAAOkZ,EAAMnF,WAAWzB,cAAeqJ,aAAyD,EAASA,EAAgB9U,SACzNiY,IACEA,EAASE,uBACXF,EAASE,wBAAuB,GAEhCF,EAASG,gBAAe,GAG9B,EACIC,EAAkB,WACpB,IAAIvD,EAAkBzJ,GAAcgH,EAAMnF,YAC1C,GAAsC,OAAlCmF,EAAMnF,WAAWzB,cAAyBqJ,EAAiB,CAC7D,IAAIpY,EAAOoY,EAAgBpY,KACzBsP,EAAiB8I,EAAgB9I,eACjCE,EAAU4I,EAAgB5I,QAC1BlM,EAAS8U,EAAgB9U,OAC3BA,EAAOgH,SAAS/F,GAAc,CAC5BqF,MAAOA,EACP5J,KAAMA,EACNsP,eAAgBA,EAChBE,QAASA,EACT4H,QAASA,EACT9T,OAAQA,EACRzE,MAAO8W,EAAMnF,YACZ6G,GACL,CACF,EAEAzN,EAAMmQ,kBAI0B,IAA5BpE,EAAMnF,WAAWrE,SAAqBhR,EAAMmW,aAAexB,QAAQ6F,EAAMnF,WAAWwB,QACtFiF,GAAQ1S,GAAc,CACpBqF,MAAOA,EACPzO,MAAOA,EACP6W,MAAO2D,EAAMnF,WAAWwB,MACxBoF,QAASA,EACTzB,MAAOA,GACN0B,IAAU9J,MAAK,WAChBoI,EAAMlF,SAAS7G,EAAM/N,IAAK,CACxB+f,iBAAkBzgB,EAAMsT,sBAE1BkN,IAIAvd,WAAWkd,EAAuB,EACpC,KAEA3F,EAAMlF,SAAS7G,EAAM/N,IAAK,CAAA,GAC1B8f,IACAL,IAEJ,MAAO,GAAkB,WAAd1R,EAAM/N,IAIf+N,EAAMmQ,iBACNpE,EAAMlF,SAAS7G,EAAM/N,IAAK,MAM1B8Z,EAAM/E,gBAAgBG,iBACjB,GAAkB,QAAdnH,EAAM/N,IACf8Z,EAAMlF,SAAS,OAAQ,MAMvBkF,EAAM/E,gBAAgBG,iBACjB,GAAkB,UAAdnH,EAAM/N,IAAiB,CAGhC,GAAsC,OAAlC8Z,EAAMnF,WAAWzB,cAAyB4G,EAAMnF,WAAW1R,YAAY4X,OAAM,SAAU1X,GACzF,OAAmC,IAA5BA,EAAWC,MAAMlB,MAC1B,IAQE,YAHK5C,EAAMkW,OACTsE,EAAM/E,gBAAgBG,aAO1BnH,EAAMmQ,iBACN,IAAIjU,EAAQ6I,GAAcgH,EAAMnF,YAC9BxQ,EAAO8F,EAAM9F,KACbsP,EAAiBxJ,EAAMwJ,eACvBE,EAAU1J,EAAM0J,QAChBlM,EAASwC,EAAMxC,OACjB,GAAIsG,EAAMiS,SAAWjS,EAAMkS,aACTle,IAAZ4R,IACFlM,EAAO8G,SAAS7F,GAAc,CAC5BqF,MAAOA,EACP5J,KAAMA,EACNsP,eAAgBA,EAChBE,QAASA,EACT4H,QAASA,EACT9T,OAAQA,EACRzE,MAAO8W,EAAMnF,YACZ6G,IACHlc,EAAMa,UAAUmY,eAAe,CAC7B3E,QAASA,EACTxP,KAAMA,EACNnB,MAAO8W,EAAMnF,mBAGZ,GAAI5G,EAAMmS,cACCne,IAAZ4R,IACFlM,EAAO8G,SAAS7F,GAAc,CAC5BqF,MAAOA,EACP5J,KAAMA,EACNsP,eAAgBA,EAChBE,QAASA,EACT4H,QAASA,EACT9T,OAAQA,EACRzE,MAAO8W,EAAMnF,YACZ6G,IACHlc,EAAMa,UAAUuY,kBAAkB,CAChC/E,QAASA,EACTxP,KAAMA,EACNnB,MAAO8W,EAAMnF,mBAGZ,GAAI5G,EAAMoS,YAEV,CACL,QAAgBpe,IAAZ4R,EAeF,OAdAlM,EAAO8G,SAAS7F,GAAc,CAC5BqF,MAAOA,EACP5J,KAAMA,EACNsP,eAAgBA,EAChBE,QAASA,EACT4H,QAASA,EACT9T,OAAQA,EACRzE,MAAO8W,EAAMnF,YACZ6G,SACHlc,EAAMa,UAAUgY,SAAS,CACvBxE,QAASA,EACTxP,KAAMA,EACNnB,MAAO8W,EAAMnF,aAIjByG,GAAQ1S,GAAc,CACpBqF,MAAOA,EACPuN,UAAW,CACThL,QAAQ,GAEVhR,MAAOA,EACP6W,MAAO1C,EACP8H,QAASA,EACTzB,MAAOA,GACN0B,IAAU9J,MAAK,WAChBjK,EAAO8G,SAAS7F,GAAc,CAC5BqF,MAAOA,EACP5J,KAAMA,EACNsP,eAAgBA,EAChBE,QAASA,EACT4H,QAASA,EACT9T,OAAQA,EACRzE,MAAO8W,EAAMnF,YACZ6G,GACL,GACF,CACF,CACF,CCAQ4E,CAAW1X,GAAc,CACvBqF,MAAOA,EACPzO,MAAOA,EACPic,QAASA,EACTzB,MAAOA,GACN0B,GACJ,EACDgD,QAASA,EAIT6B,OAAQvc,EACRwc,QAAS,SAAiBvS,GAQpB+O,EAAcC,eAAiBzd,EAAMgQ,YAAYG,SAASiO,eAAkB5D,EAAMnF,WAAWrE,QAC/FkO,EAAQzQ,EAEZ,GACC8P,EACJ,EA8GC0C,cA7FkB,SAAuB1C,GACzC,OAAOnV,GAAc,CACnB8U,YAAa,SAAqBzP,GAIhCA,EAAMmQ,gBACP,EACDsC,aAAc,WACZ1G,EAAMlF,SAAS,aAAc,KAC/B,GACCiJ,EACJ,EAkFC4C,aAxGiB,SAAsB3D,GACvC,IAAIhQ,EAAQgQ,GAAiB,CAAE,EAC7BrV,EAASqF,EAAMrF,OACfoW,EAAOte,GAAyBuN,EAAO4P,IACzC,OAAOhU,GAAc,CACnBoV,KAAM,UACN,kBAAmBjK,GAAyBvU,EAAM4W,GAAI,SACtDA,GAAIrC,GAAyBvU,EAAM4W,GAAI,OAAQzO,IAC9CoW,EACJ,EAgGC6C,aAlFiB,SAAsB5D,GACvC,IAAI3Y,EAAO2Y,EAAc3Y,KACvBsD,EAASqV,EAAcrV,OACvBoW,EAAOte,GAAyBud,EAAeH,IACjD,OAAOjU,GAAc,CACnBwN,GAAIrC,GAAyBvU,EAAM4W,GAAI,QAAQtV,OAAOuD,EAAKwc,mBAAoBlZ,GAC/EqW,KAAM,SACN,gBAAiBhE,EAAMnF,WAAWzB,eAAiB/O,EAAKwc,kBACxDC,YAAa,SAAqB7S,GAChC,GAAI5J,EAAKwc,oBAAsB7G,EAAMnF,WAAWzB,aAAhD,CAGA4G,EAAMlF,SAAS,YAAazQ,EAAKwc,mBACjC,IAAIhC,EAAa7L,GAAcgH,EAAMnF,YACrC,GAAsC,OAAlCmF,EAAMnF,WAAWzB,cAAyByL,EAAY,CACxD,IAAIkC,EAAQlC,EAAWxa,KACrBsP,EAAiBkL,EAAWlL,eAC5BE,EAAUgL,EAAWhL,QACrBmN,EAAUnC,EAAWlX,OACvBqZ,EAAQrS,SAAS/F,GAAc,CAC7BqF,MAAOA,EACP5J,KAAM0c,EACNpN,eAAgBA,EAChBE,QAASA,EACT4H,QAASA,EACT9T,OAAQqZ,EACR9d,MAAO8W,EAAMnF,YACZ6G,GACL,CAjBA,CAkBD,EACDgC,YAAa,SAAqBzP,GAGhCA,EAAMmQ,gBACP,EACDoC,QAAS,SAAiBvS,GACxB,IAAI0F,EAAiBhM,EAAOiM,kBAAkB,CAC5CvP,KAAMA,EACNnB,MAAO8W,EAAMnF,aAEXhB,EAAUlM,EAAOmM,WAAW,CAC9BzP,KAAMA,EACNnB,MAAO8W,EAAMnF,cAQKhB,EAAUoD,QAAQI,UAAYiE,GAAQ1S,GAAc,CACtEqF,MAAOA,EACPuN,UAAW,CACThL,QAAQ,GAEVhR,MAAOA,EACP6W,MAAO1C,EACP8H,QAASA,EACTzB,MAAOA,GACN0B,KACW9J,MAAK,WACjBjK,EAAO8G,SAAS7F,GAAc,CAC5BqF,MAAOA,EACP5J,KAAMA,EACNsP,eAAgBA,EAChBE,QAASA,EACT4H,QAASA,EACT9T,OAAQA,EACRzE,MAAO8W,EAAMnF,YACZ6G,GACL,GACF,GACCqC,EACJ,EAWH,CE9UA,SAAS1W,GAAQ7D,GAAkC,OAAO6D,GAAU,mBAAqBjC,QAAU,iBAAmBA,OAAOC,SAAW,SAAU7B,GAAO,cAAcA,CAAM,EAAG,SAAUA,GAAO,OAAOA,GAAO,mBAAqB4B,QAAU5B,EAAIoD,cAAgBxB,QAAU5B,IAAQ4B,OAAOqB,UAAY,gBAAkBjD,CAAK,EAAG6D,GAAQ7D,EAAM,CAC/U,SAAS4E,GAAQC,EAAQC,GAAkB,IAAIzE,EAAOJ,OAAOI,KAAKwE,GAAS,GAAI5E,OAAOwE,sBAAuB,CAAE,IAAIM,EAAU9E,OAAOwE,sBAAsBI,GAASC,IAAmBC,EAAUA,EAAQC,QAAO,SAAUC,GAAO,OAAOhF,OAAOiF,yBAAyBL,EAAQI,GAAKE,UAAY,KAAK9E,EAAKmC,KAAKtD,MAAMmB,EAAM0E,EAAU,CAAE,OAAO1E,CAAM,CACpV,SAAS+E,GAAcf,GAAU,IAAK,IAAI5C,EAAI,EAAGA,EAAI9C,UAAUC,OAAQ6C,IAAK,CAAE,IAAI0C,EAAS,MAAQxF,UAAU8C,GAAK9C,UAAU8C,GAAK,CAAE,EAAEA,EAAI,EAAImD,GAAQ3E,OAAOkE,IAAS,GAAIkB,SAAQ,SAAU3I,GAAO4I,GAAgBjB,EAAQ3H,EAAKyH,EAAOzH,GAAO,IAAKuD,OAAOsF,0BAA4BtF,OAAOuF,iBAAiBnB,EAAQpE,OAAOsF,0BAA0BpB,IAAWS,GAAQ3E,OAAOkE,IAASkB,SAAQ,SAAU3I,GAAOuD,OAAOwF,eAAepB,EAAQ3H,EAAKuD,OAAOiF,yBAAyBf,EAAQzH,GAAO,GAAI,CAAE,OAAO2H,CAAQ,CACzf,SAASiB,GAAgBtF,EAAKtD,EAAK+F,GAA4L,OAAnL/F,EAC5C,SAAwBgJ,GAAO,IAAIhJ,EACnC,SAAsBiJ,EAAOC,GAAQ,GAAuB,WAAnB/B,GAAQ8B,IAAiC,OAAVA,EAAgB,OAAOA,EAAO,IAAIE,EAAOF,EAAM/D,OAAOkE,aAAc,QAAarH,IAAToH,EAAoB,CAAE,IAAIE,EAAMF,EAAKxD,KAAKsD,EAAOC,GAAQ,WAAY,GAAqB,WAAjB/B,GAAQkC,GAAmB,OAAOA,EAAK,MAAM,IAAIvC,UAAU,+CAAiD,CAAE,OAAiB,WAAToC,EAAoBI,OAASC,QAAQN,EAAQ,CADnVO,CAAaR,EAAK,UAAW,MAAwB,WAAjB7B,GAAQnH,GAAoBA,EAAMsJ,OAAOtJ,EAAM,CAD1EyJ,CAAezJ,MAAiBsD,EAAOC,OAAOwF,eAAezF,EAAKtD,EAAK,CAAE+F,MAAOA,EAAO0C,YAAY,EAAMiB,cAAc,EAAMC,UAAU,IAAkBrG,EAAItD,GAAO+F,EAAgBzC,CAAK,CAIpO,SAASyd,GAAY7hB,GAC1B,IAAI8hB,EAAGC,EAAuBC,EAAwBC,EAClD5L,EAAUrW,EAAKqW,QACjBrH,EAAUhP,EAAKgP,QACbkT,EAAuL,QAAzKJ,IAAoE,QAA7DC,EAAwB/S,EAAQmT,+BAA+D,IAA1BJ,OAAmC,EAASA,EAAsBld,aAAe,IAAI,UAAsB,IAANid,OAAe,EAASA,EAAEhd,QACzNsd,EAAeF,EAAaxY,GAAgB,CAAE,EAAEwY,EAAY7d,OAAOI,MAAqE,QAA9Dud,EAAyBhT,EAAQmT,+BAAgE,IAA3BH,OAAoC,EAASA,EAAuBhT,UAAY,KAAO,CAAE,EAC7O,MAAO,CACLqH,QAASA,EAAQzL,KAAI,SAAUmN,GAC7B,MAAO,CACLtQ,KAAMsQ,EAAOtQ,KACbuH,QAAS3K,OAAOI,KAAKsT,EAAO7F,8BAAgC,IAEhE,IACAlD,QAASxF,GAAc,CACrB,oBAAqBnF,OAAOI,KAAKuK,IAChCoT,GACHC,GAAIxd,EAAWnD,QAAuE,QAA9DugB,EAAyBjT,EAAQmT,+BAAgE,IAA3BF,OAAoC,EAASA,EAAuBpd,aAAe,IAErL,CCxBO,SAASyd,GAActiB,GAC5B,IAAIuiB,EACAze,EAAQ9D,EAAK8D,MACjB,OAAqB,IAAjBA,EAAMsN,QAA2C,OAAvBtN,EAAMkQ,aAC3B,MAE2C,QAA3CuO,EAAiB3O,GAAc9P,UAAuC,IAAnBye,OAA4B,EAASA,EAAehO,iBAAmB,IACrI,CCRA,SAAStM,GAAQ7D,GAAkC,OAAO6D,GAAU,mBAAqBjC,QAAU,iBAAmBA,OAAOC,SAAW,SAAU7B,GAAO,cAAcA,CAAM,EAAG,SAAUA,GAAO,OAAOA,GAAO,mBAAqB4B,QAAU5B,EAAIoD,cAAgBxB,QAAU5B,IAAQ4B,OAAOqB,UAAY,gBAAkBjD,CAAK,EAAG6D,GAAQ7D,EAAM,CAC/U,SAAS4E,GAAQC,EAAQC,GAAkB,IAAIzE,EAAOJ,OAAOI,KAAKwE,GAAS,GAAI5E,OAAOwE,sBAAuB,CAAE,IAAIM,EAAU9E,OAAOwE,sBAAsBI,GAASC,IAAmBC,EAAUA,EAAQC,QAAO,SAAUC,GAAO,OAAOhF,OAAOiF,yBAAyBL,EAAQI,GAAKE,UAAY,KAAK9E,EAAKmC,KAAKtD,MAAMmB,EAAM0E,EAAU,CAAE,OAAO1E,CAAM,CACpV,SAAS+E,GAAcf,GAAU,IAAK,IAAI5C,EAAI,EAAGA,EAAI9C,UAAUC,OAAQ6C,IAAK,CAAE,IAAI0C,EAAS,MAAQxF,UAAU8C,GAAK9C,UAAU8C,GAAK,CAAE,EAAEA,EAAI,EAAImD,GAAQ3E,OAAOkE,IAAS,GAAIkB,SAAQ,SAAU3I,GAAO4I,GAAgBjB,EAAQ3H,EAAKyH,EAAOzH,GAAO,IAAKuD,OAAOsF,0BAA4BtF,OAAOuF,iBAAiBnB,EAAQpE,OAAOsF,0BAA0BpB,IAAWS,GAAQ3E,OAAOkE,IAASkB,SAAQ,SAAU3I,GAAOuD,OAAOwF,eAAepB,EAAQ3H,EAAKuD,OAAOiF,yBAAyBf,EAAQzH,GAAO,GAAI,CAAE,OAAO2H,CAAQ,CACzf,SAASiB,GAAgBtF,EAAKtD,EAAK+F,GAA4L,OAAnL/F,EAC5C,SAAwBgJ,GAAO,IAAIhJ,EACnC,SAAsBiJ,EAAOC,GAAQ,GAAuB,WAAnB/B,GAAQ8B,IAAiC,OAAVA,EAAgB,OAAOA,EAAO,IAAIE,EAAOF,EAAM/D,OAAOkE,aAAc,QAAarH,IAAToH,EAAoB,CAAE,IAAIE,EAAMF,EAAKxD,KAAKsD,EAAOC,GAAQ,WAAY,GAAqB,WAAjB/B,GAAQkC,GAAmB,OAAOA,EAAK,MAAM,IAAIvC,UAAU,+CAAiD,CAAE,OAAiB,WAAToC,EAAoBI,OAASC,QAAQN,EAAQ,CADnVO,CAAaR,EAAK,UAAW,MAAwB,WAAjB7B,GAAQnH,GAAoBA,EAAMsJ,OAAOtJ,EAAM,CAD1EyJ,CAAezJ,MAAiBsD,EAAOC,OAAOwF,eAAezF,EAAKtD,EAAK,CAAE+F,MAAOA,EAAO0C,YAAY,EAAMiB,cAAc,EAAMC,UAAU,IAAkBrG,EAAItD,GAAO+F,EAAgBzC,CAAK,CAMpO,IAAIoe,GAAe,SAAsB1e,EAAO6R,GACrD,OAAQA,EAAOlU,MACb,IAAK,kBAqHL,IAAK,YAED,OAAO+H,GAAcA,GAAc,CAAE,EAAE1F,GAAQ,CAAA,EAAI,CACjDkQ,aAAc2B,EAAOhI,UAlH3B,IAAK,WAED,OAAOnE,GAAcA,GAAc,CAAE,EAAE1F,GAAQ,CAAA,EAAI,CACjDmT,MAAOtB,EAAOhI,QACduJ,WAAY,OAGlB,IAAK,iBAED,OAAO1N,GAAcA,GAAc,CAAE,EAAE1F,GAAQ,CAAA,EAAI,CACjDC,YAAa4R,EAAOhI,UAG1B,IAAK,YAED,OAAOnE,GAAcA,GAAc,CAAE,EAAE1F,GAAQ,CAAA,EAAI,CACjDsN,OAAQuE,EAAOhI,UAGrB,IAAK,YAED,OAAOnE,GAAcA,GAAc,CAAE,EAAE1F,GAAQ,CAAA,EAAI,CACjDqT,OAAQxB,EAAOhI,UAGrB,IAAK,aAED,OAAOnE,GAAcA,GAAc,CAAE,EAAE1F,GAAQ,CAAA,EAAI,CACjDsL,QAAS5F,GAAcA,GAAc,CAAA,EAAI1F,EAAMsL,SAAUuG,EAAOhI,WAGtE,IAAK,YAED,IAAIyO,EAAY5S,GAAcA,GAAc,CAAA,EAAI1F,GAAQ,GAAI,CAC1DkQ,aAAc2B,EAAOhI,QAAQ8U,eAAe,oBAAsB9M,EAAOhI,QAAQkT,iBAAmBvN,GAAoB,EAAGxP,EAAMkQ,aAAcnQ,EAAcC,GAAQ6R,EAAOvV,MAAMsT,uBAEpL,OAAOlK,GAAcA,GAAc,CAAE,EAAE4S,GAAY,CAAA,EAAI,CACrDlF,WAAYoL,GAAc,CACxBxe,MAAOsY,MAIf,IAAK,UAED,IAAIsG,EAAalZ,GAAcA,GAAc,CAAA,EAAI1F,GAAQ,GAAI,CAC3DkQ,aAAcV,IAAqB,EAAGxP,EAAMkQ,aAAcnQ,EAAcC,GAAQ6R,EAAOvV,MAAMsT,uBAE/F,OAAOlK,GAAcA,GAAc,CAAE,EAAEkZ,GAAa,CAAA,EAAI,CACtDxL,WAAYoL,GAAc,CACxBxe,MAAO4e,MAIf,IAAK,SAED,OAAI5e,EAAMsN,OACD5H,GAAcA,GAAc,CAAE,EAAE1F,GAAQ,CAAA,EAAI,CACjDkQ,aAAc,KACd5C,QAAQ,EACR8F,WAAY,OAGT1N,GAAcA,GAAc,CAAE,EAAE1F,GAAQ,CAAA,EAAI,CACjDkQ,aAAc,KACdiD,MAAO,GACPE,OAAQ,OACRpT,YAAa,KAGnB,IAAK,SAED,OAAOyF,GAAcA,GAAc,CAAE,EAAE1F,GAAQ,CAAA,EAAI,CACjDkQ,aAAc,KACd5C,QAAQ,EACR+F,OAAQ,SAGd,IAAK,QAED,OAAO3N,GAAcA,GAAc,CAAE,EAAE1F,GAAQ,CAAA,EAAI,CACjDkQ,cAM6B,IAA7B2B,EAAOvV,MAAMmW,YAAuBZ,EAAOvV,MAAMsT,oBAAsB,KACvEyD,OAAQ,OACRD,WAAY,KACZD,MAAO,KAGb,IAAK,QAED,OAAOzN,GAAcA,GAAc,CAAE,EAAE1F,GAAQ,CAAA,EAAI,CACjDkQ,aAAc2B,EAAOvV,MAAMsT,oBAC3BtC,QAASuE,EAAOvV,MAAMmW,aAAexB,QAAQjR,EAAMmT,SAAWtB,EAAOvV,MAAMyW,gBAAgB,CACzF/S,MAAOA,MAIf,IAAK,OAED,OAAI6R,EAAOvV,MAAMkW,MACRxS,EAEF0F,GAAcA,GAAc,CAAE,EAAE1F,GAAQ,CAAA,EAAI,CACjDsN,QAAQ,EACR4C,aAAc,OASpB,IAAK,aAED,OAAOxK,GAAcA,GAAc,CAAE,EAAE1F,GAAQ,CAAA,EAAI,CACjDkQ,aAAc2B,EAAOvV,MAAMsT,sBAGjC,QAEE,MADiB,sBAAsBhS,OAAO2W,KAAKC,UAAU3C,EAAOlU,MAAO,sBACpEqC,EAEb,EChJA,SAASmE,GAAQ7D,GAAkC,OAAO6D,GAAU,mBAAqBjC,QAAU,iBAAmBA,OAAOC,SAAW,SAAU7B,GAAO,cAAcA,CAAM,EAAG,SAAUA,GAAO,OAAOA,GAAO,mBAAqB4B,QAAU5B,EAAIoD,cAAgBxB,QAAU5B,IAAQ4B,OAAOqB,UAAY,gBAAkBjD,CAAK,EAAG6D,GAAQ7D,EAAM,CAC/U,SAAS4E,GAAQC,EAAQC,GAAkB,IAAIzE,EAAOJ,OAAOI,KAAKwE,GAAS,GAAI5E,OAAOwE,sBAAuB,CAAE,IAAIM,EAAU9E,OAAOwE,sBAAsBI,GAASC,IAAmBC,EAAUA,EAAQC,QAAO,SAAUC,GAAO,OAAOhF,OAAOiF,yBAAyBL,EAAQI,GAAKE,UAAY,KAAK9E,EAAKmC,KAAKtD,MAAMmB,EAAM0E,EAAU,CAAE,OAAO1E,CAAM,CACpV,SAAS+E,GAAcf,GAAU,IAAK,IAAI5C,EAAI,EAAGA,EAAI9C,UAAUC,OAAQ6C,IAAK,CAAE,IAAI0C,EAAS,MAAQxF,UAAU8C,GAAK9C,UAAU8C,GAAK,CAAE,EAAEA,EAAI,EAAImD,GAAQ3E,OAAOkE,IAAS,GAAIkB,SAAQ,SAAU3I,GAAO4I,GAAgBjB,EAAQ3H,EAAKyH,EAAOzH,GAAO,IAAKuD,OAAOsF,0BAA4BtF,OAAOuF,iBAAiBnB,EAAQpE,OAAOsF,0BAA0BpB,IAAWS,GAAQ3E,OAAOkE,IAASkB,SAAQ,SAAU3I,GAAOuD,OAAOwF,eAAepB,EAAQ3H,EAAKuD,OAAOiF,yBAAyBf,EAAQzH,GAAO,GAAI,CAAE,OAAO2H,CAAQ,CACzf,SAASiB,GAAgBtF,EAAKtD,EAAK+F,GAA4L,OAAnL/F,EAC5C,SAAwBgJ,GAAO,IAAIhJ,EACnC,SAAsBiJ,EAAOC,GAAQ,GAAuB,WAAnB/B,GAAQ8B,IAAiC,OAAVA,EAAgB,OAAOA,EAAO,IAAIE,EAAOF,EAAM/D,OAAOkE,aAAc,QAAarH,IAAToH,EAAoB,CAAE,IAAIE,EAAMF,EAAKxD,KAAKsD,EAAOC,GAAQ,WAAY,GAAqB,WAAjB/B,GAAQkC,GAAmB,OAAOA,EAAK,MAAM,IAAIvC,UAAU,+CAAiD,CAAE,OAAiB,WAAToC,EAAoBI,OAASC,QAAQN,EAAQ,CADnVO,CAAaR,EAAK,UAAW,MAAwB,WAAjB7B,GAAQnH,GAAoBA,EAAMsJ,OAAOtJ,EAAM,CAD1EyJ,CAAezJ,MAAiBsD,EAAOC,OAAOwF,eAAezF,EAAKtD,EAAK,CAAE+F,MAAOA,EAAO0C,YAAY,EAAMiB,cAAc,EAAMC,UAAU,IAAkBrG,EAAItD,GAAO+F,EAAgBzC,CAAK,CAYpO,SAASue,GAAmB3T,GAEjC,IAAI4T,EAAc,GACdxiB,EAAQ8V,GAAgBlH,EAAS4T,GACjChI,EAAQvF,GAAYmN,GAAcpiB,GAUtC,SAA4BJ,GAC1B,IAAI6iB,EAAgBC,EAChBlN,EAAY5V,EAAK4V,UACnB9R,EAAQ9D,EAAK8D,MAOf,GANA1D,EAAM4R,cAAcxI,GAAc,CAChCoM,UAAWA,EACX9R,MAAOA,EACPuY,QAASA,EACTpb,UAAWb,EAAMa,WAChBqb,KACEyG,KAAyE,QAApCF,EAAiB/e,EAAMsL,eAAwC,IAAnByT,GAAgG,QAAlEC,EAAwBD,EAAepR,6BAA6D,IAA1BqR,GAAoCA,EAAsB1Q,sBAA0C,IAAnBhS,EAAMuO,SAAoB,CACvS,IAAIoJ,EAAShJ,GAA4B,CACvCS,+BAA+B,IAEjCpP,EAAMiW,QAAQzP,KAAKmR,GACnBiL,EAAiB,CAACjL,GACpB,CACF,IA1BIuE,EbbC,SAAgCtc,GACrC,IAAI4a,EAAQ5a,EAAK4a,MA+BjB,MAAO,CACL6B,gBA/BoB,SAAyB5V,GAC7C+T,EAAMlF,SAAS,kBAAmB7O,EACnC,EA8BC2V,SA7Ba,SAAkB3V,GAC/B+T,EAAMlF,SAAS,WAAY7O,EAC5B,EA4BC0V,eA3BmB,SAAwB0G,GAC3C,IAAIC,EAAa,EACbrc,EAAQoc,EAASrY,KAAI,SAAU3G,GACjC,OAAOuF,GAAcA,GAAc,CAAE,EAAEvF,GAAa,CAAA,EAAI,CAGtDC,MAAOX,EAAQU,EAAWC,OAAO0G,KAAI,SAAU3F,GAC7C,OAAOuE,GAAcA,GAAc,CAAE,EAAEvE,GAAO,CAAA,EAAI,CAChDwc,kBAAmByB,KAEtB,KAEL,IACAtI,EAAMlF,SAAS,iBAAkB7O,EAClC,EAcC2R,UAbc,SAAmB3R,GACjC+T,EAAMlF,SAAS,YAAa7O,EAC7B,EAYC6V,UAXc,SAAmB7V,GACjC+T,EAAMlF,SAAS,YAAa7O,EAC7B,EAUCyK,WATe,SAAoBzK,GACnC+T,EAAMlF,SAAS,aAAc7O,EAC9B,EASH,Ca3BgBsc,CAAuB,CACnCvI,MAAOA,IAELwI,EAAc1F,GAAelU,GAAc,CAC7CpJ,MAAOA,EACPic,QAASA,EACTzB,MAAOA,EACP3Z,UAAWb,EAAMa,WAChBqb,IAmBH,SAASD,IACP,OAAOH,GAAQ1S,GAAc,CAC3BqF,MAAO,IAAIwU,MAAM,SACjBjH,UAAW,CACThL,OAAQwJ,EAAMnF,WAAWrE,QAE3BhR,MAAOA,EACPa,UAAWb,EAAMa,UACjBgW,MAAO2D,EAAMnF,WAAWwB,MACxBoF,QAASA,EACTzB,MAAOA,GACN0B,GACL,CACA,SAAS0G,EAAiB3M,GACxBA,EAAQ5M,SAAQ,SAAUsO,GACxB,IAAIuL,EACJ,OAAkD,QAA1CA,EAAoBvL,EAAO1G,iBAA6C,IAAtBiS,OAA+B,EAASA,EAAkB7c,KAAKsR,EAAQvO,GAAcA,GAAc,GAAI8S,GAAU,GAAI,CAC7Krb,UAAWb,EAAMa,UACjBob,QAASA,EACThN,SAAU,SAAkB3M,GAC1BkgB,EAAYhc,KAAK,CACfyI,SAAU3M,GAEb,EACD6M,SAAU,SAAkB7M,GAC1BkgB,EAAYhc,KAAK,CACf2I,SAAU7M,GAEb,EACD+V,UAAW,SAAmB/V,GAC5BkgB,EAAYhc,KAAK,CACf6R,UAAW/V,GAEf,IAEJ,GACF,CACA,SAASqgB,IACP,OAAO3iB,EAAMiW,QAAQyG,MAAK,SAAU/E,GAClC,MAAuB,6BAAhBA,EAAOtQ,IAChB,GACF,CACA,GAAIrH,EAAMuO,WAAaoU,IAAkC,CACvD,IAAIQ,EAA2C,kBAAnBnjB,EAAMuO,SAAyB,CAAA,EAAKvO,EAAMuO,SACtEvO,EAAMiW,QAAQzP,KAAKmI,GAA4BwU,GACjD,CASA,OARAP,EAAiB5iB,EAAMiW,SHnElB,SAAwBjV,GAC7B,IAAIoiB,EAAuBC,EACvBC,EAAWtiB,EAAMsiB,SACnBtT,EAAchP,EAAMgP,YAEtB,GAD4E,QAAnDoT,EAAwBpT,EAAYnP,iBAAiD,IAA1BuiB,GAA2G,QAA9DC,EAAyBD,EAAsB7D,iBAAkD,IAA3B8D,OAAhF,EAA6HA,EAAuBtL,SAAS,mBAC7P,CACrB,IAAIwL,EAAoBvT,EAAYG,SAASvR,cAAc,QACvD4kB,EAAUxT,EAAYG,SAASsT,cAAc,QACjDF,EAAkBlc,KAAO,mBACzBpE,YAAW,WACTsgB,EAAkBG,QAAUzL,KAAKC,UAAUoL,GAC3CE,EAAQ9S,YAAY6S,EACrB,GAAE,EACL,CACF,CGsDEI,CAAe,CACbL,SAAU7B,GAAY,CACpBxL,QAASjW,EAAMiW,QACfrH,QAASA,IAEXoB,YAAahQ,EAAMgQ,cAEd5G,GAAcA,GAAc,CACjC6S,QAASA,EACTpb,UAAWb,EAAMa,WAChBmiB,GAAc9G,EACnB,CC/FO,SAAS0H,GAAWhkB,GAAuD,IAAAE,EAAAF,EAApDG,aAC5B8jB,QADwC,IAAA/jB,EAAG,CAAE,EAAAA,GACrCgkB,aAAAA,OAAe,IAAHD,EAAG,YAAWA,EAElC,OACEllB,EAAAC,cAAA,IAAA,CACEmlB,KAAI,qDAAAziB,OAAuDS,OAAO+W,SAASkL,SAA6E,sEACxJ3b,OAAO,SACP4b,IAAI,uBAEJtlB,EAAAC,cAAA,OAAA,CAAMG,UAAU,mBAAmB+kB,GACnCnlB,EAAAC,cAAA,MAAA,CACEC,MAAM,KACNC,OAAO,KACP,aAAW,UACX0f,KAAK,MACL5H,GAAG,UACHsN,MAAM,6BACN5kB,QAAQ,kBAERX,EAAAC,cAAA,OAAA,KACED,EAAAC,cAAQ,QAAA,KAAA,2DAEVD,EAAAC,cAAA,OAAA,CACEG,UAAU,QACVC,EAAE,qOAEJL,EAAAC,cAAA,OAAA,CAAMG,UAAU,QAAQoG,EAAE,UAAUgf,EAAE,SAAStlB,MAAM,QAAQC,OAAO,QAAQslB,GAAG,MAAMC,GAAG,QACxF1lB,EAAAC,cAAA,OAAA,CACEG,UAAU,QACVC,EAAE,yJAEJL,EAAAC,cAAA,OAAA,CACEG,UAAU,QACVC,EAAE,qOAEJL,EAAAC,cAAA,OAAA,CACEG,UAAU,QACVC,EAAE,k0BAEJL,EAAAC,cAAA,OAAA,CACEG,UAAU,QACVC,EAAE,sqBAEJL,EAAAC,cAAA,OAAA,CACEG,UAAU,QACVC,EAAE,uqBAEJL,EAAAC,cAAA,OAAA,CACEG,UAAU,QACVC,EAAE,82BAEJL,EAAAC,cAAA,OAAA,CACEG,UAAU,QACVC,EAAE,4zBAKZ,CC5CA,SAASslB,GAAYtkB,GACnB,OACErB,EAAAC,cAAA,MAAA,CAAKC,MAAM,KAAKC,OAAO,KAAK,aAAYkB,EAAMukB,UAAW/F,KAAK,OAC5D7f,EAAAC,cAAA,IAAA,CAAGO,KAAK,OAAOD,OAAO,eAAeE,cAAc,QAAQI,eAAe,QAAQP,YAAY,OAC3Fe,EAAM2B,UAIf,CAEO,SAAS6iB,GAAM5kB,GAAkD,IAAAE,EAAAF,EAA/CG,aAAAA,OAAY,IAAAD,EAAG,CAAE,EAAAA,EACxC2kB,EASI1kB,EARF2kB,WAAAA,OAAa,IAAHD,EAAG,YAAWA,EAAAE,EAQtB5kB,EAPF6kB,mBAAAA,OAAqB,IAAHD,EAAG,YAAWA,EAAAE,EAO9B9kB,EANF+kB,aAAAA,OAAe,IAAHD,EAAG,cAAaA,EAAAE,EAM1BhlB,EALFilB,uBAAAA,OAAyB,IAAHD,EAAG,WAAUA,EAAAE,EAKjCllB,EAJFmlB,yBAAAA,OAA2B,IAAHD,EAAG,aAAYA,EAAAE,EAIrCplB,EAHFqlB,UAAAA,OAAY,IAAHD,EAAG,WAAUA,EAAAE,EAGpBtlB,EAFFulB,kBAAAA,OAAoB,IAAHD,EAAG,aAAYA,EAAAxB,EAE9B9jB,EADF+jB,aAAAA,OAAe,IAAHD,EAAG,YAAWA,EAG5B,OACEllB,EAAAC,cAAAD,EAAA4C,SAAA,KACE5C,EAAAC,cAAA,MAAA,CAAKG,UAAU,kBACbJ,EAAAC,cAACglB,GAAW,CAAC7jB,aAAc,CAAE+jB,aAAAA,MAE/BnlB,EAAAC,cAAA,KAAA,CAAIG,UAAU,sBACZJ,EAAAC,cACED,KAAAA,KAAAA,EAAAC,cAAA,MAAA,CAAKG,UAAU,0BACbJ,EAAAC,cAAC0lB,GAAW,CAACC,UAAWK,GACtBjmB,EAAAC,cAAA,OAAA,CAAMI,EAAE,sDAGZL,EAAAC,cAAA,OAAA,CAAMG,UAAU,mBAAmB2lB,IAErC/lB,EAAAC,cAAA,KAAA,KACED,EAAAC,cAAA,MAAA,CAAKG,UAAU,0BACbJ,EAAAC,cAAC0lB,GAAW,CAACC,UAAWW,GACtBvmB,EAAAC,cAAA,OAAA,CAAMI,EAAE,mCAGZL,EAAAC,cAAA,MAAA,CAAKG,UAAU,0BACbJ,EAAAC,cAAC0lB,GAAW,CAACC,UAAWS,GACtBrmB,EAAAC,cAAA,OAAA,CAAMI,EAAE,qCAGZL,EAAAC,cAAA,OAAA,CAAMG,UAAU,mBAAmB+lB,IAErCnmB,EAAAC,cAAA,KAAA,KACED,EAAAC,cAAA,MAAA,CAAKG,UAAU,0BACbJ,EAAAC,cAAC0lB,GAAW,CAACC,UAAWe,GACtB3mB,EAAAC,cAAA,OAAA,CAAMI,EAAE,0fAGZL,EAAAC,cAAA,OAAA,CAAMG,UAAU,mBAAmBqmB,KAK7C,CC3EO,SAASG,GAAG3lB,GAA2C,IAAxCsO,EAAGtO,EAAHsO,IAAKvM,EAAQ/B,EAAR+B,SACzB,OAAOhD,EAAAC,cAAA,IAAA,CAAGmlB,KAAM7V,EAAIsX,KAAM7jB,EAC5B,CCTO,SAAS8jB,KACd,OACE9mB,EAAAC,cAAA,MAAA,CAAKU,QAAQ,YAAYJ,OAAO,eAAewmB,cAAc,MAC3D/mB,EAAAC,cAAA,IAAA,CAAGO,KAAK,OAAOI,SAAS,WACtBZ,EAAAC,cAAA,IAAA,CAAGic,UAAU,iBAAiB5b,YAAY,KACxCN,EAAAC,cAAA,SAAA,CAAQ8mB,cAAc,KAAKC,GAAG,KAAKC,GAAG,KAAKC,EAAE,OAC7ClnB,EAAAC,cAAA,OAAA,CAAMI,EAAE,+BACNL,EAAAC,cAAA,mBAAA,CACEknB,cAAc,YACdzkB,KAAK,SACLiG,KAAK,UACLye,GAAG,YACHC,IAAI,KACJC,YAAY,kBAO1B,CCpBO,SAASC,KACd,OACEvnB,EAAAC,cAAA,MAAA,CAAKC,MAAM,KAAKC,OAAO,KAAKQ,QAAQ,aAClCX,EAAAC,cAAA,IAAA,CAAGM,OAAO,eAAeC,KAAK,OAAOI,SAAS,UAAUH,cAAc,QAAQI,eAAe,SAC3Fb,EAAAC,cAAA,OAAA,CAAMI,EAAE,gEACRL,EAAAC,cAAA,OAAA,CAAMI,EAAE,uDAIhB,CCTO,SAASmnB,KACd,OACExnB,EAAAC,cAAA,MAAA,CAAKC,MAAM,KAAKC,OAAO,KAAKQ,QAAQ,aAClCX,EAAAC,cAAA,OAAA,CACEI,EAAE,gFACFE,OAAO,eACPC,KAAK,OACLI,SAAS,UACTH,cAAc,QACdI,eAAe,UAIvB,CCbO,SAAS4mB,KACd,OACEznB,EAAAC,cAAA,MAAA,CAAKG,UAAU,4BAA4BF,MAAM,KAAKC,OAAO,KAAKQ,QAAQ,aACxEX,EAAAC,cAAA,IAAA,CAAGM,OAAO,eAAeC,KAAK,OAAOI,SAAS,UAAUH,cAAc,QAAQI,eAAe,SAC3Fb,EAAAC,cAAA,OAAA,CAAMI,EAAE,0BACRL,EAAAC,cAAA,OAAA,CAAMI,EAAE,oBAIhB,CCTA,IAAMqnB,GAAoB,WACxB,OACE1nB,EAAAC,cAAA,MAAA,CAAKC,MAAM,KAAKC,OAAO,KAAKQ,QAAQ,aAClCX,EAAAC,cAAA,OAAA,CACEI,EAAE,wGACFE,OAAO,eACPC,KAAK,OACLI,SAAS,UACTC,eAAe,UAIvB,EAEO,SAAS8mB,GAAWtmB,GACzB,OAAQA,EAAMqB,MACZ,IAAK,OACH,OAAO1C,EAAAC,cAACynB,SACV,IAAK,UACH,OAAO1nB,EAAAC,cAAC2nB,SACV,QACE,OAAO5nB,EAAAC,cAAC4nB,SAEd,CAEA,SAASA,KACP,OACE7nB,EAAAC,cAAA,MAAA,CAAKC,MAAM,KAAKC,OAAO,KAAKQ,QAAQ,aAClCX,EAAAC,cAAA,OAAA,CACEI,EAAE,gEACFE,OAAO,eACPC,KAAK,OACLI,SAAS,UACTH,cAAc,QACdI,eAAe,UAIvB,CAEA,SAAS+mB,KACP,OACE5nB,EAAAC,cAAA,MAAA,CAAKC,MAAM,KAAKC,OAAO,KAAKQ,QAAQ,aAClCX,EAAAC,cAAA,OAAA,CACEI,EAAE,kCACFE,OAAO,eACPC,KAAK,OACLI,SAAS,UACTC,eAAe,UAIvB,CCpDO,SAASinB,KACd,OACE9nB,EAAAC,cAAA,MAAA,CAAKC,MAAM,KAAKC,OAAO,KAAKQ,QAAQ,aAClCX,EAAAC,cAAA,OAAA,CACEI,EAAE,+DACFE,OAAO,eACPC,KAAK,OACLI,SAAS,UACTC,eAAe,UAIvB,CCZO,SAASknB,KACd,OACE/nB,EAAAC,cAAA,MAAA,CACEC,MAAM,KACNC,OAAO,KACPQ,QAAQ,YACRH,KAAK,OACLI,SAAS,UACTL,OAAO,eACPE,cAAc,QACdI,eAAe,SAEfb,EAAAC,cAAA,OAAA,CAAMI,EAAE,2KAGd,CCfO,SAAS2nB,KACd,OACEhoB,EAAAC,cAAA,MAAA,CACEC,MAAM,KACNC,OAAO,KACPQ,QAAQ,YACRH,KAAK,OACLI,SAAS,UACTL,OAAO,eACPE,cAAc,QACdI,eAAe,SAEfb,EAAAC,cAAA,OAAA,CAAMI,EAAE,2GAGd,CCJO,SAAS4nB,GAAWhnB,GAAuD,IAAAE,EAAAF,EAApDG,aAAAA,OAAY,IAAAD,EAAG,CAAE,EAAAA,EAC7C+mB,EACE9mB,EADM+mB,UAAAA,OAAY,IAAHD,EAAG,0BAAyBA,EAAAE,EAC3ChnB,EAD6CinB,SAAAA,OAAW,IAAHD,EAAG,mDAAkDA,EAE5G,OACEpoB,EAAAC,cAAA,MAAA,CAAKG,UAAU,yBACbJ,EAAAC,cAAA,MAAA,CAAKG,UAAU,yBACbJ,EAAAC,cAAC8nB,GAAW,OAEd/nB,EAAAC,cAAA,IAAA,CAAGG,UAAU,mBAAmB+nB,GAChCnoB,EAAAC,cAAA,IAAA,CAAGG,UAAU,kBAAkBioB,GAGrC,yBCRO,SAASC,GAAernB,GAAqE,IAAAE,EAAAF,EAAlEG,aAAAA,OAAY,IAAAD,EAAG,CAAE,EAAAA,EAAKE,EAAKC,EAAAL,EAAAM,IAC3DgnB,EAKInnB,EAJFonB,cAAAA,OAAgB,IAAHD,EAAG,iBAAgBA,EAAAE,EAI9BrnB,EAHFsnB,mBAAAA,OAAqB,IAAHD,EAAG,oBAAmBA,EAAAE,EAGtCvnB,EAFFwnB,yBAAAA,OAA2B,IAAHD,EAAG,4CAA2CA,EAAAE,EAEpEznB,EADF0nB,6BAAAA,OAA+B,IAAHD,EAAG,eAAcA,EAEzCE,EAA0C1nB,EAAM0D,MAAMsL,QAAQ0Y,kBAEpE,OACE/oB,EAAAC,cAAA,MAAA,CAAKG,UAAU,uBACbJ,EAAAC,cAAA,MAAA,CAAKG,UAAU,yBACbJ,EAAAC,cAAC+nB,GAAe,OAElBhoB,EAAAC,cAAA,IAAA,CAAGG,UAAU,mBACVooB,EAAc,KAAExoB,EAAAC,cAASoB,SAAAA,KAAAA,EAAM0D,MAAMmT,OAAe,KAGtD6Q,GAAqBA,EAAkB9kB,OAAS,GAC/CjE,EAAAC,cAAA,MAAA,CAAKG,UAAU,oCACbJ,EAAAC,cAAA,IAAA,CAAGG,UAAU,kBAAkBsoB,EAAmB,KAClD1oB,EAAAC,cAAA,KAAA,KACG8oB,EAAkBvgB,MAAM,EAAG,GAAG9D,QAC7B,SAAC8J,EAAKwa,GAAM,MAAArmB,GAAAA,OAAAwG,EACPqF,GACHxO,CAAAA,EAAAC,cAAA,KAAA,CAAI8B,IAAKinB,GACPhpB,EAAAC,cAAA,SAAA,CACEG,UAAU,oBACV2B,IAAKinB,EACLtmB,KAAK,SACL2f,QAAS,WACPhhB,EAAMoc,SAASuL,EAAOC,cAAgB,KACtC5nB,EAAMic,UACNjc,EAAM6nB,SAAS/Z,QAASqL,OAC1B,GAECwO,KAEA,GAEP,MAMP3nB,EAAM8nB,sBACLnpB,EAAAC,cAAA,IAAA,CAAGG,UAAU,kBAAgB,GAAAuC,OACvBimB,EACJ5oB,KAAAA,EAAAC,cAAA,IAAA,CAAGmlB,KAAM/jB,EAAM8nB,qBAAqB,CAAEjR,MAAO7W,EAAM0D,MAAMmT,QAAUxO,OAAO,SAAS4b,IAAI,uBACpFwD,IAMb,sCCrEA,SAASM,GAAkBlf,EAA6Bmf,GAGtD,OAFcA,EAAK9c,MAAM,KAEZ7H,QAAO,SAAC4kB,EAAMna,GACzB,OAAIma,SAAAA,EAAOna,GAAiBma,EAAKna,GAC1B,IACR,GAAEjF,EACL,CASO,SAASqf,GAAOtoB,GAKc,IAJnCsO,EAAGtO,EAAHsO,IACAia,EAASvoB,EAATuoB,UAASC,EAAAxoB,EACTyoB,QAAAA,OAAU,IAAHD,EAAG,OAAMA,EACb7J,EAAIte,EAAAL,EAAAM,IAEP,OAAOtB,EAAAA,cAAcypB,EAAOjf,EAAAA,KACvBmV,GAAI,GAAA,CACP+J,wBAAyB,CACvBC,OAAQR,GAAkB7Z,oBAAG5M,OAAoB6mB,EAAS,YAAaJ,GAAkB7Z,EAAKia,MAGpG,CCXO,SAASK,GAA0CxoB,GACxD,OAAKA,EAAM6D,YAAgD,IAAlC7D,EAAM6D,WAAWC,MAAMlB,OAK9CjE,EAAAC,cAAA,UAAA,CAASG,UAAU,kBACjBJ,EAAAC,cAAA,MAAA,CAAKG,UAAU,wBAAwBiB,EAAMyoB,OAE7C9pB,EAAAC,cAAA,KAAQoB,EAAMmhB,eACXnhB,EAAM6D,WAAWC,MAAM0G,KAAI,SAAC3F,EAAME,GACjC,OAAOpG,EAAAC,cAAC8pB,GAAMtnB,EAAA,CAACV,IAAK,CAACV,EAAMyoB,MAAO5jB,EAAKO,UAAUwP,KAAK,KAAM/P,KAAMA,EAAME,MAAOA,GAAW/E,GAC3F,MAVE,IAcX,CAOA,SAAS0oB,GAAM9oB,GASqB,IARlCiF,EAAIjF,EAAJiF,KACAE,EAAKnF,EAALmF,MACA4jB,EAAU/oB,EAAV+oB,WACAC,EAAYhpB,EAAZgpB,aACAxH,EAAYxhB,EAAZwhB,aACAyH,EAAWjpB,EAAXipB,YACAhlB,EAAUjE,EAAViE,WACAilB,EAAYlpB,EAAZkpB,aAEyDC,EAAAvoB,EAArB7B,EAAM8B,UAAS,GAAM,GAAlDuoB,EAAUD,EAAA,GAAEE,EAAaF,EAAA,GAC6BG,EAAA1oB,EAArB7B,EAAM8B,UAAS,GAAM,GAAtD0oB,EAAYD,EAAA,GAAEE,EAAeF,EAAA,GAC9B3T,EAAS5W,EAAM0qB,OAA4B,MAC3C9D,EAAMuD,EAYZ,OACEnqB,EAAAC,cAAA,KAAAwC,EAAA,CACErC,UAAW,CACT,gBACC8F,EAAyCykB,oBAAsB,uBAChEN,GAAc,0BACdG,GAAgB,6BAEfngB,OAAO2L,SACPC,KAAK,KACR2U,gBAAiB,WACXhU,EAAOzH,SACTyH,EAAOzH,SAEX,GACIsT,EAAa,CACfvc,KAAAA,EACAsD,OAAQtE,EAAWsE,OACnB6Y,QAAAA,SAAQvS,GACNoa,EAAYhkB,EAAM4J,EACpB,KAGF9P,EAAAC,cAAC2mB,EAAG,CAACrX,IAAKrJ,GACRlG,EAAAC,cAAA,MAAA,CAAKG,UAAU,2BACZ4pB,EAAW,CAAE9jB,KAAAA,EAAME,MAAAA,IAEnBF,EAAK2kB,UAAU3kB,EAAKxD,OAAuB,SAAdwD,EAAKxD,MACjC1C,EAAAC,cAAA,MAAA,CAAKG,UAAU,iCACbJ,EAAAC,cAACspB,GAAO,CAACnpB,UAAU,sBAAsBmP,IAAKrJ,EAAMsjB,UAAU,mBAC7DtjB,EAAK6e,SAAW/kB,EAAAC,cAACspB,GAAO,CAACnpB,UAAU,qBAAqBmP,IAAKrJ,EAAMsjB,UAAU,aAIjFtjB,EAAK2kB,UAAU3kB,EAAKxD,QACJ,SAAdwD,EAAKxD,MACU,SAAdwD,EAAKxD,MACS,SAAdwD,EAAKxD,MACS,SAAdwD,EAAKxD,MACS,SAAdwD,EAAKxD,OACL1C,EAAAC,cAAA,MAAA,CAAKG,UAAU,iCACbJ,EAAAC,cAACspB,GAAO,CAACnpB,UAAU,sBAAsBmP,IAAKrJ,EAAMsjB,UAAS7mB,aAAAA,OAAeuD,EAAKxD,QACjF1C,EAAAC,cAACspB,GAAO,CAACnpB,UAAU,qBAAqBmP,IAAKrJ,EAAMsjB,UAAU,oBAIpD,YAAdtjB,EAAKxD,MACJ1C,EAAAC,cAAA,MAAA,CAAKG,UAAU,iCACbJ,EAAAC,cAACspB,GAAO,CAACnpB,UAAU,sBAAsBmP,IAAKrJ,EAAMsjB,UAAU,YAC9DxpB,EAAAC,cAACspB,GAAO,CAACnpB,UAAU,qBAAqBmP,IAAKrJ,EAAMsjB,UAAU,oBAIhES,EAAa,CAAE/jB,KAAAA,EAAM4kB,oBA/D9B,SAA6BC,GAC3BT,GAAc,GACd1T,EAAOzH,QAAU4b,CACnB,EA4DmDC,sBA1DnD,SAA+BD,GAC7BN,GAAgB,GAChB7T,EAAOzH,QAAU4b,CACnB,MA4DF,CC/HO,SAASE,GACdxmB,EACAymB,EACAC,GAEA,OAAO1mB,EAAOC,QAAiC,SAAC8J,EAAKtI,GACnD,IAAMnE,EAAMmpB,EAAUhlB,GAYtB,OAVKsI,EAAIkV,eAAe3hB,KACtByM,EAAIzM,GAAO,IAKTyM,EAAIzM,GAAKkC,QAAUknB,GAAsB,IAC3C3c,EAAIzM,GAAK8F,KAAK3B,GAGTsI,CACR,GAAE,GACL,CCpBO,SAAS4c,GAAiB5kB,GAC/B,OAAOA,CACT,CCEO,SAAS6kB,GAA2Dvb,GAGzE,OAFuD,IAAhCA,EAAqBwb,QAEpBxb,EAAMoS,QAAUpS,EAAMkS,SAAWlS,EAAMiS,SAAWjS,EAAMmS,QAClF,CCRO,SAASpc,KAA4B,CCE5C,IAAM0lB,GAAqB,qBACrBC,GAAwBC,OAAOF,GAAmB/hB,QAEjD,SAASkiB,GAAoBnc,GAAkD,IAAAoc,EAAAC,EAC9EC,EAAuBtc,EAE7B,IAAKsc,EAAqBlB,qBAAuBpb,EAAImN,iBACnD,OAAOnN,EAAIsb,UAAUiB,KAGvB,IAAMA,EAAOD,EAAqBlB,mBACS,QADSgB,EAChDE,EAAqBlB,0BAAkBgB,IAAAA,WAAAA,EAAvCA,EAAyCjP,wBAAgB,IAAAiP,GAAWA,QAAXA,EAAzDA,EAA2Dd,iBAA3Dc,IAAoEA,OAApEA,EAAAA,EAAsEG,KAClD,QADsDF,EAC1Erc,EAAImN,wBAAgBkP,IAAAA,WAAAA,EAApBA,EAAsBf,iBAAS,IAAAe,OAAA,EAA/BA,EAAiCE,KAErC,OAAKA,EAIEA,EAAKhkB,OAAS0jB,GAAsBrpB,KAAK2pB,EAAKhkB,OAASgkB,EAAKhkB,MAAMoO,QAAQqV,GAAoB,IAAMO,EAAKhkB,MAHvGyH,EAAIsb,UAAUiB,IAIzB,CCXO,SAASC,GAAc1qB,GAC5B,OACErB,EAAAC,cAAA,MAAA,CAAKG,UAAU,gCACZiB,EAAM0D,MAAMC,YAAY6G,KAAI,SAAC3G,GAC5B,GAAgC,IAA5BA,EAAWC,MAAMlB,OACnB,OAAO,KAGT,IAAM6lB,EAAQ4B,GAAoBxmB,EAAWC,MAAM,IAEnD,OACEnF,EAAAC,cAAC4pB,GAAOpnB,KACFpB,EAAK,CACTU,IAAKmD,EAAWsE,OAAOuM,SACvB+T,MAAOA,EACP5kB,WAAYA,EACZ8kB,WAAY,SAAF/oB,GAAA,IAAA+qB,EAAK9lB,EAAIjF,EAAJiF,KAAME,EAAKnF,EAALmF,MAAK,OACxBpG,EAAAC,cAAAD,EAAA4C,SACGsD,KAAAA,EAAKykB,oBACJ3qB,EAAAC,cAAA,MAAA,CAAKG,UAAU,qBAAqBO,QAAQ,aAC1CX,EAAAC,cAAA,IAAA,CACEM,OAAO,eACPC,KAAK,OACLI,SAAS,UACTH,cAAc,QACdI,eAAe,SAEdqF,EAAKykB,sBAAkDqB,QAAhCA,EAAK9mB,EAAWC,MAAMiB,EAAQ,UAAzB4lB,IAA2BA,OAA3BA,EAAAA,EAA6BrB,oBACxD3qB,EAAAC,cAAA,OAAA,CAAMI,EAAE,sBAERL,EAAAC,cAAA,OAAA,CAAMI,EAAE,wBAMhBL,EAAAC,cAAA,MAAA,CAAKG,UAAU,sBACbJ,EAAAC,cAAC0nB,GAAU,CAACjlB,KAAMwD,EAAKxD,QAG3B,EACFunB,aAAc,WAAF,OACVjqB,EAAAC,cAAA,MAAA,CAAKG,UAAU,wBACbJ,EAAAC,cAACwnB,GAAU,MACP,IAIb,IAEApmB,EAAM4qB,wBACLjsB,EAAAC,cAAA,UAAA,CAASG,UAAU,wBACjBJ,EAAAC,cAACoB,EAAM4qB,uBAAsB,CAAClnB,MAAO1D,EAAM0D,SAKrD,yBC9CO,SAASmnB,GAAWjrB,GAAwE,IAAAE,EAAAF,EAArEG,aAAAA,OAAY,IAAAD,EAAG,CAAE,EAAAA,EAAKE,EAAKC,EAAAL,EAAAM,IACvD4qB,EAOI/qB,EANFgrB,oBAAAA,OAAsB,IAAHD,EAAG,SAAQA,EAAAE,EAM5BjrB,EALFkrB,qBAAAA,OAAuB,IAAHD,EAAG,qBAAoBA,EAAAE,EAKzCnrB,EAJForB,4BAAAA,OAA8B,IAAHD,EAAG,mBAAkBA,EAAAE,EAI9CrrB,EAHFsrB,8BAAAA,OAAgC,IAAHD,EAAG,kCAAiCA,EAAAE,EAG/DvrB,EAFFwrB,sBAAAA,OAAwB,IAAHD,EAAG,WAAUA,EAAAE,EAEhCzrB,EADF0rB,gCAAAA,OAAkC,IAAHD,EAAG,oCAAmCA,EAEvE,MAA2B,SAAvBxrB,EAAM0D,MAAMqT,SAA8C,IAAzB/W,EAAM0rB,eACrC1rB,EAAM2rB,2BACD,KAIPhtB,EAAAC,cAAA,MAAA,CAAKG,UAAU,yBACbJ,EAAAC,cAAA,IAAA,CAAGG,UAAU,kBAAkBksB,KAKR,IAAzBjrB,EAAM0rB,eACD,KAIP/sB,EAAAC,cAAA,MAAA,CAAKG,UAAU,gCACbJ,EAAAC,cAAC4pB,GAAOpnB,KACFpB,EAAK,CACTyoB,MAAOsC,EACPlnB,WAAY7D,EAAM0D,MAAMC,YAAY,GACpCglB,WAAY,WAAF,OACRhqB,EAAAC,cAAA,MAAA,CAAKG,UAAU,sBACbJ,EAAAC,cAACsnB,GAAU,MAEb,EACF0C,aAAc,SAAFje,GAAA,IAAK9F,EAAI8F,EAAJ9F,KAAM8kB,EAAqBhf,EAArBgf,sBAAuBF,EAAmB9e,EAAnB8e,oBAAmB,OAC/D9qB,EAAAC,cAAAD,EAAA4C,SAAA,KACE5C,EAAAC,cAAA,MAAA,CAAKG,UAAU,wBACbJ,EAAAC,cAAA,SAAA,CACEG,UAAU,8BACV0pB,MAAO0C,EACP9pB,KAAK,SACL2f,QAAS,SAACvS,GACRA,EAAMmQ,iBACNnQ,EAAMmd,kBACNjC,GAAsB,WACpB3pB,EAAM6rB,iBAAiBnW,IAAI7Q,GAC3B7E,EAAM8rB,eAAeC,OAAOlnB,GAC5B7E,EAAMic,SACR,GACF,GAEAtd,EAAAC,cAAC6nB,GAAQ,QAGb9nB,EAAAC,cAAA,MAAA,CAAKG,UAAU,wBACbJ,EAAAC,cAAA,SAAA,CACEG,UAAU,8BACV0pB,MAAO4C,EACPhqB,KAAK,SACL2f,QAAS,SAACvS,GACRA,EAAMmQ,iBACNnQ,EAAMmd,kBACNnC,GAAoB,WAClBzpB,EAAM8rB,eAAeC,OAAOlnB,GAC5B7E,EAAMic,SACR,GACF,GAEAtd,EAAAC,cAACunB,GAAW,QAGf,KAIPxnB,EAAAC,cAAC4pB,GAAOpnB,EAAA,CAAA,EACFpB,EAAK,CACTyoB,MAAO8C,EACP1nB,WAAY7D,EAAM0D,MAAMC,YAAY,GACpCglB,WAAY,WAAF,OACRhqB,EAAAC,cAAA,MAAA,CAAKG,UAAU,sBACbJ,EAAAC,cAAC6nB,GAAQ,MAEX,EACFmC,aAAc,SAAF5nB,GAAA,IAAK6D,EAAI7D,EAAJ6D,KAAM4kB,EAAmBzoB,EAAnByoB,oBAAmB,OACxC9qB,EAAAC,cAAA,MAAA,CAAKG,UAAU,wBACbJ,EAAAC,cAAA,SAAA,CACEG,UAAU,8BACV0pB,MAAOgD,EACPpqB,KAAK,SACL2f,QAAS,SAACvS,GACRA,EAAMmQ,iBACNnQ,EAAMmd,kBACNnC,GAAoB,WAClBzpB,EAAM6rB,iBAAiBE,OAAOlnB,GAC9B7E,EAAMic,SACR,GACF,GAEAtd,EAAAC,cAACunB,GAAW,OAEV,KAKhB,yBC9Fa6F,GAAcrtB,EAAMstB,MAC/B,SAAArsB,GAA6E,IAAAE,EAAAF,EAA1EG,aAAAA,OAAY,IAAAD,EAAG,CAAE,EAAAA,EAAKE,EAAKC,EAAAL,EAAAM,IAC5B,GAA2B,UAAvBF,EAAM0D,MAAMqT,OACd,OAAOpY,EAAAC,cAACgoB,GAAW,CAAC7mB,aAAcA,aAAY,EAAZA,EAAcmsB,cAGlD,IAAMR,EAAiB1rB,EAAM0D,MAAMC,YAAY+Y,MAAK,SAAC7Y,GAAU,OAAKA,EAAWC,MAAMlB,OAAS,KAE9F,OAAK5C,EAAM0D,MAAMmT,OAIM,IAAnB6U,EACK/sB,EAAAC,cAACqoB,GAAe7lB,KAAKpB,EAAK,CAAED,aAAcA,aAAY,EAAZA,EAAcosB,mBAG1DxtB,EAAAC,cAAC8rB,GAAkB1qB,GAPjBrB,EAAAC,cAACisB,GAAWzpB,KAAKpB,EAAK,CAAE0rB,eAAgBA,EAAgB3rB,aAAcA,aAAY,EAAZA,EAAcqsB,cAQ/F,IACA,SAAkBC,EAAYC,GAK5B,MAAkC,YAA3BA,EAAU5oB,MAAMqT,QAAmD,YAA3BuV,EAAU5oB,MAAMqT,MACjE,wBC/BK,SAASwV,GAAS3sB,GAA+D,IAAAE,EAAAF,EAA5DG,aAAAA,OAAY,IAAAD,EAAG,CAAE,EAAAA,EAAKE,EAAKC,EAAAL,EAAAM,IACrDssB,EAMIzsB,EALF0sB,iBAAAA,OAAmB,IAAHD,EAAG,kBAAiBA,EAAAE,EAKlC3sB,EAJF4sB,qBAAAA,OAAuB,IAAHD,EAAG,kBAAiBA,EAAAE,EAItC7sB,EAHF8sB,iBAAAA,OAAmB,IAAHD,EAAG,SAAQA,EAAAE,EAGzB/sB,EAFFgtB,sBAAAA,OAAwB,IAAHD,EAAG,SAAQA,EAAAE,EAE9BjtB,EADFktB,iBAAAA,OAAmB,IAAHD,EAAG,SAAQA,EAErB3V,EAAYrX,EAAMye,aAAa,CACrChB,aAAczd,EAAM6nB,SAAS/Z,UADvBuJ,QAgBR,OAZA1Y,EAAMiC,WAAU,WACVZ,EAAMuW,WAAavW,EAAM6nB,SAAS/Z,SACpC9N,EAAM6nB,SAAS/Z,QAAQqL,OAE1B,GAAE,CAACnZ,EAAMuW,UAAWvW,EAAM6nB,WAE3BlpB,EAAMiC,WAAU,WACVZ,EAAMktB,iBAAmBltB,EAAM6nB,SAAS/Z,SAC1C9N,EAAM6nB,SAAS/Z,QAAQqf,QAE1B,GAAE,CAACntB,EAAMktB,gBAAiBltB,EAAM6nB,WAG/BlpB,EAAAC,cAAAD,EAAA4C,SAAA,KACE5C,EAAAC,cAAA,OAAA,CACEG,UAAU,iBACVmY,SAAU,SAACzI,GACTA,EAAMmQ,gBACN,EACFvH,QAASA,GAET1Y,EAAAC,cAAA,QAAAwC,EAAA,CAAOrC,UAAU,4BAA+BiB,EAAM8e,iBACpDngB,EAAAC,cAACS,EAAY,MACbV,EAAAC,cAAA,OAAA,CAAMG,UAAU,4CAA4CkuB,IAG9DtuB,EAAAC,cAAA,MAAA,CAAKG,UAAU,8BACbJ,EAAAC,cAAC6mB,GAAW,OAGd9mB,EAAAC,sBAAAwC,EAAA,CACErC,UAAU,kBACVc,IAAKG,EAAM6nB,UACP7nB,EAAMgf,cAAc,CACtBvB,aAAczd,EAAM6nB,SAAS/Z,QAC7ByI,UAAWvW,EAAMuW,UACjB6I,UC5EkB,ODgFtBzgB,EAAAC,cAAA,SAAA,CACEyC,KAAK,QACLonB,MAAOgE,EACP1tB,UAAU,kBACV,aAAY4tB,EACZS,QAASptB,EAAM0D,MAAMmT,OAErBlY,EAAAC,cAACunB,GAAS,QAIdxnB,EAAAC,cAAA,SAAA,CAAQG,UAAU,mBAAmBsC,KAAK,QAAQ,aAAY0rB,EAAuB/L,QAAShhB,EAAMqtB,SACjGR,GAIT,8CEhFA,SAASS,GAAqB5sB,GAC5B,OAAkC,IAfpC,WACE,IAAMA,EAAM,eAEZ,IAIE,OAHA6sB,aAAaC,QAAQ9sB,EAAK,IAC1B6sB,aAAaE,WAAW/sB,IAEjB,CACR,CAAC,MAAAgtB,GACA,OAAO,CACT,CACF,CAIMC,GACK,CACLH,QAAAA,WAAkB,EAClBI,QAAO,WACL,MAAO,EACT,GAIG,CACLJ,QAAAA,SAAQ3oB,GACN,OAAO9C,OAAOwrB,aAAaC,QAAQ9sB,EAAKuX,KAAKC,UAAUrT,GACxD,EACD+oB,QAAO,WACL,IAAM/oB,EAAO9C,OAAOwrB,aAAaK,QAAQltB,GAEzC,OAAOmE,EAAOoT,KAAK4V,MAAMhpB,GAAQ,EACnC,EAEJ,CAaO,SAASipB,GAAoBluB,GAGuB,IAFzDc,EAAGd,EAAHc,IAAGqtB,EAAAnuB,EACHouB,MAAAA,OAAQ,IAAHD,EAAG,EAACA,EAEHE,EAAUX,GAAqB5sB,GACjCoD,EAAQmqB,EAAQL,UAAUzmB,MAAM,EAAG6mB,GAEvC,MAAO,CACLtY,IAAAA,SAAI7Q,GACF8F,IAAAA,EAAqD9F,EAA7B8F,EAAhB0Q,iBAAgC1Q,EAAdujB,eAAmBhgB,IAAAA,EAAGjO,EAAA0K,EAAAzK,IAE1CiuB,EAAsBrqB,EAAMoB,WAAU,SAACC,GAAC,OAAKA,EAAEC,WAAa8I,EAAI9I,YAElE+oB,GAAuB,GACzBrqB,EAAMsqB,OAAOD,EAAqB,GAGpCrqB,EAAMuqB,QAAQngB,GACdpK,EAAQA,EAAMqD,MAAM,EAAG6mB,GAEvBC,EAAQT,QAAQ1pB,EACjB,EACDioB,OAAAA,SAAOlnB,GACLf,EAAQA,EAAMkF,QAAO,SAAC7D,GAAC,OAAKA,EAAEC,WAAaP,EAAKO,YAEhD6oB,EAAQT,QAAQ1pB,EACjB,EACDwqB,OAAM,WACJ,OAAOxqB,CACT,EAEJ,CC9EO,SAASyqB,GAA+B3f,GAC7C,IAAIqf,EAEEO,EAAAltB,qBAAAA,OAAoCsN,EAAQlO,KAElD,SAAS+tB,IAKP,YAJgB,IAAZR,IACFA,EAAUrf,EAAQ2e,cAAgBxrB,OAAOwrB,cAGpCU,CACT,CAEA,SAASS,IACP,OAAOzW,KAAK4V,MAAMY,IAAab,QAAQY,IAAiB,KAC1D,CAEA,SAASG,EAAaC,GACpBH,IAAajB,QAAQgB,EAAcvW,KAAKC,UAAU0W,GACpD,CA8BA,MAAO,CACLC,IAAAA,SACEnuB,EACAouB,GAIiB,IAHjBC,EAAApsB,UAAAC,OAAAD,QAAAF,IAAAE,UAAAF,GAAAE,UAA8B,GAAA,CAC5BqsB,KAAM,WAAN,OAAYvX,QAAQI,SAAQ,GAG9B,OAAOJ,QAAQI,UACZzF,MAAK,WArCZ,IACQ6c,EACAL,EAEAM,EAoCA,OAvCAD,EAAargB,EAAQqgB,WAAkC,IAArBrgB,EAAQqgB,WAAoB,KAC9DL,EAAYF,IAQlBC,EANMO,EAAiDjrB,OAAOkrB,YAC5DlrB,OAAOmrB,QAAQR,GAAW5lB,QAAO,SAAApJ,GAC/B,YAA+B,IADmBY,EAAAZ,EAAA,GAAN,GAC3ByvB,SACnB,MAKGJ,GAaLN,EAT6C1qB,OAAOkrB,YAClDlrB,OAAOmrB,QAAQF,GAAgDlmB,QAAO,SAAAhI,GAAmB,IAAfsuB,EAAe9uB,EAAAQ,EAAA,GAAN,GAC3EuuB,GAAmB,IAAIC,MAAOC,UAGpC,QAFkBH,EAAUD,UAAYJ,EAAaM,EAGvD,MAkBWb,IAAsDzW,KAAKC,UAAUxX,GAC9E,IACC0R,MAAK,SAAC3L,GACL,OAAOgR,QAAQC,IAAI,CAACjR,EAAQA,EAAMA,MAAQqoB,SAA0B,IAAVroB,GAC5D,IACC2L,MAAK,SAAA5E,GAAqB,IAAAmE,EAAAnR,EAAAgN,EAAA,GAAnB/G,EAAAkL,EAAA,GAAO+d,EAAM/d,EAAA,GACnB,OAAO8F,QAAQC,IAAI,CAACjR,EAAOipB,GAAUX,EAAOC,KAAKvoB,IACnD,IACC2L,MAAK,SAAAP,GAAO,OAAPrR,EAAAqR,EAAA,GAAO,EAAW,GAC5B,EAEA8d,IAAAA,SAAYjvB,EAAmC+F,GAC7C,OAAOgR,QAAQI,UAAUzF,MAAK,WAC5B,IAAMwc,EAAYF,IASlB,OAPAE,EAAU3W,KAAKC,UAAUxX,IAAQ,CAC/B2uB,eAAeG,MAAOC,UACtBhpB,MAAAA,GAGFgoB,IAAajB,QAAQgB,EAAcvW,KAAKC,UAAU0W,IAE3CnoB,IAEX,EAEAmpB,OAAA,SAAOlvB,GACL,OAAO+W,QAAQI,UAAUzF,MAAK,WAC5B,IAAMwc,EAAYF,WAEXE,EAAU3W,KAAKC,UAAUxX,IAEhC+tB,IAAajB,QAAQgB,EAAcvW,KAAKC,UAAU0W,MAEtD,EAEAiB,MAAA,WACE,OAAOpY,QAAQI,UAAUzF,MAAK,WAC5Bqc,IAAahB,WAAWe,KAE5B,EAEJ,ktVCzGO,IAAM7pB,GAAU,QCMhB,SAASmrB,GACdrkB,EACAC,EACAqkB,GAkBA,OAhBqBpxB,EAAMqxB,SAAQ,WACjC,IAAMC,ECUH,SAAoBxkB,EAAeC,EAAgBkD,GACxD,IAAKnD,GAA0B,iBAAVA,EACnB,MAAM,IAAIuM,MAAM,uBAGlB,IAAKtM,GAA4B,iBAAXA,EACpB,MAAM,IAAIsM,MAAM,wBAGlB,OAAOkY,GAAA9mB,EAAA,CACLqC,MAAAA,EACAC,OAAAA,EACAykB,SAAU,CACRC,YACAC,SACAC,WAEFC,kJACAC,UCyBK,CAAEC,KA5DT,SAAcC,GACZ,OAAO,IAAIjZ,SAAS,SAAAkZ,GAClB,IAAMvuB,EAAgB,IAAIwuB,eAC1BxuB,EAAc8W,KAAKwX,EAAQtlB,OAAQslB,EAAQlL,KAAK,GAEhDvhB,OAAOI,KAAKqsB,EAAQllB,SAASnC,SAAS,SAAArC,GAAA,OAAQ5E,EAAcyuB,iBAAiB7pB,EAAK0pB,EAAQllB,QAAQxE,GAAK,IAEvG,IAcIH,EAdEpB,EAAgB,SAACuB,EAAiB1D,GAAA,OAC/BL,YAAW,WAChBb,EAAc0uB,QAEdH,EAAQ,CACN5Z,OAAQ,EACR2M,QAAApgB,EACAytB,YAAY,GAEhB,GAAG/pB,EAAO,EAGNgqB,EAAiBvrB,EAAcirB,EAAQO,eAAgB,sBAI7D7uB,EAAc8uB,mBAAqB,WAC7B9uB,EAAc+uB,WAAa/uB,EAAcgvB,aAA8B,IAApBvqB,IACrD7D,aAAaguB,GAEbnqB,EAAkBpB,EAAcirB,EAAQW,gBAAiB,kBAE7D,EAEAjvB,EAAckO,QAAU,WAEO,IAAzBlO,EAAc2U,SAChB/T,aAAaguB,GACbhuB,aAAa6D,GAEb8pB,EAAQ,CACNjN,QAASthB,EAAckvB,cAAgB,yBACvCva,OAAQ3U,EAAc2U,OACtBga,YAAY,IAGlB,EAEA3uB,EAAcmvB,OAAS,WACrBvuB,aAAaguB,GACbhuB,aAAa6D,GAEb8pB,EAAQ,CACNjN,QAASthB,EAAckvB,aACvBva,OAAQ3U,EAAc2U,OACtBga,YAAY,GAEhB,EAEA3uB,EAAcquB,KAAKC,EAAQc,KAC7B,GACF,GDtBEC,cAAe,CAAC,CAAE/sB,QAAS,YAC3BgtB,SAAU,wBACVC,eAAgBC,KAChBC,cAAeD,GAAkB,CAAEE,cAAc,IACjDC,WAAYC,GAAwB,CAClCC,OAAQ,CAAC1D,GAA+B,CAAE7tB,OAAAY,OAAQ4wB,GAAgB5wB,KAAAA,OAAImK,KAAYmmB,SAEjFhjB,GAEP,CDtCmBujB,CAAW1mB,EAAOC,GAYjC,OAXAukB,EAAOmC,gBAAgB,YAAaztB,KAOsC,IAAtE,sBAAsB7D,KAAKmvB,EAAOoC,YAAYC,aAAa7rB,QAC7DwpB,EAAOmC,gBAAgB,kBAAmBztB,IAGrCorB,EAAsBE,EAC9B,GAAE,CAACxkB,EAAOC,EAAQqkB,GAGrB,+BGIO,SAASwC,GAAc3yB,GAmBO,IAlBnC6L,EAAK7L,EAAL6L,MACAC,EAAM9L,EAAN8L,OACAsC,EAASpO,EAAToO,UAASwkB,EAAA5yB,EACT0W,YAAAA,OAAc,IAAHkc,EAAG,cAAaA,EAC3BC,EAAgB7yB,EAAhB6yB,iBACA3I,EAAkBlqB,EAAlBkqB,mBAAkB4I,EAAA9yB,EAClBytB,QAAAA,OAAU7oB,IAAHkuB,EAAGluB,GAAIkuB,EAAAC,EAAA/yB,EACdgzB,eAAAA,OAAiB7I,IAAH4I,EAAG5I,GAAQ4I,EAAAE,EAAAjzB,EACzBkpB,aAAAA,OAAevD,IAAHsN,EAAGtN,GAAGsN,EAAAC,EAAAlzB,EAClBgrB,uBAAAA,OAAyB,IAAHkI,EAAG,WAAA,OAA0B,IAAI,EAAAA,EACvDjyB,EAASjB,EAATiB,UAASkyB,EAAAnzB,EACTozB,eAAAA,OAAiB,IAAHD,EAAG,EAACA,EAAAE,EAAArzB,EAClBmwB,sBAAAA,OAAwBhG,IAAHkJ,EAAGlJ,GAAQkJ,EAAAC,EAAAtzB,EAChC+rB,2BAAAA,OAA6B,IAAHuH,GAAQA,EAAAC,EAAAvzB,EAClCwzB,aAAcC,OAAuB,IAAHF,EAAG,GAAEA,EAAArzB,EAAAF,EACvCG,aAAAA,OAAY,IAAAD,EAAG,CAAE,EAAAA,EACjBgoB,EAAoBloB,EAApBkoB,qBAAoBwL,EAAA1zB,EACpB2O,SAAAA,OAAW,IAAH+kB,GAAQA,EAEAC,EAAqFxzB,EAA7FyzB,OAAuCC,EAAsD1zB,EAAjE2zB,UAAqCC,EAAuB1zB,EAAKF,EAAYG,IAS/G6oB,EAAAvoB,EARwB7B,EAAM8B,SAA+C,CAC7EoW,MAAO,GACPlT,YAAa,GACbmT,WAAY,KACZ9H,QAAS,CAAE,EACXgC,QAAQ,EACR4C,aAAc,KACdmD,OAAQ,SACR,GARKrT,EAAKqlB,EAAA,GAAE6K,EAAQ7K,EAAA,GAUhB8K,EAAel1B,EAAM0qB,OAA8B,MACnDyK,EAAWn1B,EAAM0qB,OAA8B,MAC/C0K,EAAiBp1B,EAAM0qB,OAA8B,MACrD2K,EAAcr1B,EAAM0qB,OAA8B,MAClDxB,EAAWlpB,EAAM0qB,OAAgC,MACjD4K,EAAgBt1B,EAAM0qB,OAAe,IACrC6K,EAA4Bv1B,EAAM0qB,OACpB,oBAAXtnB,OAAyBA,OAAOoyB,eAAgBjtB,WAAWC,MAAM,EPtE9C,IOsEmE,IAC7F2G,QACIslB,EAAez0B,EAAM0qB,OAAOgK,GAAwBa,GAA2BpmB,QAE/EkM,EAAe8V,GAAgBrkB,EAAOC,EAAQqkB,GAC9ClE,EAAmBltB,EAAM0qB,OAC7ByE,GAAyC,CACvCptB,IAAGY,kCAAAA,OAAoC0M,GACvCggB,MAAO,MAETlgB,QACIge,EAAiBntB,EAAM0qB,OAC3ByE,GAAyC,CACvCptB,IAAGY,gCAAAA,OAAkC0M,GAGrCggB,MAA4C,IAArCnC,EAAiByC,SAAS1rB,OAAe,EAAI,KAEtDkL,QAEIsmB,EAAmBz1B,EAAM01B,aAC7B,SAA0BxvB,GACxB,IAAI8mB,EAAJ,CAKA,IAAMhE,EAAuB,YAAd9iB,EAAKxD,KAAqBwD,EAAKykB,mBAAqBzkB,EAG/D8iB,IAA0F,IAAhFkE,EAAiByC,SAASppB,WAAU,SAACC,GAAC,OAAKA,EAAEC,WAAauiB,EAAOviB,aAC7E0mB,EAAepW,IAAIiS,EAPrB,CASD,GACD,CAACkE,EAAkBC,EAAgBH,IAG/B2I,EAAqB31B,EAAM01B,aAC/B,SAACxvB,GACC,GAAKnB,EAAMsL,QAAQqC,uBAA0BxM,EAAKwc,kBAAlD,CAEA,IAAMkT,EAAe1vB,EAEf2vB,EAAsB,CAC1B9lB,UAAW,gBACX3J,MAAOwvB,EAAavvB,yBACpBlB,MAAO,CAACywB,GACRtvB,UAAW,CAACJ,EAAKwc,mBACjBhc,QAASkvB,EAAajvB,wBAGxB5B,EAAMsL,QAAQqC,sBAAsB9C,SAASvC,4BAA4BwoB,EAZJ,CAatE,GACD,CAAC9wB,EAAMsL,QAAQqC,wBAGXojB,GAAe91B,EAAMqxB,SACzB,WAAA,OACEzN,GACE,CACE3L,GAAI,YACJtD,oBAAqB,EACrBgD,YAAAA,EACAH,aAAa,EACbhE,aAAc,CACZ0E,MAAOuc,EACPpkB,QAAS,CACP0Y,kBAAmB,KAGvBnZ,SAAAA,EACA1N,UAAAA,EACA+Q,cAAAA,SAAc5R,GACZ4zB,EAAS5zB,EAAM0D,MAChB,EACD8T,WAAAA,SAAU7M,GAAwD,IAArDkM,EAAKlM,EAALkM,MAAc6d,EAAY/pB,EAAnBjH,MAAqBwN,EAAUvG,EAAVuG,WAAYoL,EAAS3R,EAAT2R,UACnD,IAAKzF,EACH,OAAI8U,EACK,GAGF,CACL,CACEjX,SAAU,iBACVzF,SAAAA,SAAQjO,GAAwB,IAArB6D,EAAI7D,EAAJ6D,KAAM4J,EAAKzN,EAALyN,MACf2lB,EAAiBvvB,GAEZmlB,GAAgBvb,IACnB4e,GAEH,EACD/Y,WAAAA,SAAU5S,GACR,OADeA,EAAJmD,KACC2gB,GACb,EACD9T,SAAQ,WACN,OAAOoa,EAAewC,QACxB,GAEF,CACE5Z,SAAU,mBACVzF,SAAAA,SAAQzB,GAAwB,IAArB3I,EAAI2I,EAAJ3I,KAAM4J,EAAKjB,EAALiB,MACf2lB,EAAiBvvB,GAEZmlB,GAAgBvb,IACnB4e,GAEH,EACD/Y,WAAAA,SAAU3C,GACR,OADeA,EAAJ9M,KACC2gB,GACb,EACD9T,SAAQ,WACN,OAAOma,EAAiByC,QAC1B,IAKN,IAAMqG,EAAiBhgB,QAAQpG,GAE/B,OAAOyL,EACJ2N,OAAqB,CACpB9N,SAAU,CAAAzQ,EAAA,CAENyN,MAAAA,EACA7I,UAAAA,EACA4mB,qBAAsB,CACpB,iBACA,iBACA,iBACA,iBACA,iBACA,iBACA,iBACA,UACA,OACA,OAEFC,oBAAqB,CAAAvzB,kBAAAA,OACD2yB,EAAcnmB,SAAOxM,kBAAAA,OACrB2yB,EAAcnmB,SAAOxM,kBAAAA,OACrB2yB,EAAcnmB,SAAOxM,kBAAAA,OACrB2yB,EAAcnmB,SAAOxM,kBAAAA,OACrB2yB,EAAcnmB,SAAOxM,kBAAAA,OACrB2yB,EAAcnmB,SAAOxM,WAAAA,OAC5B2yB,EAAcnmB,UAE3BgnB,oBAAqB,IACrBC,gBAAiB,SACjBC,iBAAkB,UAClBC,YAAa,GACb1jB,eAAgBojB,GACblC,MAIRjgB,OAAM,SAAChC,GASN,KAJmB,eAAfA,EAAMnJ,MACRiV,EAAU,SAGN9L,CACR,IACC4B,MAAK,SAAAP,GAAiB,IACfqjB,EADQrjB,EAAP+I,QACqB,GACpBE,EAAiBoa,EAAjBpa,KAAMqa,EAAWD,EAAXC,OACRxe,EAAUiT,GAAsB9O,GAAM,SAAC5M,GAAG,OAAKmc,GAAoBnc,EAAI,GAAE4b,GAI1E4K,EAAa1lB,QAAQ0Y,kBAA4B9kB,OAASqB,OAAOI,KAAKsS,GAAS/T,QAClFsO,EAAW,CACTwW,kBAAmBzjB,OAAOI,KAAKsS,KAInCzF,EAAW,CAAEikB,OAAAA,IAEb,IAAIhS,EAAiB,CAAE,EAavB,OAXIwR,IACFxR,EAAiB,CACfne,yBAA0BgJ,EAC1B1I,uBAAwB4vB,EAAY7vB,QACpCkG,kCAAmC,CACjCE,MAAAA,EACAC,OAAAA,KAKCzH,OAAOb,OAAuBuT,GAASnM,KAAI,SAAC1G,EAAOiB,GACxD,MAAO,CACL2P,SAAQpT,OAAAA,OAASyD,GACjBkK,SAAAA,SAAQH,GAAwB,IAArBjK,EAAIiK,EAAJjK,KAAM4J,EAAKK,EAALL,MACf2lB,EAAiBvvB,GAEZmlB,GAAgBvb,IACnB4e,GAEH,EACD/Y,WAAAA,SAAUpF,GACR,OADeA,EAAJrK,KACC2gB,GACb,EACD9T,SAAQ,WACN,OAAOzN,OAAOb,OAAOwmB,GAAQ9lB,GAAO,SAACe,GAAI,OAAKA,EAAK2kB,UAAU4L,IAAI,GAAEtL,IAChEtf,IAAIooB,GACJpoB,KAAI,SAAC6qB,GAAW,OACfA,EAAY7qB,KAAI,SAAC3F,GACf,IAAIiZ,EAAsC,KAEpCwX,EAAkBD,EAAYnb,MAClC,SAACqb,GAAW,MACW,SAArBA,EAAYl0B,MAAmBk0B,EAAY/L,UAAU4L,OAASvwB,EAAK2kB,UAAU4L,IAAI,IAOrF,MAJkB,SAAdvwB,EAAKxD,MAAmBi0B,IAC1BxX,EAASwX,GAGXlsB,EAAAA,EAAA,CAAA,EACKvE,GAAI,CAAA,EAAA,CACPykB,mBAAoBxL,GACjBqF,EAEP,GACF,IACCqS,MACL,EAEJ,GACF,GACJ,GAEH,GACH,CACExnB,EACAykB,EACA3I,EACA9P,EACAqT,EACAvB,EACAD,EACAuI,EACAhB,EACA9c,EACAzV,EACA+xB,EACAjH,EACApd,EACA9C,EACAC,IAII6R,GAA+CkX,GAA/ClX,oBAAqBe,GAA0BmW,GAA1BnW,aAAcrC,GAAYwY,GAAZxY,QAyE3C,OCvYK,SAAuBrc,GAKA,IAJ5B2d,EAAmB3d,EAAnB2d,oBACAI,EAAY/d,EAAZ+d,aACAD,EAAW9d,EAAX8d,YACAD,EAAY7d,EAAZ6d,aAEA9e,EAAMiC,WAAU,WACd,GAAM+c,GAAgBD,GAAeD,EAArC,CAIA,IAAAgY,EAAsClY,EAAoB,CACxDI,aAAAA,EACAD,YAAAA,EACAD,aAAAA,IAHMQ,EAAYwX,EAAZxX,aAAcE,EAAWsX,EAAXtX,YAStB,OAHApc,OAAOC,iBAAiB,aAAcic,GACtClc,OAAOC,iBAAiB,YAAamc,GAE9B,WACLpc,OAAOI,oBAAoB,aAAc8b,GACzClc,OAAOI,oBAAoB,YAAagc,EACzC,CAdD,CAeD,GAAE,CAACZ,EAAqBI,EAAcD,EAAaD,GACtD,CDuSEiY,CAAe,CACbnY,oBAAAA,GACAI,aAAcqW,EAAYlmB,QAC1B4P,YAAaqW,EAAejmB,QAC5B2P,aAAcoK,EAAS/Z,UExUpB,SAAqBlO,GAAyC,IAAtCqa,EAASra,EAATqa,UAC7Btb,EAAMiC,WAAU,WACd,GAAKqZ,EAAL,CAIA,IAAM0b,EAAoB1b,EAAU2b,iBAClC,0EAEIC,EAAeF,EAAkB,GACjCG,EAAcH,EAAkBA,EAAkB/yB,OAAS,GAoBjE,OAFAqX,EAAUjY,iBAAiB,UAAW+zB,GAE/B,WACL9b,EAAU9X,oBAAoB,UAAW4zB,EAC1C,CA5BD,CAQA,SAASA,EAAUtnB,GACC,QAAdA,EAAM/N,MAIN+N,EAAMmS,SACJzQ,SAASiO,gBAAkByX,IAC7BpnB,EAAMmQ,iBACNkX,EAAY3c,SAELhJ,SAASiO,gBAAkB0X,IACpCrnB,EAAMmQ,iBACNiX,EAAa1c,SAEjB,CAOF,GAAG,CAACc,GACN,CFwSE+b,CAAa,CAAE/b,UAAW4Z,EAAa/lB,UAEvCnP,EAAMiC,WAAU,WAGd,OAFAuP,SAASM,KAAKwlB,UAAUvgB,IAAI,qBAErB,WAAY,IAAAwgB,EAAAC,EACjBhmB,SAASM,KAAKwlB,UAAUlK,OAAO,qBAIhBmK,QAAfA,GAAAC,EAAAp0B,QAAOq0B,oBAAQF,GAAfA,EAAA7vB,KAAA8vB,EAAkB,EAAGnD,EACtB,CAGF,GAAE,IAEHr0B,EAAMiC,WAAU,WACamB,OAAOs0B,WAAW,sBAEtB3b,UACrBuZ,EAAcnmB,QAAU,EAE3B,GAAE,IAEHnP,EAAMiC,WAAU,WACVozB,EAAYlmB,UACdkmB,EAAYlmB,QAAQwoB,UAAY,EAEpC,GAAG,CAAC5yB,EAAMmT,QAOVlY,EAAMiC,WAAU,WACVwyB,EAAaxwB,OAAS,IACxBqZ,KAEI4L,EAAS/Z,SACX+Z,EAAS/Z,QAAQqL,QAGvB,GAAG,CAACia,EAAcnX,KAKlBtd,EAAMiC,WAAU,WACd,SAAS21B,IACP,GAAIzC,EAAShmB,QAAS,CACpB,IAAM0oB,EAA0B,IAArBz0B,OAAO00B,YAClB3C,EAAShmB,QAAQ4oB,MAAMC,YAAY,iBAAgBr1B,GAAAA,OAAKk1B,QAC1D,CACF,CAMA,OAJAD,IAEAx0B,OAAOC,iBAAiB,SAAUu0B,GAE3B,WACLx0B,OAAOI,oBAAoB,SAAUo0B,EACtC,CACF,GAAE,IAGD53B,EAAAC,cAAA,MAAAwC,EAAA,CACEvB,IAAKg0B,GACDvV,GAAa,CACf,iBAAiB,IACjB,CACFvf,UAAW,CACT,YACA,sBACiB,YAAjB2E,EAAMqT,QAAwB,+BACb,UAAjBrT,EAAMqT,QAAsB,gCAE3B/N,OAAO2L,SACPC,KAAK,KACR4J,KAAK,SACLoY,SAAU,EACV1Y,YAAa,SAACzP,GACRA,EAAMpG,SAAWoG,EAAMsR,eACzBsN,GAEJ,IAEA1uB,EAAAC,cAAA,MAAA,CAAKG,UAAU,kBAAkBc,IAAKi0B,GACpCn1B,EAAAC,cAAA,SAAA,CAAQG,UAAU,sBAAsBc,IAAKk0B,GAC3Cp1B,EAAAC,cAAC2tB,GAASnrB,KACJqzB,GAAY,CAChB/wB,MAAOA,EACP6S,UAAmC,IAAxB6c,EAAaxwB,OACxBilB,SAAUA,EACVqF,gBAAiBvY,QAAQye,IAAiBA,IAAiBc,EAC3Dn0B,aAAc0zB,EACdpG,QAASA,MAIb1uB,EAAAC,cAAA,MAAA,CAAKG,UAAU,qBAAqBc,IAAKm0B,GACvCr1B,EAAAC,cAACotB,GAAW5qB,KACNqzB,GAAY,CAChBzmB,UAAWA,EACXtK,MAAOA,EACPolB,aAAcA,EACd8B,uBAAwBA,EACxBe,2BAA4BA,EAC5BG,eAAgBA,EAChBD,iBAAkBA,EAClBhE,SAAUA,EACV9nB,aAAc4zB,EACd7L,qBAAsBA,EACtBe,YAAa,SAAChkB,EAAM4J,GAElB6lB,EAAmBzvB,GAEnBuvB,EAAiBvvB,GACZmlB,GAAgBvb,IACnB4e,GAEJ,MAIJ1uB,EAAAC,cAAA,SAAA,CAAQG,UAAU,oBAChBJ,EAAAC,cAAC4lB,GAAM,CAACzkB,aAAcwzB,MAKhC,CGlcO,SAASsD,GAA0Bj3B,GAMA,IALxCoR,EAAMpR,EAANoR,OACA8lB,EAAMl3B,EAANk3B,OACAzJ,EAAOztB,EAAPytB,QACAvR,EAAOlc,EAAPkc,QACAib,EAAen3B,EAAfm3B,gBAEAp4B,EAAMiC,WAAU,WACd,SAASsf,EAAUzR,GAA4B,IAAAuoB,EAC7C,GACkB,WAAfvoB,EAAMwoB,MAAqBjmB,GAKE,OAApBgmB,QAATA,EAAAvoB,EAAM/N,WAANs2B,IAASA,OAATA,EAAAA,EAAWpP,iBAA0BnZ,EAAMiS,SAAWjS,EAAMkS,WAtBrE,SAA0BlS,GACxB,IAAMyoB,EAAUzoB,EAAMpG,OAChBggB,EAAU6O,EAAQ7O,QAExB,OAAO6O,EAAQC,mBAAiC,UAAZ9O,GAAmC,WAAZA,GAAoC,aAAZA,CACrF,CAoBU+O,CAAiB3oB,IAAwB,MAAdA,EAAM/N,MAAgBsQ,EAYnD,OAVAvC,EAAMmQ,sBAEF5N,EACFqc,IACUld,SAASM,KAAKwlB,UAAUjY,SAAS,sBAG3C8Y,KAMAC,GAAmBA,EAAgBjpB,UAAYqC,SAASiO,eAAiBtC,GACvE,cAAchb,KAAKkJ,OAAOqtB,aAAa5oB,EAAM6oB,WAC/Cxb,EAAQrN,EAGd,CAIA,OAFA1M,OAAOC,iBAAiB,UAAWke,GAE5B,WACLne,OAAOI,oBAAoB,UAAW+d,EACvC,CACH,GAAG,CAAClP,EAAQ8lB,EAAQzJ,EAASvR,EAASib,GACxC,aCpBO,SAAmB/2B,GAAoC,IAAAu3B,EAAAC,EACtDT,EAAkBp4B,EAAM0qB,OAA0B,MACPN,EAAAvoB,EAArB7B,EAAM8B,UAAS,GAAM,GAA1CuQ,EAAM+X,EAAA,GAAE3Q,EAAS2Q,EAAA,GACoFG,EAAA1oB,EAApE7B,EAAM8B,UAA6BT,eAAAA,EAAOozB,oBAAgB3wB,GAAU,GAArG2wB,EAAYlK,EAAA,GAAEuO,EAAevO,EAAA,GAE9B4N,EAASn4B,EAAM01B,aAAY,WAC/Bjc,GAAU,EACZ,GAAG,CAACA,IAEEiV,EAAU1uB,EAAM01B,aAAY,WAChCjc,GAAU,GACVqf,EAAgBz3B,eAAAA,EAAOozB,aACxB,GAAE,CAAChb,EAAWpY,EAAMozB,eAkBrB,OARAyD,GAA2B,CACzB7lB,OAAAA,EACA8lB,OAAAA,EACAzJ,QAAAA,EACAvR,QAZcnd,EAAM01B,aACpB,SAAC5lB,GACC2J,GAAU,GACVqf,EAAgBhpB,EAAM/N,IACxB,GACA,CAAC0X,EAAWqf,IAQZV,gBAAAA,IAIAp4B,EAAAC,cAAAD,EAAA4C,SAAA,KACE5C,EAAAC,cAACc,EAAe,CAACG,IAAKk3B,EAAiBh3B,aAAcC,SAAmBu3B,QAAdA,EAALv3B,EAAOD,oBAAPw3B,IAAmBA,OAAnBA,EAAAA,EAAqBtN,OAAQjJ,QAAS8V,IAE1F9lB,GACC0mB,EAAAA,aACE/4B,EAAAC,cAAC2zB,GAAcnxB,KACTpB,EAAK,CACTgzB,eAAgBjxB,OAAO41B,QACvBvE,aAAcA,EACdrzB,aAAcC,SAAmBw3B,QAAdA,EAALx3B,EAAOD,oBAAPy3B,IAAmBA,OAAnBA,EAAAA,EAAqBI,MACnCvK,QAASA,KAEXld,SAASM,MAInB", "x_google_ignoreList": [3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 68, 71, 72]}