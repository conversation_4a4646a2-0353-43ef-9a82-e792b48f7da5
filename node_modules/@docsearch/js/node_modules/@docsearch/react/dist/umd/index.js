/*! @docsearch/react 3.8.2 | MIT License | © Algolia, Inc. and contributors | https://docsearch.algolia.com */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react"),require("react-dom")):"function"==typeof define&&define.amd?define(["exports","react","react-dom"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self)["@docsearch/react"]={},e.React,e.ReactDOM)}(this,(function(e,t,r){"use strict";function n(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function o(e,t,r,n,o,i,a){try{var c=e[i](a),u=c.value}catch(e){return void r(e)}c.done?t(u):Promise.resolve(u).then(n,o)}function i(e){return function(){var t=this,r=arguments;return new Promise((function(n,i){var a=e.apply(t,r);function c(e){o(a,n,i,c,u,"next",e)}function u(e){o(a,n,i,c,u,"throw",e)}c(void 0)}))}}function a(e,t,r){return t=f(t),function(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,p()?Reflect.construct(t,r||[],f(e).constructor):t.apply(e,r))}function c(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function u(e,t,r){return Object.defineProperty(e,"prototype",{writable:!1}),e}function l(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function s(){return s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},s.apply(null,arguments)}function f(e){return f=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},f(e)}function m(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&g(e,t)}function p(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(p=function(){return!!e})()}function v(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function d(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?v(Object(r),!0).forEach((function(t){l(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):v(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function h(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.includes(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],t.includes(r)||{}.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}function y(){y=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(e,t,r){e[t]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function l(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{l({},"")}catch(e){l=function(e,t,r){return e[t]=r}}function s(e,t,r,n){var i=t&&t.prototype instanceof g?t:g,a=Object.create(i.prototype),c=new x(n||[]);return o(a,"_invoke",{value:D(e,r,c)}),a}function f(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}t.wrap=s;var m="suspendedStart",p="suspendedYield",v="executing",d="completed",h={};function g(){}function b(){}function S(){}var O={};l(O,a,(function(){return this}));var w=Object.getPrototypeOf,E=w&&w(w(C([])));E&&E!==r&&n.call(E,a)&&(O=E);var j=S.prototype=g.prototype=Object.create(O);function P(e){["next","throw","return"].forEach((function(t){l(e,t,(function(e){return this._invoke(t,e)}))}))}function I(e,t){function r(o,i,a,c){var u=f(e[o],e,i);if("throw"!==u.type){var l=u.arg,s=l.value;return s&&"object"==typeof s&&n.call(s,"__await")?t.resolve(s.__await).then((function(e){r("next",e,a,c)}),(function(e){r("throw",e,a,c)})):t.resolve(s).then((function(e){l.value=e,a(l)}),(function(e){return r("throw",e,a,c)}))}c(u.arg)}var i;o(this,"_invoke",{value:function(e,n){function o(){return new t((function(t,o){r(e,n,t,o)}))}return i=i?i.then(o,o):o()}})}function D(t,r,n){var o=m;return function(i,a){if(o===v)throw Error("Generator is already running");if(o===d){if("throw"===i)throw a;return{value:e,done:!0}}for(n.method=i,n.arg=a;;){var c=n.delegate;if(c){var u=k(c,n);if(u){if(u===h)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===m)throw o=d,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=v;var l=f(t,r,n);if("normal"===l.type){if(o=n.done?d:p,l.arg===h)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(o=d,n.method="throw",n.arg=l.arg)}}}function k(t,r){var n=r.method,o=t.iterator[n];if(o===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,k(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),h;var i=f(o,t.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,h;var a=i.arg;return a?a.done?(r[t.resultName]=a.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,h):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,h)}function A(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function _(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function x(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(A,this),this.reset(!0)}function C(t){if(t||""===t){var r=t[a];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,i=function r(){for(;++o<t.length;)if(n.call(t,o))return r.value=t[o],r.done=!1,r;return r.value=e,r.done=!0,r};return i.next=i}}throw new TypeError(typeof t+" is not iterable")}return b.prototype=S,o(j,"constructor",{value:S,configurable:!0}),o(S,"constructor",{value:b,configurable:!0}),b.displayName=l(S,u,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===b||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,S):(e.__proto__=S,l(e,u,"GeneratorFunction")),e.prototype=Object.create(j),e},t.awrap=function(e){return{__await:e}},P(I.prototype),l(I.prototype,c,(function(){return this})),t.AsyncIterator=I,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new I(s(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},P(j),l(j,u,"Generator"),l(j,a,(function(){return this})),l(j,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=C,x.prototype={constructor:x,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(_),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function o(n,o){return c.type="throw",c.arg=t,r.next=n,o&&(r.method="next",r.arg=e),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var u=n.call(a,"catchLoc"),l=n.call(a,"finallyLoc");if(u&&l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,h):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),h},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),_(r),h}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var o=n.arg;_(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:C(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),h}},t}function g(e,t){return g=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},g(e,t)}function b(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==t);u=!0);}catch(e){l=!0,o=e}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(e,t)||O(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function S(e){return function(e){if(Array.isArray(e))return n(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||O(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function O(e,t){if(e){if("string"==typeof e)return n(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?n(e,t):void 0}}function w(e){var t="function"==typeof Map?new Map:void 0;return w=function(e){if(null===e||!function(e){try{return-1!==Function.toString.call(e).indexOf("[native code]")}catch(t){return"function"==typeof e}}(e))return e;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,r)}function r(){return function(e,t,r){if(p())return Reflect.construct.apply(null,arguments);var n=[null];n.push.apply(n,t);var o=new(e.bind.apply(e,n));return r&&g(o,r.prototype),o}(e,arguments,f(this).constructor)}return r.prototype=Object.create(e.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),g(r,e)},w(e)}function E(){return t.createElement("svg",{width:"15",height:"15",className:"DocSearch-Control-Key-Icon"},t.createElement("path",{d:"M4.505 4.496h2M5.505 5.496v5M8.216 4.496l.055 5.993M10 7.5c.333.333.5.667.5 1v2M12.326 4.5v5.996M8.384 4.496c1.674 0 2.116 0 2.116 1.5s-.442 1.5-2.116 1.5M3.205 9.303c-.09.448-.277 1.21-1.241 1.203C1 10.5.5 9.513.5 8V7c0-1.57.5-2.5 1.464-2.494.964.006 1.134.598 1.24 1.342M12.553 10.5h1.953",strokeWidth:"1.2",stroke:"currentColor",fill:"none",strokeLinecap:"square"}))}function j(){return t.createElement("svg",{width:"20",height:"20",className:"DocSearch-Search-Icon",viewBox:"0 0 20 20","aria-hidden":"true"},t.createElement("path",{d:"M14.386 14.386l4.0877 4.0877-4.0877-4.0877c-2.9418 2.9419-7.7115 2.9419-10.6533 0-2.9419-2.9418-2.9419-7.7115 0-10.6533 2.9418-2.9419 7.7115-2.9419 10.6533 0 2.9419 2.9418 2.9419 7.7115 0 10.6533z",stroke:"currentColor",fill:"none",fillRule:"evenodd",strokeLinecap:"round",strokeLinejoin:"round"}))}var P=["translations"],I="Ctrl";var D=t.forwardRef((function(e,r){var n=e.translations,o=void 0===n?{}:n,i=h(e,P),a=o.buttonText,c=void 0===a?"Search":a,u=o.buttonAriaLabel,l=void 0===u?"Search":u,f=b(t.useState(null),2),m=f[0],p=f[1];t.useEffect((function(){"undefined"!=typeof navigator&&(/(Mac|iPhone|iPod|iPad)/i.test(navigator.platform)?p("⌘"):p(I))}),[]);var v=b(m===I?[I,"Ctrl",t.createElement(E,null)]:["Meta","Command",m],3),d=v[0],y=v[1],g=v[2];return t.createElement("button",s({type:"button",className:"DocSearch DocSearch-Button","aria-label":"".concat(l," (").concat(y,"+K)")},i,{ref:r}),t.createElement("span",{className:"DocSearch-Button-Container"},t.createElement(j,null),t.createElement("span",{className:"DocSearch-Button-Placeholder"},c)),t.createElement("span",{className:"DocSearch-Button-Keys"},null!==m&&t.createElement(t.Fragment,null,t.createElement(k,{reactsToKey:d},g),t.createElement(k,{reactsToKey:"k"},"K"))))}));function k(e){var r=e.reactsToKey,n=e.children,o=b(t.useState(!1),2),i=o[0],a=o[1];return t.useEffect((function(){if(r)return window.addEventListener("keydown",e),window.addEventListener("keyup",t),function(){window.removeEventListener("keydown",e),window.removeEventListener("keyup",t)};function e(e){e.key===r&&a(!0)}function t(e){e.key!==r&&"Meta"!==e.key||a(!1)}}),[r]),t.createElement("kbd",{className:i?"DocSearch-Button-Key DocSearch-Button-Key--pressed":"DocSearch-Button-Key"},n)}function A(e,t){var r=void 0;return function(){for(var n=arguments.length,o=new Array(n),i=0;i<n;i++)o[i]=arguments[i];r&&clearTimeout(r),r=setTimeout((function(){return e.apply(void 0,o)}),t)}}function _(e){return e.reduce((function(e,t){return e.concat(t)}),[])}var x=0;function C(e){return 0===e.collections.length?0:e.collections.reduce((function(e,t){return e+t.items.length}),0)}function T(e){return e!==Object(e)}function N(e,t){if(e===t)return!0;if(T(e)||T(t)||"function"==typeof e||"function"==typeof t)return e===t;if(Object.keys(e).length!==Object.keys(t).length)return!1;for(var r=0,n=Object.keys(e);r<n.length;r++){var o=n[r];if(!(o in t))return!1;if(!N(e[o],t[o]))return!1}return!0}var L=function(){};var R=[{segment:"autocomplete-core",version:"1.17.7"}];function q(e){var t=e.item,r=e.items,n=void 0===r?[]:r;return{index:t.__autocomplete_indexName,items:[t],positions:[1+n.findIndex((function(e){return e.objectID===t.objectID}))],queryID:t.__autocomplete_queryID,algoliaSource:["autocomplete"]}}function M(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(e)).next,0===t);else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==t);u=!0);}catch(e){l=!0,o=e}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return H(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return H(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function H(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var B=["items"],F=["items"];function U(e){return U="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},U(e)}function K(e){return function(e){if(Array.isArray(e))return V(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return V(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return V(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function V(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function z(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r,n,o={},i=Object.keys(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}function J(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Q(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?J(Object(r),!0).forEach((function(t){W(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):J(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function W(e,t,r){return(t=function(e){var t=function(e,t){if("object"!==U(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==U(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===U(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Z(e){return e.map((function(e){var t=e.items,r=z(e,B);return Q(Q({},r),{},{objectIDs:(null==t?void 0:t.map((function(e){return e.objectID})))||r.objectIDs})}))}function G(e){var t,r,n,o=(t=M((e.version||"").split(".").map(Number),2),r=t[0],n=t[1],r>=3||2===r&&n>=4||1===r&&n>=10);function i(t,r,n){if(o&&void 0!==n){var i=n[0].__autocomplete_algoliaCredentials,a={"X-Algolia-Application-Id":i.appId,"X-Algolia-API-Key":i.apiKey};e.apply(void 0,[t].concat(K(r),[{headers:a}]))}else e.apply(void 0,[t].concat(K(r)))}return{init:function(t,r){e("init",{appId:t,apiKey:r})},setAuthenticatedUserToken:function(t){e("setAuthenticatedUserToken",t)},setUserToken:function(t){e("setUserToken",t)},clickedObjectIDsAfterSearch:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];t.length>0&&i("clickedObjectIDsAfterSearch",Z(t),t[0].items)},clickedObjectIDs:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];t.length>0&&i("clickedObjectIDs",Z(t),t[0].items)},clickedFilters:function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];r.length>0&&e.apply(void 0,["clickedFilters"].concat(r))},convertedObjectIDsAfterSearch:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];t.length>0&&i("convertedObjectIDsAfterSearch",Z(t),t[0].items)},convertedObjectIDs:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];t.length>0&&i("convertedObjectIDs",Z(t),t[0].items)},convertedFilters:function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];r.length>0&&e.apply(void 0,["convertedFilters"].concat(r))},viewedObjectIDs:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];t.length>0&&t.reduce((function(e,t){var r=t.items,n=z(t,F);return[].concat(K(e),K(function(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:20,r=[],n=0;n<e.objectIDs.length;n+=t)r.push(Q(Q({},e),{},{objectIDs:e.objectIDs.slice(n,n+t)}));return r}(Q(Q({},n),{},{objectIDs:(null==r?void 0:r.map((function(e){return e.objectID})))||n.objectIDs})).map((function(e){return{items:r,payload:e}}))))}),[]).forEach((function(e){var t=e.items;return i("viewedObjectIDs",[e.payload],t)}))},viewedFilters:function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];r.length>0&&e.apply(void 0,["viewedFilters"].concat(r))}}}function Y(e){var t=e.items.reduce((function(e,t){var r;return e[t.__autocomplete_indexName]=(null!==(r=e[t.__autocomplete_indexName])&&void 0!==r?r:[]).concat(t),e}),{});return Object.keys(t).map((function(e){return{index:e,items:t[e],algoliaSource:["autocomplete"]}}))}function $(e){return e.objectID&&e.__autocomplete_indexName&&e.__autocomplete_queryID}function X(e){return X="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},X(e)}function ee(e){return function(e){if(Array.isArray(e))return te(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return te(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return te(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function te(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function re(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function ne(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?re(Object(r),!0).forEach((function(t){oe(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):re(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function oe(e,t,r){return(t=function(e){var t=function(e,t){if("object"!==X(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==X(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===X(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var ie="2.15.0",ae="https://cdn.jsdelivr.net/npm/search-insights@".concat(ie,"/dist/search-insights.min.js"),ce=A((function(e){var t=e.onItemsChange,r=e.items,n=e.insights,o=e.state;t({insights:n,insightsEvents:Y({items:r}).map((function(e){return ne({eventName:"Items Viewed"},e)})),state:o})}),400);function ue(e){var t=function(e){return ne({onItemsChange:function(e){var t=e.insights,r=e.insightsEvents,n=e.state;t.viewedObjectIDs.apply(t,ee(r.map((function(e){return ne(ne({},e),{},{algoliaSource:le(e.algoliaSource,n.context)})}))))},onSelect:function(e){var t=e.insights,r=e.insightsEvents,n=e.state;t.clickedObjectIDsAfterSearch.apply(t,ee(r.map((function(e){return ne(ne({},e),{},{algoliaSource:le(e.algoliaSource,n.context)})}))))},onActive:L,__autocomplete_clickAnalytics:!0},e)}(e),r=t.insightsClient,n=t.insightsInitParams,o=t.onItemsChange,i=t.onSelect,a=t.onActive,c=t.__autocomplete_clickAnalytics,u=r;if(r||function(e){if("undefined"!=typeof window)e({window:window})}((function(e){var t=e.window,r=t.AlgoliaAnalyticsObject||"aa";"string"==typeof r&&(u=t[r]),u||(t.AlgoliaAnalyticsObject=r,t[r]||(t[r]=function(){t[r].queue||(t[r].queue=[]);for(var e=arguments.length,n=new Array(e),o=0;o<e;o++)n[o]=arguments[o];t[r].queue.push(n)}),t[r].version=ie,u=t[r],function(e){var t="[Autocomplete]: Could not load search-insights.js. Please load it manually following https://alg.li/insights-autocomplete";try{var r=e.document.createElement("script");r.async=!0,r.src=ae,r.onerror=function(){console.error(t)},document.body.appendChild(r)}catch(e){console.error(t)}}(t))})),!u)return{};n&&u("init",ne({partial:!0},n));var l=G(u),s={current:[]},f=A((function(e){var t=e.state;if(t.isOpen){var r=t.collections.reduce((function(e,t){return[].concat(ee(e),ee(t.items))}),[]).filter($);N(s.current.map((function(e){return e.objectID})),r.map((function(e){return e.objectID})))||(s.current=r,r.length>0&&ce({onItemsChange:o,items:r,insights:l,state:t}))}}),0);return{name:"aa.algoliaInsightsPlugin",subscribe:function(e){var t=e.setContext,r=e.onSelect,n=e.onActive,o=!1;function s(e){t({algoliaInsightsPlugin:{__algoliaSearchParameters:ne(ne({},c?{clickAnalytics:!0}:{}),e?{userToken:se(e)}:{}),insights:l}})}u("addAlgoliaAgent","insights-plugin"),s(),u("onUserTokenChange",(function(e){o||s(e)})),u("getUserToken",null,(function(e,t){o||s(t)})),u("onAuthenticatedUserTokenChange",(function(e){e?(o=!0,s(e)):(o=!1,u("getUserToken",null,(function(e,t){return s(t)})))})),u("getAuthenticatedUserToken",null,(function(e,t){t&&(o=!0,s(t))})),r((function(e){var t=e.item,r=e.state,n=e.event,o=e.source;$(t)&&i({state:r,event:n,insights:l,item:t,insightsEvents:[ne({eventName:"Item Selected"},q({item:t,items:o.getItems().filter($)}))]})})),n((function(e){var t=e.item,r=e.source,n=e.state,o=e.event;$(t)&&a({state:n,event:o,insights:l,item:t,insightsEvents:[ne({eventName:"Item Active"},q({item:t,items:r.getItems().filter($)}))]})}))},onStateChange:function(e){var t=e.state;f({state:t})},__autocomplete_pluginOptions:e}}function le(){var e,t=arguments.length>1?arguments[1]:void 0;return[].concat(ee(arguments.length>0&&void 0!==arguments[0]?arguments[0]:[]),["autocomplete-internal"],ee(null!==(e=t.algoliaInsightsPlugin)&&void 0!==e&&e.__automaticInsights?["autocomplete-automatic"]:[]))}function se(e){return"number"==typeof e?e.toString():e}function fe(e,t){var r=t;return{then:function(t,n){return fe(e.then(pe(t,r,e),pe(n,r,e)),r)},catch:function(t){return fe(e.catch(pe(t,r,e)),r)},finally:function(t){return t&&r.onCancelList.push(t),fe(e.finally(pe(t&&function(){return r.onCancelList=[],t()},r,e)),r)},cancel:function(){r.isCanceled=!0;var e=r.onCancelList;r.onCancelList=[],e.forEach((function(e){e()}))},isCanceled:function(){return!0===r.isCanceled}}}function me(e){return fe(e,{isCanceled:!1,onCancelList:[]})}function pe(e,t,r){return e?function(r){return t.isCanceled?r:e(r)}:r}function ve(e,t,r,n){if(!r)return null;if(e<0&&(null===t||null!==n&&0===t))return r+e;var o=(null===t?-1:t)+e;return o<=-1||o>=r?null===n?null:0:o}function de(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function he(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?de(Object(r),!0).forEach((function(t){ye(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):de(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function ye(e,t,r){return(t=function(e){var t=function(e,t){if("object"!==ge(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==ge(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===ge(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ge(e){return ge="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ge(e)}function be(e){var t=function(e){var t=e.collections.map((function(e){return e.items.length})).reduce((function(e,t,r){var n=(e[r-1]||0)+t;return e.push(n),e}),[]).reduce((function(t,r){return r<=e.activeItemId?t+1:t}),0);return e.collections[t]}(e);if(!t)return null;var r=t.items[function(e){for(var t=e.state,r=e.collection,n=!1,o=0,i=0;!1===n;){var a=t.collections[o];if(a===r){n=!0;break}i+=a.items.length,o++}return t.activeItemId-i}({state:e,collection:t})],n=t.source;return{item:r,itemInputValue:n.getItemInputValue({item:r,state:e}),itemUrl:n.getItemUrl({item:r,state:e}),source:n}}function Se(e,t,r){return[e,null==r?void 0:r.sourceId,t].filter(Boolean).join("-").replace(/\s/g,"")}var Oe=/((gt|sm)-|galaxy nexus)|samsung[- ]|samsungbrowser/i;function we(e){return e.nativeEvent||e}function Ee(e){return Ee="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ee(e)}function je(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Pe(e,t,r){return(t=function(e){var t=function(e,t){if("object"!==Ee(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==Ee(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Ee(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Ie(e,t,r){var n,o=t.initialState;return{getState:function(){return o},dispatch:function(n,i){var a=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?je(Object(r),!0).forEach((function(t){Pe(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):je(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}({},o);o=e(o,{type:n,props:t,payload:i}),r({state:o,prevState:a})},pendingRequests:(n=[],{add:function(e){return n.push(e),e.finally((function(){n=n.filter((function(t){return t!==e}))}))},cancelAll:function(){n.forEach((function(e){return e.cancel()}))},isEmpty:function(){return 0===n.length}})}}function De(e){return De="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},De(e)}function ke(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Ae(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ke(Object(r),!0).forEach((function(t){_e(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ke(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function _e(e,t,r){return(t=function(e){var t=function(e,t){if("object"!==De(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==De(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===De(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function xe(e){return xe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},xe(e)}function Ce(e){return function(e){if(Array.isArray(e))return Te(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return Te(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Te(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Te(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Ne(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Le(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Ne(Object(r),!0).forEach((function(t){Re(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ne(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Re(e,t,r){return(t=function(e){var t=function(e,t){if("object"!==xe(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==xe(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===xe(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function qe(e,t){var r,n="undefined"!=typeof window?window:{},o=e.plugins||[];return Le(Le({debug:!1,openOnFocus:!1,enterKeyHint:void 0,ignoreCompositionEvents:!1,placeholder:"",autoFocus:!1,defaultActiveItemId:null,stallThreshold:300,insights:void 0,environment:n,shouldPanelOpen:function(e){return C(e.state)>0},reshape:function(e){return e.sources}},e),{},{id:null!==(r=e.id)&&void 0!==r?r:"autocomplete-".concat(x++),plugins:o,initialState:Le({activeItemId:null,query:"",completion:null,collections:[],isOpen:!1,status:"idle",context:{}},e.initialState),onStateChange:function(t){var r;null===(r=e.onStateChange)||void 0===r||r.call(e,t),o.forEach((function(e){var r;return null===(r=e.onStateChange)||void 0===r?void 0:r.call(e,t)}))},onSubmit:function(t){var r;null===(r=e.onSubmit)||void 0===r||r.call(e,t),o.forEach((function(e){var r;return null===(r=e.onSubmit)||void 0===r?void 0:r.call(e,t)}))},onReset:function(t){var r;null===(r=e.onReset)||void 0===r||r.call(e,t),o.forEach((function(e){var r;return null===(r=e.onReset)||void 0===r?void 0:r.call(e,t)}))},getSources:function(r){return Promise.all([].concat(Ce(o.map((function(e){return e.getSources}))),[e.getSources]).filter(Boolean).map((function(e){return function(e,t){var r=[];return Promise.resolve(e(t)).then((function(e){return Promise.all(e.filter((function(e){return Boolean(e)})).map((function(e){if(e.sourceId,r.includes(e.sourceId))throw new Error("[Autocomplete] The `sourceId` ".concat(JSON.stringify(e.sourceId)," is not unique."));r.push(e.sourceId);var t={getItemInputValue:function(e){return e.state.query},getItemUrl:function(){},onSelect:function(e){(0,e.setIsOpen)(!1)},onActive:L,onResolve:L};Object.keys(t).forEach((function(e){t[e].__default=!0}));var n=he(he({},t),e);return Promise.resolve(n)})))}))}(e,r)}))).then((function(e){return _(e)})).then((function(e){return e.map((function(e){return Le(Le({},e),{},{onSelect:function(r){e.onSelect(r),t.forEach((function(e){var t;return null===(t=e.onSelect)||void 0===t?void 0:t.call(e,r)}))},onActive:function(r){e.onActive(r),t.forEach((function(e){var t;return null===(t=e.onActive)||void 0===t?void 0:t.call(e,r)}))},onResolve:function(r){e.onResolve(r),t.forEach((function(e){var t;return null===(t=e.onResolve)||void 0===t?void 0:t.call(e,r)}))}})}))}))},navigator:Le({navigate:function(e){var t=e.itemUrl;n.location.assign(t)},navigateNewTab:function(e){var t=e.itemUrl,r=n.open(t,"_blank","noopener");null==r||r.focus()},navigateNewWindow:function(e){var t=e.itemUrl;n.open(t,"_blank","noopener")}},e.navigator)})}function Me(e){return Me="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Me(e)}function He(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Be(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?He(Object(r),!0).forEach((function(t){Fe(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):He(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Fe(e,t,r){return(t=function(e){var t=function(e,t){if("object"!==Me(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==Me(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Me(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Ue(e){return Ue="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ue(e)}function Ke(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Ve(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Ke(Object(r),!0).forEach((function(t){ze(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ke(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function ze(e,t,r){return(t=function(e){var t=function(e,t){if("object"!==Ue(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==Ue(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Ue(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Je(e){return function(e){if(Array.isArray(e))return Qe(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return Qe(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Qe(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Qe(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function We(e){return Boolean(e.execute)}function Ze(e,t,r){if(o=e,Boolean(null==o?void 0:o.execute)){var n="algolia"===e.requesterId?Object.assign.apply(Object,[{}].concat(Je(Object.keys(r.context).map((function(e){var t;return null===(t=r.context[e])||void 0===t?void 0:t.__algoliaSearchParameters}))))):{};return Ve(Ve({},e),{},{requests:e.queries.map((function(r){return{query:"algolia"===e.requesterId?Ve(Ve({},r),{},{params:Ve(Ve({},n),r.params)}):r,sourceId:t,transformResponse:e.transformResponse}}))})}var o;return{items:e,sourceId:t}}function Ge(e){var t=e.reduce((function(e,t){if(!We(t))return e.push(t),e;var r=t.searchClient,n=t.execute,o=t.requesterId,i=t.requests,a=e.find((function(e){return We(t)&&We(e)&&e.searchClient===r&&Boolean(o)&&e.requesterId===o}));if(a){var c;(c=a.items).push.apply(c,Je(i))}else{var u={execute:n,requesterId:o,items:i,searchClient:r};e.push(u)}return e}),[]).map((function(e){if(!We(e))return Promise.resolve(e);var t=e,r=t.execute,n=t.items;return r({searchClient:t.searchClient,requests:n})}));return Promise.all(t).then((function(e){return _(e)}))}function Ye(e,t,r){return t.map((function(t){var n,o=e.filter((function(e){return e.sourceId===t.sourceId})),i=o.map((function(e){return e.items})),a=o[0].transformResponse,c=a?a({results:n=i,hits:n.map((function(e){return e.hits})).filter(Boolean),facetHits:n.map((function(e){var t;return null===(t=e.facetHits)||void 0===t?void 0:t.map((function(e){return{label:e.value,count:e.count,_highlightResult:{label:{value:e.highlighted}}}}))})).filter(Boolean)}):i;return t.onResolve({source:t,results:i,items:c,state:r.getState()}),c.every(Boolean),'The `getItems` function from source "'.concat(t.sourceId,'" must return an array of items but returned ').concat(JSON.stringify(void 0),".\n\nDid you forget to return items?\n\nSee: https://www.algolia.com/doc/ui-libraries/autocomplete/core-concepts/sources/#param-getitems"),{source:t,items:c}}))}function $e(e){return $e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},$e(e)}var Xe=["event","nextState","props","query","refresh","store"];function et(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function tt(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?et(Object(r),!0).forEach((function(t){rt(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):et(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function rt(e,t,r){return(t=function(e){var t=function(e,t){if("object"!==$e(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==$e(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===$e(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function nt(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r,n,o={},i=Object.keys(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}var ot,it,at,ct=null,ut=(ot=-1,it=-1,at=void 0,function(e){var t=++ot;return Promise.resolve(e).then((function(e){return at&&t<it?at:(it=t,at=e,e)}))});function lt(e){var t=e.event,r=e.nextState,n=void 0===r?{}:r,o=e.props,i=e.query,a=e.refresh,c=e.store,u=nt(e,Xe);ct&&o.environment.clearTimeout(ct);var l=u.setCollections,s=u.setIsOpen,f=u.setQuery,m=u.setActiveItemId,p=u.setStatus,v=u.setContext;if(f(i),m(o.defaultActiveItemId),!i&&!1===o.openOnFocus){var d,h=c.getState().collections.map((function(e){return tt(tt({},e),{},{items:[]})}));p("idle"),l(h),s(null!==(d=n.isOpen)&&void 0!==d?d:o.shouldPanelOpen({state:c.getState()}));var y=me(ut(h).then((function(){return Promise.resolve()})));return c.pendingRequests.add(y)}p("loading"),ct=o.environment.setTimeout((function(){p("stalled")}),o.stallThreshold);var g=me(ut(o.getSources(tt({query:i,refresh:a,state:c.getState()},u)).then((function(e){return Promise.all(e.map((function(e){return Promise.resolve(e.getItems(tt({query:i,refresh:a,state:c.getState()},u))).then((function(t){return Ze(t,e.sourceId,c.getState())}))}))).then(Ge).then((function(t){var r,n=t.some((function(e){return function(e){return!Array.isArray(e)&&Boolean(null==e?void 0:e._automaticInsights)}(e.items)}));n&&v({algoliaInsightsPlugin:tt(tt({},(null===(r=c.getState().context)||void 0===r?void 0:r.algoliaInsightsPlugin)||{}),{},{__automaticInsights:n})});return Ye(t,e,c)})).then((function(e){return function(e){var t=e.collections,r=e.props,n=e.state,o=t.reduce((function(e,t){return Be(Be({},e),{},Fe({},t.source.sourceId,Be(Be({},t.source),{},{getItems:function(){return _(t.items)}})))}),{}),i=r.plugins.reduce((function(e,t){return t.reshape?t.reshape(e):e}),{sourcesBySourceId:o,state:n}).sourcesBySourceId;return _(r.reshape({sourcesBySourceId:i,sources:Object.values(i),state:n})).filter(Boolean).map((function(e){return{source:e,items:e.getItems()}}))}({collections:e,props:o,state:c.getState()})}))})))).then((function(e){var r;p("idle"),l(e);var f=o.shouldPanelOpen({state:c.getState()});s(null!==(r=n.isOpen)&&void 0!==r?r:o.openOnFocus&&!i&&f||f);var m=be(c.getState());if(null!==c.getState().activeItemId&&m){var v=m.item,d=m.itemInputValue,h=m.itemUrl,y=m.source;y.onActive(tt({event:t,item:v,itemInputValue:d,itemUrl:h,refresh:a,source:y,state:c.getState()},u))}})).finally((function(){p("idle"),ct&&o.environment.clearTimeout(ct)}));return c.pendingRequests.add(g)}function st(e){return st="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},st(e)}var ft=["event","props","refresh","store"];function mt(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function pt(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?mt(Object(r),!0).forEach((function(t){vt(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):mt(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function vt(e,t,r){return(t=function(e){var t=function(e,t){if("object"!==st(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==st(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===st(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function dt(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r,n,o={},i=Object.keys(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}function ht(e){return ht="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ht(e)}var yt=["props","refresh","store"],gt=["inputElement","formElement","panelElement"],bt=["inputElement"],St=["inputElement","maxLength"],Ot=["source"],wt=["item","source"];function Et(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function jt(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Et(Object(r),!0).forEach((function(t){Pt(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Et(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Pt(e,t,r){return(t=function(e){var t=function(e,t){if("object"!==ht(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==ht(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===ht(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function It(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r,n,o={},i=Object.keys(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}function Dt(e){var t=e.props,r=e.refresh,n=e.store,o=It(e,yt);return{getEnvironmentProps:function(e){var r=e.inputElement,o=e.formElement,i=e.panelElement;function a(e){!n.getState().isOpen&&n.pendingRequests.isEmpty()||e.target===r||!1===[o,i].some((function(t){return r=t,n=e.target,r===n||r.contains(n);var r,n}))&&(n.dispatch("blur",null),t.debug||n.pendingRequests.cancelAll())}return jt({onTouchStart:a,onMouseDown:a,onTouchMove:function(e){!1!==n.getState().isOpen&&r===t.environment.document.activeElement&&e.target!==r&&r.blur()}},It(e,gt))},getRootProps:function(e){return jt({role:"combobox","aria-expanded":n.getState().isOpen,"aria-haspopup":"listbox","aria-controls":n.getState().isOpen?n.getState().collections.map((function(e){var r=e.source;return Se(t.id,"list",r)})).join(" "):void 0,"aria-labelledby":Se(t.id,"label")},e)},getFormProps:function(e){return e.inputElement,jt({action:"",noValidate:!0,role:"search",onSubmit:function(i){var a;i.preventDefault(),t.onSubmit(jt({event:i,refresh:r,state:n.getState()},o)),n.dispatch("submit",null),null===(a=e.inputElement)||void 0===a||a.blur()},onReset:function(i){var a;i.preventDefault(),t.onReset(jt({event:i,refresh:r,state:n.getState()},o)),n.dispatch("reset",null),null===(a=e.inputElement)||void 0===a||a.focus()}},It(e,bt))},getLabelProps:function(e){return jt({htmlFor:Se(t.id,"input"),id:Se(t.id,"label")},e)},getInputProps:function(e){var i;function a(e){(t.openOnFocus||Boolean(n.getState().query))&&lt(jt({event:e,props:t,query:n.getState().completion||n.getState().query,refresh:r,store:n},o)),n.dispatch("focus",null)}var c=e||{};c.inputElement;var u=c.maxLength,l=void 0===u?512:u,s=It(c,St),f=be(n.getState()),m=function(e){return Boolean(e&&e.match(Oe))}((null===(i=t.environment.navigator)||void 0===i?void 0:i.userAgent)||""),p=t.enterKeyHint||(null!=f&&f.itemUrl&&!m?"go":"search");return jt({"aria-autocomplete":"both","aria-activedescendant":n.getState().isOpen&&null!==n.getState().activeItemId?Se(t.id,"item-".concat(n.getState().activeItemId),null==f?void 0:f.source):void 0,"aria-controls":n.getState().isOpen?n.getState().collections.map((function(e){var r=e.source;return Se(t.id,"list",r)})).join(" "):void 0,"aria-labelledby":Se(t.id,"label"),value:n.getState().completion||n.getState().query,id:Se(t.id,"input"),autoComplete:"off",autoCorrect:"off",autoCapitalize:"off",enterKeyHint:p,spellCheck:"false",autoFocus:t.autoFocus,placeholder:t.placeholder,maxLength:l,type:"search",onChange:function(e){var i=e.currentTarget.value;t.ignoreCompositionEvents&&we(e).isComposing?o.setQuery(i):lt(jt({event:e,props:t,query:i.slice(0,l),refresh:r,store:n},o))},onCompositionEnd:function(e){lt(jt({event:e,props:t,query:e.currentTarget.value.slice(0,l),refresh:r,store:n},o))},onKeyDown:function(e){we(e).isComposing||function(e){var t=e.event,r=e.props,n=e.refresh,o=e.store,i=dt(e,ft);if("ArrowUp"===t.key||"ArrowDown"===t.key){var a=function(){var e=be(o.getState()),t=r.environment.document.getElementById(Se(r.id,"item-".concat(o.getState().activeItemId),null==e?void 0:e.source));t&&(t.scrollIntoViewIfNeeded?t.scrollIntoViewIfNeeded(!1):t.scrollIntoView(!1))},c=function(){var e=be(o.getState());if(null!==o.getState().activeItemId&&e){var r=e.item,a=e.itemInputValue,c=e.itemUrl,u=e.source;u.onActive(pt({event:t,item:r,itemInputValue:a,itemUrl:c,refresh:n,source:u,state:o.getState()},i))}};t.preventDefault(),!1===o.getState().isOpen&&(r.openOnFocus||Boolean(o.getState().query))?lt(pt({event:t,props:r,query:o.getState().query,refresh:n,store:o},i)).then((function(){o.dispatch(t.key,{nextActiveItemId:r.defaultActiveItemId}),c(),setTimeout(a,0)})):(o.dispatch(t.key,{}),c(),a())}else if("Escape"===t.key)t.preventDefault(),o.dispatch(t.key,null),o.pendingRequests.cancelAll();else if("Tab"===t.key)o.dispatch("blur",null),o.pendingRequests.cancelAll();else if("Enter"===t.key){if(null===o.getState().activeItemId||o.getState().collections.every((function(e){return 0===e.items.length})))return void(r.debug||o.pendingRequests.cancelAll());t.preventDefault();var u=be(o.getState()),l=u.item,s=u.itemInputValue,f=u.itemUrl,m=u.source;if(t.metaKey||t.ctrlKey)void 0!==f&&(m.onSelect(pt({event:t,item:l,itemInputValue:s,itemUrl:f,refresh:n,source:m,state:o.getState()},i)),r.navigator.navigateNewTab({itemUrl:f,item:l,state:o.getState()}));else if(t.shiftKey)void 0!==f&&(m.onSelect(pt({event:t,item:l,itemInputValue:s,itemUrl:f,refresh:n,source:m,state:o.getState()},i)),r.navigator.navigateNewWindow({itemUrl:f,item:l,state:o.getState()}));else if(t.altKey);else{if(void 0!==f)return m.onSelect(pt({event:t,item:l,itemInputValue:s,itemUrl:f,refresh:n,source:m,state:o.getState()},i)),void r.navigator.navigate({itemUrl:f,item:l,state:o.getState()});lt(pt({event:t,nextState:{isOpen:!1},props:r,query:s,refresh:n,store:o},i)).then((function(){m.onSelect(pt({event:t,item:l,itemInputValue:s,itemUrl:f,refresh:n,source:m,state:o.getState()},i))}))}}}(jt({event:e,props:t,refresh:r,store:n},o))},onFocus:a,onBlur:L,onClick:function(r){e.inputElement!==t.environment.document.activeElement||n.getState().isOpen||a(r)}},s)},getPanelProps:function(e){return jt({onMouseDown:function(e){e.preventDefault()},onMouseLeave:function(){n.dispatch("mouseleave",null)}},e)},getListProps:function(e){var r=e||{},n=r.source,o=It(r,Ot);return jt({role:"listbox","aria-labelledby":Se(t.id,"label"),id:Se(t.id,"list",n)},o)},getItemProps:function(e){var i=e.item,a=e.source,c=It(e,wt);return jt({id:Se(t.id,"item-".concat(i.__autocomplete_id),a),role:"option","aria-selected":n.getState().activeItemId===i.__autocomplete_id,onMouseMove:function(e){if(i.__autocomplete_id!==n.getState().activeItemId){n.dispatch("mousemove",i.__autocomplete_id);var t=be(n.getState());if(null!==n.getState().activeItemId&&t){var a=t.item,c=t.itemInputValue,u=t.itemUrl,l=t.source;l.onActive(jt({event:e,item:a,itemInputValue:c,itemUrl:u,refresh:r,source:l,state:n.getState()},o))}}},onMouseDown:function(e){e.preventDefault()},onClick:function(e){var c=a.getItemInputValue({item:i,state:n.getState()}),u=a.getItemUrl({item:i,state:n.getState()});(u?Promise.resolve():lt(jt({event:e,nextState:{isOpen:!1},props:t,query:c,refresh:r,store:n},o))).then((function(){a.onSelect(jt({event:e,item:i,itemInputValue:c,itemUrl:u,refresh:r,source:a,state:n.getState()},o))}))}},c)}}}function kt(e){return kt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},kt(e)}function At(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function _t(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?At(Object(r),!0).forEach((function(t){xt(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):At(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function xt(e,t,r){return(t=function(e){var t=function(e,t){if("object"!==kt(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==kt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===kt(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Ct(e){var t,r,n,o,i=e.plugins,a=e.options,c=null===(t=((null===(r=a.__autocomplete_metadata)||void 0===r?void 0:r.userAgents)||[])[0])||void 0===t?void 0:t.segment,u=c?xt({},c,Object.keys((null===(n=a.__autocomplete_metadata)||void 0===n?void 0:n.options)||{})):{};return{plugins:i.map((function(e){return{name:e.name,options:Object.keys(e.__autocomplete_pluginOptions||[])}})),options:_t({"autocomplete-core":Object.keys(a)},u),ua:R.concat((null===(o=a.__autocomplete_metadata)||void 0===o?void 0:o.userAgents)||[])}}function Tt(e){var t,r=e.state;return!1===r.isOpen||null===r.activeItemId?null:(null===(t=be(r))||void 0===t?void 0:t.itemInputValue)||null}function Nt(e){return Nt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Nt(e)}function Lt(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Rt(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Lt(Object(r),!0).forEach((function(t){qt(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Lt(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function qt(e,t,r){return(t=function(e){var t=function(e,t){if("object"!==Nt(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==Nt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Nt(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Mt=function(e,t){switch(t.type){case"setActiveItemId":case"mousemove":return Rt(Rt({},e),{},{activeItemId:t.payload});case"setQuery":return Rt(Rt({},e),{},{query:t.payload,completion:null});case"setCollections":return Rt(Rt({},e),{},{collections:t.payload});case"setIsOpen":return Rt(Rt({},e),{},{isOpen:t.payload});case"setStatus":return Rt(Rt({},e),{},{status:t.payload});case"setContext":return Rt(Rt({},e),{},{context:Rt(Rt({},e.context),t.payload)});case"ArrowDown":var r=Rt(Rt({},e),{},{activeItemId:t.payload.hasOwnProperty("nextActiveItemId")?t.payload.nextActiveItemId:ve(1,e.activeItemId,C(e),t.props.defaultActiveItemId)});return Rt(Rt({},r),{},{completion:Tt({state:r})});case"ArrowUp":var n=Rt(Rt({},e),{},{activeItemId:ve(-1,e.activeItemId,C(e),t.props.defaultActiveItemId)});return Rt(Rt({},n),{},{completion:Tt({state:n})});case"Escape":return e.isOpen?Rt(Rt({},e),{},{activeItemId:null,isOpen:!1,completion:null}):Rt(Rt({},e),{},{activeItemId:null,query:"",status:"idle",collections:[]});case"submit":return Rt(Rt({},e),{},{activeItemId:null,isOpen:!1,status:"idle"});case"reset":return Rt(Rt({},e),{},{activeItemId:!0===t.props.openOnFocus?t.props.defaultActiveItemId:null,status:"idle",completion:null,query:""});case"focus":return Rt(Rt({},e),{},{activeItemId:t.props.defaultActiveItemId,isOpen:(t.props.openOnFocus||Boolean(e.query))&&t.props.shouldPanelOpen({state:e})});case"blur":return t.props.debug?e:Rt(Rt({},e),{},{isOpen:!1,activeItemId:null});case"mouseleave":return Rt(Rt({},e),{},{activeItemId:t.props.defaultActiveItemId});default:return"The reducer action ".concat(JSON.stringify(t.type)," is not supported."),e}};function Ht(e){return Ht="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ht(e)}function Bt(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Ft(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Bt(Object(r),!0).forEach((function(t){Ut(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Bt(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Ut(e,t,r){return(t=function(e){var t=function(e,t){if("object"!==Ht(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==Ht(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Ht(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Kt(e){var t=[],r=qe(e,t),n=Ie(Mt,r,(function(e){var t,n,i=e.prevState,l=e.state;if(r.onStateChange(Ft({prevState:i,state:l,refresh:a,navigator:r.navigator},o)),!u()&&null!==(t=l.context)&&void 0!==t&&null!==(n=t.algoliaInsightsPlugin)&&void 0!==n&&n.__automaticInsights&&!1!==r.insights){var s=ue({__autocomplete_clickAnalytics:!1});r.plugins.push(s),c([s])}})),o=function(e){var t=e.store;return{setActiveItemId:function(e){t.dispatch("setActiveItemId",e)},setQuery:function(e){t.dispatch("setQuery",e)},setCollections:function(e){var r=0,n=e.map((function(e){return Ae(Ae({},e),{},{items:_(e.items).map((function(e){return Ae(Ae({},e),{},{__autocomplete_id:r++})}))})}));t.dispatch("setCollections",n)},setIsOpen:function(e){t.dispatch("setIsOpen",e)},setStatus:function(e){t.dispatch("setStatus",e)},setContext:function(e){t.dispatch("setContext",e)}}}({store:n}),i=Dt(Ft({props:r,refresh:a,store:n,navigator:r.navigator},o));function a(){return lt(Ft({event:new Event("input"),nextState:{isOpen:n.getState().isOpen},props:r,navigator:r.navigator,query:n.getState().query,refresh:a,store:n},o))}function c(e){e.forEach((function(e){var n;return null===(n=e.subscribe)||void 0===n?void 0:n.call(e,Ft(Ft({},o),{},{navigator:r.navigator,refresh:a,onSelect:function(e){t.push({onSelect:e})},onActive:function(e){t.push({onActive:e})},onResolve:function(e){t.push({onResolve:e})}}))}))}function u(){return r.plugins.some((function(e){return"aa.algoliaInsightsPlugin"===e.name}))}if(r.insights&&!u()){var l="boolean"==typeof r.insights?{}:r.insights;r.plugins.push(ue(l))}return c(r.plugins),function(e){var t,r,n=e.metadata,o=e.environment;if(null===(t=o.navigator)||void 0===t||null===(r=t.userAgent)||void 0===r?void 0:r.includes("Algolia Crawler")){var i=o.document.createElement("meta"),a=o.document.querySelector("head");i.name="algolia:metadata",setTimeout((function(){i.content=JSON.stringify(n),a.appendChild(i)}),0)}}({metadata:Ct({plugins:r.plugins,options:e}),environment:r.environment}),Ft(Ft({refresh:a,navigator:r.navigator},i),o)}function Vt(e){var r=e.translations,n=(void 0===r?{}:r).searchByText,o=void 0===n?"Search by":n;return t.createElement("a",{href:"https://www.algolia.com/ref/docsearch/?utm_source=".concat(window.location.hostname,"&utm_medium=referral&utm_content=powered_by&utm_campaign=docsearch"),target:"_blank",rel:"noopener noreferrer"},t.createElement("span",{className:"DocSearch-Label"},o),t.createElement("svg",{width:"77",height:"19","aria-label":"Algolia",role:"img",id:"Layer_1",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 2196.2 500"},t.createElement("defs",null,t.createElement("style",null,".cls-1,.cls-2{fill:#003dff;}.cls-2{fill-rule:evenodd;}")),t.createElement("path",{className:"cls-2",d:"M1070.38,275.3V5.91c0-3.63-3.24-6.39-6.82-5.83l-50.46,7.94c-2.87,.45-4.99,2.93-4.99,5.84l.17,273.22c0,12.92,0,92.7,95.97,95.49,3.33,.1,6.09-2.58,6.09-5.91v-40.78c0-2.96-2.19-5.51-5.12-5.84-34.85-4.01-34.85-47.57-34.85-54.72Z"}),t.createElement("rect",{className:"cls-1",x:"1845.88",y:"104.73",width:"62.58",height:"277.9",rx:"5.9",ry:"5.9"}),t.createElement("path",{className:"cls-2",d:"M1851.78,71.38h50.77c3.26,0,5.9-2.64,5.9-5.9V5.9c0-3.62-3.24-6.39-6.82-5.83l-50.77,7.95c-2.87,.45-4.99,2.92-4.99,5.83v51.62c0,3.26,2.64,5.9,5.9,5.9Z"}),t.createElement("path",{className:"cls-2",d:"M1764.03,275.3V5.91c0-3.63-3.24-6.39-6.82-5.83l-50.46,7.94c-2.87,.45-4.99,2.93-4.99,5.84l.17,273.22c0,12.92,0,92.7,95.97,95.49,3.33,.1,6.09-2.58,6.09-5.91v-40.78c0-2.96-2.19-5.51-5.12-5.84-34.85-4.01-34.85-47.57-34.85-54.72Z"}),t.createElement("path",{className:"cls-2",d:"M1631.95,142.72c-11.14-12.25-24.83-21.65-40.78-28.31-15.92-6.53-33.26-9.85-52.07-9.85-18.78,0-36.15,3.17-51.92,9.85-15.59,6.66-29.29,16.05-40.76,28.31-11.47,12.23-20.38,26.87-26.76,44.03-6.38,17.17-9.24,37.37-9.24,58.36,0,20.99,3.19,36.87,9.55,54.21,6.38,17.32,15.14,32.11,26.45,44.36,11.29,12.23,24.83,21.62,40.6,28.46,15.77,6.83,40.12,10.33,52.4,10.48,12.25,0,36.78-3.82,52.7-10.48,15.92-6.68,29.46-16.23,40.78-28.46,11.29-12.25,20.05-27.04,26.25-44.36,6.22-17.34,9.24-33.22,9.24-54.21,0-20.99-3.34-41.19-10.03-58.36-6.38-17.17-15.14-31.8-26.43-44.03Zm-44.43,163.75c-11.47,15.75-27.56,23.7-48.09,23.7-20.55,0-36.63-7.8-48.1-23.7-11.47-15.75-17.21-34.01-17.21-61.2,0-26.89,5.59-49.14,17.06-64.87,11.45-15.75,27.54-23.52,48.07-23.52,20.55,0,36.63,7.78,48.09,23.52,11.47,15.57,17.36,37.98,17.36,64.87,0,27.19-5.72,45.3-17.19,61.2Z"}),t.createElement("path",{className:"cls-2",d:"M894.42,104.73h-49.33c-48.36,0-90.91,25.48-115.75,64.1-14.52,22.58-22.99,49.63-22.99,78.73,0,44.89,20.13,84.92,51.59,111.1,2.93,2.6,6.05,4.98,9.31,7.14,12.86,8.49,28.11,13.47,44.52,13.47,1.23,0,2.46-.03,3.68-.09,.36-.02,.71-.05,1.07-.07,.87-.05,1.75-.11,2.62-.2,.34-.03,.68-.08,1.02-.12,.91-.1,1.82-.21,2.73-.34,.21-.03,.42-.07,.63-.1,32.89-5.07,61.56-30.82,70.9-62.81v57.83c0,3.26,2.64,5.9,5.9,5.9h50.42c3.26,0,5.9-2.64,5.9-5.9V110.63c0-3.26-2.64-5.9-5.9-5.9h-56.32Zm0,206.92c-12.2,10.16-27.97,13.98-44.84,15.12-.16,.01-.33,.03-.49,.04-1.12,.07-2.24,.1-3.36,.1-42.24,0-77.12-35.89-77.12-79.37,0-10.25,1.96-20.01,5.42-28.98,11.22-29.12,38.77-49.74,71.06-49.74h49.33v142.83Z"}),t.createElement("path",{className:"cls-2",d:"M2133.97,104.73h-49.33c-48.36,0-90.91,25.48-115.75,64.1-14.52,22.58-22.99,49.63-22.99,78.73,0,44.89,20.13,84.92,51.59,111.1,2.93,2.6,6.05,4.98,9.31,7.14,12.86,8.49,28.11,13.47,44.52,13.47,1.23,0,2.46-.03,3.68-.09,.36-.02,.71-.05,1.07-.07,.87-.05,1.75-.11,2.62-.2,.34-.03,.68-.08,1.02-.12,.91-.1,1.82-.21,2.73-.34,.21-.03,.42-.07,.63-.1,32.89-5.07,61.56-30.82,70.9-62.81v57.83c0,3.26,2.64,5.9,5.9,5.9h50.42c3.26,0,5.9-2.64,5.9-5.9V110.63c0-3.26-2.64-5.9-5.9-5.9h-56.32Zm0,206.92c-12.2,10.16-27.97,13.98-44.84,15.12-.16,.01-.33,.03-.49,.04-1.12,.07-2.24,.1-3.36,.1-42.24,0-77.12-35.89-77.12-79.37,0-10.25,1.96-20.01,5.42-28.98,11.22-29.12,38.77-49.74,71.06-49.74h49.33v142.83Z"}),t.createElement("path",{className:"cls-2",d:"M1314.05,104.73h-49.33c-48.36,0-90.91,25.48-115.75,64.1-11.79,18.34-19.6,39.64-22.11,62.59-.58,5.3-.88,10.68-.88,16.14s.31,11.15,.93,16.59c4.28,38.09,23.14,71.61,50.66,94.52,2.93,2.6,6.05,4.98,9.31,7.14,12.86,8.49,28.11,13.47,44.52,13.47h0c17.99,0,34.61-5.93,48.16-15.97,16.29-11.58,28.88-28.54,34.48-47.75v50.26h-.11v11.08c0,21.84-5.71,38.27-17.34,49.36-11.61,11.08-31.04,16.63-58.25,16.63-11.12,0-28.79-.59-46.6-2.41-2.83-.29-5.46,1.5-6.27,4.22l-12.78,43.11c-1.02,3.46,1.27,7.02,4.83,7.53,21.52,3.08,42.52,4.68,54.65,4.68,48.91,0,85.16-10.75,108.89-32.21,21.48-19.41,33.15-48.89,35.2-88.52V110.63c0-3.26-2.64-5.9-5.9-5.9h-56.32Zm0,64.1s.65,139.13,0,143.36c-12.08,9.77-27.11,13.59-43.49,14.7-.16,.01-.33,.03-.49,.04-1.12,.07-2.24,.1-3.36,.1-1.32,0-2.63-.03-3.94-.1-40.41-2.11-74.52-37.26-74.52-79.38,0-10.25,1.96-20.01,5.42-28.98,11.22-29.12,38.77-49.74,71.06-49.74h49.33Z"}),t.createElement("path",{className:"cls-1",d:"M249.83,0C113.3,0,2,110.09,.03,246.16c-2,138.19,110.12,252.7,248.33,253.5,42.68,.25,83.79-10.19,120.3-30.03,3.56-1.93,4.11-6.83,1.08-9.51l-23.38-20.72c-4.75-4.21-11.51-5.4-17.36-2.92-25.48,10.84-53.17,16.38-81.71,16.03-111.68-1.37-201.91-94.29-200.13-205.96,1.76-110.26,92-199.41,202.67-199.41h202.69V407.41l-115-102.18c-3.72-3.31-9.42-2.66-12.42,1.31-18.46,24.44-48.53,39.64-81.93,37.34-46.33-3.2-83.87-40.5-87.34-86.81-4.15-55.24,39.63-101.52,94-101.52,49.18,0,89.68,37.85,93.91,85.95,.38,4.28,2.31,8.27,5.52,11.12l29.95,26.55c3.4,3.01,8.79,1.17,9.63-3.3,2.16-11.55,2.92-23.58,2.07-35.92-4.82-70.34-61.8-126.93-132.17-131.26-80.68-4.97-148.13,58.14-150.27,137.25-2.09,77.1,61.08,143.56,138.19,145.26,32.19,.71,62.03-9.41,86.14-26.95l150.26,133.2c6.44,5.71,16.61,1.14,16.61-7.47V9.48C499.66,4.25,495.42,0,490.18,0H249.83Z"})))}function zt(e){return t.createElement("svg",{width:"15",height:"15","aria-label":e.ariaLabel,role:"img"},t.createElement("g",{fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"1.2"},e.children))}function Jt(e){var r=e.translations,n=void 0===r?{}:r,o=n.selectText,i=void 0===o?"to select":o,a=n.selectKeyAriaLabel,c=void 0===a?"Enter key":a,u=n.navigateText,l=void 0===u?"to navigate":u,s=n.navigateUpKeyAriaLabel,f=void 0===s?"Arrow up":s,m=n.navigateDownKeyAriaLabel,p=void 0===m?"Arrow down":m,v=n.closeText,d=void 0===v?"to close":v,h=n.closeKeyAriaLabel,y=void 0===h?"Escape key":h,g=n.searchByText,b=void 0===g?"Search by":g;return t.createElement(t.Fragment,null,t.createElement("div",{className:"DocSearch-Logo"},t.createElement(Vt,{translations:{searchByText:b}})),t.createElement("ul",{className:"DocSearch-Commands"},t.createElement("li",null,t.createElement("kbd",{className:"DocSearch-Commands-Key"},t.createElement(zt,{ariaLabel:c},t.createElement("path",{d:"M12 3.53088v3c0 1-1 2-2 2H4M7 11.53088l-3-3 3-3"}))),t.createElement("span",{className:"DocSearch-Label"},i)),t.createElement("li",null,t.createElement("kbd",{className:"DocSearch-Commands-Key"},t.createElement(zt,{ariaLabel:p},t.createElement("path",{d:"M7.5 3.5v8M10.5 8.5l-3 3-3-3"}))),t.createElement("kbd",{className:"DocSearch-Commands-Key"},t.createElement(zt,{ariaLabel:f},t.createElement("path",{d:"M7.5 11.5v-8M10.5 6.5l-3-3-3 3"}))),t.createElement("span",{className:"DocSearch-Label"},l)),t.createElement("li",null,t.createElement("kbd",{className:"DocSearch-Commands-Key"},t.createElement(zt,{ariaLabel:y},t.createElement("path",{d:"M13.6167 8.936c-.1065.3583-.6883.962-1.4875.962-.7993 0-1.653-.9165-1.653-2.1258v-.5678c0-1.2548.7896-2.1016 1.653-2.1016.8634 0 1.3601.4778 1.4875 1.0724M9 6c-.1352-.4735-.7506-.9219-1.46-.8972-.7092.0246-1.344.57-1.344 1.2166s.4198.8812 1.3445.9805C8.465 7.3992 8.968 7.9337 9 8.5c.032.5663-.454 1.398-1.4595 1.398C6.6593 9.898 6 9 5.963 8.4851m-1.4748.5368c-.2635.5941-.8099.876-1.5443.876s-1.7073-.6248-1.7073-2.204v-.4603c0-1.0416.721-2.131 1.7073-2.131.9864 0 1.6425 1.031 1.5443 2.2492h-2.956"}))),t.createElement("span",{className:"DocSearch-Label"},d))))}function Qt(e){var r=e.hit,n=e.children;return t.createElement("a",{href:r.url},n)}function Wt(){return t.createElement("svg",{viewBox:"0 0 38 38",stroke:"currentColor",strokeOpacity:".5"},t.createElement("g",{fill:"none",fillRule:"evenodd"},t.createElement("g",{transform:"translate(1 1)",strokeWidth:"2"},t.createElement("circle",{strokeOpacity:".3",cx:"18",cy:"18",r:"18"}),t.createElement("path",{d:"M36 18c0-9.94-8.06-18-18-18"},t.createElement("animateTransform",{attributeName:"transform",type:"rotate",from:"0 18 18",to:"360 18 18",dur:"1s",repeatCount:"indefinite"})))))}function Zt(){return t.createElement("svg",{width:"20",height:"20",viewBox:"0 0 20 20"},t.createElement("g",{stroke:"currentColor",fill:"none",fillRule:"evenodd",strokeLinecap:"round",strokeLinejoin:"round"},t.createElement("path",{d:"M3.18 6.6a8.23 8.23 0 1112.93 9.94h0a8.23 8.23 0 01-11.63 0"}),t.createElement("path",{d:"M6.44 7.25H2.55V3.36M10.45 6v5.6M10.45 11.6L13 13"})))}function Gt(){return t.createElement("svg",{width:"20",height:"20",viewBox:"0 0 20 20"},t.createElement("path",{d:"M10 10l5.09-5.09L10 10l5.09 5.09L10 10zm0 0L4.91 4.91 10 10l-5.09 5.09L10 10z",stroke:"currentColor",fill:"none",fillRule:"evenodd",strokeLinecap:"round",strokeLinejoin:"round"}))}function Yt(){return t.createElement("svg",{className:"DocSearch-Hit-Select-Icon",width:"20",height:"20",viewBox:"0 0 20 20"},t.createElement("g",{stroke:"currentColor",fill:"none",fillRule:"evenodd",strokeLinecap:"round",strokeLinejoin:"round"},t.createElement("path",{d:"M18 3v4c0 2-2 4-4 4H2"}),t.createElement("path",{d:"M8 17l-6-6 6-6"})))}var $t=function(){return t.createElement("svg",{width:"20",height:"20",viewBox:"0 0 20 20"},t.createElement("path",{d:"M17 6v12c0 .52-.2 1-1 1H4c-.7 0-1-.33-1-1V2c0-.55.42-1 1-1h8l5 5zM14 8h-3.13c-.51 0-.87-.34-.87-.87V4",stroke:"currentColor",fill:"none",fillRule:"evenodd",strokeLinejoin:"round"}))};function Xt(e){switch(e.type){case"lvl1":return t.createElement($t,null);case"content":return t.createElement(tr,null);default:return t.createElement(er,null)}}function er(){return t.createElement("svg",{width:"20",height:"20",viewBox:"0 0 20 20"},t.createElement("path",{d:"M13 13h4-4V8H7v5h6v4-4H7V8H3h4V3v5h6V3v5h4-4v5zm-6 0v4-4H3h4z",stroke:"currentColor",fill:"none",fillRule:"evenodd",strokeLinecap:"round",strokeLinejoin:"round"}))}function tr(){return t.createElement("svg",{width:"20",height:"20",viewBox:"0 0 20 20"},t.createElement("path",{d:"M17 5H3h14zm0 5H3h14zm0 5H3h14z",stroke:"currentColor",fill:"none",fillRule:"evenodd",strokeLinejoin:"round"}))}function rr(){return t.createElement("svg",{width:"20",height:"20",viewBox:"0 0 20 20"},t.createElement("path",{d:"M10 14.2L5 17l1-5.6-4-4 5.5-.7 2.5-5 2.5 5 5.6.8-4 4 .9 5.5z",stroke:"currentColor",fill:"none",fillRule:"evenodd",strokeLinejoin:"round"}))}function nr(){return t.createElement("svg",{width:"40",height:"40",viewBox:"0 0 20 20",fill:"none",fillRule:"evenodd",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"},t.createElement("path",{d:"M19 4.8a16 16 0 00-2-1.2m-3.3-1.2A16 16 0 001.1 4.7M16.7 8a12 12 0 00-2.8-1.4M10 6a12 12 0 00-6.7 2M12.3 14.7a4 4 0 00-4.5 0M14.5 11.4A8 8 0 0010 10M3 16L18 2M10 18h0"}))}function or(){return t.createElement("svg",{width:"40",height:"40",viewBox:"0 0 20 20",fill:"none",fillRule:"evenodd",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"},t.createElement("path",{d:"M15.5 4.8c2 3 1.7 7-1 9.7h0l4.3 4.3-4.3-4.3a7.8 7.8 0 01-9.8 1m-2.2-2.2A7.8 7.8 0 0113.2 2.4M2 18L18 2"}))}function ir(e){var r=e.translations,n=void 0===r?{}:r,o=n.titleText,i=void 0===o?"Unable to fetch results":o,a=n.helpText,c=void 0===a?"You might want to check your network connection.":a;return t.createElement("div",{className:"DocSearch-ErrorScreen"},t.createElement("div",{className:"DocSearch-Screen-Icon"},t.createElement(nr,null)),t.createElement("p",{className:"DocSearch-Title"},i),t.createElement("p",{className:"DocSearch-Help"},c))}var ar=["translations"];function cr(e){var r=e.translations,n=void 0===r?{}:r,o=h(e,ar),i=n.noResultsText,a=void 0===i?"No results for":i,c=n.suggestedQueryText,u=void 0===c?"Try searching for":c,l=n.reportMissingResultsText,s=void 0===l?"Believe this query should return results?":l,f=n.reportMissingResultsLinkText,m=void 0===f?"Let us know.":f,p=o.state.context.searchSuggestions;return t.createElement("div",{className:"DocSearch-NoResults"},t.createElement("div",{className:"DocSearch-Screen-Icon"},t.createElement(or,null)),t.createElement("p",{className:"DocSearch-Title"},a,' "',t.createElement("strong",null,o.state.query),'"'),p&&p.length>0&&t.createElement("div",{className:"DocSearch-NoResults-Prefill-List"},t.createElement("p",{className:"DocSearch-Help"},u,":"),t.createElement("ul",null,p.slice(0,3).reduce((function(e,r){return[].concat(S(e),[t.createElement("li",{key:r},t.createElement("button",{className:"DocSearch-Prefill",key:r,type:"button",onClick:function(){o.setQuery(r.toLowerCase()+" "),o.refresh(),o.inputRef.current.focus()}},r))])}),[]))),o.getMissingResultsUrl&&t.createElement("p",{className:"DocSearch-Help"},"".concat(s," "),t.createElement("a",{href:o.getMissingResultsUrl({query:o.state.query}),target:"_blank",rel:"noopener noreferrer"},m)))}var ur=["hit","attribute","tagName"];function lr(e,t){return t.split(".").reduce((function(e,t){return null!=e&&e[t]?e[t]:null}),e)}function sr(e){var r=e.hit,n=e.attribute,o=e.tagName,i=void 0===o?"span":o,a=h(e,ur);return t.createElement(i,d(d({},a),{},{dangerouslySetInnerHTML:{__html:lr(r,"_snippetResult.".concat(n,".value"))||lr(r,n)}}))}function fr(e){return e.collection&&0!==e.collection.items.length?t.createElement("section",{className:"DocSearch-Hits"},t.createElement("div",{className:"DocSearch-Hit-source"},e.title),t.createElement("ul",e.getListProps(),e.collection.items.map((function(r,n){return t.createElement(mr,s({key:[e.title,r.objectID].join(":"),item:r,index:n},e))})))):null}function mr(e){var r=e.item,n=e.index,o=e.renderIcon,i=e.renderAction,a=e.getItemProps,c=e.onItemClick,u=e.collection,l=e.hitComponent,f=b(t.useState(!1),2),m=f[0],p=f[1],v=b(t.useState(!1),2),d=v[0],h=v[1],y=t.useRef(null),g=l;return t.createElement("li",s({className:["DocSearch-Hit",r.__docsearch_parent&&"DocSearch-Hit--Child",m&&"DocSearch-Hit--deleting",d&&"DocSearch-Hit--favoriting"].filter(Boolean).join(" "),onTransitionEnd:function(){y.current&&y.current()}},a({item:r,source:u.source,onClick:function(e){c(r,e)}})),t.createElement(g,{hit:r},t.createElement("div",{className:"DocSearch-Hit-Container"},o({item:r,index:n}),r.hierarchy[r.type]&&"lvl1"===r.type&&t.createElement("div",{className:"DocSearch-Hit-content-wrapper"},t.createElement(sr,{className:"DocSearch-Hit-title",hit:r,attribute:"hierarchy.lvl1"}),r.content&&t.createElement(sr,{className:"DocSearch-Hit-path",hit:r,attribute:"content"})),r.hierarchy[r.type]&&("lvl2"===r.type||"lvl3"===r.type||"lvl4"===r.type||"lvl5"===r.type||"lvl6"===r.type)&&t.createElement("div",{className:"DocSearch-Hit-content-wrapper"},t.createElement(sr,{className:"DocSearch-Hit-title",hit:r,attribute:"hierarchy.".concat(r.type)}),t.createElement(sr,{className:"DocSearch-Hit-path",hit:r,attribute:"hierarchy.lvl1"})),"content"===r.type&&t.createElement("div",{className:"DocSearch-Hit-content-wrapper"},t.createElement(sr,{className:"DocSearch-Hit-title",hit:r,attribute:"content"}),t.createElement(sr,{className:"DocSearch-Hit-path",hit:r,attribute:"hierarchy.lvl1"})),i({item:r,runDeleteTransition:function(e){p(!0),y.current=e},runFavoriteTransition:function(e){h(!0),y.current=e}}))))}function pr(e,t,r){return e.reduce((function(e,n){var o=t(n);return e.hasOwnProperty(o)||(e[o]=[]),e[o].length<(r||5)&&e[o].push(n),e}),{})}function vr(e){return e}function dr(e){return 1===e.button||e.altKey||e.ctrlKey||e.metaKey||e.shiftKey}function hr(){}var yr=/(<mark>|<\/mark>)/g,gr=RegExp(yr.source);function br(e){var t,r,n=e;if(!n.__docsearch_parent&&!e._highlightResult)return e.hierarchy.lvl0;var o=n.__docsearch_parent?null===(t=n.__docsearch_parent)||void 0===t||null===(t=t._highlightResult)||void 0===t||null===(t=t.hierarchy)||void 0===t?void 0:t.lvl0:null===(r=e._highlightResult)||void 0===r||null===(r=r.hierarchy)||void 0===r?void 0:r.lvl0;return o?o.value&&gr.test(o.value)?o.value.replace(yr,""):o.value:e.hierarchy.lvl0}function Sr(e){return t.createElement("div",{className:"DocSearch-Dropdown-Container"},e.state.collections.map((function(r){if(0===r.items.length)return null;var n=br(r.items[0]);return t.createElement(fr,s({},e,{key:r.source.sourceId,title:n,collection:r,renderIcon:function(e){var n,o=e.item,i=e.index;return t.createElement(t.Fragment,null,o.__docsearch_parent&&t.createElement("svg",{className:"DocSearch-Hit-Tree",viewBox:"0 0 24 54"},t.createElement("g",{stroke:"currentColor",fill:"none",fillRule:"evenodd",strokeLinecap:"round",strokeLinejoin:"round"},o.__docsearch_parent!==(null===(n=r.items[i+1])||void 0===n?void 0:n.__docsearch_parent)?t.createElement("path",{d:"M8 6v21M20 27H8.3"}):t.createElement("path",{d:"M8 6v42M20 27H8.3"}))),t.createElement("div",{className:"DocSearch-Hit-icon"},t.createElement(Xt,{type:o.type})))},renderAction:function(){return t.createElement("div",{className:"DocSearch-Hit-action"},t.createElement(Yt,null))}}))})),e.resultsFooterComponent&&t.createElement("section",{className:"DocSearch-HitsFooter"},t.createElement(e.resultsFooterComponent,{state:e.state})))}var Or=["translations"];function wr(e){var r=e.translations,n=void 0===r?{}:r,o=h(e,Or),i=n.recentSearchesTitle,a=void 0===i?"Recent":i,c=n.noRecentSearchesText,u=void 0===c?"No recent searches":c,l=n.saveRecentSearchButtonTitle,f=void 0===l?"Save this search":l,m=n.removeRecentSearchButtonTitle,p=void 0===m?"Remove this search from history":m,v=n.favoriteSearchesTitle,d=void 0===v?"Favorite":v,y=n.removeFavoriteSearchButtonTitle,g=void 0===y?"Remove this search from favorites":y;return"idle"===o.state.status&&!1===o.hasCollections?o.disableUserPersonalization?null:t.createElement("div",{className:"DocSearch-StartScreen"},t.createElement("p",{className:"DocSearch-Help"},u)):!1===o.hasCollections?null:t.createElement("div",{className:"DocSearch-Dropdown-Container"},t.createElement(fr,s({},o,{title:a,collection:o.state.collections[0],renderIcon:function(){return t.createElement("div",{className:"DocSearch-Hit-icon"},t.createElement(Zt,null))},renderAction:function(e){var r=e.item,n=e.runFavoriteTransition,i=e.runDeleteTransition;return t.createElement(t.Fragment,null,t.createElement("div",{className:"DocSearch-Hit-action"},t.createElement("button",{className:"DocSearch-Hit-action-button",title:f,type:"submit",onClick:function(e){e.preventDefault(),e.stopPropagation(),n((function(){o.favoriteSearches.add(r),o.recentSearches.remove(r),o.refresh()}))}},t.createElement(rr,null))),t.createElement("div",{className:"DocSearch-Hit-action"},t.createElement("button",{className:"DocSearch-Hit-action-button",title:p,type:"submit",onClick:function(e){e.preventDefault(),e.stopPropagation(),i((function(){o.recentSearches.remove(r),o.refresh()}))}},t.createElement(Gt,null))))}})),t.createElement(fr,s({},o,{title:d,collection:o.state.collections[1],renderIcon:function(){return t.createElement("div",{className:"DocSearch-Hit-icon"},t.createElement(rr,null))},renderAction:function(e){var r=e.item,n=e.runDeleteTransition;return t.createElement("div",{className:"DocSearch-Hit-action"},t.createElement("button",{className:"DocSearch-Hit-action-button",title:g,type:"submit",onClick:function(e){e.preventDefault(),e.stopPropagation(),n((function(){o.favoriteSearches.remove(r),o.refresh()}))}},t.createElement(Gt,null)))}})))}var Er=["translations"],jr=t.memo((function(e){var r=e.translations,n=void 0===r?{}:r,o=h(e,Er);if("error"===o.state.status)return t.createElement(ir,{translations:null==n?void 0:n.errorScreen});var i=o.state.collections.some((function(e){return e.items.length>0}));return o.state.query?!1===i?t.createElement(cr,s({},o,{translations:null==n?void 0:n.noResultsScreen})):t.createElement(Sr,o):t.createElement(wr,s({},o,{hasCollections:i,translations:null==n?void 0:n.startScreen}))}),(function(e,t){return"loading"===t.state.status||"stalled"===t.state.status})),Pr=["translations"];function Ir(e){var r=e.translations,n=void 0===r?{}:r,o=h(e,Pr),i=n.resetButtonTitle,a=void 0===i?"Clear the query":i,c=n.resetButtonAriaLabel,u=void 0===c?"Clear the query":c,l=n.cancelButtonText,f=void 0===l?"Cancel":l,m=n.cancelButtonAriaLabel,p=void 0===m?"Cancel":m,v=n.searchInputLabel,d=void 0===v?"Search":v,y=o.getFormProps({inputElement:o.inputRef.current}).onReset;return t.useEffect((function(){o.autoFocus&&o.inputRef.current&&o.inputRef.current.focus()}),[o.autoFocus,o.inputRef]),t.useEffect((function(){o.isFromSelection&&o.inputRef.current&&o.inputRef.current.select()}),[o.isFromSelection,o.inputRef]),t.createElement(t.Fragment,null,t.createElement("form",{className:"DocSearch-Form",onSubmit:function(e){e.preventDefault()},onReset:y},t.createElement("label",s({className:"DocSearch-MagnifierLabel"},o.getLabelProps()),t.createElement(j,null),t.createElement("span",{className:"DocSearch-VisuallyHiddenForAccessibility"},d)),t.createElement("div",{className:"DocSearch-LoadingIndicator"},t.createElement(Wt,null)),t.createElement("input",s({className:"DocSearch-Input",ref:o.inputRef},o.getInputProps({inputElement:o.inputRef.current,autoFocus:o.autoFocus,maxLength:64}))),t.createElement("button",{type:"reset",title:a,className:"DocSearch-Reset","aria-label":u,hidden:!o.state.query},t.createElement(Gt,null))),t.createElement("button",{className:"DocSearch-Cancel",type:"reset","aria-label":p,onClick:o.onClose},f))}var Dr=["_highlightResult","_snippetResult"];function kr(e){return!1===function(){var e="__TEST_KEY__";try{return localStorage.setItem(e,""),localStorage.removeItem(e),!0}catch(e){return!1}}()?{setItem:function(){},getItem:function(){return[]}}:{setItem:function(t){return window.localStorage.setItem(e,JSON.stringify(t))},getItem:function(){var t=window.localStorage.getItem(e);return t?JSON.parse(t):[]}}}function Ar(e){var t=e.key,r=e.limit,n=void 0===r?5:r,o=kr(t),i=o.getItem().slice(0,n);return{add:function(e){var t=e;t._highlightResult,t._snippetResult;var r=h(t,Dr),a=i.findIndex((function(e){return e.objectID===r.objectID}));a>-1&&i.splice(a,1),i.unshift(r),i=i.slice(0,n),o.setItem(i)},remove:function(e){i=i.filter((function(t){return t.objectID!==e.objectID})),o.setItem(i)},getAll:function(){return i}}}function _r(e){var t,r="algolia-client-js-".concat(e.key);function n(){return void 0===t&&(t=e.localStorage||window.localStorage),t}function o(){return JSON.parse(n().getItem(r)||"{}")}function i(e){n().setItem(r,JSON.stringify(e))}return{get:function(t,r){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{miss:function(){return Promise.resolve()}};return Promise.resolve().then((function(){var r,n,a;return r=e.timeToLive?1e3*e.timeToLive:null,n=o(),i(a=Object.fromEntries(Object.entries(n).filter((function(e){return void 0!==b(e,2)[1].timestamp})))),r&&i(Object.fromEntries(Object.entries(a).filter((function(e){var t=b(e,2)[1],n=(new Date).getTime();return!(t.timestamp+r<n)})))),o()[JSON.stringify(t)]})).then((function(e){return Promise.all([e?e.value:r(),void 0!==e])})).then((function(e){var t=b(e,2),r=t[0],o=t[1];return Promise.all([r,o||n.miss(r)])})).then((function(e){return b(e,1)[0]}))},set:function(e,t){return Promise.resolve().then((function(){var i=o();return i[JSON.stringify(e)]={timestamp:(new Date).getTime(),value:t},n().setItem(r,JSON.stringify(i)),t}))},delete:function(e){return Promise.resolve().then((function(){var t=o();delete t[JSON.stringify(e)],n().setItem(r,JSON.stringify(t))}))},clear:function(){return Promise.resolve().then((function(){n().removeItem(r)}))}}}function xr(e){var t=S(e.caches),r=t.shift();return void 0===r?{get:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{miss:function(){return Promise.resolve()}};return t().then((function(e){return Promise.all([e,r.miss(e)])})).then((function(e){return b(e,1)[0]}))},set:function(e,t){return Promise.resolve(t)},delete:function(e){return Promise.resolve()},clear:function(){return Promise.resolve()}}:{get:function(e,n){var o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{miss:function(){return Promise.resolve()}};return r.get(e,n,o).catch((function(){return xr({caches:t}).get(e,n,o)}))},set:function(e,n){return r.set(e,n).catch((function(){return xr({caches:t}).set(e,n)}))},delete:function(e){return r.delete(e).catch((function(){return xr({caches:t}).delete(e)}))},clear:function(){return r.clear().catch((function(){return xr({caches:t}).clear()}))}}}function Cr(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{serializable:!0},t={};return{get:function(r,n){var o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{miss:function(){return Promise.resolve()}},i=JSON.stringify(r);if(i in t)return Promise.resolve(e.serializable?JSON.parse(t[i]):t[i]);var a=n();return a.then((function(e){return o.miss(e)})).then((function(){return a}))},set:function(r,n){return t[JSON.stringify(r)]=e.serializable?JSON.stringify(n):n,Promise.resolve(n)},delete:function(e){return delete t[JSON.stringify(e)],Promise.resolve()},clear:function(){return t={},Promise.resolve()}}}function Tr(e){var t=e.algoliaAgents,r=e.client,n=e.version,o=function(e){var t={value:"Algolia for JavaScript (".concat(e,")"),add:function(e){var r="; ".concat(e.segment).concat(void 0!==e.version?" (".concat(e.version,")"):"");return-1===t.value.indexOf(r)&&(t.value="".concat(t.value).concat(r)),t}};return t}(n).add({segment:r,version:n});return t.forEach((function(e){return o.add(e)})),o}var Nr=12e4;function Lr(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"up",r=Date.now();return d(d({},e),{},{status:t,lastUpdate:r,isUp:function(){return"up"===t||Date.now()-r>Nr},isTimedOut:function(){return"timed out"===t&&Date.now()-r<=Nr}})}var Rr=function(e){function t(e,r){var n;return c(this,t),l(n=a(this,t,[e]),"name","AlgoliaError"),r&&(n.name=r),n}return m(t,e),u(t)}(w(Error)),qr=function(e){function t(e,r,n){var o;return c(this,t),l(o=a(this,t,[e,n]),"stackTrace",void 0),o.stackTrace=r,o}return m(t,e),u(t)}(Rr),Mr=function(e){function t(e){return c(this,t),a(this,t,["Unreachable hosts - your application id may be incorrect. If the error persists, please reach out to the Algolia Support team: https://alg.li/support.",e,"RetryError"])}return m(t,e),u(t)}(qr),Hr=function(e){function t(e,r,n){var o,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"ApiError";return c(this,t),l(o=a(this,t,[e,n,i]),"status",void 0),o.status=r,o}return m(t,e),u(t)}(qr),Br=function(e){function t(e,r){var n;return c(this,t),l(n=a(this,t,[e,"DeserializationError"]),"response",void 0),n.response=r,n}return m(t,e),u(t)}(Rr),Fr=function(e){function t(e,r,n,o){var i;return c(this,t),l(i=a(this,t,[e,r,o,"DetailedApiError"]),"error",void 0),i.error=n,i}return m(t,e),u(t)}(Hr);function Ur(e,t,r){var n,o=(n=r,Object.keys(n).filter((function(e){return void 0!==n[e]})).sort().map((function(e){return"".concat(e,"=").concat(encodeURIComponent("[object Array]"===Object.prototype.toString.call(n[e])?n[e].join(","):n[e]).replace(/\+/g,"%20"))})).join("&")),i="".concat(e.protocol,"://").concat(e.url).concat(e.port?":".concat(e.port):"","/").concat("/"===t.charAt(0)?t.substring(1):t);return o.length&&(i+="?".concat(o)),i}function Kr(e,t){if("GET"!==e.method&&(void 0!==e.data||void 0!==t.data)){var r=Array.isArray(e.data)?e.data:d(d({},e.data),t.data);return JSON.stringify(r)}}function Vr(e,t,r){var n=d(d(d({Accept:"application/json"},e),t),r),o={};return Object.keys(n).forEach((function(e){var t=n[e];o[e.toLowerCase()]=t})),o}function zr(e){try{return JSON.parse(e.content)}catch(t){throw new Br(t.message,e)}}function Jr(e,t){var r=e.content,n=e.status;try{var o=JSON.parse(r);return"error"in o?new Fr(o.message,n,o.error,t):new Hr(o.message,n,t)}catch(e){}return new Hr(r,n,t)}function Qr(e){return e.map((function(e){return Wr(e)}))}function Wr(e){var t=e.request.headers["x-algolia-api-key"]?{"x-algolia-api-key":"*****"}:{};return d(d({},e),{},{request:d(d({},e.request),{},{headers:d(d({},e.request.headers),t)})})}var Zr=["appId","apiKey","authMode","algoliaAgents"],Gr=["params"],Yr="5.14.2";function $r(e){return[{url:"".concat(e,"-dsn.algolia.net"),accept:"read",protocol:"https"},{url:"".concat(e,".algolia.net"),accept:"write",protocol:"https"}].concat(function(e){for(var t=e,r=e.length-1;r>0;r--){var n=Math.floor(Math.random()*(r+1)),o=e[r];t[r]=e[n],t[n]=o}return t}([{url:"".concat(e,"-1.algolianet.com"),accept:"readWrite",protocol:"https"},{url:"".concat(e,"-2.algolianet.com"),accept:"readWrite",protocol:"https"},{url:"".concat(e,"-3.algolianet.com"),accept:"readWrite",protocol:"https"}]))}function Xr(e){var t=e.appId,r=e.apiKey,n=e.authMode,o=e.algoliaAgents,a=h(e,Zr),c=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"WithinHeaders",n={"x-algolia-api-key":t,"x-algolia-application-id":e};return{headers:function(){return"WithinHeaders"===r?n:{}},queryParameters:function(){return"WithinQueryParameters"===r?n:{}}}}(t,r,n),u=function(e){var t=e.hosts,r=e.hostsCache,n=e.baseHeaders,o=e.logger,a=e.baseQueryParameters,c=e.algoliaAgent,u=e.timeouts,l=e.requester,s=e.requestsCache,f=e.responsesCache;function m(e){return p.apply(this,arguments)}function p(){return(p=i(y().mark((function e(t){var n,o,i,a,c;return y().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Promise.all(t.map((function(e){return r.get(e,(function(){return Promise.resolve(Lr(e))}))})));case 2:return n=e.sent,o=n.filter((function(e){return e.isUp()})),i=n.filter((function(e){return e.isTimedOut()})),a=[].concat(S(o),S(i)),c=a.length>0?a:t,e.abrupt("return",{hosts:c,getTimeout:function(e,t){return(0===i.length&&0===e?1:i.length+3+e)*t}});case 8:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function v(e,t){return h.apply(this,arguments)}function h(){return h=i(y().mark((function e(s,f){var p,v,h,g,b,O,w,E,j,P,I,D,k,A=arguments;return y().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(p=!(A.length>2&&void 0!==A[2])||A[2],v=[],h=Kr(s,f),g=Vr(n,s.headers,f.headers),b="GET"===s.method?d(d({},s.data),f.data):{},O=d(d(d({},a),s.queryParameters),b),c.value&&(O["x-algolia-agent"]=c.value),f&&f.queryParameters)for(w=0,E=Object.keys(f.queryParameters);w<E.length;w++)j=E[w],f.queryParameters[j]&&"[object Object]"!==Object.prototype.toString.call(f.queryParameters[j])?O[j]=f.queryParameters[j].toString():O[j]=f.queryParameters[j];return P=0,I=function(){var e=i(y().mark((function e(t,n){var i,a,c,m,b,S;return y().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(void 0!==(i=t.pop())){e.next=3;break}throw new Mr(Qr(v));case 3:return a=d(d({},u),f.timeouts),c={data:h,headers:g,method:s.method,url:Ur(i,s.path,O),connectTimeout:n(P,a.connect),responseTimeout:n(P,p?a.read:a.write)},m=function(e){var r={request:c,response:e,host:i,triesLeft:t.length};return v.push(r),r},e.next=8,l.send(c);case 8:if(w=void 0,E=void 0,w=(y=b=e.sent).isTimedOut,E=y.status,!(w||function(e){var t=e.isTimedOut,r=e.status;return!t&&!~~r}({isTimedOut:w,status:E})||2!=~~(E/100)&&4!=~~(E/100))){e.next=16;break}return S=m(b),b.isTimedOut&&P++,o.info("Retryable failure",Wr(S)),e.next=15,r.set(i,Lr(i,b.isTimedOut?"timed out":"down"));case 15:return e.abrupt("return",I(t,n));case 16:if(2!=~~(b.status/100)){e.next=18;break}return e.abrupt("return",zr(b));case 18:throw m(b),Jr(b,v);case 20:case"end":return e.stop()}var y,w,E}),e)})));return function(t,r){return e.apply(this,arguments)}}(),D=t.filter((function(e){return"readWrite"===e.accept||(p?"read"===e.accept:"write"===e.accept)})),e.next=13,m(D);case 13:return k=e.sent,e.abrupt("return",I(S(k.hosts).reverse(),k.getTimeout));case 15:case"end":return e.stop()}}),e)}))),h.apply(this,arguments)}return{hostsCache:r,requester:l,timeouts:u,logger:o,algoliaAgent:c,baseHeaders:n,baseQueryParameters:a,hosts:t,request:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.useReadTransporter||"GET"===e.method;if(!r)return v(e,t,r);var o=function(){return v(e,t)};if(!0!==(t.cacheable||e.cacheable))return o();var i={request:e,requestOptions:t,transporter:{queryParameters:a,headers:n}};return f.get(i,(function(){return s.get(i,(function(){return s.set(i,o()).then((function(e){return Promise.all([s.delete(i),e])}),(function(e){return Promise.all([s.delete(i),Promise.reject(e)])})).then((function(e){var t=b(e,2);return t[0],t[1]}))}))}),{miss:function(e){return f.set(i,e)}})},requestsCache:s,responsesCache:f}}(d(d({hosts:$r(t)},a),{},{algoliaAgent:Tr({algoliaAgents:o,client:"Lite",version:Yr}),baseHeaders:d(d({"content-type":"text/plain"},c.headers()),a.baseHeaders),baseQueryParameters:d(d({},c.queryParameters()),a.baseQueryParameters)}));return{transporter:u,appId:t,clearCache:function(){return Promise.all([u.requestsCache.clear(),u.responsesCache.clear()]).then((function(){}))},get _ua(){return u.algoliaAgent.value},addAlgoliaAgent:function(e,t){u.algoliaAgent.add({segment:e,version:t})},setClientApiKey:function(e){var t=e.apiKey;n&&"WithinHeaders"!==n?u.baseQueryParameters["x-algolia-api-key"]=t:u.baseHeaders["x-algolia-api-key"]=t},searchForHits:function(e,t){return this.search(e,t)},searchForFacets:function(e,t){return this.search(e,t)},customPost:function(e,t){var r=e.path,n=e.parameters,o=e.body;if(!r)throw new Error("Parameter `path` is required when calling `customPost`.");var i={method:"POST",path:"/{path}".replace("{path}",r),queryParameters:n||{},headers:{},data:o||{}};return u.request(i,t)},getRecommendations:function(e,t){e&&Array.isArray(e)&&(e={requests:e});if(!e)throw new Error("Parameter `getRecommendationsParams` is required when calling `getRecommendations`.");if(!e.requests)throw new Error("Parameter `getRecommendationsParams.requests` is required when calling `getRecommendations`.");var r={method:"POST",path:"/1/indexes/*/recommendations",queryParameters:{},headers:{},data:e,useReadTransporter:!0,cacheable:!0};return u.request(r,t)},search:function(e,t){if(e&&Array.isArray(e)){var r={requests:e.map((function(e){var t=e.params,r=h(e,Gr);return"facet"===r.type?d(d(d({},r),t),{},{type:"facet"}):d(d(d({},r),t),{},{facet:void 0,maxFacetHits:void 0,facetQuery:void 0})}))};e=r}if(!e)throw new Error("Parameter `searchMethodParams` is required when calling `search`.");if(!e.requests)throw new Error("Parameter `searchMethodParams.requests` is required when calling `search`.");var n={method:"POST",path:"/1/indexes/*/queries",queryParameters:{},headers:{},data:e,useReadTransporter:!0,cacheable:!0};return u.request(n,t)}}}var en="3.8.2";function tn(e,r,n){return t.useMemo((function(){var t=function(e,t,r){if(!e||"string"!=typeof e)throw new Error("`appId` is missing.");if(!t||"string"!=typeof t)throw new Error("`apiKey` is missing.");return Xr(d({appId:e,apiKey:t,timeouts:{connect:1e3,read:2e3,write:3e4},logger:{debug:function(e,t){return Promise.resolve()},info:function(e,t){return Promise.resolve()},error:function(e,t){return Promise.resolve()}},requester:{send:function(e){return new Promise((function(t){var r=new XMLHttpRequest;r.open(e.method,e.url,!0),Object.keys(e.headers).forEach((function(t){return r.setRequestHeader(t,e.headers[t])}));var n,o=function(e,n){return setTimeout((function(){r.abort(),t({status:0,content:n,isTimedOut:!0})}),e)},i=o(e.connectTimeout,"Connection timeout");r.onreadystatechange=function(){r.readyState>r.OPENED&&void 0===n&&(clearTimeout(i),n=o(e.responseTimeout,"Socket timeout"))},r.onerror=function(){0===r.status&&(clearTimeout(i),clearTimeout(n),t({content:r.responseText||"Network request failed",status:r.status,isTimedOut:!1}))},r.onload=function(){clearTimeout(i),clearTimeout(n),t({content:r.responseText,status:r.status,isTimedOut:!1})},r.send(e.data)}))}},algoliaAgents:[{segment:"Browser"}],authMode:"WithinQueryParameters",responsesCache:Cr(),requestsCache:Cr({serializable:!1}),hostsCache:xr({caches:[_r({key:"".concat(Yr,"-").concat(e)}),Cr()]})},r))}(e,r);return t.addAlgoliaAgent("docsearch",en),!1===/docsearch.js \(.*\)/.test(t.transporter.algoliaAgent.value)&&t.addAlgoliaAgent("docsearch-react",en),n(t)}),[e,r,n])}var rn=["footer","searchBox"];function nn(e){var r=e.appId,n=e.apiKey,o=e.indexName,i=e.placeholder,a=void 0===i?"Search docs":i,c=e.searchParameters,u=e.maxResultsPerGroup,l=e.onClose,f=void 0===l?hr:l,m=e.transformItems,p=void 0===m?vr:m,v=e.hitComponent,y=void 0===v?Qt:v,g=e.resultsFooterComponent,S=void 0===g?function(){return null}:g,O=e.navigator,w=e.initialScrollY,E=void 0===w?0:w,j=e.transformSearchClient,P=void 0===j?vr:j,I=e.disableUserPersonalization,D=void 0!==I&&I,k=e.initialQuery,A=void 0===k?"":k,_=e.translations,x=void 0===_?{}:_,C=e.getMissingResultsUrl,T=e.insights,N=void 0!==T&&T,L=x.footer,R=x.searchBox,q=h(x,rn),M=b(t.useState({query:"",collections:[],completion:null,context:{},isOpen:!1,activeItemId:null,status:"idle"}),2),H=M[0],B=M[1],F=t.useRef(null),U=t.useRef(null),K=t.useRef(null),V=t.useRef(null),z=t.useRef(null),J=t.useRef(10),Q=t.useRef("undefined"!=typeof window?window.getSelection().toString().slice(0,64):"").current,W=t.useRef(A||Q).current,Z=tn(r,n,P),G=t.useRef(Ar({key:"__DOCSEARCH_FAVORITE_SEARCHES__".concat(o),limit:10})).current,Y=t.useRef(Ar({key:"__DOCSEARCH_RECENT_SEARCHES__".concat(o),limit:0===G.getAll().length?7:4})).current,$=t.useCallback((function(e){if(!D){var t="content"===e.type?e.__docsearch_parent:e;t&&-1===G.getAll().findIndex((function(e){return e.objectID===t.objectID}))&&Y.add(t)}}),[G,Y,D]),X=t.useCallback((function(e){if(H.context.algoliaInsightsPlugin&&e.__autocomplete_id){var t=e,r={eventName:"Item Selected",index:t.__autocomplete_indexName,items:[t],positions:[e.__autocomplete_id],queryID:t.__autocomplete_queryID};H.context.algoliaInsightsPlugin.insights.clickedObjectIDsAfterSearch(r)}}),[H.context.algoliaInsightsPlugin]),ee=t.useMemo((function(){return Kt({id:"docsearch",defaultActiveItemId:0,placeholder:a,openOnFocus:!0,initialState:{query:W,context:{searchSuggestions:[]}},insights:N,navigator:O,onStateChange:function(e){B(e.state)},getSources:function(e){var t=e.query,i=e.state,a=e.setContext,l=e.setStatus;if(!t)return D?[]:[{sourceId:"recentSearches",onSelect:function(e){var t=e.item,r=e.event;$(t),dr(r)||f()},getItemUrl:function(e){return e.item.url},getItems:function(){return Y.getAll()}},{sourceId:"favoriteSearches",onSelect:function(e){var t=e.item,r=e.event;$(t),dr(r)||f()},getItemUrl:function(e){return e.item.url},getItems:function(){return G.getAll()}}];var s=Boolean(N);return Z.search({requests:[d({query:t,indexName:o,attributesToRetrieve:["hierarchy.lvl0","hierarchy.lvl1","hierarchy.lvl2","hierarchy.lvl3","hierarchy.lvl4","hierarchy.lvl5","hierarchy.lvl6","content","type","url"],attributesToSnippet:["hierarchy.lvl1:".concat(J.current),"hierarchy.lvl2:".concat(J.current),"hierarchy.lvl3:".concat(J.current),"hierarchy.lvl4:".concat(J.current),"hierarchy.lvl5:".concat(J.current),"hierarchy.lvl6:".concat(J.current),"content:".concat(J.current)],snippetEllipsisText:"…",highlightPreTag:"<mark>",highlightPostTag:"</mark>",hitsPerPage:20,clickAnalytics:s},c)]}).catch((function(e){throw"RetryError"===e.name&&l("error"),e})).then((function(e){var t=e.results[0],c=t.hits,l=t.nbHits,m=pr(c,(function(e){return br(e)}),u);i.context.searchSuggestions.length<Object.keys(m).length&&a({searchSuggestions:Object.keys(m)}),a({nbHits:l});var v={};return s&&(v={__autocomplete_indexName:o,__autocomplete_queryID:t.queryID,__autocomplete_algoliaCredentials:{appId:r,apiKey:n}}),Object.values(m).map((function(e,t){return{sourceId:"hits".concat(t),onSelect:function(e){var t=e.item,r=e.event;$(t),dr(r)||f()},getItemUrl:function(e){return e.item.url},getItems:function(){return Object.values(pr(e,(function(e){return e.hierarchy.lvl1}),u)).map(p).map((function(e){return e.map((function(t){var r=null,n=e.find((function(e){return"lvl1"===e.type&&e.hierarchy.lvl1===t.hierarchy.lvl1}));return"lvl1"!==t.type&&n&&(r=n),d(d({},t),{},{__docsearch_parent:r},v)}))})).flat()}}}))}))}})}),[o,c,u,Z,f,Y,G,$,W,a,O,p,D,N,r,n]),te=ee.getEnvironmentProps,re=ee.getRootProps,ne=ee.refresh;return function(e){var r=e.getEnvironmentProps,n=e.panelElement,o=e.formElement,i=e.inputElement;t.useEffect((function(){if(n&&o&&i){var e=r({panelElement:n,formElement:o,inputElement:i}),t=e.onTouchStart,a=e.onTouchMove;return window.addEventListener("touchstart",t),window.addEventListener("touchmove",a),function(){window.removeEventListener("touchstart",t),window.removeEventListener("touchmove",a)}}}),[r,n,o,i])}({getEnvironmentProps:te,panelElement:V.current,formElement:K.current,inputElement:z.current}),function(e){var r=e.container;t.useEffect((function(){if(r){var e=r.querySelectorAll("a[href]:not([disabled]), button:not([disabled]), input:not([disabled])"),t=e[0],n=e[e.length-1];return r.addEventListener("keydown",o),function(){r.removeEventListener("keydown",o)}}function o(e){"Tab"===e.key&&(e.shiftKey?document.activeElement===t&&(e.preventDefault(),n.focus()):document.activeElement===n&&(e.preventDefault(),t.focus()))}}),[r])}({container:F.current}),t.useEffect((function(){return document.body.classList.add("DocSearch--active"),function(){var e,t;document.body.classList.remove("DocSearch--active"),null===(e=(t=window).scrollTo)||void 0===e||e.call(t,0,E)}}),[]),t.useEffect((function(){window.matchMedia("(max-width: 768px)").matches&&(J.current=5)}),[]),t.useEffect((function(){V.current&&(V.current.scrollTop=0)}),[H.query]),t.useEffect((function(){W.length>0&&(ne(),z.current&&z.current.focus())}),[W,ne]),t.useEffect((function(){function e(){if(U.current){var e=.01*window.innerHeight;U.current.style.setProperty("--docsearch-vh","".concat(e,"px"))}}return e(),window.addEventListener("resize",e),function(){window.removeEventListener("resize",e)}}),[]),t.createElement("div",s({ref:F},re({"aria-expanded":!0}),{className:["DocSearch","DocSearch-Container","stalled"===H.status&&"DocSearch-Container--Stalled","error"===H.status&&"DocSearch-Container--Errored"].filter(Boolean).join(" "),role:"button",tabIndex:0,onMouseDown:function(e){e.target===e.currentTarget&&f()}}),t.createElement("div",{className:"DocSearch-Modal",ref:U},t.createElement("header",{className:"DocSearch-SearchBar",ref:K},t.createElement(Ir,s({},ee,{state:H,autoFocus:0===W.length,inputRef:z,isFromSelection:Boolean(W)&&W===Q,translations:R,onClose:f}))),t.createElement("div",{className:"DocSearch-Dropdown",ref:V},t.createElement(jr,s({},ee,{indexName:o,state:H,hitComponent:y,resultsFooterComponent:S,disableUserPersonalization:D,recentSearches:Y,favoriteSearches:G,inputRef:z,translations:q,getMissingResultsUrl:C,onItemClick:function(e,t){X(e),$(e),dr(t)||f()}}))),t.createElement("footer",{className:"DocSearch-Footer"},t.createElement(Jt,{translations:L}))))}function on(e){var r=e.isOpen,n=e.onOpen,o=e.onClose,i=e.onInput,a=e.searchButtonRef;t.useEffect((function(){function e(e){var t;if("Escape"===e.code&&r||"k"===(null===(t=e.key)||void 0===t?void 0:t.toLowerCase())&&(e.metaKey||e.ctrlKey)||!function(e){var t=e.target,r=t.tagName;return t.isContentEditable||"INPUT"===r||"SELECT"===r||"TEXTAREA"===r}(e)&&"/"===e.key&&!r)return e.preventDefault(),void(r?o():document.body.classList.contains("DocSearch--active")||n());a&&a.current===document.activeElement&&i&&/[a-zA-Z0-9]/.test(String.fromCharCode(e.keyCode))&&i(e)}return window.addEventListener("keydown",e),function(){window.removeEventListener("keydown",e)}}),[r,n,o,i,a])}e.DocSearch=function(e){var n,o,i=t.useRef(null),a=b(t.useState(!1),2),c=a[0],u=a[1],l=b(t.useState((null==e?void 0:e.initialQuery)||void 0),2),f=l[0],m=l[1],p=t.useCallback((function(){u(!0)}),[u]),v=t.useCallback((function(){u(!1),m(null==e?void 0:e.initialQuery)}),[u,e.initialQuery]);return on({isOpen:c,onOpen:p,onClose:v,onInput:t.useCallback((function(e){u(!0),m(e.key)}),[u,m]),searchButtonRef:i}),t.createElement(t.Fragment,null,t.createElement(D,{ref:i,translations:null==e||null===(n=e.translations)||void 0===n?void 0:n.button,onClick:p}),c&&r.createPortal(t.createElement(nn,s({},e,{initialScrollY:window.scrollY,initialQuery:f,translations:null==e||null===(o=e.translations)||void 0===o?void 0:o.modal,onClose:v})),document.body))},e.DocSearchButton=D,e.DocSearchModal=nn,e.useDocSearchKeyboardEvents=on,e.version=en}));
//# sourceMappingURL=index.js.map
