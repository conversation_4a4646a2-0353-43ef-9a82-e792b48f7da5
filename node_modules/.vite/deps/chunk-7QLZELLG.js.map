{"version": 3, "sources": ["../../ajv/lib/compile/codegen/code.ts", "../../ajv/lib/compile/codegen/scope.ts", "../../ajv/lib/compile/codegen/index.ts", "../../ajv/lib/compile/util.ts", "../../ajv/lib/compile/names.ts", "../../ajv/lib/compile/errors.ts", "../../ajv/lib/compile/validate/boolSchema.ts", "../../ajv/lib/compile/rules.ts", "../../ajv/lib/compile/validate/applicability.ts", "../../ajv/lib/compile/validate/dataType.ts", "../../ajv/lib/compile/validate/defaults.ts", "../../ajv/lib/vocabularies/code.ts", "../../ajv/lib/compile/validate/keyword.ts", "../../ajv/lib/compile/validate/subschema.ts", "../../fast-deep-equal/index.js", "../../json-schema-traverse/index.js", "../../ajv/lib/compile/resolve.ts", "../../ajv/lib/compile/validate/index.ts", "../../ajv/lib/runtime/validation_error.ts", "../../ajv/lib/compile/ref_error.ts", "../../ajv/lib/compile/index.ts", "../../ajv/dist/refs/data.json", "../../fast-uri/lib/scopedChars.js", "../../fast-uri/lib/utils.js", "../../fast-uri/lib/schemes.js", "../../fast-uri/index.js", "../../ajv/lib/runtime/uri.ts", "../../ajv/lib/core.ts", "../../ajv/lib/vocabularies/core/id.ts", "../../ajv/lib/vocabularies/core/ref.ts", "../../ajv/lib/vocabularies/core/index.ts", "../../ajv/lib/vocabularies/validation/limitNumber.ts", "../../ajv/lib/vocabularies/validation/multipleOf.ts", "../../ajv/lib/runtime/ucs2length.ts", "../../ajv/lib/vocabularies/validation/limitLength.ts", "../../ajv/lib/vocabularies/validation/pattern.ts", "../../ajv/lib/vocabularies/validation/limitProperties.ts", "../../ajv/lib/vocabularies/validation/required.ts", "../../ajv/lib/vocabularies/validation/limitItems.ts", "../../ajv/lib/runtime/equal.ts", "../../ajv/lib/vocabularies/validation/uniqueItems.ts", "../../ajv/lib/vocabularies/validation/const.ts", "../../ajv/lib/vocabularies/validation/enum.ts", "../../ajv/lib/vocabularies/validation/index.ts", "../../ajv/lib/vocabularies/applicator/additionalItems.ts", "../../ajv/lib/vocabularies/applicator/items.ts", "../../ajv/lib/vocabularies/applicator/prefixItems.ts", "../../ajv/lib/vocabularies/applicator/items2020.ts", "../../ajv/lib/vocabularies/applicator/contains.ts", "../../ajv/lib/vocabularies/applicator/dependencies.ts", "../../ajv/lib/vocabularies/applicator/propertyNames.ts", "../../ajv/lib/vocabularies/applicator/additionalProperties.ts", "../../ajv/lib/vocabularies/applicator/properties.ts", "../../ajv/lib/vocabularies/applicator/patternProperties.ts", "../../ajv/lib/vocabularies/applicator/not.ts", "../../ajv/lib/vocabularies/applicator/anyOf.ts", "../../ajv/lib/vocabularies/applicator/oneOf.ts", "../../ajv/lib/vocabularies/applicator/allOf.ts", "../../ajv/lib/vocabularies/applicator/if.ts", "../../ajv/lib/vocabularies/applicator/thenElse.ts", "../../ajv/lib/vocabularies/applicator/index.ts", "../../ajv/lib/vocabularies/format/format.ts", "../../ajv/lib/vocabularies/format/index.ts", "../../ajv/lib/vocabularies/metadata.ts", "../../ajv/lib/vocabularies/draft7.ts", "../../ajv/lib/vocabularies/discriminator/types.ts", "../../ajv/lib/vocabularies/discriminator/index.ts", "../../ajv/dist/refs/json-schema-draft-07.json", "../../ajv/lib/ajv.ts"], "sourcesContent": ["// eslint-disable-next-line @typescript-eslint/no-extraneous-class\nexport abstract class _CodeOrName {\n  abstract readonly str: string\n  abstract readonly names: UsedNames\n  abstract toString(): string\n  abstract emptyStr(): boolean\n}\n\nexport const IDENTIFIER = /^[a-z$_][a-z$_0-9]*$/i\n\nexport class Name extends _CodeOrName {\n  readonly str: string\n  constructor(s: string) {\n    super()\n    if (!IDENTIFIER.test(s)) throw new Error(\"CodeGen: name must be a valid identifier\")\n    this.str = s\n  }\n\n  toString(): string {\n    return this.str\n  }\n\n  emptyStr(): boolean {\n    return false\n  }\n\n  get names(): UsedNames {\n    return {[this.str]: 1}\n  }\n}\n\nexport class _Code extends _CodeOrName {\n  readonly _items: readonly CodeItem[]\n  private _str?: string\n  private _names?: UsedNames\n\n  constructor(code: string | readonly CodeItem[]) {\n    super()\n    this._items = typeof code === \"string\" ? [code] : code\n  }\n\n  toString(): string {\n    return this.str\n  }\n\n  emptyStr(): boolean {\n    if (this._items.length > 1) return false\n    const item = this._items[0]\n    return item === \"\" || item === '\"\"'\n  }\n\n  get str(): string {\n    return (this._str ??= this._items.reduce((s: string, c: CodeItem) => `${s}${c}`, \"\"))\n  }\n\n  get names(): UsedNames {\n    return (this._names ??= this._items.reduce((names: UsedNames, c) => {\n      if (c instanceof Name) names[c.str] = (names[c.str] || 0) + 1\n      return names\n    }, {}))\n  }\n}\n\nexport type CodeItem = Name | string | number | boolean | null\n\nexport type UsedNames = Record<string, number | undefined>\n\nexport type Code = _Code | Name\n\nexport type SafeExpr = Code | number | boolean | null\n\nexport const nil = new _Code(\"\")\n\ntype CodeArg = SafeExpr | string | undefined\n\nexport function _(strs: TemplateStringsArray, ...args: CodeArg[]): _Code {\n  const code: CodeItem[] = [strs[0]]\n  let i = 0\n  while (i < args.length) {\n    addCodeArg(code, args[i])\n    code.push(strs[++i])\n  }\n  return new _Code(code)\n}\n\nconst plus = new _Code(\"+\")\n\nexport function str(strs: TemplateStringsArray, ...args: (CodeArg | string[])[]): _Code {\n  const expr: CodeItem[] = [safeStringify(strs[0])]\n  let i = 0\n  while (i < args.length) {\n    expr.push(plus)\n    addCodeArg(expr, args[i])\n    expr.push(plus, safeStringify(strs[++i]))\n  }\n  optimize(expr)\n  return new _Code(expr)\n}\n\nexport function addCodeArg(code: CodeItem[], arg: CodeArg | string[]): void {\n  if (arg instanceof _Code) code.push(...arg._items)\n  else if (arg instanceof Name) code.push(arg)\n  else code.push(interpolate(arg))\n}\n\nfunction optimize(expr: CodeItem[]): void {\n  let i = 1\n  while (i < expr.length - 1) {\n    if (expr[i] === plus) {\n      const res = mergeExprItems(expr[i - 1], expr[i + 1])\n      if (res !== undefined) {\n        expr.splice(i - 1, 3, res)\n        continue\n      }\n      expr[i++] = \"+\"\n    }\n    i++\n  }\n}\n\nfunction mergeExprItems(a: CodeItem, b: CodeItem): CodeItem | undefined {\n  if (b === '\"\"') return a\n  if (a === '\"\"') return b\n  if (typeof a == \"string\") {\n    if (b instanceof Name || a[a.length - 1] !== '\"') return\n    if (typeof b != \"string\") return `${a.slice(0, -1)}${b}\"`\n    if (b[0] === '\"') return a.slice(0, -1) + b.slice(1)\n    return\n  }\n  if (typeof b == \"string\" && b[0] === '\"' && !(a instanceof Name)) return `\"${a}${b.slice(1)}`\n  return\n}\n\nexport function strConcat(c1: Code, c2: Code): Code {\n  return c2.emptyStr() ? c1 : c1.emptyStr() ? c2 : str`${c1}${c2}`\n}\n\n// TODO do not allow arrays here\nfunction interpolate(x?: string | string[] | number | boolean | null): SafeExpr | string {\n  return typeof x == \"number\" || typeof x == \"boolean\" || x === null\n    ? x\n    : safeStringify(Array.isArray(x) ? x.join(\",\") : x)\n}\n\nexport function stringify(x: unknown): Code {\n  return new _Code(safeStringify(x))\n}\n\nexport function safeStringify(x: unknown): string {\n  return JSON.stringify(x)\n    .replace(/\\u2028/g, \"\\\\u2028\")\n    .replace(/\\u2029/g, \"\\\\u2029\")\n}\n\nexport function getProperty(key: Code | string | number): Code {\n  return typeof key == \"string\" && IDENTIFIER.test(key) ? new _Code(`.${key}`) : _`[${key}]`\n}\n\n//Does best effort to format the name properly\nexport function getEsmExportName(key: Code | string | number): Code {\n  if (typeof key == \"string\" && IDENTIFIER.test(key)) {\n    return new _Code(`${key}`)\n  }\n  throw new Error(`CodeGen: invalid export name: ${key}, use explicit $id name mapping`)\n}\n\nexport function regexpCode(rx: RegExp): Code {\n  return new _Code(rx.toString())\n}\n", "import {_, nil, Code, Name} from \"./code\"\n\ninterface NameGroup {\n  prefix: string\n  index: number\n}\n\nexport interface NameValue {\n  ref: ValueReference // this is the reference to any value that can be referred to from generated code via `globals` var in the closure\n  key?: unknown // any key to identify a global to avoid duplicates, if not passed ref is used\n  code?: Code // this is the code creating the value needed for standalone code wit_out closure - can be a primitive value, function or import (`require`)\n}\n\nexport type ValueReference = unknown // possibly make CodeGen parameterized type on this type\n\nclass ValueError extends Error {\n  readonly value?: NameValue\n  constructor(name: ValueScopeName) {\n    super(`CodeGen: \"code\" for ${name} not defined`)\n    this.value = name.value\n  }\n}\n\ninterface ScopeOptions {\n  prefixes?: Set<string>\n  parent?: Scope\n}\n\ninterface ValueScopeOptions extends ScopeOptions {\n  scope: ScopeStore\n  es5?: boolean\n  lines?: boolean\n}\n\nexport type ScopeStore = Record<string, ValueReference[] | undefined>\n\ntype ScopeValues = {\n  [Prefix in string]?: Map<unknown, ValueScopeName>\n}\n\nexport type ScopeValueSets = {\n  [Prefix in string]?: Set<ValueScopeName>\n}\n\nexport enum UsedValueState {\n  Started,\n  Completed,\n}\n\nexport type UsedScopeValues = {\n  [Prefix in string]?: Map<ValueScopeName, UsedValueState | undefined>\n}\n\nexport const varKinds = {\n  const: new Name(\"const\"),\n  let: new Name(\"let\"),\n  var: new Name(\"var\"),\n}\n\nexport class Scope {\n  protected readonly _names: {[Prefix in string]?: NameGroup} = {}\n  protected readonly _prefixes?: Set<string>\n  protected readonly _parent?: Scope\n\n  constructor({prefixes, parent}: ScopeOptions = {}) {\n    this._prefixes = prefixes\n    this._parent = parent\n  }\n\n  toName(nameOrPrefix: Name | string): Name {\n    return nameOrPrefix instanceof Name ? nameOrPrefix : this.name(nameOrPrefix)\n  }\n\n  name(prefix: string): Name {\n    return new Name(this._newName(prefix))\n  }\n\n  protected _newName(prefix: string): string {\n    const ng = this._names[prefix] || this._nameGroup(prefix)\n    return `${prefix}${ng.index++}`\n  }\n\n  private _nameGroup(prefix: string): NameGroup {\n    if (this._parent?._prefixes?.has(prefix) || (this._prefixes && !this._prefixes.has(prefix))) {\n      throw new Error(`CodeGen: prefix \"${prefix}\" is not allowed in this scope`)\n    }\n    return (this._names[prefix] = {prefix, index: 0})\n  }\n}\n\ninterface ScopePath {\n  property: string\n  itemIndex: number\n}\n\nexport class ValueScopeName extends Name {\n  readonly prefix: string\n  value?: NameValue\n  scopePath?: Code\n\n  constructor(prefix: string, nameStr: string) {\n    super(nameStr)\n    this.prefix = prefix\n  }\n\n  setValue(value: NameValue, {property, itemIndex}: ScopePath): void {\n    this.value = value\n    this.scopePath = _`.${new Name(property)}[${itemIndex}]`\n  }\n}\n\ninterface VSOptions extends ValueScopeOptions {\n  _n: Code\n}\n\nconst line = _`\\n`\n\nexport class ValueScope extends Scope {\n  protected readonly _values: ScopeValues = {}\n  protected readonly _scope: ScopeStore\n  readonly opts: VSOptions\n\n  constructor(opts: ValueScopeOptions) {\n    super(opts)\n    this._scope = opts.scope\n    this.opts = {...opts, _n: opts.lines ? line : nil}\n  }\n\n  get(): ScopeStore {\n    return this._scope\n  }\n\n  name(prefix: string): ValueScopeName {\n    return new ValueScopeName(prefix, this._newName(prefix))\n  }\n\n  value(nameOrPrefix: ValueScopeName | string, value: NameValue): ValueScopeName {\n    if (value.ref === undefined) throw new Error(\"CodeGen: ref must be passed in value\")\n    const name = this.toName(nameOrPrefix) as ValueScopeName\n    const {prefix} = name\n    const valueKey = value.key ?? value.ref\n    let vs = this._values[prefix]\n    if (vs) {\n      const _name = vs.get(valueKey)\n      if (_name) return _name\n    } else {\n      vs = this._values[prefix] = new Map()\n    }\n    vs.set(valueKey, name)\n\n    const s = this._scope[prefix] || (this._scope[prefix] = [])\n    const itemIndex = s.length\n    s[itemIndex] = value.ref\n    name.setValue(value, {property: prefix, itemIndex})\n    return name\n  }\n\n  getValue(prefix: string, keyOrRef: unknown): ValueScopeName | undefined {\n    const vs = this._values[prefix]\n    if (!vs) return\n    return vs.get(keyOrRef)\n  }\n\n  scopeRefs(scopeName: Name, values: ScopeValues | ScopeValueSets = this._values): Code {\n    return this._reduceValues(values, (name: ValueScopeName) => {\n      if (name.scopePath === undefined) throw new Error(`CodeGen: name \"${name}\" has no value`)\n      return _`${scopeName}${name.scopePath}`\n    })\n  }\n\n  scopeCode(\n    values: ScopeValues | ScopeValueSets = this._values,\n    usedValues?: UsedScopeValues,\n    getCode?: (n: ValueScopeName) => Code | undefined\n  ): Code {\n    return this._reduceValues(\n      values,\n      (name: ValueScopeName) => {\n        if (name.value === undefined) throw new Error(`CodeGen: name \"${name}\" has no value`)\n        return name.value.code\n      },\n      usedValues,\n      getCode\n    )\n  }\n\n  private _reduceValues(\n    values: ScopeValues | ScopeValueSets,\n    valueCode: (n: ValueScopeName) => Code | undefined,\n    usedValues: UsedScopeValues = {},\n    getCode?: (n: ValueScopeName) => Code | undefined\n  ): Code {\n    let code: Code = nil\n    for (const prefix in values) {\n      const vs = values[prefix]\n      if (!vs) continue\n      const nameSet = (usedValues[prefix] = usedValues[prefix] || new Map())\n      vs.forEach((name: ValueScopeName) => {\n        if (nameSet.has(name)) return\n        nameSet.set(name, UsedValueState.Started)\n        let c = valueCode(name)\n        if (c) {\n          const def = this.opts.es5 ? varKinds.var : varKinds.const\n          code = _`${code}${def} ${name} = ${c};${this.opts._n}`\n        } else if ((c = getCode?.(name))) {\n          code = _`${code}${c}${this.opts._n}`\n        } else {\n          throw new ValueError(name)\n        }\n        nameSet.set(name, UsedValueState.Completed)\n      })\n    }\n    return code\n  }\n}\n", "import type {ScopeValueSets, NameValue, ValueScope, ValueScopeName} from \"./scope\"\nimport {_, nil, _Code, Code, Name, UsedNames, CodeItem, addCodeArg, _CodeOrName} from \"./code\"\nimport {Scope, varKinds} from \"./scope\"\n\nexport {_, str, strConcat, nil, getProperty, stringify, regexpCode, Name, Code} from \"./code\"\nexport {Scope, ScopeStore, ValueScope, ValueScopeName, ScopeValueSets, varKinds} from \"./scope\"\n\n// type for expressions that can be safely inserted in code without quotes\nexport type SafeExpr = Code | number | boolean | null\n\n// type that is either Code of function that adds code to CodeGen instance using its methods\nexport type Block = Code | (() => void)\n\nexport const operators = {\n  GT: new _Code(\">\"),\n  GTE: new _Code(\">=\"),\n  LT: new _Code(\"<\"),\n  LTE: new _Code(\"<=\"),\n  EQ: new _Code(\"===\"),\n  NEQ: new _Code(\"!==\"),\n  NOT: new _Code(\"!\"),\n  OR: new _Code(\"||\"),\n  AND: new _Code(\"&&\"),\n  ADD: new _Code(\"+\"),\n}\n\nabstract class Node {\n  abstract readonly names: UsedNames\n\n  optimizeNodes(): this | ChildNode | ChildNode[] | undefined {\n    return this\n  }\n\n  optimizeNames(_names: UsedNames, _constants: Constants): this | undefined {\n    return this\n  }\n\n  // get count(): number {\n  //   return 1\n  // }\n}\n\nclass Def extends Node {\n  constructor(\n    private readonly varKind: Name,\n    private readonly name: Name,\n    private rhs?: SafeExpr\n  ) {\n    super()\n  }\n\n  render({es5, _n}: CGOptions): string {\n    const varKind = es5 ? varKinds.var : this.varKind\n    const rhs = this.rhs === undefined ? \"\" : ` = ${this.rhs}`\n    return `${varKind} ${this.name}${rhs};` + _n\n  }\n\n  optimizeNames(names: UsedNames, constants: Constants): this | undefined {\n    if (!names[this.name.str]) return\n    if (this.rhs) this.rhs = optimizeExpr(this.rhs, names, constants)\n    return this\n  }\n\n  get names(): UsedNames {\n    return this.rhs instanceof _CodeOrName ? this.rhs.names : {}\n  }\n}\n\nclass Assign extends Node {\n  constructor(\n    readonly lhs: Code,\n    public rhs: SafeExpr,\n    private readonly sideEffects?: boolean\n  ) {\n    super()\n  }\n\n  render({_n}: CGOptions): string {\n    return `${this.lhs} = ${this.rhs};` + _n\n  }\n\n  optimizeNames(names: UsedNames, constants: Constants): this | undefined {\n    if (this.lhs instanceof Name && !names[this.lhs.str] && !this.sideEffects) return\n    this.rhs = optimizeExpr(this.rhs, names, constants)\n    return this\n  }\n\n  get names(): UsedNames {\n    const names = this.lhs instanceof Name ? {} : {...this.lhs.names}\n    return addExprNames(names, this.rhs)\n  }\n}\n\nclass AssignOp extends Assign {\n  constructor(\n    lhs: Code,\n    private readonly op: Code,\n    rhs: SafeExpr,\n    sideEffects?: boolean\n  ) {\n    super(lhs, rhs, sideEffects)\n  }\n\n  render({_n}: CGOptions): string {\n    return `${this.lhs} ${this.op}= ${this.rhs};` + _n\n  }\n}\n\nclass Label extends Node {\n  readonly names: UsedNames = {}\n  constructor(readonly label: Name) {\n    super()\n  }\n\n  render({_n}: CGOptions): string {\n    return `${this.label}:` + _n\n  }\n}\n\nclass Break extends Node {\n  readonly names: UsedNames = {}\n  constructor(readonly label?: Code) {\n    super()\n  }\n\n  render({_n}: CGOptions): string {\n    const label = this.label ? ` ${this.label}` : \"\"\n    return `break${label};` + _n\n  }\n}\n\nclass Throw extends Node {\n  constructor(readonly error: Code) {\n    super()\n  }\n\n  render({_n}: CGOptions): string {\n    return `throw ${this.error};` + _n\n  }\n\n  get names(): UsedNames {\n    return this.error.names\n  }\n}\n\nclass AnyCode extends Node {\n  constructor(private code: SafeExpr) {\n    super()\n  }\n\n  render({_n}: CGOptions): string {\n    return `${this.code};` + _n\n  }\n\n  optimizeNodes(): this | undefined {\n    return `${this.code}` ? this : undefined\n  }\n\n  optimizeNames(names: UsedNames, constants: Constants): this {\n    this.code = optimizeExpr(this.code, names, constants)\n    return this\n  }\n\n  get names(): UsedNames {\n    return this.code instanceof _CodeOrName ? this.code.names : {}\n  }\n}\n\nabstract class ParentNode extends Node {\n  constructor(readonly nodes: ChildNode[] = []) {\n    super()\n  }\n\n  render(opts: CGOptions): string {\n    return this.nodes.reduce((code, n) => code + n.render(opts), \"\")\n  }\n\n  optimizeNodes(): this | ChildNode | ChildNode[] | undefined {\n    const {nodes} = this\n    let i = nodes.length\n    while (i--) {\n      const n = nodes[i].optimizeNodes()\n      if (Array.isArray(n)) nodes.splice(i, 1, ...n)\n      else if (n) nodes[i] = n\n      else nodes.splice(i, 1)\n    }\n    return nodes.length > 0 ? this : undefined\n  }\n\n  optimizeNames(names: UsedNames, constants: Constants): this | undefined {\n    const {nodes} = this\n    let i = nodes.length\n    while (i--) {\n      // iterating backwards improves 1-pass optimization\n      const n = nodes[i]\n      if (n.optimizeNames(names, constants)) continue\n      subtractNames(names, n.names)\n      nodes.splice(i, 1)\n    }\n    return nodes.length > 0 ? this : undefined\n  }\n\n  get names(): UsedNames {\n    return this.nodes.reduce((names: UsedNames, n) => addNames(names, n.names), {})\n  }\n\n  // get count(): number {\n  //   return this.nodes.reduce((c, n) => c + n.count, 1)\n  // }\n}\n\nabstract class BlockNode extends ParentNode {\n  render(opts: CGOptions): string {\n    return \"{\" + opts._n + super.render(opts) + \"}\" + opts._n\n  }\n}\n\nclass Root extends ParentNode {}\n\nclass Else extends BlockNode {\n  static readonly kind = \"else\"\n}\n\nclass If extends BlockNode {\n  static readonly kind = \"if\"\n  else?: If | Else\n  constructor(\n    private condition: Code | boolean,\n    nodes?: ChildNode[]\n  ) {\n    super(nodes)\n  }\n\n  render(opts: CGOptions): string {\n    let code = `if(${this.condition})` + super.render(opts)\n    if (this.else) code += \"else \" + this.else.render(opts)\n    return code\n  }\n\n  optimizeNodes(): If | ChildNode[] | undefined {\n    super.optimizeNodes()\n    const cond = this.condition\n    if (cond === true) return this.nodes // else is ignored here\n    let e = this.else\n    if (e) {\n      const ns = e.optimizeNodes()\n      e = this.else = Array.isArray(ns) ? new Else(ns) : (ns as Else | undefined)\n    }\n    if (e) {\n      if (cond === false) return e instanceof If ? e : e.nodes\n      if (this.nodes.length) return this\n      return new If(not(cond), e instanceof If ? [e] : e.nodes)\n    }\n    if (cond === false || !this.nodes.length) return undefined\n    return this\n  }\n\n  optimizeNames(names: UsedNames, constants: Constants): this | undefined {\n    this.else = this.else?.optimizeNames(names, constants)\n    if (!(super.optimizeNames(names, constants) || this.else)) return\n    this.condition = optimizeExpr(this.condition, names, constants)\n    return this\n  }\n\n  get names(): UsedNames {\n    const names = super.names\n    addExprNames(names, this.condition)\n    if (this.else) addNames(names, this.else.names)\n    return names\n  }\n\n  // get count(): number {\n  //   return super.count + (this.else?.count || 0)\n  // }\n}\n\nabstract class For extends BlockNode {\n  static readonly kind = \"for\"\n}\n\nclass ForLoop extends For {\n  constructor(private iteration: Code) {\n    super()\n  }\n\n  render(opts: CGOptions): string {\n    return `for(${this.iteration})` + super.render(opts)\n  }\n\n  optimizeNames(names: UsedNames, constants: Constants): this | undefined {\n    if (!super.optimizeNames(names, constants)) return\n    this.iteration = optimizeExpr(this.iteration, names, constants)\n    return this\n  }\n\n  get names(): UsedNames {\n    return addNames(super.names, this.iteration.names)\n  }\n}\n\nclass ForRange extends For {\n  constructor(\n    private readonly varKind: Name,\n    private readonly name: Name,\n    private readonly from: SafeExpr,\n    private readonly to: SafeExpr\n  ) {\n    super()\n  }\n\n  render(opts: CGOptions): string {\n    const varKind = opts.es5 ? varKinds.var : this.varKind\n    const {name, from, to} = this\n    return `for(${varKind} ${name}=${from}; ${name}<${to}; ${name}++)` + super.render(opts)\n  }\n\n  get names(): UsedNames {\n    const names = addExprNames(super.names, this.from)\n    return addExprNames(names, this.to)\n  }\n}\n\nclass ForIter extends For {\n  constructor(\n    private readonly loop: \"of\" | \"in\",\n    private readonly varKind: Name,\n    private readonly name: Name,\n    private iterable: Code\n  ) {\n    super()\n  }\n\n  render(opts: CGOptions): string {\n    return `for(${this.varKind} ${this.name} ${this.loop} ${this.iterable})` + super.render(opts)\n  }\n\n  optimizeNames(names: UsedNames, constants: Constants): this | undefined {\n    if (!super.optimizeNames(names, constants)) return\n    this.iterable = optimizeExpr(this.iterable, names, constants)\n    return this\n  }\n\n  get names(): UsedNames {\n    return addNames(super.names, this.iterable.names)\n  }\n}\n\nclass Func extends BlockNode {\n  static readonly kind = \"func\"\n  constructor(\n    public name: Name,\n    public args: Code,\n    public async?: boolean\n  ) {\n    super()\n  }\n\n  render(opts: CGOptions): string {\n    const _async = this.async ? \"async \" : \"\"\n    return `${_async}function ${this.name}(${this.args})` + super.render(opts)\n  }\n}\n\nclass Return extends ParentNode {\n  static readonly kind = \"return\"\n\n  render(opts: CGOptions): string {\n    return \"return \" + super.render(opts)\n  }\n}\n\nclass Try extends BlockNode {\n  catch?: Catch\n  finally?: Finally\n\n  render(opts: CGOptions): string {\n    let code = \"try\" + super.render(opts)\n    if (this.catch) code += this.catch.render(opts)\n    if (this.finally) code += this.finally.render(opts)\n    return code\n  }\n\n  optimizeNodes(): this {\n    super.optimizeNodes()\n    this.catch?.optimizeNodes() as Catch | undefined\n    this.finally?.optimizeNodes() as Finally | undefined\n    return this\n  }\n\n  optimizeNames(names: UsedNames, constants: Constants): this {\n    super.optimizeNames(names, constants)\n    this.catch?.optimizeNames(names, constants)\n    this.finally?.optimizeNames(names, constants)\n    return this\n  }\n\n  get names(): UsedNames {\n    const names = super.names\n    if (this.catch) addNames(names, this.catch.names)\n    if (this.finally) addNames(names, this.finally.names)\n    return names\n  }\n\n  // get count(): number {\n  //   return super.count + (this.catch?.count || 0) + (this.finally?.count || 0)\n  // }\n}\n\nclass Catch extends BlockNode {\n  static readonly kind = \"catch\"\n  constructor(readonly error: Name) {\n    super()\n  }\n\n  render(opts: CGOptions): string {\n    return `catch(${this.error})` + super.render(opts)\n  }\n}\n\nclass Finally extends BlockNode {\n  static readonly kind = \"finally\"\n  render(opts: CGOptions): string {\n    return \"finally\" + super.render(opts)\n  }\n}\n\ntype StartBlockNode = If | For | Func | Return | Try\n\ntype LeafNode = Def | Assign | Label | Break | Throw | AnyCode\n\ntype ChildNode = StartBlockNode | LeafNode\n\ntype EndBlockNodeType =\n  | typeof If\n  | typeof Else\n  | typeof For\n  | typeof Func\n  | typeof Return\n  | typeof Catch\n  | typeof Finally\n\ntype Constants = Record<string, SafeExpr | undefined>\n\nexport interface CodeGenOptions {\n  es5?: boolean\n  lines?: boolean\n  ownProperties?: boolean\n}\n\ninterface CGOptions extends CodeGenOptions {\n  _n: \"\\n\" | \"\"\n}\n\nexport class CodeGen {\n  readonly _scope: Scope\n  readonly _extScope: ValueScope\n  readonly _values: ScopeValueSets = {}\n  private readonly _nodes: ParentNode[]\n  private readonly _blockStarts: number[] = []\n  private readonly _constants: Constants = {}\n  private readonly opts: CGOptions\n\n  constructor(extScope: ValueScope, opts: CodeGenOptions = {}) {\n    this.opts = {...opts, _n: opts.lines ? \"\\n\" : \"\"}\n    this._extScope = extScope\n    this._scope = new Scope({parent: extScope})\n    this._nodes = [new Root()]\n  }\n\n  toString(): string {\n    return this._root.render(this.opts)\n  }\n\n  // returns unique name in the internal scope\n  name(prefix: string): Name {\n    return this._scope.name(prefix)\n  }\n\n  // reserves unique name in the external scope\n  scopeName(prefix: string): ValueScopeName {\n    return this._extScope.name(prefix)\n  }\n\n  // reserves unique name in the external scope and assigns value to it\n  scopeValue(prefixOrName: ValueScopeName | string, value: NameValue): Name {\n    const name = this._extScope.value(prefixOrName, value)\n    const vs = this._values[name.prefix] || (this._values[name.prefix] = new Set())\n    vs.add(name)\n    return name\n  }\n\n  getScopeValue(prefix: string, keyOrRef: unknown): ValueScopeName | undefined {\n    return this._extScope.getValue(prefix, keyOrRef)\n  }\n\n  // return code that assigns values in the external scope to the names that are used internally\n  // (same names that were returned by gen.scopeName or gen.scopeValue)\n  scopeRefs(scopeName: Name): Code {\n    return this._extScope.scopeRefs(scopeName, this._values)\n  }\n\n  scopeCode(): Code {\n    return this._extScope.scopeCode(this._values)\n  }\n\n  private _def(\n    varKind: Name,\n    nameOrPrefix: Name | string,\n    rhs?: SafeExpr,\n    constant?: boolean\n  ): Name {\n    const name = this._scope.toName(nameOrPrefix)\n    if (rhs !== undefined && constant) this._constants[name.str] = rhs\n    this._leafNode(new Def(varKind, name, rhs))\n    return name\n  }\n\n  // `const` declaration (`var` in es5 mode)\n  const(nameOrPrefix: Name | string, rhs: SafeExpr, _constant?: boolean): Name {\n    return this._def(varKinds.const, nameOrPrefix, rhs, _constant)\n  }\n\n  // `let` declaration with optional assignment (`var` in es5 mode)\n  let(nameOrPrefix: Name | string, rhs?: SafeExpr, _constant?: boolean): Name {\n    return this._def(varKinds.let, nameOrPrefix, rhs, _constant)\n  }\n\n  // `var` declaration with optional assignment\n  var(nameOrPrefix: Name | string, rhs?: SafeExpr, _constant?: boolean): Name {\n    return this._def(varKinds.var, nameOrPrefix, rhs, _constant)\n  }\n\n  // assignment code\n  assign(lhs: Code, rhs: SafeExpr, sideEffects?: boolean): CodeGen {\n    return this._leafNode(new Assign(lhs, rhs, sideEffects))\n  }\n\n  // `+=` code\n  add(lhs: Code, rhs: SafeExpr): CodeGen {\n    return this._leafNode(new AssignOp(lhs, operators.ADD, rhs))\n  }\n\n  // appends passed SafeExpr to code or executes Block\n  code(c: Block | SafeExpr): CodeGen {\n    if (typeof c == \"function\") c()\n    else if (c !== nil) this._leafNode(new AnyCode(c))\n    return this\n  }\n\n  // returns code for object literal for the passed argument list of key-value pairs\n  object(...keyValues: [Name | string, SafeExpr | string][]): _Code {\n    const code: CodeItem[] = [\"{\"]\n    for (const [key, value] of keyValues) {\n      if (code.length > 1) code.push(\",\")\n      code.push(key)\n      if (key !== value || this.opts.es5) {\n        code.push(\":\")\n        addCodeArg(code, value)\n      }\n    }\n    code.push(\"}\")\n    return new _Code(code)\n  }\n\n  // `if` clause (or statement if `thenBody` and, optionally, `elseBody` are passed)\n  if(condition: Code | boolean, thenBody?: Block, elseBody?: Block): CodeGen {\n    this._blockNode(new If(condition))\n\n    if (thenBody && elseBody) {\n      this.code(thenBody).else().code(elseBody).endIf()\n    } else if (thenBody) {\n      this.code(thenBody).endIf()\n    } else if (elseBody) {\n      throw new Error('CodeGen: \"else\" body without \"then\" body')\n    }\n    return this\n  }\n\n  // `else if` clause - invalid without `if` or after `else` clauses\n  elseIf(condition: Code | boolean): CodeGen {\n    return this._elseNode(new If(condition))\n  }\n\n  // `else` clause - only valid after `if` or `else if` clauses\n  else(): CodeGen {\n    return this._elseNode(new Else())\n  }\n\n  // end `if` statement (needed if gen.if was used only with condition)\n  endIf(): CodeGen {\n    return this._endBlockNode(If, Else)\n  }\n\n  private _for(node: For, forBody?: Block): CodeGen {\n    this._blockNode(node)\n    if (forBody) this.code(forBody).endFor()\n    return this\n  }\n\n  // a generic `for` clause (or statement if `forBody` is passed)\n  for(iteration: Code, forBody?: Block): CodeGen {\n    return this._for(new ForLoop(iteration), forBody)\n  }\n\n  // `for` statement for a range of values\n  forRange(\n    nameOrPrefix: Name | string,\n    from: SafeExpr,\n    to: SafeExpr,\n    forBody: (index: Name) => void,\n    varKind: Code = this.opts.es5 ? varKinds.var : varKinds.let\n  ): CodeGen {\n    const name = this._scope.toName(nameOrPrefix)\n    return this._for(new ForRange(varKind, name, from, to), () => forBody(name))\n  }\n\n  // `for-of` statement (in es5 mode replace with a normal for loop)\n  forOf(\n    nameOrPrefix: Name | string,\n    iterable: Code,\n    forBody: (item: Name) => void,\n    varKind: Code = varKinds.const\n  ): CodeGen {\n    const name = this._scope.toName(nameOrPrefix)\n    if (this.opts.es5) {\n      const arr = iterable instanceof Name ? iterable : this.var(\"_arr\", iterable)\n      return this.forRange(\"_i\", 0, _`${arr}.length`, (i) => {\n        this.var(name, _`${arr}[${i}]`)\n        forBody(name)\n      })\n    }\n    return this._for(new ForIter(\"of\", varKind, name, iterable), () => forBody(name))\n  }\n\n  // `for-in` statement.\n  // With option `ownProperties` replaced with a `for-of` loop for object keys\n  forIn(\n    nameOrPrefix: Name | string,\n    obj: Code,\n    forBody: (item: Name) => void,\n    varKind: Code = this.opts.es5 ? varKinds.var : varKinds.const\n  ): CodeGen {\n    if (this.opts.ownProperties) {\n      return this.forOf(nameOrPrefix, _`Object.keys(${obj})`, forBody)\n    }\n    const name = this._scope.toName(nameOrPrefix)\n    return this._for(new ForIter(\"in\", varKind, name, obj), () => forBody(name))\n  }\n\n  // end `for` loop\n  endFor(): CodeGen {\n    return this._endBlockNode(For)\n  }\n\n  // `label` statement\n  label(label: Name): CodeGen {\n    return this._leafNode(new Label(label))\n  }\n\n  // `break` statement\n  break(label?: Code): CodeGen {\n    return this._leafNode(new Break(label))\n  }\n\n  // `return` statement\n  return(value: Block | SafeExpr): CodeGen {\n    const node = new Return()\n    this._blockNode(node)\n    this.code(value)\n    if (node.nodes.length !== 1) throw new Error('CodeGen: \"return\" should have one node')\n    return this._endBlockNode(Return)\n  }\n\n  // `try` statement\n  try(tryBody: Block, catchCode?: (e: Name) => void, finallyCode?: Block): CodeGen {\n    if (!catchCode && !finallyCode) throw new Error('CodeGen: \"try\" without \"catch\" and \"finally\"')\n    const node = new Try()\n    this._blockNode(node)\n    this.code(tryBody)\n    if (catchCode) {\n      const error = this.name(\"e\")\n      this._currNode = node.catch = new Catch(error)\n      catchCode(error)\n    }\n    if (finallyCode) {\n      this._currNode = node.finally = new Finally()\n      this.code(finallyCode)\n    }\n    return this._endBlockNode(Catch, Finally)\n  }\n\n  // `throw` statement\n  throw(error: Code): CodeGen {\n    return this._leafNode(new Throw(error))\n  }\n\n  // start self-balancing block\n  block(body?: Block, nodeCount?: number): CodeGen {\n    this._blockStarts.push(this._nodes.length)\n    if (body) this.code(body).endBlock(nodeCount)\n    return this\n  }\n\n  // end the current self-balancing block\n  endBlock(nodeCount?: number): CodeGen {\n    const len = this._blockStarts.pop()\n    if (len === undefined) throw new Error(\"CodeGen: not in self-balancing block\")\n    const toClose = this._nodes.length - len\n    if (toClose < 0 || (nodeCount !== undefined && toClose !== nodeCount)) {\n      throw new Error(`CodeGen: wrong number of nodes: ${toClose} vs ${nodeCount} expected`)\n    }\n    this._nodes.length = len\n    return this\n  }\n\n  // `function` heading (or definition if funcBody is passed)\n  func(name: Name, args: Code = nil, async?: boolean, funcBody?: Block): CodeGen {\n    this._blockNode(new Func(name, args, async))\n    if (funcBody) this.code(funcBody).endFunc()\n    return this\n  }\n\n  // end function definition\n  endFunc(): CodeGen {\n    return this._endBlockNode(Func)\n  }\n\n  optimize(n = 1): void {\n    while (n-- > 0) {\n      this._root.optimizeNodes()\n      this._root.optimizeNames(this._root.names, this._constants)\n    }\n  }\n\n  private _leafNode(node: LeafNode): CodeGen {\n    this._currNode.nodes.push(node)\n    return this\n  }\n\n  private _blockNode(node: StartBlockNode): void {\n    this._currNode.nodes.push(node)\n    this._nodes.push(node)\n  }\n\n  private _endBlockNode(N1: EndBlockNodeType, N2?: EndBlockNodeType): CodeGen {\n    const n = this._currNode\n    if (n instanceof N1 || (N2 && n instanceof N2)) {\n      this._nodes.pop()\n      return this\n    }\n    throw new Error(`CodeGen: not in block \"${N2 ? `${N1.kind}/${N2.kind}` : N1.kind}\"`)\n  }\n\n  private _elseNode(node: If | Else): CodeGen {\n    const n = this._currNode\n    if (!(n instanceof If)) {\n      throw new Error('CodeGen: \"else\" without \"if\"')\n    }\n    this._currNode = n.else = node\n    return this\n  }\n\n  private get _root(): Root {\n    return this._nodes[0] as Root\n  }\n\n  private get _currNode(): ParentNode {\n    const ns = this._nodes\n    return ns[ns.length - 1]\n  }\n\n  private set _currNode(node: ParentNode) {\n    const ns = this._nodes\n    ns[ns.length - 1] = node\n  }\n\n  // get nodeCount(): number {\n  //   return this._root.count\n  // }\n}\n\nfunction addNames(names: UsedNames, from: UsedNames): UsedNames {\n  for (const n in from) names[n] = (names[n] || 0) + (from[n] || 0)\n  return names\n}\n\nfunction addExprNames(names: UsedNames, from: SafeExpr): UsedNames {\n  return from instanceof _CodeOrName ? addNames(names, from.names) : names\n}\n\nfunction optimizeExpr<T extends SafeExpr | Code>(expr: T, names: UsedNames, constants: Constants): T\nfunction optimizeExpr(expr: SafeExpr, names: UsedNames, constants: Constants): SafeExpr {\n  if (expr instanceof Name) return replaceName(expr)\n  if (!canOptimize(expr)) return expr\n  return new _Code(\n    expr._items.reduce((items: CodeItem[], c: SafeExpr | string) => {\n      if (c instanceof Name) c = replaceName(c)\n      if (c instanceof _Code) items.push(...c._items)\n      else items.push(c)\n      return items\n    }, [])\n  )\n\n  function replaceName(n: Name): SafeExpr {\n    const c = constants[n.str]\n    if (c === undefined || names[n.str] !== 1) return n\n    delete names[n.str]\n    return c\n  }\n\n  function canOptimize(e: SafeExpr): e is _Code {\n    return (\n      e instanceof _Code &&\n      e._items.some(\n        (c) => c instanceof Name && names[c.str] === 1 && constants[c.str] !== undefined\n      )\n    )\n  }\n}\n\nfunction subtractNames(names: UsedNames, from: UsedNames): void {\n  for (const n in from) names[n] = (names[n] || 0) - (from[n] || 0)\n}\n\nexport function not<T extends Code | SafeExpr>(x: T): T\nexport function not(x: Code | SafeExpr): Code | SafeExpr {\n  return typeof x == \"boolean\" || typeof x == \"number\" || x === null ? !x : _`!${par(x)}`\n}\n\nconst andCode = mappend(operators.AND)\n\n// boolean AND (&&) expression with the passed arguments\nexport function and(...args: Code[]): Code {\n  return args.reduce(andCode)\n}\n\nconst orCode = mappend(operators.OR)\n\n// boolean OR (||) expression with the passed arguments\nexport function or(...args: Code[]): Code {\n  return args.reduce(orCode)\n}\n\ntype MAppend = (x: Code, y: Code) => Code\n\nfunction mappend(op: Code): MAppend {\n  return (x, y) => (x === nil ? y : y === nil ? x : _`${par(x)} ${op} ${par(y)}`)\n}\n\nfunction par(x: Code): Code {\n  return x instanceof Name ? x : _`(${x})`\n}\n", "import type {AnySchema, EvaluatedProperties, EvaluatedItems} from \"../types\"\nimport type {SchemaCxt, SchemaObjCxt} from \".\"\nimport {_, getProperty, Code, Name, CodeGen} from \"./codegen\"\nimport {_Code} from \"./codegen/code\"\nimport type {Rule, ValidationRules} from \"./rules\"\n\n// TODO refactor to use Set\nexport function toHash<T extends string = string>(arr: T[]): {[K in T]?: true} {\n  const hash: {[K in T]?: true} = {}\n  for (const item of arr) hash[item] = true\n  return hash\n}\n\nexport function alwaysValidSchema(it: SchemaCxt, schema: AnySchema): boolean | void {\n  if (typeof schema == \"boolean\") return schema\n  if (Object.keys(schema).length === 0) return true\n  checkUnknownRules(it, schema)\n  return !schemaHasRules(schema, it.self.RULES.all)\n}\n\nexport function checkUnknownRules(it: SchemaCxt, schema: AnySchema = it.schema): void {\n  const {opts, self} = it\n  if (!opts.strictSchema) return\n  if (typeof schema === \"boolean\") return\n  const rules = self.RULES.keywords\n  for (const key in schema) {\n    if (!rules[key]) checkStrictMode(it, `unknown keyword: \"${key}\"`)\n  }\n}\n\nexport function schemaHasRules(\n  schema: AnySchema,\n  rules: {[Key in string]?: boolean | Rule}\n): boolean {\n  if (typeof schema == \"boolean\") return !schema\n  for (const key in schema) if (rules[key]) return true\n  return false\n}\n\nexport function schemaHasRulesButRef(schema: AnySchema, RULES: ValidationRules): boolean {\n  if (typeof schema == \"boolean\") return !schema\n  for (const key in schema) if (key !== \"$ref\" && RULES.all[key]) return true\n  return false\n}\n\nexport function schemaRefOrVal(\n  {topSchemaRef, schemaPath}: SchemaObjCxt,\n  schema: unknown,\n  keyword: string,\n  $data?: string | false\n): Code | number | boolean {\n  if (!$data) {\n    if (typeof schema == \"number\" || typeof schema == \"boolean\") return schema\n    if (typeof schema == \"string\") return _`${schema}`\n  }\n  return _`${topSchemaRef}${schemaPath}${getProperty(keyword)}`\n}\n\nexport function unescapeFragment(str: string): string {\n  return unescapeJsonPointer(decodeURIComponent(str))\n}\n\nexport function escapeFragment(str: string | number): string {\n  return encodeURIComponent(escapeJsonPointer(str))\n}\n\nexport function escapeJsonPointer(str: string | number): string {\n  if (typeof str == \"number\") return `${str}`\n  return str.replace(/~/g, \"~0\").replace(/\\//g, \"~1\")\n}\n\nexport function unescapeJsonPointer(str: string): string {\n  return str.replace(/~1/g, \"/\").replace(/~0/g, \"~\")\n}\n\nexport function eachItem<T>(xs: T | T[], f: (x: T) => void): void {\n  if (Array.isArray(xs)) {\n    for (const x of xs) f(x)\n  } else {\n    f(xs)\n  }\n}\n\ntype SomeEvaluated = EvaluatedProperties | EvaluatedItems\n\ntype MergeEvaluatedFunc<T extends SomeEvaluated> = (\n  gen: CodeGen,\n  from: Name | T,\n  to: Name | Exclude<T, true> | undefined,\n  toName?: typeof Name\n) => Name | T\n\ninterface MakeMergeFuncArgs<T extends SomeEvaluated> {\n  mergeNames: (gen: CodeGen, from: Name, to: Name) => void\n  mergeToName: (gen: CodeGen, from: T, to: Name) => void\n  mergeValues: (from: T, to: Exclude<T, true>) => T\n  resultToName: (gen: CodeGen, res?: T) => Name\n}\n\nfunction makeMergeEvaluated<T extends SomeEvaluated>({\n  mergeNames,\n  mergeToName,\n  mergeValues,\n  resultToName,\n}: MakeMergeFuncArgs<T>): MergeEvaluatedFunc<T> {\n  return (gen, from, to, toName) => {\n    const res =\n      to === undefined\n        ? from\n        : to instanceof Name\n        ? (from instanceof Name ? mergeNames(gen, from, to) : mergeToName(gen, from, to), to)\n        : from instanceof Name\n        ? (mergeToName(gen, to, from), from)\n        : mergeValues(from, to)\n    return toName === Name && !(res instanceof Name) ? resultToName(gen, res) : res\n  }\n}\n\ninterface MergeEvaluated {\n  props: MergeEvaluatedFunc<EvaluatedProperties>\n  items: MergeEvaluatedFunc<EvaluatedItems>\n}\n\nexport const mergeEvaluated: MergeEvaluated = {\n  props: makeMergeEvaluated({\n    mergeNames: (gen, from, to) =>\n      gen.if(_`${to} !== true && ${from} !== undefined`, () => {\n        gen.if(\n          _`${from} === true`,\n          () => gen.assign(to, true),\n          () => gen.assign(to, _`${to} || {}`).code(_`Object.assign(${to}, ${from})`)\n        )\n      }),\n    mergeToName: (gen, from, to) =>\n      gen.if(_`${to} !== true`, () => {\n        if (from === true) {\n          gen.assign(to, true)\n        } else {\n          gen.assign(to, _`${to} || {}`)\n          setEvaluated(gen, to, from)\n        }\n      }),\n    mergeValues: (from, to) => (from === true ? true : {...from, ...to}),\n    resultToName: evaluatedPropsToName,\n  }),\n  items: makeMergeEvaluated({\n    mergeNames: (gen, from, to) =>\n      gen.if(_`${to} !== true && ${from} !== undefined`, () =>\n        gen.assign(to, _`${from} === true ? true : ${to} > ${from} ? ${to} : ${from}`)\n      ),\n    mergeToName: (gen, from, to) =>\n      gen.if(_`${to} !== true`, () =>\n        gen.assign(to, from === true ? true : _`${to} > ${from} ? ${to} : ${from}`)\n      ),\n    mergeValues: (from, to) => (from === true ? true : Math.max(from, to)),\n    resultToName: (gen, items) => gen.var(\"items\", items),\n  }),\n}\n\nexport function evaluatedPropsToName(gen: CodeGen, ps?: EvaluatedProperties): Name {\n  if (ps === true) return gen.var(\"props\", true)\n  const props = gen.var(\"props\", _`{}`)\n  if (ps !== undefined) setEvaluated(gen, props, ps)\n  return props\n}\n\nexport function setEvaluated(gen: CodeGen, props: Name, ps: {[K in string]?: true}): void {\n  Object.keys(ps).forEach((p) => gen.assign(_`${props}${getProperty(p)}`, true))\n}\n\nconst snippets: {[S in string]?: _Code} = {}\n\nexport function useFunc(gen: CodeGen, f: {code: string}): Name {\n  return gen.scopeValue(\"func\", {\n    ref: f,\n    code: snippets[f.code] || (snippets[f.code] = new _Code(f.code)),\n  })\n}\n\nexport enum Type {\n  Num,\n  Str,\n}\n\nexport function getErrorPath(\n  dataProp: Name | string | number,\n  dataPropType?: Type,\n  jsPropertySyntax?: boolean\n): Code | string {\n  // let path\n  if (dataProp instanceof Name) {\n    const isNumber = dataPropType === Type.Num\n    return jsPropertySyntax\n      ? isNumber\n        ? _`\"[\" + ${dataProp} + \"]\"`\n        : _`\"['\" + ${dataProp} + \"']\"`\n      : isNumber\n      ? _`\"/\" + ${dataProp}`\n      : _`\"/\" + ${dataProp}.replace(/~/g, \"~0\").replace(/\\\\//g, \"~1\")` // TODO maybe use global escapePointer\n  }\n  return jsPropertySyntax ? getProperty(dataProp).toString() : \"/\" + escapeJsonPointer(dataProp)\n}\n\nexport function checkStrictMode(\n  it: SchemaCxt,\n  msg: string,\n  mode: boolean | \"log\" = it.opts.strictSchema\n): void {\n  if (!mode) return\n  msg = `strict mode: ${msg}`\n  if (mode === true) throw new Error(msg)\n  it.self.logger.warn(msg)\n}\n", "import {Name} from \"./codegen\"\n\nconst names = {\n  // validation function arguments\n  data: new Name(\"data\"), // data passed to validation function\n  // args passed from referencing schema\n  valCxt: new Name(\"valCxt\"), // validation/data context - should not be used directly, it is destructured to the names below\n  instancePath: new Name(\"instancePath\"),\n  parentData: new Name(\"parentData\"),\n  parentDataProperty: new Name(\"parentDataProperty\"),\n  rootData: new Name(\"rootData\"), // root data - same as the data passed to the first/top validation function\n  dynamicAnchors: new Name(\"dynamicAnchors\"), // used to support recursiveRef and dynamicRef\n  // function scoped variables\n  vErrors: new Name(\"vErrors\"), // null or array of validation errors\n  errors: new Name(\"errors\"), // counter of validation errors\n  this: new Name(\"this\"),\n  // \"globals\"\n  self: new Name(\"self\"),\n  scope: new Name(\"scope\"),\n  // JTD serialize/parse name for JSON string and position\n  json: new Name(\"json\"),\n  jsonPos: new Name(\"jsonPos\"),\n  jsonLen: new Name(\"jsonLen\"),\n  jsonPart: new Name(\"jsonPart\"),\n}\n\nexport default names\n", "import type {KeywordErrorCxt, KeywordErrorDefinition} from \"../types\"\nimport type {SchemaCxt} from \"./index\"\nimport {CodeGen, _, str, strConcat, Code, Name} from \"./codegen\"\nimport {SafeExpr} from \"./codegen/code\"\nimport {getErrorPath, Type} from \"./util\"\nimport N from \"./names\"\n\nexport const keywordError: KeywordErrorDefinition = {\n  message: ({keyword}) => str`must pass \"${keyword}\" keyword validation`,\n}\n\nexport const keyword$DataError: KeywordErrorDefinition = {\n  message: ({keyword, schemaType}) =>\n    schemaType\n      ? str`\"${keyword}\" keyword must be ${schemaType} ($data)`\n      : str`\"${keyword}\" keyword is invalid ($data)`,\n}\n\nexport interface ErrorPaths {\n  instancePath?: Code\n  schemaPath?: string\n  parentSchema?: boolean\n}\n\nexport function reportError(\n  cxt: KeywordErrorCxt,\n  error: KeywordErrorDefinition = keywordError,\n  errorPaths?: ErrorPaths,\n  overrideAllErrors?: boolean\n): void {\n  const {it} = cxt\n  const {gen, compositeRule, allErrors} = it\n  const errObj = errorObjectCode(cxt, error, errorPaths)\n  if (overrideAllErrors ?? (compositeRule || allErrors)) {\n    addError(gen, errObj)\n  } else {\n    returnErrors(it, _`[${errObj}]`)\n  }\n}\n\nexport function reportExtraError(\n  cxt: KeywordErrorCxt,\n  error: KeywordErrorDefinition = keywordError,\n  errorPaths?: ErrorPaths\n): void {\n  const {it} = cxt\n  const {gen, compositeRule, allErrors} = it\n  const errObj = errorObjectCode(cxt, error, errorPaths)\n  addError(gen, errObj)\n  if (!(compositeRule || allErrors)) {\n    returnErrors(it, N.vErrors)\n  }\n}\n\nexport function resetErrorsCount(gen: CodeGen, errsCount: Name): void {\n  gen.assign(N.errors, errsCount)\n  gen.if(_`${N.vErrors} !== null`, () =>\n    gen.if(\n      errsCount,\n      () => gen.assign(_`${N.vErrors}.length`, errsCount),\n      () => gen.assign(N.vErrors, null)\n    )\n  )\n}\n\nexport function extendErrors({\n  gen,\n  keyword,\n  schemaValue,\n  data,\n  errsCount,\n  it,\n}: KeywordErrorCxt): void {\n  /* istanbul ignore if */\n  if (errsCount === undefined) throw new Error(\"ajv implementation error\")\n  const err = gen.name(\"err\")\n  gen.forRange(\"i\", errsCount, N.errors, (i) => {\n    gen.const(err, _`${N.vErrors}[${i}]`)\n    gen.if(_`${err}.instancePath === undefined`, () =>\n      gen.assign(_`${err}.instancePath`, strConcat(N.instancePath, it.errorPath))\n    )\n    gen.assign(_`${err}.schemaPath`, str`${it.errSchemaPath}/${keyword}`)\n    if (it.opts.verbose) {\n      gen.assign(_`${err}.schema`, schemaValue)\n      gen.assign(_`${err}.data`, data)\n    }\n  })\n}\n\nfunction addError(gen: CodeGen, errObj: Code): void {\n  const err = gen.const(\"err\", errObj)\n  gen.if(\n    _`${N.vErrors} === null`,\n    () => gen.assign(N.vErrors, _`[${err}]`),\n    _`${N.vErrors}.push(${err})`\n  )\n  gen.code(_`${N.errors}++`)\n}\n\nfunction returnErrors(it: SchemaCxt, errs: Code): void {\n  const {gen, validateName, schemaEnv} = it\n  if (schemaEnv.$async) {\n    gen.throw(_`new ${it.ValidationError as Name}(${errs})`)\n  } else {\n    gen.assign(_`${validateName}.errors`, errs)\n    gen.return(false)\n  }\n}\n\nconst E = {\n  keyword: new Name(\"keyword\"),\n  schemaPath: new Name(\"schemaPath\"), // also used in JTD errors\n  params: new Name(\"params\"),\n  propertyName: new Name(\"propertyName\"),\n  message: new Name(\"message\"),\n  schema: new Name(\"schema\"),\n  parentSchema: new Name(\"parentSchema\"),\n}\n\nfunction errorObjectCode(\n  cxt: KeywordErrorCxt,\n  error: KeywordErrorDefinition,\n  errorPaths?: ErrorPaths\n): Code {\n  const {createErrors} = cxt.it\n  if (createErrors === false) return _`{}`\n  return errorObject(cxt, error, errorPaths)\n}\n\nfunction errorObject(\n  cxt: KeywordErrorCxt,\n  error: KeywordErrorDefinition,\n  errorPaths: ErrorPaths = {}\n): Code {\n  const {gen, it} = cxt\n  const keyValues: [Name, SafeExpr | string][] = [\n    errorInstancePath(it, errorPaths),\n    errorSchemaPath(cxt, errorPaths),\n  ]\n  extraErrorProps(cxt, error, keyValues)\n  return gen.object(...keyValues)\n}\n\nfunction errorInstancePath({errorPath}: SchemaCxt, {instancePath}: ErrorPaths): [Name, Code] {\n  const instPath = instancePath\n    ? str`${errorPath}${getErrorPath(instancePath, Type.Str)}`\n    : errorPath\n  return [N.instancePath, strConcat(N.instancePath, instPath)]\n}\n\nfunction errorSchemaPath(\n  {keyword, it: {errSchemaPath}}: KeywordErrorCxt,\n  {schemaPath, parentSchema}: ErrorPaths\n): [Name, string | Code] {\n  let schPath = parentSchema ? errSchemaPath : str`${errSchemaPath}/${keyword}`\n  if (schemaPath) {\n    schPath = str`${schPath}${getErrorPath(schemaPath, Type.Str)}`\n  }\n  return [E.schemaPath, schPath]\n}\n\nfunction extraErrorProps(\n  cxt: KeywordErrorCxt,\n  {params, message}: KeywordErrorDefinition,\n  keyValues: [Name, SafeExpr | string][]\n): void {\n  const {keyword, data, schemaValue, it} = cxt\n  const {opts, propertyName, topSchemaRef, schemaPath} = it\n  keyValues.push(\n    [E.keyword, keyword],\n    [E.params, typeof params == \"function\" ? params(cxt) : params || _`{}`]\n  )\n  if (opts.messages) {\n    keyValues.push([E.message, typeof message == \"function\" ? message(cxt) : message])\n  }\n  if (opts.verbose) {\n    keyValues.push(\n      [E.schema, schemaValue],\n      [E.parentSchema, _`${topSchemaRef}${schemaPath}`],\n      [N.data, data]\n    )\n  }\n  if (propertyName) keyValues.push([E.propertyName, propertyName])\n}\n", "import type {KeywordErrorDefinition, KeywordErrorCxt} from \"../../types\"\nimport type {SchemaCxt} from \"..\"\nimport {reportError} from \"../errors\"\nimport {_, Name} from \"../codegen\"\nimport N from \"../names\"\n\nconst boolError: KeywordErrorDefinition = {\n  message: \"boolean schema is false\",\n}\n\nexport function topBoolOrEmptySchema(it: SchemaCxt): void {\n  const {gen, schema, validateName} = it\n  if (schema === false) {\n    falseSchemaError(it, false)\n  } else if (typeof schema == \"object\" && schema.$async === true) {\n    gen.return(N.data)\n  } else {\n    gen.assign(_`${validateName}.errors`, null)\n    gen.return(true)\n  }\n}\n\nexport function boolOrEmptySchema(it: SchemaCxt, valid: Name): void {\n  const {gen, schema} = it\n  if (schema === false) {\n    gen.var(valid, false) // TODO var\n    falseSchemaError(it)\n  } else {\n    gen.var(valid, true) // TODO var\n  }\n}\n\nfunction falseSchemaError(it: SchemaCxt, overrideAllErrors?: boolean): void {\n  const {gen, data} = it\n  // TODO maybe some other interface should be used for non-keyword validation errors...\n  const cxt: KeywordErrorCxt = {\n    gen,\n    keyword: \"false schema\",\n    data,\n    schema: false,\n    schemaCode: false,\n    schemaValue: false,\n    params: {},\n    it,\n  }\n  reportError(cxt, boolError, undefined, overrideAllErrors)\n}\n", "import type {AddedKeywordDefinition} from \"../types\"\n\nconst _jsonTypes = [\"string\", \"number\", \"integer\", \"boolean\", \"null\", \"object\", \"array\"] as const\n\nexport type JSONType = (typeof _jsonTypes)[number]\n\nconst jsonTypes: Set<string> = new Set(_jsonTypes)\n\nexport function isJSONType(x: unknown): x is JSONType {\n  return typeof x == \"string\" && jsonTypes.has(x)\n}\n\ntype ValidationTypes = {\n  [K in JSONType]: boolean | RuleGroup | undefined\n}\n\nexport interface ValidationRules {\n  rules: RuleGroup[]\n  post: RuleGroup\n  all: {[Key in string]?: boolean | Rule} // rules that have to be validated\n  keywords: {[Key in string]?: boolean} // all known keywords (superset of \"all\")\n  types: ValidationTypes\n}\n\nexport interface RuleGroup {\n  type?: JSONType\n  rules: Rule[]\n}\n\n// This interface wraps KeywordDefinition because definition can have multiple keywords\nexport interface Rule {\n  keyword: string\n  definition: AddedKeywordDefinition\n}\n\nexport function getRules(): ValidationRules {\n  const groups: Record<\"number\" | \"string\" | \"array\" | \"object\", RuleGroup> = {\n    number: {type: \"number\", rules: []},\n    string: {type: \"string\", rules: []},\n    array: {type: \"array\", rules: []},\n    object: {type: \"object\", rules: []},\n  }\n  return {\n    types: {...groups, integer: true, boolean: true, null: true},\n    rules: [{rules: []}, groups.number, groups.string, groups.array, groups.object],\n    post: {rules: []},\n    all: {},\n    keywords: {},\n  }\n}\n", "import type {AnySchemaObject} from \"../../types\"\nimport type {SchemaObjCxt} from \"..\"\nimport type {JSONType, RuleGroup, Rule} from \"../rules\"\n\nexport function schemaHasRulesForType(\n  {schema, self}: SchemaObjCxt,\n  type: JSONType\n): boolean | undefined {\n  const group = self.RULES.types[type]\n  return group && group !== true && shouldUseGroup(schema, group)\n}\n\nexport function shouldUseGroup(schema: AnySchemaObject, group: RuleGroup): boolean {\n  return group.rules.some((rule) => shouldUseRule(schema, rule))\n}\n\nexport function shouldUseRule(schema: AnySchemaObject, rule: Rule): boolean | undefined {\n  return (\n    schema[rule.keyword] !== undefined ||\n    rule.definition.implements?.some((kwd) => schema[kwd] !== undefined)\n  )\n}\n", "import type {\n  KeywordErrorDefinition,\n  KeywordErrorCxt,\n  ErrorObject,\n  AnySchemaObject,\n} from \"../../types\"\nimport type {SchemaObjCxt} from \"..\"\nimport {isJSONType, JSONType} from \"../rules\"\nimport {schemaHasRulesForType} from \"./applicability\"\nimport {reportError} from \"../errors\"\nimport {_, nil, and, not, operators, Code, Name} from \"../codegen\"\nimport {toHash, schemaRefOrVal} from \"../util\"\n\nexport enum DataType {\n  Correct,\n  Wrong,\n}\n\nexport function getSchemaTypes(schema: AnySchemaObject): JSONType[] {\n  const types = getJSONTypes(schema.type)\n  const hasNull = types.includes(\"null\")\n  if (hasNull) {\n    if (schema.nullable === false) throw new Error(\"type: null contradicts nullable: false\")\n  } else {\n    if (!types.length && schema.nullable !== undefined) {\n      throw new Error('\"nullable\" cannot be used without \"type\"')\n    }\n    if (schema.nullable === true) types.push(\"null\")\n  }\n  return types\n}\n\n// eslint-disable-next-line @typescript-eslint/no-redundant-type-constituents\nexport function getJSONTypes(ts: unknown | unknown[]): JSONType[] {\n  const types: unknown[] = Array.isArray(ts) ? ts : ts ? [ts] : []\n  if (types.every(isJSONType)) return types\n  throw new Error(\"type must be JSONType or JSONType[]: \" + types.join(\",\"))\n}\n\nexport function coerceAndCheckDataType(it: SchemaObjCxt, types: JSONType[]): boolean {\n  const {gen, data, opts} = it\n  const coerceTo = coerceToTypes(types, opts.coerceTypes)\n  const checkTypes =\n    types.length > 0 &&\n    !(coerceTo.length === 0 && types.length === 1 && schemaHasRulesForType(it, types[0]))\n  if (checkTypes) {\n    const wrongType = checkDataTypes(types, data, opts.strictNumbers, DataType.Wrong)\n    gen.if(wrongType, () => {\n      if (coerceTo.length) coerceData(it, types, coerceTo)\n      else reportTypeError(it)\n    })\n  }\n  return checkTypes\n}\n\nconst COERCIBLE: Set<JSONType> = new Set([\"string\", \"number\", \"integer\", \"boolean\", \"null\"])\nfunction coerceToTypes(types: JSONType[], coerceTypes?: boolean | \"array\"): JSONType[] {\n  return coerceTypes\n    ? types.filter((t) => COERCIBLE.has(t) || (coerceTypes === \"array\" && t === \"array\"))\n    : []\n}\n\nfunction coerceData(it: SchemaObjCxt, types: JSONType[], coerceTo: JSONType[]): void {\n  const {gen, data, opts} = it\n  const dataType = gen.let(\"dataType\", _`typeof ${data}`)\n  const coerced = gen.let(\"coerced\", _`undefined`)\n  if (opts.coerceTypes === \"array\") {\n    gen.if(_`${dataType} == 'object' && Array.isArray(${data}) && ${data}.length == 1`, () =>\n      gen\n        .assign(data, _`${data}[0]`)\n        .assign(dataType, _`typeof ${data}`)\n        .if(checkDataTypes(types, data, opts.strictNumbers), () => gen.assign(coerced, data))\n    )\n  }\n  gen.if(_`${coerced} !== undefined`)\n  for (const t of coerceTo) {\n    if (COERCIBLE.has(t) || (t === \"array\" && opts.coerceTypes === \"array\")) {\n      coerceSpecificType(t)\n    }\n  }\n  gen.else()\n  reportTypeError(it)\n  gen.endIf()\n\n  gen.if(_`${coerced} !== undefined`, () => {\n    gen.assign(data, coerced)\n    assignParentData(it, coerced)\n  })\n\n  function coerceSpecificType(t: string): void {\n    switch (t) {\n      case \"string\":\n        gen\n          .elseIf(_`${dataType} == \"number\" || ${dataType} == \"boolean\"`)\n          .assign(coerced, _`\"\" + ${data}`)\n          .elseIf(_`${data} === null`)\n          .assign(coerced, _`\"\"`)\n        return\n      case \"number\":\n        gen\n          .elseIf(\n            _`${dataType} == \"boolean\" || ${data} === null\n              || (${dataType} == \"string\" && ${data} && ${data} == +${data})`\n          )\n          .assign(coerced, _`+${data}`)\n        return\n      case \"integer\":\n        gen\n          .elseIf(\n            _`${dataType} === \"boolean\" || ${data} === null\n              || (${dataType} === \"string\" && ${data} && ${data} == +${data} && !(${data} % 1))`\n          )\n          .assign(coerced, _`+${data}`)\n        return\n      case \"boolean\":\n        gen\n          .elseIf(_`${data} === \"false\" || ${data} === 0 || ${data} === null`)\n          .assign(coerced, false)\n          .elseIf(_`${data} === \"true\" || ${data} === 1`)\n          .assign(coerced, true)\n        return\n      case \"null\":\n        gen.elseIf(_`${data} === \"\" || ${data} === 0 || ${data} === false`)\n        gen.assign(coerced, null)\n        return\n\n      case \"array\":\n        gen\n          .elseIf(\n            _`${dataType} === \"string\" || ${dataType} === \"number\"\n              || ${dataType} === \"boolean\" || ${data} === null`\n          )\n          .assign(coerced, _`[${data}]`)\n    }\n  }\n}\n\nfunction assignParentData({gen, parentData, parentDataProperty}: SchemaObjCxt, expr: Name): void {\n  // TODO use gen.property\n  gen.if(_`${parentData} !== undefined`, () =>\n    gen.assign(_`${parentData}[${parentDataProperty}]`, expr)\n  )\n}\n\nexport function checkDataType(\n  dataType: JSONType,\n  data: Name,\n  strictNums?: boolean | \"log\",\n  correct = DataType.Correct\n): Code {\n  const EQ = correct === DataType.Correct ? operators.EQ : operators.NEQ\n  let cond: Code\n  switch (dataType) {\n    case \"null\":\n      return _`${data} ${EQ} null`\n    case \"array\":\n      cond = _`Array.isArray(${data})`\n      break\n    case \"object\":\n      cond = _`${data} && typeof ${data} == \"object\" && !Array.isArray(${data})`\n      break\n    case \"integer\":\n      cond = numCond(_`!(${data} % 1) && !isNaN(${data})`)\n      break\n    case \"number\":\n      cond = numCond()\n      break\n    default:\n      return _`typeof ${data} ${EQ} ${dataType}`\n  }\n  return correct === DataType.Correct ? cond : not(cond)\n\n  function numCond(_cond: Code = nil): Code {\n    return and(_`typeof ${data} == \"number\"`, _cond, strictNums ? _`isFinite(${data})` : nil)\n  }\n}\n\nexport function checkDataTypes(\n  dataTypes: JSONType[],\n  data: Name,\n  strictNums?: boolean | \"log\",\n  correct?: DataType\n): Code {\n  if (dataTypes.length === 1) {\n    return checkDataType(dataTypes[0], data, strictNums, correct)\n  }\n  let cond: Code\n  const types = toHash(dataTypes)\n  if (types.array && types.object) {\n    const notObj = _`typeof ${data} != \"object\"`\n    cond = types.null ? notObj : _`!${data} || ${notObj}`\n    delete types.null\n    delete types.array\n    delete types.object\n  } else {\n    cond = nil\n  }\n  if (types.number) delete types.integer\n  for (const t in types) cond = and(cond, checkDataType(t as JSONType, data, strictNums, correct))\n  return cond\n}\n\nexport type TypeError = ErrorObject<\"type\", {type: string}>\n\nconst typeError: KeywordErrorDefinition = {\n  message: ({schema}) => `must be ${schema}`,\n  params: ({schema, schemaValue}) =>\n    typeof schema == \"string\" ? _`{type: ${schema}}` : _`{type: ${schemaValue}}`,\n}\n\nexport function reportTypeError(it: SchemaObjCxt): void {\n  const cxt = getTypeErrorContext(it)\n  reportError(cxt, typeError)\n}\n\nfunction getTypeErrorContext(it: SchemaObjCxt): KeywordErrorCxt {\n  const {gen, data, schema} = it\n  const schemaCode = schemaRefOrVal(it, schema, \"type\")\n  return {\n    gen,\n    keyword: \"type\",\n    data,\n    schema: schema.type,\n    schemaCode,\n    schemaValue: schemaCode,\n    parentSchema: schema,\n    params: {},\n    it,\n  }\n}\n", "import type {SchemaObjCxt} from \"..\"\nimport {_, getProperty, stringify} from \"../codegen\"\nimport {checkStrictMode} from \"../util\"\n\nexport function assignDefaults(it: SchemaObjCxt, ty?: string): void {\n  const {properties, items} = it.schema\n  if (ty === \"object\" && properties) {\n    for (const key in properties) {\n      assignDefault(it, key, properties[key].default)\n    }\n  } else if (ty === \"array\" && Array.isArray(items)) {\n    items.forEach((sch, i: number) => assignDefault(it, i, sch.default))\n  }\n}\n\nfunction assignDefault(it: SchemaObjCxt, prop: string | number, defaultValue: unknown): void {\n  const {gen, compositeRule, data, opts} = it\n  if (defaultValue === undefined) return\n  const childData = _`${data}${getProperty(prop)}`\n  if (compositeRule) {\n    checkStrictMode(it, `default is ignored for: ${childData}`)\n    return\n  }\n\n  let condition = _`${childData} === undefined`\n  if (opts.useDefaults === \"empty\") {\n    condition = _`${condition} || ${childData} === null || ${childData} === \"\"`\n  }\n  // `${childData} === undefined` +\n  // (opts.useDefaults === \"empty\" ? ` || ${childData} === null || ${childData} === \"\"` : \"\")\n  gen.if(condition, _`${childData} = ${stringify(defaultValue)}`)\n}\n", "import type {AnySchema, SchemaMap} from \"../types\"\nimport type {SchemaCxt} from \"../compile\"\nimport type {KeywordCxt} from \"../compile/validate\"\nimport {CodeGen, _, and, or, not, nil, strConcat, getProperty, Code, Name} from \"../compile/codegen\"\nimport {alwaysValidSchema, Type} from \"../compile/util\"\nimport N from \"../compile/names\"\nimport {useFunc} from \"../compile/util\"\nexport function checkReportMissingProp(cxt: KeywordCxt, prop: string): void {\n  const {gen, data, it} = cxt\n  gen.if(noPropertyInData(gen, data, prop, it.opts.ownProperties), () => {\n    cxt.setParams({missingProperty: _`${prop}`}, true)\n    cxt.error()\n  })\n}\n\nexport function checkMissingProp(\n  {gen, data, it: {opts}}: KeywordCxt,\n  properties: string[],\n  missing: Name\n): Code {\n  return or(\n    ...properties.map((prop) =>\n      and(noPropertyInData(gen, data, prop, opts.ownProperties), _`${missing} = ${prop}`)\n    )\n  )\n}\n\nexport function reportMissingProp(cxt: KeywordCxt, missing: Name): void {\n  cxt.setParams({missingProperty: missing}, true)\n  cxt.error()\n}\n\nexport function hasPropFunc(gen: CodeGen): Name {\n  return gen.scopeValue(\"func\", {\n    // eslint-disable-next-line @typescript-eslint/unbound-method\n    ref: Object.prototype.hasOwnProperty,\n    code: _`Object.prototype.hasOwnProperty`,\n  })\n}\n\nexport function isOwnProperty(gen: CodeGen, data: Name, property: Name | string): Code {\n  return _`${hasPropFunc(gen)}.call(${data}, ${property})`\n}\n\nexport function propertyInData(\n  gen: CodeGen,\n  data: Name,\n  property: Name | string,\n  ownProperties?: boolean\n): Code {\n  const cond = _`${data}${getProperty(property)} !== undefined`\n  return ownProperties ? _`${cond} && ${isOwnProperty(gen, data, property)}` : cond\n}\n\nexport function noPropertyInData(\n  gen: CodeGen,\n  data: Name,\n  property: Name | string,\n  ownProperties?: boolean\n): Code {\n  const cond = _`${data}${getProperty(property)} === undefined`\n  return ownProperties ? or(cond, not(isOwnProperty(gen, data, property))) : cond\n}\n\nexport function allSchemaProperties(schemaMap?: SchemaMap): string[] {\n  return schemaMap ? Object.keys(schemaMap).filter((p) => p !== \"__proto__\") : []\n}\n\nexport function schemaProperties(it: SchemaCxt, schemaMap: SchemaMap): string[] {\n  return allSchemaProperties(schemaMap).filter(\n    (p) => !alwaysValidSchema(it, schemaMap[p] as AnySchema)\n  )\n}\n\nexport function callValidateCode(\n  {schemaCode, data, it: {gen, topSchemaRef, schemaPath, errorPath}, it}: KeywordCxt,\n  func: Code,\n  context: Code,\n  passSchema?: boolean\n): Code {\n  const dataAndSchema = passSchema ? _`${schemaCode}, ${data}, ${topSchemaRef}${schemaPath}` : data\n  const valCxt: [Name, Code | number][] = [\n    [N.instancePath, strConcat(N.instancePath, errorPath)],\n    [N.parentData, it.parentData],\n    [N.parentDataProperty, it.parentDataProperty],\n    [N.rootData, N.rootData],\n  ]\n  if (it.opts.dynamicRef) valCxt.push([N.dynamicAnchors, N.dynamicAnchors])\n  const args = _`${dataAndSchema}, ${gen.object(...valCxt)}`\n  return context !== nil ? _`${func}.call(${context}, ${args})` : _`${func}(${args})`\n}\n\nconst newRegExp = _`new RegExp`\n\nexport function usePattern({gen, it: {opts}}: KeywordCxt, pattern: string): Name {\n  const u = opts.unicodeRegExp ? \"u\" : \"\"\n  const {regExp} = opts.code\n  const rx = regExp(pattern, u)\n\n  return gen.scopeValue(\"pattern\", {\n    key: rx.toString(),\n    ref: rx,\n    code: _`${regExp.code === \"new RegExp\" ? newRegExp : useFunc(gen, regExp)}(${pattern}, ${u})`,\n  })\n}\n\nexport function validateArray(cxt: KeywordCxt): Name {\n  const {gen, data, keyword, it} = cxt\n  const valid = gen.name(\"valid\")\n  if (it.allErrors) {\n    const validArr = gen.let(\"valid\", true)\n    validateItems(() => gen.assign(validArr, false))\n    return validArr\n  }\n  gen.var(valid, true)\n  validateItems(() => gen.break())\n  return valid\n\n  function validateItems(notValid: () => void): void {\n    const len = gen.const(\"len\", _`${data}.length`)\n    gen.forRange(\"i\", 0, len, (i) => {\n      cxt.subschema(\n        {\n          keyword,\n          dataProp: i,\n          dataPropType: Type.Num,\n        },\n        valid\n      )\n      gen.if(not(valid), notValid)\n    })\n  }\n}\n\nexport function validateUnion(cxt: KeywordCxt): void {\n  const {gen, schema, keyword, it} = cxt\n  /* istanbul ignore if */\n  if (!Array.isArray(schema)) throw new Error(\"ajv implementation error\")\n  const alwaysValid = schema.some((sch: AnySchema) => alwaysValidSchema(it, sch))\n  if (alwaysValid && !it.opts.unevaluated) return\n\n  const valid = gen.let(\"valid\", false)\n  const schValid = gen.name(\"_valid\")\n\n  gen.block(() =>\n    schema.forEach((_sch: AnySchema, i: number) => {\n      const schCxt = cxt.subschema(\n        {\n          keyword,\n          schemaProp: i,\n          compositeRule: true,\n        },\n        schValid\n      )\n      gen.assign(valid, _`${valid} || ${schValid}`)\n      const merged = cxt.mergeValidEvaluated(schCxt, schValid)\n      // can short-circuit if `unevaluatedProperties/Items` not supported (opts.unevaluated !== true)\n      // or if all properties and items were evaluated (it.props === true && it.items === true)\n      if (!merged) gen.if(not(valid))\n    })\n  )\n\n  cxt.result(\n    valid,\n    () => cxt.reset(),\n    () => cxt.error(true)\n  )\n}\n", "import type {KeywordCxt} from \".\"\nimport type {\n  AnySchema,\n  SchemaValidateFunction,\n  AnyValidateFunction,\n  AddedKeywordDefinition,\n  MacroKeywordDefinition,\n  FuncKeywordDefinition,\n} from \"../../types\"\nimport type {SchemaObjCxt} from \"..\"\nimport {_, nil, not, stringify, Code, Name, CodeGen} from \"../codegen\"\nimport N from \"../names\"\nimport type {JSONType} from \"../rules\"\nimport {callValidateCode} from \"../../vocabularies/code\"\nimport {extendErrors} from \"../errors\"\n\ntype KeywordCompilationResult = AnySchema | SchemaValidateFunction | AnyValidateFunction\n\nexport function macroKeywordCode(cxt: KeywordCxt, def: MacroKeywordDefinition): void {\n  const {gen, keyword, schema, parentSchema, it} = cxt\n  const macroSchema = def.macro.call(it.self, schema, parentSchema, it)\n  const schemaRef = useKeyword(gen, keyword, macroSchema)\n  if (it.opts.validateSchema !== false) it.self.validateSchema(macroSchema, true)\n\n  const valid = gen.name(\"valid\")\n  cxt.subschema(\n    {\n      schema: macroSchema,\n      schemaPath: nil,\n      errSchemaPath: `${it.errSchemaPath}/${keyword}`,\n      topSchemaRef: schemaRef,\n      compositeRule: true,\n    },\n    valid\n  )\n  cxt.pass(valid, () => cxt.error(true))\n}\n\nexport function funcKeywordCode(cxt: KeywordCxt, def: FuncKeywordDefinition): void {\n  const {gen, keyword, schema, parentSchema, $data, it} = cxt\n  checkAsyncKeyword(it, def)\n  const validate =\n    !$data && def.compile ? def.compile.call(it.self, schema, parentSchema, it) : def.validate\n  const validateRef = useKeyword(gen, keyword, validate)\n  const valid = gen.let(\"valid\")\n  cxt.block$data(valid, validateKeyword)\n  cxt.ok(def.valid ?? valid)\n\n  function validateKeyword(): void {\n    if (def.errors === false) {\n      assignValid()\n      if (def.modifying) modifyData(cxt)\n      reportErrs(() => cxt.error())\n    } else {\n      const ruleErrs = def.async ? validateAsync() : validateSync()\n      if (def.modifying) modifyData(cxt)\n      reportErrs(() => addErrs(cxt, ruleErrs))\n    }\n  }\n\n  function validateAsync(): Name {\n    const ruleErrs = gen.let(\"ruleErrs\", null)\n    gen.try(\n      () => assignValid(_`await `),\n      (e) =>\n        gen.assign(valid, false).if(\n          _`${e} instanceof ${it.ValidationError as Name}`,\n          () => gen.assign(ruleErrs, _`${e}.errors`),\n          () => gen.throw(e)\n        )\n    )\n    return ruleErrs\n  }\n\n  function validateSync(): Code {\n    const validateErrs = _`${validateRef}.errors`\n    gen.assign(validateErrs, null)\n    assignValid(nil)\n    return validateErrs\n  }\n\n  function assignValid(_await: Code = def.async ? _`await ` : nil): void {\n    const passCxt = it.opts.passContext ? N.this : N.self\n    const passSchema = !((\"compile\" in def && !$data) || def.schema === false)\n    gen.assign(\n      valid,\n      _`${_await}${callValidateCode(cxt, validateRef, passCxt, passSchema)}`,\n      def.modifying\n    )\n  }\n\n  function reportErrs(errors: () => void): void {\n    gen.if(not(def.valid ?? valid), errors)\n  }\n}\n\nfunction modifyData(cxt: KeywordCxt): void {\n  const {gen, data, it} = cxt\n  gen.if(it.parentData, () => gen.assign(data, _`${it.parentData}[${it.parentDataProperty}]`))\n}\n\nfunction addErrs(cxt: KeywordCxt, errs: Code): void {\n  const {gen} = cxt\n  gen.if(\n    _`Array.isArray(${errs})`,\n    () => {\n      gen\n        .assign(N.vErrors, _`${N.vErrors} === null ? ${errs} : ${N.vErrors}.concat(${errs})`)\n        .assign(N.errors, _`${N.vErrors}.length`)\n      extendErrors(cxt)\n    },\n    () => cxt.error()\n  )\n}\n\nfunction checkAsyncKeyword({schemaEnv}: SchemaObjCxt, def: FuncKeywordDefinition): void {\n  if (def.async && !schemaEnv.$async) throw new Error(\"async keyword in sync schema\")\n}\n\nfunction useKeyword(gen: CodeGen, keyword: string, result?: KeywordCompilationResult): Name {\n  if (result === undefined) throw new Error(`keyword \"${keyword}\" failed to compile`)\n  return gen.scopeValue(\n    \"keyword\",\n    typeof result == \"function\" ? {ref: result} : {ref: result, code: stringify(result)}\n  )\n}\n\nexport function validSchemaType(\n  schema: unknown,\n  schemaType: JSONType[],\n  allowUndefined = false\n): boolean {\n  // TODO add tests\n  return (\n    !schemaType.length ||\n    schemaType.some((st) =>\n      st === \"array\"\n        ? Array.isArray(schema)\n        : st === \"object\"\n        ? schema && typeof schema == \"object\" && !Array.isArray(schema)\n        : typeof schema == st || (allowUndefined && typeof schema == \"undefined\")\n    )\n  )\n}\n\nexport function validateKeywordUsage(\n  {schema, opts, self, errSchemaPath}: SchemaObjCxt,\n  def: AddedKeywordDefinition,\n  keyword: string\n): void {\n  /* istanbul ignore if */\n  if (Array.isArray(def.keyword) ? !def.keyword.includes(keyword) : def.keyword !== keyword) {\n    throw new Error(\"ajv implementation error\")\n  }\n\n  const deps = def.dependencies\n  if (deps?.some((kwd) => !Object.prototype.hasOwnProperty.call(schema, kwd))) {\n    throw new Error(`parent schema must have dependencies of ${keyword}: ${deps.join(\",\")}`)\n  }\n\n  if (def.validateSchema) {\n    const valid = def.validateSchema(schema[keyword])\n    if (!valid) {\n      const msg =\n        `keyword \"${keyword}\" value is invalid at path \"${errSchemaPath}\": ` +\n        self.errorsText(def.validateSchema.errors)\n      if (opts.validateSchema === \"log\") self.logger.error(msg)\n      else throw new Error(msg)\n    }\n  }\n}\n", "import type {AnySchema} from \"../../types\"\nimport type {SchemaObjCxt} from \"..\"\nimport {_, str, getProperty, Code, Name} from \"../codegen\"\nimport {escapeFragment, getErrorPath, Type} from \"../util\"\nimport type {JSONType} from \"../rules\"\n\nexport interface SubschemaContext {\n  // TODO use Optional? align with SchemCxt property types\n  schema: AnySchema\n  schemaPath: Code\n  errSchemaPath: string\n  topSchemaRef?: Code\n  errorPath?: Code\n  dataLevel?: number\n  dataTypes?: JSONType[]\n  data?: Name\n  parentData?: Name\n  parentDataProperty?: Code | number\n  dataNames?: Name[]\n  dataPathArr?: (Code | number)[]\n  propertyName?: Name\n  jtdDiscriminator?: string\n  jtdMetadata?: boolean\n  compositeRule?: true\n  createErrors?: boolean\n  allErrors?: boolean\n}\n\nexport type SubschemaArgs = Partial<{\n  keyword: string\n  schemaProp: string | number\n  schema: AnySchema\n  schemaPath: Code\n  errSchemaPath: string\n  topSchemaRef: Code\n  data: Name | Code\n  dataProp: Code | string | number\n  dataTypes: JSONType[]\n  definedProperties: Set<string>\n  propertyName: Name\n  dataPropType: Type\n  jtdDiscriminator: string\n  jtdMetadata: boolean\n  compositeRule: true\n  createErrors: boolean\n  allErrors: boolean\n}>\n\nexport function getSubschema(\n  it: SchemaObjCxt,\n  {keyword, schemaProp, schema, schemaPath, errSchemaPath, topSchemaRef}: SubschemaArgs\n): SubschemaContext {\n  if (keyword !== undefined && schema !== undefined) {\n    throw new Error('both \"keyword\" and \"schema\" passed, only one allowed')\n  }\n\n  if (keyword !== undefined) {\n    const sch = it.schema[keyword]\n    return schemaProp === undefined\n      ? {\n          schema: sch,\n          schemaPath: _`${it.schemaPath}${getProperty(keyword)}`,\n          errSchemaPath: `${it.errSchemaPath}/${keyword}`,\n        }\n      : {\n          schema: sch[schemaProp],\n          schemaPath: _`${it.schemaPath}${getProperty(keyword)}${getProperty(schemaProp)}`,\n          errSchemaPath: `${it.errSchemaPath}/${keyword}/${escapeFragment(schemaProp)}`,\n        }\n  }\n\n  if (schema !== undefined) {\n    if (schemaPath === undefined || errSchemaPath === undefined || topSchemaRef === undefined) {\n      throw new Error('\"schemaPath\", \"errSchemaPath\" and \"topSchemaRef\" are required with \"schema\"')\n    }\n    return {\n      schema,\n      schemaPath,\n      topSchemaRef,\n      errSchemaPath,\n    }\n  }\n\n  throw new Error('either \"keyword\" or \"schema\" must be passed')\n}\n\nexport function extendSubschemaData(\n  subschema: SubschemaContext,\n  it: SchemaObjCxt,\n  {dataProp, dataPropType: dpType, data, dataTypes, propertyName}: SubschemaArgs\n): void {\n  if (data !== undefined && dataProp !== undefined) {\n    throw new Error('both \"data\" and \"dataProp\" passed, only one allowed')\n  }\n\n  const {gen} = it\n\n  if (dataProp !== undefined) {\n    const {errorPath, dataPathArr, opts} = it\n    const nextData = gen.let(\"data\", _`${it.data}${getProperty(dataProp)}`, true)\n    dataContextProps(nextData)\n    subschema.errorPath = str`${errorPath}${getErrorPath(dataProp, dpType, opts.jsPropertySyntax)}`\n    subschema.parentDataProperty = _`${dataProp}`\n    subschema.dataPathArr = [...dataPathArr, subschema.parentDataProperty]\n  }\n\n  if (data !== undefined) {\n    const nextData = data instanceof Name ? data : gen.let(\"data\", data, true) // replaceable if used once?\n    dataContextProps(nextData)\n    if (propertyName !== undefined) subschema.propertyName = propertyName\n    // TODO something is possibly wrong here with not changing parentDataProperty and not appending dataPathArr\n  }\n\n  if (dataTypes) subschema.dataTypes = dataTypes\n\n  function dataContextProps(_nextData: Name): void {\n    subschema.data = _nextData\n    subschema.dataLevel = it.dataLevel + 1\n    subschema.dataTypes = []\n    it.definedProperties = new Set<string>()\n    subschema.parentData = it.data\n    subschema.dataNames = [...it.dataNames, _nextData]\n  }\n}\n\nexport function extendSubschemaMode(\n  subschema: SubschemaContext,\n  {jtdDiscriminator, jtdMetadata, compositeRule, createErrors, allErrors}: SubschemaArgs\n): void {\n  if (compositeRule !== undefined) subschema.compositeRule = compositeRule\n  if (createErrors !== undefined) subschema.createErrors = createErrors\n  if (allErrors !== undefined) subschema.allErrors = allErrors\n  subschema.jtdDiscriminator = jtdDiscriminator // not inherited\n  subschema.jtdMetadata = jtdMetadata // not inherited\n}\n", "'use strict';\n\n// do not edit .js files directly - edit src/index.jst\n\n\n\nmodule.exports = function equal(a, b) {\n  if (a === b) return true;\n\n  if (a && b && typeof a == 'object' && typeof b == 'object') {\n    if (a.constructor !== b.constructor) return false;\n\n    var length, i, keys;\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;)\n        if (!equal(a[i], b[i])) return false;\n      return true;\n    }\n\n\n\n    if (a.constructor === RegExp) return a.source === b.source && a.flags === b.flags;\n    if (a.valueOf !== Object.prototype.valueOf) return a.valueOf() === b.valueOf();\n    if (a.toString !== Object.prototype.toString) return a.toString() === b.toString();\n\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) return false;\n\n    for (i = length; i-- !== 0;)\n      if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return false;\n\n    for (i = length; i-- !== 0;) {\n      var key = keys[i];\n\n      if (!equal(a[key], b[key])) return false;\n    }\n\n    return true;\n  }\n\n  // true if both NaN, false otherwise\n  return a!==a && b!==b;\n};\n", "'use strict';\n\nvar traverse = module.exports = function (schema, opts, cb) {\n  // Legacy support for v0.3.1 and earlier.\n  if (typeof opts == 'function') {\n    cb = opts;\n    opts = {};\n  }\n\n  cb = opts.cb || cb;\n  var pre = (typeof cb == 'function') ? cb : cb.pre || function() {};\n  var post = cb.post || function() {};\n\n  _traverse(opts, pre, post, schema, '', schema);\n};\n\n\ntraverse.keywords = {\n  additionalItems: true,\n  items: true,\n  contains: true,\n  additionalProperties: true,\n  propertyNames: true,\n  not: true,\n  if: true,\n  then: true,\n  else: true\n};\n\ntraverse.arrayKeywords = {\n  items: true,\n  allOf: true,\n  anyOf: true,\n  oneOf: true\n};\n\ntraverse.propsKeywords = {\n  $defs: true,\n  definitions: true,\n  properties: true,\n  patternProperties: true,\n  dependencies: true\n};\n\ntraverse.skipKeywords = {\n  default: true,\n  enum: true,\n  const: true,\n  required: true,\n  maximum: true,\n  minimum: true,\n  exclusiveMaximum: true,\n  exclusiveMinimum: true,\n  multipleOf: true,\n  maxLength: true,\n  minLength: true,\n  pattern: true,\n  format: true,\n  maxItems: true,\n  minItems: true,\n  uniqueItems: true,\n  maxProperties: true,\n  minProperties: true\n};\n\n\nfunction _traverse(opts, pre, post, schema, jsonPtr, rootSchema, parentJsonPtr, parentKeyword, parentSchema, keyIndex) {\n  if (schema && typeof schema == 'object' && !Array.isArray(schema)) {\n    pre(schema, jsonPtr, rootSchema, parentJsonPtr, parentKeyword, parentSchema, keyIndex);\n    for (var key in schema) {\n      var sch = schema[key];\n      if (Array.isArray(sch)) {\n        if (key in traverse.arrayKeywords) {\n          for (var i=0; i<sch.length; i++)\n            _traverse(opts, pre, post, sch[i], jsonPtr + '/' + key + '/' + i, rootSchema, jsonPtr, key, schema, i);\n        }\n      } else if (key in traverse.propsKeywords) {\n        if (sch && typeof sch == 'object') {\n          for (var prop in sch)\n            _traverse(opts, pre, post, sch[prop], jsonPtr + '/' + key + '/' + escapeJsonPtr(prop), rootSchema, jsonPtr, key, schema, prop);\n        }\n      } else if (key in traverse.keywords || (opts.allKeys && !(key in traverse.skipKeywords))) {\n        _traverse(opts, pre, post, sch, jsonPtr + '/' + key, rootSchema, jsonPtr, key, schema);\n      }\n    }\n    post(schema, jsonPtr, rootSchema, parentJsonPtr, parentKeyword, parentSchema, keyIndex);\n  }\n}\n\n\nfunction escapeJsonPtr(str) {\n  return str.replace(/~/g, '~0').replace(/\\//g, '~1');\n}\n", "import type {AnySchema, AnySchemaObject, UriResolver} from \"../types\"\nimport type Ajv from \"../ajv\"\nimport type {URIComponent} from \"fast-uri\"\nimport {eachItem} from \"./util\"\nimport * as equal from \"fast-deep-equal\"\nimport * as traverse from \"json-schema-traverse\"\n\n// the hash of local references inside the schema (created by getSchemaRefs), used for inline resolution\nexport type LocalRefs = {[Ref in string]?: AnySchemaObject}\n\n// TODO refactor to use keyword definitions\nconst SIMPLE_INLINED = new Set([\n  \"type\",\n  \"format\",\n  \"pattern\",\n  \"maxLength\",\n  \"minLength\",\n  \"maxProperties\",\n  \"minProperties\",\n  \"maxItems\",\n  \"minItems\",\n  \"maximum\",\n  \"minimum\",\n  \"uniqueItems\",\n  \"multipleOf\",\n  \"required\",\n  \"enum\",\n  \"const\",\n])\n\nexport function inlineRef(schema: AnySchema, limit: boolean | number = true): boolean {\n  if (typeof schema == \"boolean\") return true\n  if (limit === true) return !hasRef(schema)\n  if (!limit) return false\n  return countKeys(schema) <= limit\n}\n\nconst REF_KEYWORDS = new Set([\n  \"$ref\",\n  \"$recursiveRef\",\n  \"$recursiveAnchor\",\n  \"$dynamicRef\",\n  \"$dynamicAnchor\",\n])\n\nfunction hasRef(schema: AnySchemaObject): boolean {\n  for (const key in schema) {\n    if (REF_KEYWORDS.has(key)) return true\n    const sch = schema[key]\n    if (Array.isArray(sch) && sch.some(hasRef)) return true\n    if (typeof sch == \"object\" && hasRef(sch)) return true\n  }\n  return false\n}\n\nfunction countKeys(schema: AnySchemaObject): number {\n  let count = 0\n  for (const key in schema) {\n    if (key === \"$ref\") return Infinity\n    count++\n    if (SIMPLE_INLINED.has(key)) continue\n    if (typeof schema[key] == \"object\") {\n      eachItem(schema[key], (sch) => (count += countKeys(sch)))\n    }\n    if (count === Infinity) return Infinity\n  }\n  return count\n}\n\nexport function getFullPath(resolver: UriResolver, id = \"\", normalize?: boolean): string {\n  if (normalize !== false) id = normalizeId(id)\n  const p = resolver.parse(id)\n  return _getFullPath(resolver, p)\n}\n\nexport function _getFullPath(resolver: UriResolver, p: URIComponent): string {\n  const serialized = resolver.serialize(p)\n  return serialized.split(\"#\")[0] + \"#\"\n}\n\nconst TRAILING_SLASH_HASH = /#\\/?$/\nexport function normalizeId(id: string | undefined): string {\n  return id ? id.replace(TRAILING_SLASH_HASH, \"\") : \"\"\n}\n\nexport function resolveUrl(resolver: UriResolver, baseId: string, id: string): string {\n  id = normalizeId(id)\n  return resolver.resolve(baseId, id)\n}\n\nconst ANCHOR = /^[a-z_][-a-z0-9._]*$/i\n\nexport function getSchemaRefs(this: Ajv, schema: AnySchema, baseId: string): LocalRefs {\n  if (typeof schema == \"boolean\") return {}\n  const {schemaId, uriResolver} = this.opts\n  const schId = normalizeId(schema[schemaId] || baseId)\n  const baseIds: {[JsonPtr in string]?: string} = {\"\": schId}\n  const pathPrefix = getFullPath(uriResolver, schId, false)\n  const localRefs: LocalRefs = {}\n  const schemaRefs: Set<string> = new Set()\n\n  traverse(schema, {allKeys: true}, (sch, jsonPtr, _, parentJsonPtr) => {\n    if (parentJsonPtr === undefined) return\n    const fullPath = pathPrefix + jsonPtr\n    let innerBaseId = baseIds[parentJsonPtr]\n    if (typeof sch[schemaId] == \"string\") innerBaseId = addRef.call(this, sch[schemaId])\n    addAnchor.call(this, sch.$anchor)\n    addAnchor.call(this, sch.$dynamicAnchor)\n    baseIds[jsonPtr] = innerBaseId\n\n    function addRef(this: Ajv, ref: string): string {\n      // eslint-disable-next-line @typescript-eslint/unbound-method\n      const _resolve = this.opts.uriResolver.resolve\n      ref = normalizeId(innerBaseId ? _resolve(innerBaseId, ref) : ref)\n      if (schemaRefs.has(ref)) throw ambiguos(ref)\n      schemaRefs.add(ref)\n      let schOrRef = this.refs[ref]\n      if (typeof schOrRef == \"string\") schOrRef = this.refs[schOrRef]\n      if (typeof schOrRef == \"object\") {\n        checkAmbiguosRef(sch, schOrRef.schema, ref)\n      } else if (ref !== normalizeId(fullPath)) {\n        if (ref[0] === \"#\") {\n          checkAmbiguosRef(sch, localRefs[ref], ref)\n          localRefs[ref] = sch\n        } else {\n          this.refs[ref] = fullPath\n        }\n      }\n      return ref\n    }\n\n    function addAnchor(this: Ajv, anchor: unknown): void {\n      if (typeof anchor == \"string\") {\n        if (!ANCHOR.test(anchor)) throw new Error(`invalid anchor \"${anchor}\"`)\n        addRef.call(this, `#${anchor}`)\n      }\n    }\n  })\n\n  return localRefs\n\n  function checkAmbiguosRef(sch1: AnySchema, sch2: AnySchema | undefined, ref: string): void {\n    if (sch2 !== undefined && !equal(sch1, sch2)) throw ambiguos(ref)\n  }\n\n  function ambiguos(ref: string): Error {\n    return new Error(`reference \"${ref}\" resolves to more than one schema`)\n  }\n}\n", "import type {\n  AddedKeywordDefinition,\n  AnySchema,\n  AnySchemaObject,\n  KeywordErrorCxt,\n  KeywordCxtParams,\n} from \"../../types\"\nimport type {SchemaCxt, SchemaObjCxt} from \"..\"\nimport type {InstanceOptions} from \"../../core\"\nimport {boolOrEmptySchema, topBoolOrEmptySchema} from \"./boolSchema\"\nimport {coerceAndCheckDataType, getSchemaTypes} from \"./dataType\"\nimport {shouldUseGroup, shouldUseRule} from \"./applicability\"\nimport {checkDataType, checkDataTypes, reportTypeError, DataType} from \"./dataType\"\nimport {assignDefaults} from \"./defaults\"\nimport {funcKeywordCode, macroKeywordCode, validateKeywordUsage, validSchemaType} from \"./keyword\"\nimport {getSubschema, extendSubschemaData, SubschemaArgs, extendSubschemaMode} from \"./subschema\"\nimport {_, nil, str, or, not, getProperty, Block, Code, Name, CodeGen} from \"../codegen\"\nimport N from \"../names\"\nimport {resolveUrl} from \"../resolve\"\nimport {\n  schemaRefOrVal,\n  schemaHasRulesButRef,\n  checkUnknownRules,\n  checkStrictMode,\n  unescapeJsonPointer,\n  mergeEvaluated,\n} from \"../util\"\nimport type {JSONType, Rule, RuleGroup} from \"../rules\"\nimport {\n  ErrorPaths,\n  reportError,\n  reportExtraError,\n  resetErrorsCount,\n  keyword$DataError,\n} from \"../errors\"\n\n// schema compilation - generates validation function, subschemaCode (below) is used for subschemas\nexport function validateFunctionCode(it: SchemaCxt): void {\n  if (isSchemaObj(it)) {\n    checkKeywords(it)\n    if (schemaCxtHasRules(it)) {\n      topSchemaObjCode(it)\n      return\n    }\n  }\n  validateFunction(it, () => topBoolOrEmptySchema(it))\n}\n\nfunction validateFunction(\n  {gen, validateName, schema, schemaEnv, opts}: SchemaCxt,\n  body: Block\n): void {\n  if (opts.code.es5) {\n    gen.func(validateName, _`${N.data}, ${N.valCxt}`, schemaEnv.$async, () => {\n      gen.code(_`\"use strict\"; ${funcSourceUrl(schema, opts)}`)\n      destructureValCxtES5(gen, opts)\n      gen.code(body)\n    })\n  } else {\n    gen.func(validateName, _`${N.data}, ${destructureValCxt(opts)}`, schemaEnv.$async, () =>\n      gen.code(funcSourceUrl(schema, opts)).code(body)\n    )\n  }\n}\n\nfunction destructureValCxt(opts: InstanceOptions): Code {\n  return _`{${N.instancePath}=\"\", ${N.parentData}, ${N.parentDataProperty}, ${N.rootData}=${\n    N.data\n  }${opts.dynamicRef ? _`, ${N.dynamicAnchors}={}` : nil}}={}`\n}\n\nfunction destructureValCxtES5(gen: CodeGen, opts: InstanceOptions): void {\n  gen.if(\n    N.valCxt,\n    () => {\n      gen.var(N.instancePath, _`${N.valCxt}.${N.instancePath}`)\n      gen.var(N.parentData, _`${N.valCxt}.${N.parentData}`)\n      gen.var(N.parentDataProperty, _`${N.valCxt}.${N.parentDataProperty}`)\n      gen.var(N.rootData, _`${N.valCxt}.${N.rootData}`)\n      if (opts.dynamicRef) gen.var(N.dynamicAnchors, _`${N.valCxt}.${N.dynamicAnchors}`)\n    },\n    () => {\n      gen.var(N.instancePath, _`\"\"`)\n      gen.var(N.parentData, _`undefined`)\n      gen.var(N.parentDataProperty, _`undefined`)\n      gen.var(N.rootData, N.data)\n      if (opts.dynamicRef) gen.var(N.dynamicAnchors, _`{}`)\n    }\n  )\n}\n\nfunction topSchemaObjCode(it: SchemaObjCxt): void {\n  const {schema, opts, gen} = it\n  validateFunction(it, () => {\n    if (opts.$comment && schema.$comment) commentKeyword(it)\n    checkNoDefault(it)\n    gen.let(N.vErrors, null)\n    gen.let(N.errors, 0)\n    if (opts.unevaluated) resetEvaluated(it)\n    typeAndKeywords(it)\n    returnResults(it)\n  })\n  return\n}\n\nfunction resetEvaluated(it: SchemaObjCxt): void {\n  // TODO maybe some hook to execute it in the end to check whether props/items are Name, as in assignEvaluated\n  const {gen, validateName} = it\n  it.evaluated = gen.const(\"evaluated\", _`${validateName}.evaluated`)\n  gen.if(_`${it.evaluated}.dynamicProps`, () => gen.assign(_`${it.evaluated}.props`, _`undefined`))\n  gen.if(_`${it.evaluated}.dynamicItems`, () => gen.assign(_`${it.evaluated}.items`, _`undefined`))\n}\n\nfunction funcSourceUrl(schema: AnySchema, opts: InstanceOptions): Code {\n  const schId = typeof schema == \"object\" && schema[opts.schemaId]\n  return schId && (opts.code.source || opts.code.process) ? _`/*# sourceURL=${schId} */` : nil\n}\n\n// schema compilation - this function is used recursively to generate code for sub-schemas\nfunction subschemaCode(it: SchemaCxt, valid: Name): void {\n  if (isSchemaObj(it)) {\n    checkKeywords(it)\n    if (schemaCxtHasRules(it)) {\n      subSchemaObjCode(it, valid)\n      return\n    }\n  }\n  boolOrEmptySchema(it, valid)\n}\n\nfunction schemaCxtHasRules({schema, self}: SchemaCxt): boolean {\n  if (typeof schema == \"boolean\") return !schema\n  for (const key in schema) if (self.RULES.all[key]) return true\n  return false\n}\n\nfunction isSchemaObj(it: SchemaCxt): it is SchemaObjCxt {\n  return typeof it.schema != \"boolean\"\n}\n\nfunction subSchemaObjCode(it: SchemaObjCxt, valid: Name): void {\n  const {schema, gen, opts} = it\n  if (opts.$comment && schema.$comment) commentKeyword(it)\n  updateContext(it)\n  checkAsyncSchema(it)\n  const errsCount = gen.const(\"_errs\", N.errors)\n  typeAndKeywords(it, errsCount)\n  // TODO var\n  gen.var(valid, _`${errsCount} === ${N.errors}`)\n}\n\nfunction checkKeywords(it: SchemaObjCxt): void {\n  checkUnknownRules(it)\n  checkRefsAndKeywords(it)\n}\n\nfunction typeAndKeywords(it: SchemaObjCxt, errsCount?: Name): void {\n  if (it.opts.jtd) return schemaKeywords(it, [], false, errsCount)\n  const types = getSchemaTypes(it.schema)\n  const checkedTypes = coerceAndCheckDataType(it, types)\n  schemaKeywords(it, types, !checkedTypes, errsCount)\n}\n\nfunction checkRefsAndKeywords(it: SchemaObjCxt): void {\n  const {schema, errSchemaPath, opts, self} = it\n  if (schema.$ref && opts.ignoreKeywordsWithRef && schemaHasRulesButRef(schema, self.RULES)) {\n    self.logger.warn(`$ref: keywords ignored in schema at path \"${errSchemaPath}\"`)\n  }\n}\n\nfunction checkNoDefault(it: SchemaObjCxt): void {\n  const {schema, opts} = it\n  if (schema.default !== undefined && opts.useDefaults && opts.strictSchema) {\n    checkStrictMode(it, \"default is ignored in the schema root\")\n  }\n}\n\nfunction updateContext(it: SchemaObjCxt): void {\n  const schId = it.schema[it.opts.schemaId]\n  if (schId) it.baseId = resolveUrl(it.opts.uriResolver, it.baseId, schId)\n}\n\nfunction checkAsyncSchema(it: SchemaObjCxt): void {\n  if (it.schema.$async && !it.schemaEnv.$async) throw new Error(\"async schema in sync schema\")\n}\n\nfunction commentKeyword({gen, schemaEnv, schema, errSchemaPath, opts}: SchemaObjCxt): void {\n  const msg = schema.$comment\n  if (opts.$comment === true) {\n    gen.code(_`${N.self}.logger.log(${msg})`)\n  } else if (typeof opts.$comment == \"function\") {\n    const schemaPath = str`${errSchemaPath}/$comment`\n    const rootName = gen.scopeValue(\"root\", {ref: schemaEnv.root})\n    gen.code(_`${N.self}.opts.$comment(${msg}, ${schemaPath}, ${rootName}.schema)`)\n  }\n}\n\nfunction returnResults(it: SchemaCxt): void {\n  const {gen, schemaEnv, validateName, ValidationError, opts} = it\n  if (schemaEnv.$async) {\n    // TODO assign unevaluated\n    gen.if(\n      _`${N.errors} === 0`,\n      () => gen.return(N.data),\n      () => gen.throw(_`new ${ValidationError as Name}(${N.vErrors})`)\n    )\n  } else {\n    gen.assign(_`${validateName}.errors`, N.vErrors)\n    if (opts.unevaluated) assignEvaluated(it)\n    gen.return(_`${N.errors} === 0`)\n  }\n}\n\nfunction assignEvaluated({gen, evaluated, props, items}: SchemaCxt): void {\n  if (props instanceof Name) gen.assign(_`${evaluated}.props`, props)\n  if (items instanceof Name) gen.assign(_`${evaluated}.items`, items)\n}\n\nfunction schemaKeywords(\n  it: SchemaObjCxt,\n  types: JSONType[],\n  typeErrors: boolean,\n  errsCount?: Name\n): void {\n  const {gen, schema, data, allErrors, opts, self} = it\n  const {RULES} = self\n  if (schema.$ref && (opts.ignoreKeywordsWithRef || !schemaHasRulesButRef(schema, RULES))) {\n    gen.block(() => keywordCode(it, \"$ref\", (RULES.all.$ref as Rule).definition)) // TODO typecast\n    return\n  }\n  if (!opts.jtd) checkStrictTypes(it, types)\n  gen.block(() => {\n    for (const group of RULES.rules) groupKeywords(group)\n    groupKeywords(RULES.post)\n  })\n\n  function groupKeywords(group: RuleGroup): void {\n    if (!shouldUseGroup(schema, group)) return\n    if (group.type) {\n      gen.if(checkDataType(group.type, data, opts.strictNumbers))\n      iterateKeywords(it, group)\n      if (types.length === 1 && types[0] === group.type && typeErrors) {\n        gen.else()\n        reportTypeError(it)\n      }\n      gen.endIf()\n    } else {\n      iterateKeywords(it, group)\n    }\n    // TODO make it \"ok\" call?\n    if (!allErrors) gen.if(_`${N.errors} === ${errsCount || 0}`)\n  }\n}\n\nfunction iterateKeywords(it: SchemaObjCxt, group: RuleGroup): void {\n  const {\n    gen,\n    schema,\n    opts: {useDefaults},\n  } = it\n  if (useDefaults) assignDefaults(it, group.type)\n  gen.block(() => {\n    for (const rule of group.rules) {\n      if (shouldUseRule(schema, rule)) {\n        keywordCode(it, rule.keyword, rule.definition, group.type)\n      }\n    }\n  })\n}\n\nfunction checkStrictTypes(it: SchemaObjCxt, types: JSONType[]): void {\n  if (it.schemaEnv.meta || !it.opts.strictTypes) return\n  checkContextTypes(it, types)\n  if (!it.opts.allowUnionTypes) checkMultipleTypes(it, types)\n  checkKeywordTypes(it, it.dataTypes)\n}\n\nfunction checkContextTypes(it: SchemaObjCxt, types: JSONType[]): void {\n  if (!types.length) return\n  if (!it.dataTypes.length) {\n    it.dataTypes = types\n    return\n  }\n  types.forEach((t) => {\n    if (!includesType(it.dataTypes, t)) {\n      strictTypesError(it, `type \"${t}\" not allowed by context \"${it.dataTypes.join(\",\")}\"`)\n    }\n  })\n  narrowSchemaTypes(it, types)\n}\n\nfunction checkMultipleTypes(it: SchemaObjCxt, ts: JSONType[]): void {\n  if (ts.length > 1 && !(ts.length === 2 && ts.includes(\"null\"))) {\n    strictTypesError(it, \"use allowUnionTypes to allow union type keyword\")\n  }\n}\n\nfunction checkKeywordTypes(it: SchemaObjCxt, ts: JSONType[]): void {\n  const rules = it.self.RULES.all\n  for (const keyword in rules) {\n    const rule = rules[keyword]\n    if (typeof rule == \"object\" && shouldUseRule(it.schema, rule)) {\n      const {type} = rule.definition\n      if (type.length && !type.some((t) => hasApplicableType(ts, t))) {\n        strictTypesError(it, `missing type \"${type.join(\",\")}\" for keyword \"${keyword}\"`)\n      }\n    }\n  }\n}\n\nfunction hasApplicableType(schTs: JSONType[], kwdT: JSONType): boolean {\n  return schTs.includes(kwdT) || (kwdT === \"number\" && schTs.includes(\"integer\"))\n}\n\nfunction includesType(ts: JSONType[], t: JSONType): boolean {\n  return ts.includes(t) || (t === \"integer\" && ts.includes(\"number\"))\n}\n\nfunction narrowSchemaTypes(it: SchemaObjCxt, withTypes: JSONType[]): void {\n  const ts: JSONType[] = []\n  for (const t of it.dataTypes) {\n    if (includesType(withTypes, t)) ts.push(t)\n    else if (withTypes.includes(\"integer\") && t === \"number\") ts.push(\"integer\")\n  }\n  it.dataTypes = ts\n}\n\nfunction strictTypesError(it: SchemaObjCxt, msg: string): void {\n  const schemaPath = it.schemaEnv.baseId + it.errSchemaPath\n  msg += ` at \"${schemaPath}\" (strictTypes)`\n  checkStrictMode(it, msg, it.opts.strictTypes)\n}\n\nexport class KeywordCxt implements KeywordErrorCxt {\n  readonly gen: CodeGen\n  readonly allErrors?: boolean\n  readonly keyword: string\n  readonly data: Name // Name referencing the current level of the data instance\n  readonly $data?: string | false\n  schema: any // keyword value in the schema\n  readonly schemaValue: Code | number | boolean // Code reference to keyword schema value or primitive value\n  readonly schemaCode: Code | number | boolean // Code reference to resolved schema value (different if schema is $data)\n  readonly schemaType: JSONType[] // allowed type(s) of keyword value in the schema\n  readonly parentSchema: AnySchemaObject\n  readonly errsCount?: Name // Name reference to the number of validation errors collected before this keyword,\n  // requires option trackErrors in keyword definition\n  params: KeywordCxtParams // object to pass parameters to error messages from keyword code\n  readonly it: SchemaObjCxt // schema compilation context (schema is guaranteed to be an object, not boolean)\n  readonly def: AddedKeywordDefinition\n\n  constructor(it: SchemaObjCxt, def: AddedKeywordDefinition, keyword: string) {\n    validateKeywordUsage(it, def, keyword)\n    this.gen = it.gen\n    this.allErrors = it.allErrors\n    this.keyword = keyword\n    this.data = it.data\n    this.schema = it.schema[keyword]\n    this.$data = def.$data && it.opts.$data && this.schema && this.schema.$data\n    this.schemaValue = schemaRefOrVal(it, this.schema, keyword, this.$data)\n    this.schemaType = def.schemaType\n    this.parentSchema = it.schema\n    this.params = {}\n    this.it = it\n    this.def = def\n\n    if (this.$data) {\n      this.schemaCode = it.gen.const(\"vSchema\", getData(this.$data, it))\n    } else {\n      this.schemaCode = this.schemaValue\n      if (!validSchemaType(this.schema, def.schemaType, def.allowUndefined)) {\n        throw new Error(`${keyword} value must be ${JSON.stringify(def.schemaType)}`)\n      }\n    }\n\n    if (\"code\" in def ? def.trackErrors : def.errors !== false) {\n      this.errsCount = it.gen.const(\"_errs\", N.errors)\n    }\n  }\n\n  result(condition: Code, successAction?: () => void, failAction?: () => void): void {\n    this.failResult(not(condition), successAction, failAction)\n  }\n\n  failResult(condition: Code, successAction?: () => void, failAction?: () => void): void {\n    this.gen.if(condition)\n    if (failAction) failAction()\n    else this.error()\n    if (successAction) {\n      this.gen.else()\n      successAction()\n      if (this.allErrors) this.gen.endIf()\n    } else {\n      if (this.allErrors) this.gen.endIf()\n      else this.gen.else()\n    }\n  }\n\n  pass(condition: Code, failAction?: () => void): void {\n    this.failResult(not(condition), undefined, failAction)\n  }\n\n  fail(condition?: Code): void {\n    if (condition === undefined) {\n      this.error()\n      if (!this.allErrors) this.gen.if(false) // this branch will be removed by gen.optimize\n      return\n    }\n    this.gen.if(condition)\n    this.error()\n    if (this.allErrors) this.gen.endIf()\n    else this.gen.else()\n  }\n\n  fail$data(condition: Code): void {\n    if (!this.$data) return this.fail(condition)\n    const {schemaCode} = this\n    this.fail(_`${schemaCode} !== undefined && (${or(this.invalid$data(), condition)})`)\n  }\n\n  error(append?: boolean, errorParams?: KeywordCxtParams, errorPaths?: ErrorPaths): void {\n    if (errorParams) {\n      this.setParams(errorParams)\n      this._error(append, errorPaths)\n      this.setParams({})\n      return\n    }\n    this._error(append, errorPaths)\n  }\n\n  private _error(append?: boolean, errorPaths?: ErrorPaths): void {\n    ;(append ? reportExtraError : reportError)(this, this.def.error, errorPaths)\n  }\n\n  $dataError(): void {\n    reportError(this, this.def.$dataError || keyword$DataError)\n  }\n\n  reset(): void {\n    if (this.errsCount === undefined) throw new Error('add \"trackErrors\" to keyword definition')\n    resetErrorsCount(this.gen, this.errsCount)\n  }\n\n  ok(cond: Code | boolean): void {\n    if (!this.allErrors) this.gen.if(cond)\n  }\n\n  setParams(obj: KeywordCxtParams, assign?: true): void {\n    if (assign) Object.assign(this.params, obj)\n    else this.params = obj\n  }\n\n  block$data(valid: Name, codeBlock: () => void, $dataValid: Code = nil): void {\n    this.gen.block(() => {\n      this.check$data(valid, $dataValid)\n      codeBlock()\n    })\n  }\n\n  check$data(valid: Name = nil, $dataValid: Code = nil): void {\n    if (!this.$data) return\n    const {gen, schemaCode, schemaType, def} = this\n    gen.if(or(_`${schemaCode} === undefined`, $dataValid))\n    if (valid !== nil) gen.assign(valid, true)\n    if (schemaType.length || def.validateSchema) {\n      gen.elseIf(this.invalid$data())\n      this.$dataError()\n      if (valid !== nil) gen.assign(valid, false)\n    }\n    gen.else()\n  }\n\n  invalid$data(): Code {\n    const {gen, schemaCode, schemaType, def, it} = this\n    return or(wrong$DataType(), invalid$DataSchema())\n\n    function wrong$DataType(): Code {\n      if (schemaType.length) {\n        /* istanbul ignore if */\n        if (!(schemaCode instanceof Name)) throw new Error(\"ajv implementation error\")\n        const st = Array.isArray(schemaType) ? schemaType : [schemaType]\n        return _`${checkDataTypes(st, schemaCode, it.opts.strictNumbers, DataType.Wrong)}`\n      }\n      return nil\n    }\n\n    function invalid$DataSchema(): Code {\n      if (def.validateSchema) {\n        const validateSchemaRef = gen.scopeValue(\"validate$data\", {ref: def.validateSchema}) // TODO value.code for standalone\n        return _`!${validateSchemaRef}(${schemaCode})`\n      }\n      return nil\n    }\n  }\n\n  subschema(appl: SubschemaArgs, valid: Name): SchemaCxt {\n    const subschema = getSubschema(this.it, appl)\n    extendSubschemaData(subschema, this.it, appl)\n    extendSubschemaMode(subschema, appl)\n    const nextContext = {...this.it, ...subschema, items: undefined, props: undefined}\n    subschemaCode(nextContext, valid)\n    return nextContext\n  }\n\n  mergeEvaluated(schemaCxt: SchemaCxt, toName?: typeof Name): void {\n    const {it, gen} = this\n    if (!it.opts.unevaluated) return\n    if (it.props !== true && schemaCxt.props !== undefined) {\n      it.props = mergeEvaluated.props(gen, schemaCxt.props, it.props, toName)\n    }\n    if (it.items !== true && schemaCxt.items !== undefined) {\n      it.items = mergeEvaluated.items(gen, schemaCxt.items, it.items, toName)\n    }\n  }\n\n  mergeValidEvaluated(schemaCxt: SchemaCxt, valid: Name): boolean | void {\n    const {it, gen} = this\n    if (it.opts.unevaluated && (it.props !== true || it.items !== true)) {\n      gen.if(valid, () => this.mergeEvaluated(schemaCxt, Name))\n      return true\n    }\n  }\n}\n\nfunction keywordCode(\n  it: SchemaObjCxt,\n  keyword: string,\n  def: AddedKeywordDefinition,\n  ruleType?: JSONType\n): void {\n  const cxt = new KeywordCxt(it, def, keyword)\n  if (\"code\" in def) {\n    def.code(cxt, ruleType)\n  } else if (cxt.$data && def.validate) {\n    funcKeywordCode(cxt, def)\n  } else if (\"macro\" in def) {\n    macroKeywordCode(cxt, def)\n  } else if (def.compile || def.validate) {\n    funcKeywordCode(cxt, def)\n  }\n}\n\nconst JSON_POINTER = /^\\/(?:[^~]|~0|~1)*$/\nconst RELATIVE_JSON_POINTER = /^([0-9]+)(#|\\/(?:[^~]|~0|~1)*)?$/\nexport function getData(\n  $data: string,\n  {dataLevel, dataNames, dataPathArr}: SchemaCxt\n): Code | number {\n  let jsonPointer\n  let data: Code\n  if ($data === \"\") return N.rootData\n  if ($data[0] === \"/\") {\n    if (!JSON_POINTER.test($data)) throw new Error(`Invalid JSON-pointer: ${$data}`)\n    jsonPointer = $data\n    data = N.rootData\n  } else {\n    const matches = RELATIVE_JSON_POINTER.exec($data)\n    if (!matches) throw new Error(`Invalid JSON-pointer: ${$data}`)\n    const up: number = +matches[1]\n    jsonPointer = matches[2]\n    if (jsonPointer === \"#\") {\n      if (up >= dataLevel) throw new Error(errorMsg(\"property/index\", up))\n      return dataPathArr[dataLevel - up]\n    }\n    if (up > dataLevel) throw new Error(errorMsg(\"data\", up))\n    data = dataNames[dataLevel - up]\n    if (!jsonPointer) return data\n  }\n\n  let expr = data\n  const segments = jsonPointer.split(\"/\")\n  for (const segment of segments) {\n    if (segment) {\n      data = _`${data}${getProperty(unescapeJsonPointer(segment))}`\n      expr = _`${expr} && ${data}`\n    }\n  }\n  return expr\n\n  function errorMsg(pointerType: string, up: number): string {\n    return `Cannot access ${pointerType} ${up} levels up, current level is ${dataLevel}`\n  }\n}\n", "import type {ErrorObject} from \"../types\"\n\nexport default class ValidationError extends Error {\n  readonly errors: Partial<ErrorObject>[]\n  readonly ajv: true\n  readonly validation: true\n\n  constructor(errors: Partial<ErrorObject>[]) {\n    super(\"validation failed\")\n    this.errors = errors\n    this.ajv = this.validation = true\n  }\n}\n", "import {resolveUrl, normalizeId, getFullPath} from \"./resolve\"\nimport type {UriResolver} from \"../types\"\n\nexport default class MissingRefError extends Error {\n  readonly missingRef: string\n  readonly missingSchema: string\n\n  constructor(resolver: UriResolver, baseId: string, ref: string, msg?: string) {\n    super(msg || `can't resolve reference ${ref} from id ${baseId}`)\n    this.missingRef = resolveUrl(resolver, baseId, ref)\n    this.missingSchema = normalizeId(getFullPath(resolver, this.missingRef))\n  }\n}\n", "import type {\n  AnySchema,\n  AnySchemaObject,\n  AnyValidateFunction,\n  AsyncValidateFunction,\n  EvaluatedProperties,\n  EvaluatedItems,\n} from \"../types\"\nimport type Ajv from \"../core\"\nimport type {InstanceOptions} from \"../core\"\nimport {CodeGen, _, nil, stringify, Name, Code, ValueScopeName} from \"./codegen\"\nimport ValidationError from \"../runtime/validation_error\"\nimport N from \"./names\"\nimport {LocalRefs, getFullPath, _getFullPath, inlineRef, normalizeId, resolveUrl} from \"./resolve\"\nimport {schemaHasRulesButRef, unescapeFragment} from \"./util\"\nimport {validateFunctionCode} from \"./validate\"\nimport {URIComponent} from \"fast-uri\"\nimport {JSONType} from \"./rules\"\n\nexport type SchemaRefs = {\n  [Ref in string]?: SchemaEnv | AnySchema\n}\n\nexport interface SchemaCxt {\n  readonly gen: CodeGen\n  readonly allErrors?: boolean // validation mode - whether to collect all errors or break on error\n  readonly data: Name // Name with reference to the current part of data instance\n  readonly parentData: Name // should be used in keywords modifying data\n  readonly parentDataProperty: Code | number // should be used in keywords modifying data\n  readonly dataNames: Name[]\n  readonly dataPathArr: (Code | number)[]\n  readonly dataLevel: number // the level of the currently validated data,\n  // it can be used to access both the property names and the data on all levels from the top.\n  dataTypes: JSONType[] // data types applied to the current part of data instance\n  definedProperties: Set<string> // set of properties to keep track of for required checks\n  readonly topSchemaRef: Code\n  readonly validateName: Name\n  evaluated?: Name\n  readonly ValidationError?: Name\n  readonly schema: AnySchema // current schema object - equal to parentSchema passed via KeywordCxt\n  readonly schemaEnv: SchemaEnv\n  readonly rootId: string\n  baseId: string // the current schema base URI that should be used as the base for resolving URIs in references (\\$ref)\n  readonly schemaPath: Code // the run-time expression that evaluates to the property name of the current schema\n  readonly errSchemaPath: string // this is actual string, should not be changed to Code\n  readonly errorPath: Code\n  readonly propertyName?: Name\n  readonly compositeRule?: boolean // true indicates that the current schema is inside the compound keyword,\n  // where failing some rule doesn't mean validation failure (`anyOf`, `oneOf`, `not`, `if`).\n  // This flag is used to determine whether you can return validation result immediately after any error in case the option `allErrors` is not `true.\n  // You only need to use it if you have many steps in your keywords and potentially can define multiple errors.\n  props?: EvaluatedProperties | Name // properties evaluated by this schema - used by parent schema or assigned to validation function\n  items?: EvaluatedItems | Name // last item evaluated by this schema - used by parent schema or assigned to validation function\n  jtdDiscriminator?: string\n  jtdMetadata?: boolean\n  readonly createErrors?: boolean\n  readonly opts: InstanceOptions // Ajv instance option.\n  readonly self: Ajv // current Ajv instance\n}\n\nexport interface SchemaObjCxt extends SchemaCxt {\n  readonly schema: AnySchemaObject\n}\ninterface SchemaEnvArgs {\n  readonly schema: AnySchema\n  readonly schemaId?: \"$id\" | \"id\"\n  readonly root?: SchemaEnv\n  readonly baseId?: string\n  readonly schemaPath?: string\n  readonly localRefs?: LocalRefs\n  readonly meta?: boolean\n}\n\nexport class SchemaEnv implements SchemaEnvArgs {\n  readonly schema: AnySchema\n  readonly schemaId?: \"$id\" | \"id\"\n  readonly root: SchemaEnv\n  baseId: string // TODO possibly, it should be readonly\n  schemaPath?: string\n  localRefs?: LocalRefs\n  readonly meta?: boolean\n  readonly $async?: boolean // true if the current schema is asynchronous.\n  readonly refs: SchemaRefs = {}\n  readonly dynamicAnchors: {[Ref in string]?: true} = {}\n  validate?: AnyValidateFunction\n  validateName?: ValueScopeName\n  serialize?: (data: unknown) => string\n  serializeName?: ValueScopeName\n  parse?: (data: string) => unknown\n  parseName?: ValueScopeName\n\n  constructor(env: SchemaEnvArgs) {\n    let schema: AnySchemaObject | undefined\n    if (typeof env.schema == \"object\") schema = env.schema\n    this.schema = env.schema\n    this.schemaId = env.schemaId\n    this.root = env.root || this\n    this.baseId = env.baseId ?? normalizeId(schema?.[env.schemaId || \"$id\"])\n    this.schemaPath = env.schemaPath\n    this.localRefs = env.localRefs\n    this.meta = env.meta\n    this.$async = schema?.$async\n    this.refs = {}\n  }\n}\n\n// let codeSize = 0\n// let nodeCount = 0\n\n// Compiles schema in SchemaEnv\nexport function compileSchema(this: Ajv, sch: SchemaEnv): SchemaEnv {\n  // TODO refactor - remove compilations\n  const _sch = getCompilingSchema.call(this, sch)\n  if (_sch) return _sch\n  const rootId = getFullPath(this.opts.uriResolver, sch.root.baseId) // TODO if getFullPath removed 1 tests fails\n  const {es5, lines} = this.opts.code\n  const {ownProperties} = this.opts\n  const gen = new CodeGen(this.scope, {es5, lines, ownProperties})\n  let _ValidationError\n  if (sch.$async) {\n    _ValidationError = gen.scopeValue(\"Error\", {\n      ref: ValidationError,\n      code: _`require(\"ajv/dist/runtime/validation_error\").default`,\n    })\n  }\n\n  const validateName = gen.scopeName(\"validate\")\n  sch.validateName = validateName\n\n  const schemaCxt: SchemaCxt = {\n    gen,\n    allErrors: this.opts.allErrors,\n    data: N.data,\n    parentData: N.parentData,\n    parentDataProperty: N.parentDataProperty,\n    dataNames: [N.data],\n    dataPathArr: [nil], // TODO can its length be used as dataLevel if nil is removed?\n    dataLevel: 0,\n    dataTypes: [],\n    definedProperties: new Set<string>(),\n    topSchemaRef: gen.scopeValue(\n      \"schema\",\n      this.opts.code.source === true\n        ? {ref: sch.schema, code: stringify(sch.schema)}\n        : {ref: sch.schema}\n    ),\n    validateName,\n    ValidationError: _ValidationError,\n    schema: sch.schema,\n    schemaEnv: sch,\n    rootId,\n    baseId: sch.baseId || rootId,\n    schemaPath: nil,\n    errSchemaPath: sch.schemaPath || (this.opts.jtd ? \"\" : \"#\"),\n    errorPath: _`\"\"`,\n    opts: this.opts,\n    self: this,\n  }\n\n  let sourceCode: string | undefined\n  try {\n    this._compilations.add(sch)\n    validateFunctionCode(schemaCxt)\n    gen.optimize(this.opts.code.optimize)\n    // gen.optimize(1)\n    const validateCode = gen.toString()\n    sourceCode = `${gen.scopeRefs(N.scope)}return ${validateCode}`\n    // console.log((codeSize += sourceCode.length), (nodeCount += gen.nodeCount))\n    if (this.opts.code.process) sourceCode = this.opts.code.process(sourceCode, sch)\n    // console.log(\"\\n\\n\\n *** \\n\", sourceCode)\n    const makeValidate = new Function(`${N.self}`, `${N.scope}`, sourceCode)\n    const validate: AnyValidateFunction = makeValidate(this, this.scope.get())\n    this.scope.value(validateName, {ref: validate})\n\n    validate.errors = null\n    validate.schema = sch.schema\n    validate.schemaEnv = sch\n    if (sch.$async) (validate as AsyncValidateFunction).$async = true\n    if (this.opts.code.source === true) {\n      validate.source = {validateName, validateCode, scopeValues: gen._values}\n    }\n    if (this.opts.unevaluated) {\n      const {props, items} = schemaCxt\n      validate.evaluated = {\n        props: props instanceof Name ? undefined : props,\n        items: items instanceof Name ? undefined : items,\n        dynamicProps: props instanceof Name,\n        dynamicItems: items instanceof Name,\n      }\n      if (validate.source) validate.source.evaluated = stringify(validate.evaluated)\n    }\n    sch.validate = validate\n    return sch\n  } catch (e) {\n    delete sch.validate\n    delete sch.validateName\n    if (sourceCode) this.logger.error(\"Error compiling schema, function code:\", sourceCode)\n    // console.log(\"\\n\\n\\n *** \\n\", sourceCode, this.opts)\n    throw e\n  } finally {\n    this._compilations.delete(sch)\n  }\n}\n\nexport function resolveRef(\n  this: Ajv,\n  root: SchemaEnv,\n  baseId: string,\n  ref: string\n): AnySchema | SchemaEnv | undefined {\n  ref = resolveUrl(this.opts.uriResolver, baseId, ref)\n  const schOrFunc = root.refs[ref]\n  if (schOrFunc) return schOrFunc\n\n  let _sch = resolve.call(this, root, ref)\n  if (_sch === undefined) {\n    const schema = root.localRefs?.[ref] // TODO maybe localRefs should hold SchemaEnv\n    const {schemaId} = this.opts\n    if (schema) _sch = new SchemaEnv({schema, schemaId, root, baseId})\n  }\n\n  if (_sch === undefined) return\n  return (root.refs[ref] = inlineOrCompile.call(this, _sch))\n}\n\nfunction inlineOrCompile(this: Ajv, sch: SchemaEnv): AnySchema | SchemaEnv {\n  if (inlineRef(sch.schema, this.opts.inlineRefs)) return sch.schema\n  return sch.validate ? sch : compileSchema.call(this, sch)\n}\n\n// Index of schema compilation in the currently compiled list\nexport function getCompilingSchema(this: Ajv, schEnv: SchemaEnv): SchemaEnv | void {\n  for (const sch of this._compilations) {\n    if (sameSchemaEnv(sch, schEnv)) return sch\n  }\n}\n\nfunction sameSchemaEnv(s1: SchemaEnv, s2: SchemaEnv): boolean {\n  return s1.schema === s2.schema && s1.root === s2.root && s1.baseId === s2.baseId\n}\n\n// resolve and compile the references ($ref)\n// TODO returns AnySchemaObject (if the schema can be inlined) or validation function\nfunction resolve(\n  this: Ajv,\n  root: SchemaEnv, // information about the root schema for the current schema\n  ref: string // reference to resolve\n): SchemaEnv | undefined {\n  let sch\n  while (typeof (sch = this.refs[ref]) == \"string\") ref = sch\n  return sch || this.schemas[ref] || resolveSchema.call(this, root, ref)\n}\n\n// Resolve schema, its root and baseId\nexport function resolveSchema(\n  this: Ajv,\n  root: SchemaEnv, // root object with properties schema, refs TODO below SchemaEnv is assigned to it\n  ref: string // reference to resolve\n): SchemaEnv | undefined {\n  const p = this.opts.uriResolver.parse(ref)\n  const refPath = _getFullPath(this.opts.uriResolver, p)\n  let baseId = getFullPath(this.opts.uriResolver, root.baseId, undefined)\n  // TODO `Object.keys(root.schema).length > 0` should not be needed - but removing breaks 2 tests\n  if (Object.keys(root.schema).length > 0 && refPath === baseId) {\n    return getJsonPointer.call(this, p, root)\n  }\n\n  const id = normalizeId(refPath)\n  const schOrRef = this.refs[id] || this.schemas[id]\n  if (typeof schOrRef == \"string\") {\n    const sch = resolveSchema.call(this, root, schOrRef)\n    if (typeof sch?.schema !== \"object\") return\n    return getJsonPointer.call(this, p, sch)\n  }\n\n  if (typeof schOrRef?.schema !== \"object\") return\n  if (!schOrRef.validate) compileSchema.call(this, schOrRef)\n  if (id === normalizeId(ref)) {\n    const {schema} = schOrRef\n    const {schemaId} = this.opts\n    const schId = schema[schemaId]\n    if (schId) baseId = resolveUrl(this.opts.uriResolver, baseId, schId)\n    return new SchemaEnv({schema, schemaId, root, baseId})\n  }\n  return getJsonPointer.call(this, p, schOrRef)\n}\n\nconst PREVENT_SCOPE_CHANGE = new Set([\n  \"properties\",\n  \"patternProperties\",\n  \"enum\",\n  \"dependencies\",\n  \"definitions\",\n])\n\nfunction getJsonPointer(\n  this: Ajv,\n  parsedRef: URIComponent,\n  {baseId, schema, root}: SchemaEnv\n): SchemaEnv | undefined {\n  if (parsedRef.fragment?.[0] !== \"/\") return\n  for (const part of parsedRef.fragment.slice(1).split(\"/\")) {\n    if (typeof schema === \"boolean\") return\n    const partSchema = schema[unescapeFragment(part)]\n    if (partSchema === undefined) return\n    schema = partSchema\n    // TODO PREVENT_SCOPE_CHANGE could be defined in keyword def?\n    const schId = typeof schema === \"object\" && schema[this.opts.schemaId]\n    if (!PREVENT_SCOPE_CHANGE.has(part) && schId) {\n      baseId = resolveUrl(this.opts.uriResolver, baseId, schId)\n    }\n  }\n  let env: SchemaEnv | undefined\n  if (typeof schema != \"boolean\" && schema.$ref && !schemaHasRulesButRef(schema, this.RULES)) {\n    const $ref = resolveUrl(this.opts.uriResolver, baseId, schema.$ref)\n    env = resolveSchema.call(this, root, $ref)\n  }\n  // even though resolution failed we need to return SchemaEnv to throw exception\n  // so that compileAsync loads missing schema.\n  const {schemaId} = this.opts\n  env = env || new SchemaEnv({schema, schemaId, root, baseId})\n  if (env.schema !== env.root.schema) return env\n  return undefined\n}\n", "{\n  \"$id\": \"https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#\",\n  \"description\": \"Meta-schema for $data reference (JSON AnySchema extension proposal)\",\n  \"type\": \"object\",\n  \"required\": [\"$data\"],\n  \"properties\": {\n    \"$data\": {\n      \"type\": \"string\",\n      \"anyOf\": [{\"format\": \"relative-json-pointer\"}, {\"format\": \"json-pointer\"}]\n    }\n  },\n  \"additionalProperties\": false\n}\n", "'use strict'\n\nconst HEX = {\n  0: 0,\n  1: 1,\n  2: 2,\n  3: 3,\n  4: 4,\n  5: 5,\n  6: 6,\n  7: 7,\n  8: 8,\n  9: 9,\n  a: 10,\n  A: 10,\n  b: 11,\n  B: 11,\n  c: 12,\n  C: 12,\n  d: 13,\n  D: 13,\n  e: 14,\n  E: 14,\n  f: 15,\n  F: 15\n}\n\nmodule.exports = {\n  HEX\n}\n", "'use strict'\n\nconst { HEX } = require('./scopedChars')\n\nconst IPV4_REG = /^(?:(?:25[0-5]|2[0-4]\\d|1\\d{2}|[1-9]\\d|\\d)\\.){3}(?:25[0-5]|2[0-4]\\d|1\\d{2}|[1-9]\\d|\\d)$/u\n\nfunction normalizeIPv4 (host) {\n  if (findToken(host, '.') < 3) { return { host, isIPV4: false } }\n  const matches = host.match(IPV4_REG) || []\n  const [address] = matches\n  if (address) {\n    return { host: stripLeadingZeros(address, '.'), isIPV4: true }\n  } else {\n    return { host, isIPV4: false }\n  }\n}\n\n/**\n * @param {string[]} input\n * @param {boolean} [keepZero=false]\n * @returns {string|undefined}\n */\nfunction stringArrayToHexStripped (input, keepZero = false) {\n  let acc = ''\n  let strip = true\n  for (const c of input) {\n    if (HEX[c] === undefined) return undefined\n    if (c !== '0' && strip === true) strip = false\n    if (!strip) acc += c\n  }\n  if (keepZero && acc.length === 0) acc = '0'\n  return acc\n}\n\nfunction getIPV6 (input) {\n  let tokenCount = 0\n  const output = { error: false, address: '', zone: '' }\n  const address = []\n  const buffer = []\n  let isZone = false\n  let endipv6Encountered = false\n  let endIpv6 = false\n\n  function consume () {\n    if (buffer.length) {\n      if (isZone === false) {\n        const hex = stringArrayToHexStripped(buffer)\n        if (hex !== undefined) {\n          address.push(hex)\n        } else {\n          output.error = true\n          return false\n        }\n      }\n      buffer.length = 0\n    }\n    return true\n  }\n\n  for (let i = 0; i < input.length; i++) {\n    const cursor = input[i]\n    if (cursor === '[' || cursor === ']') { continue }\n    if (cursor === ':') {\n      if (endipv6Encountered === true) {\n        endIpv6 = true\n      }\n      if (!consume()) { break }\n      tokenCount++\n      address.push(':')\n      if (tokenCount > 7) {\n        // not valid\n        output.error = true\n        break\n      }\n      if (i - 1 >= 0 && input[i - 1] === ':') {\n        endipv6Encountered = true\n      }\n      continue\n    } else if (cursor === '%') {\n      if (!consume()) { break }\n      // switch to zone detection\n      isZone = true\n    } else {\n      buffer.push(cursor)\n      continue\n    }\n  }\n  if (buffer.length) {\n    if (isZone) {\n      output.zone = buffer.join('')\n    } else if (endIpv6) {\n      address.push(buffer.join(''))\n    } else {\n      address.push(stringArrayToHexStripped(buffer))\n    }\n  }\n  output.address = address.join('')\n  return output\n}\n\nfunction normalizeIPv6 (host) {\n  if (findToken(host, ':') < 2) { return { host, isIPV6: false } }\n  const ipv6 = getIPV6(host)\n\n  if (!ipv6.error) {\n    let newHost = ipv6.address\n    let escapedHost = ipv6.address\n    if (ipv6.zone) {\n      newHost += '%' + ipv6.zone\n      escapedHost += '%25' + ipv6.zone\n    }\n    return { host: newHost, escapedHost, isIPV6: true }\n  } else {\n    return { host, isIPV6: false }\n  }\n}\n\nfunction stripLeadingZeros (str, token) {\n  let out = ''\n  let skip = true\n  const l = str.length\n  for (let i = 0; i < l; i++) {\n    const c = str[i]\n    if (c === '0' && skip) {\n      if ((i + 1 <= l && str[i + 1] === token) || i + 1 === l) {\n        out += c\n        skip = false\n      }\n    } else {\n      if (c === token) {\n        skip = true\n      } else {\n        skip = false\n      }\n      out += c\n    }\n  }\n  return out\n}\n\nfunction findToken (str, token) {\n  let ind = 0\n  for (let i = 0; i < str.length; i++) {\n    if (str[i] === token) ind++\n  }\n  return ind\n}\n\nconst RDS1 = /^\\.\\.?\\//u\nconst RDS2 = /^\\/\\.(?:\\/|$)/u\nconst RDS3 = /^\\/\\.\\.(?:\\/|$)/u\nconst RDS5 = /^\\/?(?:.|\\n)*?(?=\\/|$)/u\n\nfunction removeDotSegments (input) {\n  const output = []\n\n  while (input.length) {\n    if (input.match(RDS1)) {\n      input = input.replace(RDS1, '')\n    } else if (input.match(RDS2)) {\n      input = input.replace(RDS2, '/')\n    } else if (input.match(RDS3)) {\n      input = input.replace(RDS3, '/')\n      output.pop()\n    } else if (input === '.' || input === '..') {\n      input = ''\n    } else {\n      const im = input.match(RDS5)\n      if (im) {\n        const s = im[0]\n        input = input.slice(s.length)\n        output.push(s)\n      } else {\n        throw new Error('Unexpected dot segment condition')\n      }\n    }\n  }\n  return output.join('')\n}\n\nfunction normalizeComponentEncoding (components, esc) {\n  const func = esc !== true ? escape : unescape\n  if (components.scheme !== undefined) {\n    components.scheme = func(components.scheme)\n  }\n  if (components.userinfo !== undefined) {\n    components.userinfo = func(components.userinfo)\n  }\n  if (components.host !== undefined) {\n    components.host = func(components.host)\n  }\n  if (components.path !== undefined) {\n    components.path = func(components.path)\n  }\n  if (components.query !== undefined) {\n    components.query = func(components.query)\n  }\n  if (components.fragment !== undefined) {\n    components.fragment = func(components.fragment)\n  }\n  return components\n}\n\nfunction recomposeAuthority (components) {\n  const uriTokens = []\n\n  if (components.userinfo !== undefined) {\n    uriTokens.push(components.userinfo)\n    uriTokens.push('@')\n  }\n\n  if (components.host !== undefined) {\n    let host = unescape(components.host)\n    const ipV4res = normalizeIPv4(host)\n\n    if (ipV4res.isIPV4) {\n      host = ipV4res.host\n    } else {\n      const ipV6res = normalizeIPv6(ipV4res.host)\n      if (ipV6res.isIPV6 === true) {\n        host = `[${ipV6res.escapedHost}]`\n      } else {\n        host = components.host\n      }\n    }\n    uriTokens.push(host)\n  }\n\n  if (typeof components.port === 'number' || typeof components.port === 'string') {\n    uriTokens.push(':')\n    uriTokens.push(String(components.port))\n  }\n\n  return uriTokens.length ? uriTokens.join('') : undefined\n};\n\nmodule.exports = {\n  recomposeAuthority,\n  normalizeComponentEncoding,\n  removeDotSegments,\n  normalizeIPv4,\n  normalizeIPv6,\n  stringArrayToHexStripped\n}\n", "'use strict'\n\nconst UUID_REG = /^[\\da-f]{8}-[\\da-f]{4}-[\\da-f]{4}-[\\da-f]{4}-[\\da-f]{12}$/iu\nconst URN_REG = /([\\da-z][\\d\\-a-z]{0,31}):((?:[\\w!$'()*+,\\-.:;=@]|%[\\da-f]{2})+)/iu\n\nfunction isSecure (wsComponents) {\n  return typeof wsComponents.secure === 'boolean' ? wsComponents.secure : String(wsComponents.scheme).toLowerCase() === 'wss'\n}\n\nfunction httpParse (components) {\n  if (!components.host) {\n    components.error = components.error || 'HTTP URIs must have a host.'\n  }\n\n  return components\n}\n\nfunction httpSerialize (components) {\n  const secure = String(components.scheme).toLowerCase() === 'https'\n\n  // normalize the default port\n  if (components.port === (secure ? 443 : 80) || components.port === '') {\n    components.port = undefined\n  }\n\n  // normalize the empty path\n  if (!components.path) {\n    components.path = '/'\n  }\n\n  // NOTE: We do not parse query strings for HTTP URIs\n  // as WWW Form Url Encoded query strings are part of the HTML4+ spec,\n  // and not the HTTP spec.\n\n  return components\n}\n\nfunction wsParse (wsComponents) {\n// indicate if the secure flag is set\n  wsComponents.secure = isSecure(wsComponents)\n\n  // construct resouce name\n  wsComponents.resourceName = (wsComponents.path || '/') + (wsComponents.query ? '?' + wsComponents.query : '')\n  wsComponents.path = undefined\n  wsComponents.query = undefined\n\n  return wsComponents\n}\n\nfunction wsSerialize (wsComponents) {\n// normalize the default port\n  if (wsComponents.port === (isSecure(wsComponents) ? 443 : 80) || wsComponents.port === '') {\n    wsComponents.port = undefined\n  }\n\n  // ensure scheme matches secure flag\n  if (typeof wsComponents.secure === 'boolean') {\n    wsComponents.scheme = (wsComponents.secure ? 'wss' : 'ws')\n    wsComponents.secure = undefined\n  }\n\n  // reconstruct path from resource name\n  if (wsComponents.resourceName) {\n    const [path, query] = wsComponents.resourceName.split('?')\n    wsComponents.path = (path && path !== '/' ? path : undefined)\n    wsComponents.query = query\n    wsComponents.resourceName = undefined\n  }\n\n  // forbid fragment component\n  wsComponents.fragment = undefined\n\n  return wsComponents\n}\n\nfunction urnParse (urnComponents, options) {\n  if (!urnComponents.path) {\n    urnComponents.error = 'URN can not be parsed'\n    return urnComponents\n  }\n  const matches = urnComponents.path.match(URN_REG)\n  if (matches) {\n    const scheme = options.scheme || urnComponents.scheme || 'urn'\n    urnComponents.nid = matches[1].toLowerCase()\n    urnComponents.nss = matches[2]\n    const urnScheme = `${scheme}:${options.nid || urnComponents.nid}`\n    const schemeHandler = SCHEMES[urnScheme]\n    urnComponents.path = undefined\n\n    if (schemeHandler) {\n      urnComponents = schemeHandler.parse(urnComponents, options)\n    }\n  } else {\n    urnComponents.error = urnComponents.error || 'URN can not be parsed.'\n  }\n\n  return urnComponents\n}\n\nfunction urnSerialize (urnComponents, options) {\n  const scheme = options.scheme || urnComponents.scheme || 'urn'\n  const nid = urnComponents.nid.toLowerCase()\n  const urnScheme = `${scheme}:${options.nid || nid}`\n  const schemeHandler = SCHEMES[urnScheme]\n\n  if (schemeHandler) {\n    urnComponents = schemeHandler.serialize(urnComponents, options)\n  }\n\n  const uriComponents = urnComponents\n  const nss = urnComponents.nss\n  uriComponents.path = `${nid || options.nid}:${nss}`\n\n  options.skipEscape = true\n  return uriComponents\n}\n\nfunction urnuuidParse (urnComponents, options) {\n  const uuidComponents = urnComponents\n  uuidComponents.uuid = uuidComponents.nss\n  uuidComponents.nss = undefined\n\n  if (!options.tolerant && (!uuidComponents.uuid || !UUID_REG.test(uuidComponents.uuid))) {\n    uuidComponents.error = uuidComponents.error || 'UUID is not valid.'\n  }\n\n  return uuidComponents\n}\n\nfunction urnuuidSerialize (uuidComponents) {\n  const urnComponents = uuidComponents\n  // normalize UUID\n  urnComponents.nss = (uuidComponents.uuid || '').toLowerCase()\n  return urnComponents\n}\n\nconst http = {\n  scheme: 'http',\n  domainHost: true,\n  parse: httpParse,\n  serialize: httpSerialize\n}\n\nconst https = {\n  scheme: 'https',\n  domainHost: http.domainHost,\n  parse: httpParse,\n  serialize: httpSerialize\n}\n\nconst ws = {\n  scheme: 'ws',\n  domainHost: true,\n  parse: wsParse,\n  serialize: wsSerialize\n}\n\nconst wss = {\n  scheme: 'wss',\n  domainHost: ws.domainHost,\n  parse: ws.parse,\n  serialize: ws.serialize\n}\n\nconst urn = {\n  scheme: 'urn',\n  parse: urnParse,\n  serialize: urnSerialize,\n  skipNormalize: true\n}\n\nconst urnuuid = {\n  scheme: 'urn:uuid',\n  parse: urnuuidParse,\n  serialize: urnuuidSerialize,\n  skipNormalize: true\n}\n\nconst SCHEMES = {\n  http,\n  https,\n  ws,\n  wss,\n  urn,\n  'urn:uuid': urnuuid\n}\n\nmodule.exports = SCHEMES\n", "'use strict'\n\nconst { normalizeIPv6, normalizeIPv4, removeDotSegments, recomposeAuthority, normalizeComponentEncoding } = require('./lib/utils')\nconst SCHEMES = require('./lib/schemes')\n\nfunction normalize (uri, options) {\n  if (typeof uri === 'string') {\n    uri = serialize(parse(uri, options), options)\n  } else if (typeof uri === 'object') {\n    uri = parse(serialize(uri, options), options)\n  }\n  return uri\n}\n\nfunction resolve (baseURI, relativeURI, options) {\n  const schemelessOptions = Object.assign({ scheme: 'null' }, options)\n  const resolved = resolveComponents(parse(baseURI, schemelessOptions), parse(relativeURI, schemelessOptions), schemelessOptions, true)\n  return serialize(resolved, { ...schemelessOptions, skipEscape: true })\n}\n\nfunction resolveComponents (base, relative, options, skipNormalization) {\n  const target = {}\n  if (!skipNormalization) {\n    base = parse(serialize(base, options), options) // normalize base components\n    relative = parse(serialize(relative, options), options) // normalize relative components\n  }\n  options = options || {}\n\n  if (!options.tolerant && relative.scheme) {\n    target.scheme = relative.scheme\n    // target.authority = relative.authority;\n    target.userinfo = relative.userinfo\n    target.host = relative.host\n    target.port = relative.port\n    target.path = removeDotSegments(relative.path || '')\n    target.query = relative.query\n  } else {\n    if (relative.userinfo !== undefined || relative.host !== undefined || relative.port !== undefined) {\n      // target.authority = relative.authority;\n      target.userinfo = relative.userinfo\n      target.host = relative.host\n      target.port = relative.port\n      target.path = removeDotSegments(relative.path || '')\n      target.query = relative.query\n    } else {\n      if (!relative.path) {\n        target.path = base.path\n        if (relative.query !== undefined) {\n          target.query = relative.query\n        } else {\n          target.query = base.query\n        }\n      } else {\n        if (relative.path.charAt(0) === '/') {\n          target.path = removeDotSegments(relative.path)\n        } else {\n          if ((base.userinfo !== undefined || base.host !== undefined || base.port !== undefined) && !base.path) {\n            target.path = '/' + relative.path\n          } else if (!base.path) {\n            target.path = relative.path\n          } else {\n            target.path = base.path.slice(0, base.path.lastIndexOf('/') + 1) + relative.path\n          }\n          target.path = removeDotSegments(target.path)\n        }\n        target.query = relative.query\n      }\n      // target.authority = base.authority;\n      target.userinfo = base.userinfo\n      target.host = base.host\n      target.port = base.port\n    }\n    target.scheme = base.scheme\n  }\n\n  target.fragment = relative.fragment\n\n  return target\n}\n\nfunction equal (uriA, uriB, options) {\n  if (typeof uriA === 'string') {\n    uriA = unescape(uriA)\n    uriA = serialize(normalizeComponentEncoding(parse(uriA, options), true), { ...options, skipEscape: true })\n  } else if (typeof uriA === 'object') {\n    uriA = serialize(normalizeComponentEncoding(uriA, true), { ...options, skipEscape: true })\n  }\n\n  if (typeof uriB === 'string') {\n    uriB = unescape(uriB)\n    uriB = serialize(normalizeComponentEncoding(parse(uriB, options), true), { ...options, skipEscape: true })\n  } else if (typeof uriB === 'object') {\n    uriB = serialize(normalizeComponentEncoding(uriB, true), { ...options, skipEscape: true })\n  }\n\n  return uriA.toLowerCase() === uriB.toLowerCase()\n}\n\nfunction serialize (cmpts, opts) {\n  const components = {\n    host: cmpts.host,\n    scheme: cmpts.scheme,\n    userinfo: cmpts.userinfo,\n    port: cmpts.port,\n    path: cmpts.path,\n    query: cmpts.query,\n    nid: cmpts.nid,\n    nss: cmpts.nss,\n    uuid: cmpts.uuid,\n    fragment: cmpts.fragment,\n    reference: cmpts.reference,\n    resourceName: cmpts.resourceName,\n    secure: cmpts.secure,\n    error: ''\n  }\n  const options = Object.assign({}, opts)\n  const uriTokens = []\n\n  // find scheme handler\n  const schemeHandler = SCHEMES[(options.scheme || components.scheme || '').toLowerCase()]\n\n  // perform scheme specific serialization\n  if (schemeHandler && schemeHandler.serialize) schemeHandler.serialize(components, options)\n\n  if (components.path !== undefined) {\n    if (!options.skipEscape) {\n      components.path = escape(components.path)\n\n      if (components.scheme !== undefined) {\n        components.path = components.path.split('%3A').join(':')\n      }\n    } else {\n      components.path = unescape(components.path)\n    }\n  }\n\n  if (options.reference !== 'suffix' && components.scheme) {\n    uriTokens.push(components.scheme, ':')\n  }\n\n  const authority = recomposeAuthority(components)\n  if (authority !== undefined) {\n    if (options.reference !== 'suffix') {\n      uriTokens.push('//')\n    }\n\n    uriTokens.push(authority)\n\n    if (components.path && components.path.charAt(0) !== '/') {\n      uriTokens.push('/')\n    }\n  }\n  if (components.path !== undefined) {\n    let s = components.path\n\n    if (!options.absolutePath && (!schemeHandler || !schemeHandler.absolutePath)) {\n      s = removeDotSegments(s)\n    }\n\n    if (authority === undefined) {\n      s = s.replace(/^\\/\\//u, '/%2F') // don't allow the path to start with \"//\"\n    }\n\n    uriTokens.push(s)\n  }\n\n  if (components.query !== undefined) {\n    uriTokens.push('?', components.query)\n  }\n\n  if (components.fragment !== undefined) {\n    uriTokens.push('#', components.fragment)\n  }\n  return uriTokens.join('')\n}\n\nconst hexLookUp = Array.from({ length: 127 }, (_v, k) => /[^!\"$&'()*+,\\-.;=_`a-z{}~]/u.test(String.fromCharCode(k)))\n\nfunction nonSimpleDomain (value) {\n  let code = 0\n  for (let i = 0, len = value.length; i < len; ++i) {\n    code = value.charCodeAt(i)\n    if (code > 126 || hexLookUp[code]) {\n      return true\n    }\n  }\n  return false\n}\n\nconst URI_PARSE = /^(?:([^#/:?]+):)?(?:\\/\\/((?:([^#/?@]*)@)?(\\[[^#/?\\]]+\\]|[^#/:?]*)(?::(\\d*))?))?([^#?]*)(?:\\?([^#]*))?(?:#((?:.|[\\n\\r])*))?/u\n\nfunction parse (uri, opts) {\n  const options = Object.assign({}, opts)\n  const parsed = {\n    scheme: undefined,\n    userinfo: undefined,\n    host: '',\n    port: undefined,\n    path: '',\n    query: undefined,\n    fragment: undefined\n  }\n  const gotEncoding = uri.indexOf('%') !== -1\n  let isIP = false\n  if (options.reference === 'suffix') uri = (options.scheme ? options.scheme + ':' : '') + '//' + uri\n\n  const matches = uri.match(URI_PARSE)\n\n  if (matches) {\n    // store each component\n    parsed.scheme = matches[1]\n    parsed.userinfo = matches[3]\n    parsed.host = matches[4]\n    parsed.port = parseInt(matches[5], 10)\n    parsed.path = matches[6] || ''\n    parsed.query = matches[7]\n    parsed.fragment = matches[8]\n\n    // fix port number\n    if (isNaN(parsed.port)) {\n      parsed.port = matches[5]\n    }\n    if (parsed.host) {\n      const ipv4result = normalizeIPv4(parsed.host)\n      if (ipv4result.isIPV4 === false) {\n        const ipv6result = normalizeIPv6(ipv4result.host)\n        parsed.host = ipv6result.host.toLowerCase()\n        isIP = ipv6result.isIPV6\n      } else {\n        parsed.host = ipv4result.host\n        isIP = true\n      }\n    }\n    if (parsed.scheme === undefined && parsed.userinfo === undefined && parsed.host === undefined && parsed.port === undefined && parsed.query === undefined && !parsed.path) {\n      parsed.reference = 'same-document'\n    } else if (parsed.scheme === undefined) {\n      parsed.reference = 'relative'\n    } else if (parsed.fragment === undefined) {\n      parsed.reference = 'absolute'\n    } else {\n      parsed.reference = 'uri'\n    }\n\n    // check for reference errors\n    if (options.reference && options.reference !== 'suffix' && options.reference !== parsed.reference) {\n      parsed.error = parsed.error || 'URI is not a ' + options.reference + ' reference.'\n    }\n\n    // find scheme handler\n    const schemeHandler = SCHEMES[(options.scheme || parsed.scheme || '').toLowerCase()]\n\n    // check if scheme can't handle IRIs\n    if (!options.unicodeSupport && (!schemeHandler || !schemeHandler.unicodeSupport)) {\n      // if host component is a domain name\n      if (parsed.host && (options.domainHost || (schemeHandler && schemeHandler.domainHost)) && isIP === false && nonSimpleDomain(parsed.host)) {\n        // convert Unicode IDN -> ASCII IDN\n        try {\n          parsed.host = URL.domainToASCII(parsed.host.toLowerCase())\n        } catch (e) {\n          parsed.error = parsed.error || \"Host's domain name can not be converted to ASCII: \" + e\n        }\n      }\n      // convert IRI -> URI\n    }\n\n    if (!schemeHandler || (schemeHandler && !schemeHandler.skipNormalize)) {\n      if (gotEncoding && parsed.scheme !== undefined) {\n        parsed.scheme = unescape(parsed.scheme)\n      }\n      if (gotEncoding && parsed.host !== undefined) {\n        parsed.host = unescape(parsed.host)\n      }\n      if (parsed.path) {\n        parsed.path = escape(unescape(parsed.path))\n      }\n      if (parsed.fragment) {\n        parsed.fragment = encodeURI(decodeURIComponent(parsed.fragment))\n      }\n    }\n\n    // perform scheme specific parsing\n    if (schemeHandler && schemeHandler.parse) {\n      schemeHandler.parse(parsed, options)\n    }\n  } else {\n    parsed.error = parsed.error || 'URI can not be parsed.'\n  }\n  return parsed\n}\n\nconst fastUri = {\n  SCHEMES,\n  normalize,\n  resolve,\n  resolveComponents,\n  equal,\n  serialize,\n  parse\n}\n\nmodule.exports = fastUri\nmodule.exports.default = fastUri\nmodule.exports.fastUri = fastUri\n", "import * as uri from \"fast-uri\"\n\ntype URI = typeof uri & {code: string}\n;(uri as URI).code = 'require(\"ajv/dist/runtime/uri\").default'\n\nexport default uri as URI\n", "export {\n  Format,\n  FormatDefinition,\n  AsyncFormatDefinition,\n  KeywordDefinition,\n  KeywordErrorDefinition,\n  CodeKeywordDefinition,\n  MacroKeywordDefinition,\n  FuncKeywordDefinition,\n  Vocabulary,\n  Schema,\n  SchemaObject,\n  AnySchemaObject,\n  AsyncSchema,\n  AnySchema,\n  ValidateFunction,\n  AsyncValidateFunction,\n  AnyValidateFunction,\n  ErrorObject,\n  ErrorNoParams,\n} from \"./types\"\n\nexport {SchemaCxt, SchemaObjCxt} from \"./compile\"\nexport interface Plugin<Opts> {\n  (ajv: Ajv, options?: Opts): Ajv\n  [prop: string]: any\n}\n\nexport {KeywordCxt} from \"./compile/validate\"\nexport {DefinedError} from \"./vocabularies/errors\"\nexport {JSONType} from \"./compile/rules\"\nexport {JSONSchemaType} from \"./types/json-schema\"\nexport {JTDSchemaType, SomeJTDSchemaType, JTDDataType} from \"./types/jtd-schema\"\nexport {_, str, stringify, nil, Name, Code, CodeGen, CodeGenOptions} from \"./compile/codegen\"\n\nimport type {\n  Schema,\n  AnySchema,\n  AnySchemaObject,\n  SchemaObject,\n  AsyncSchema,\n  Vocabulary,\n  KeywordDefinition,\n  AddedKeywordDefinition,\n  AnyValidateFunction,\n  ValidateFunction,\n  AsyncValidateFunction,\n  ErrorObject,\n  Format,\n  AddedFormat,\n  RegExpEngine,\n  UriResolver,\n} from \"./types\"\nimport type {JSONSchemaType} from \"./types/json-schema\"\nimport type {JTDSchemaType, SomeJTDSchemaType, JTDDataType} from \"./types/jtd-schema\"\nimport ValidationError from \"./runtime/validation_error\"\nimport MissingRefError from \"./compile/ref_error\"\nimport {getRules, ValidationRules, Rule, RuleGroup, JSONType} from \"./compile/rules\"\nimport {SchemaEnv, compileSchema, resolveSchema} from \"./compile\"\nimport {Code, ValueScope} from \"./compile/codegen\"\nimport {normalizeId, getSchemaRefs} from \"./compile/resolve\"\nimport {getJSONTypes} from \"./compile/validate/dataType\"\nimport {eachItem} from \"./compile/util\"\nimport * as $dataRefSchema from \"./refs/data.json\"\n\nimport DefaultUriResolver from \"./runtime/uri\"\n\nconst defaultRegExp: RegExpEngine = (str, flags) => new RegExp(str, flags)\ndefaultRegExp.code = \"new RegExp\"\n\nconst META_IGNORE_OPTIONS: (keyof Options)[] = [\"removeAdditional\", \"useDefaults\", \"coerceTypes\"]\nconst EXT_SCOPE_NAMES = new Set([\n  \"validate\",\n  \"serialize\",\n  \"parse\",\n  \"wrapper\",\n  \"root\",\n  \"schema\",\n  \"keyword\",\n  \"pattern\",\n  \"formats\",\n  \"validate$data\",\n  \"func\",\n  \"obj\",\n  \"Error\",\n])\n\nexport type Options = CurrentOptions & DeprecatedOptions\n\nexport interface CurrentOptions {\n  // strict mode options (NEW)\n  strict?: boolean | \"log\"\n  strictSchema?: boolean | \"log\"\n  strictNumbers?: boolean | \"log\"\n  strictTypes?: boolean | \"log\"\n  strictTuples?: boolean | \"log\"\n  strictRequired?: boolean | \"log\"\n  allowMatchingProperties?: boolean // disables a strict mode restriction\n  allowUnionTypes?: boolean\n  validateFormats?: boolean\n  // validation and reporting options:\n  $data?: boolean\n  allErrors?: boolean\n  verbose?: boolean\n  discriminator?: boolean\n  unicodeRegExp?: boolean\n  timestamp?: \"string\" | \"date\" // JTD only\n  parseDate?: boolean // JTD only\n  allowDate?: boolean // JTD only\n  $comment?:\n    | true\n    | ((comment: string, schemaPath?: string, rootSchema?: AnySchemaObject) => unknown)\n  formats?: {[Name in string]?: Format}\n  keywords?: Vocabulary\n  schemas?: AnySchema[] | {[Key in string]?: AnySchema}\n  logger?: Logger | false\n  loadSchema?: (uri: string) => Promise<AnySchemaObject>\n  // options to modify validated data:\n  removeAdditional?: boolean | \"all\" | \"failing\"\n  useDefaults?: boolean | \"empty\"\n  coerceTypes?: boolean | \"array\"\n  // advanced options:\n  next?: boolean // NEW\n  unevaluated?: boolean // NEW\n  dynamicRef?: boolean // NEW\n  schemaId?: \"id\" | \"$id\"\n  jtd?: boolean // NEW\n  meta?: SchemaObject | boolean\n  defaultMeta?: string | AnySchemaObject\n  validateSchema?: boolean | \"log\"\n  addUsedSchema?: boolean\n  inlineRefs?: boolean | number\n  passContext?: boolean\n  loopRequired?: number\n  loopEnum?: number // NEW\n  ownProperties?: boolean\n  multipleOfPrecision?: number\n  int32range?: boolean // JTD only\n  messages?: boolean\n  code?: CodeOptions // NEW\n  uriResolver?: UriResolver\n}\n\nexport interface CodeOptions {\n  es5?: boolean\n  esm?: boolean\n  lines?: boolean\n  optimize?: boolean | number\n  formats?: Code // code to require (or construct) map of available formats - for standalone code\n  source?: boolean\n  process?: (code: string, schema?: SchemaEnv) => string\n  regExp?: RegExpEngine\n}\n\ninterface InstanceCodeOptions extends CodeOptions {\n  regExp: RegExpEngine\n  optimize: number\n}\n\ninterface DeprecatedOptions {\n  /** @deprecated */\n  ignoreKeywordsWithRef?: boolean\n  /** @deprecated */\n  jsPropertySyntax?: boolean // added instead of jsonPointers\n  /** @deprecated */\n  unicode?: boolean\n}\n\ninterface RemovedOptions {\n  format?: boolean\n  errorDataPath?: \"object\" | \"property\"\n  nullable?: boolean // \"nullable\" keyword is supported by default\n  jsonPointers?: boolean\n  extendRefs?: true | \"ignore\" | \"fail\"\n  missingRefs?: true | \"ignore\" | \"fail\"\n  processCode?: (code: string, schema?: SchemaEnv) => string\n  sourceCode?: boolean\n  strictDefaults?: boolean\n  strictKeywords?: boolean\n  uniqueItems?: boolean\n  unknownFormats?: true | string[] | \"ignore\"\n  cache?: any\n  serialize?: (schema: AnySchema) => unknown\n  ajvErrors?: boolean\n}\n\ntype OptionsInfo<T extends RemovedOptions | DeprecatedOptions> = {\n  [K in keyof T]-?: string | undefined\n}\n\nconst removedOptions: OptionsInfo<RemovedOptions> = {\n  errorDataPath: \"\",\n  format: \"`validateFormats: false` can be used instead.\",\n  nullable: '\"nullable\" keyword is supported by default.',\n  jsonPointers: \"Deprecated jsPropertySyntax can be used instead.\",\n  extendRefs: \"Deprecated ignoreKeywordsWithRef can be used instead.\",\n  missingRefs: \"Pass empty schema with $id that should be ignored to ajv.addSchema.\",\n  processCode: \"Use option `code: {process: (code, schemaEnv: object) => string}`\",\n  sourceCode: \"Use option `code: {source: true}`\",\n  strictDefaults: \"It is default now, see option `strict`.\",\n  strictKeywords: \"It is default now, see option `strict`.\",\n  uniqueItems: '\"uniqueItems\" keyword is always validated.',\n  unknownFormats: \"Disable strict mode or pass `true` to `ajv.addFormat` (or `formats` option).\",\n  cache: \"Map is used as cache, schema object as key.\",\n  serialize: \"Map is used as cache, schema object as key.\",\n  ajvErrors: \"It is default now.\",\n}\n\nconst deprecatedOptions: OptionsInfo<DeprecatedOptions> = {\n  ignoreKeywordsWithRef: \"\",\n  jsPropertySyntax: \"\",\n  unicode: '\"minLength\"/\"maxLength\" account for unicode characters by default.',\n}\n\ntype RequiredInstanceOptions = {\n  [K in\n    | \"strictSchema\"\n    | \"strictNumbers\"\n    | \"strictTypes\"\n    | \"strictTuples\"\n    | \"strictRequired\"\n    | \"inlineRefs\"\n    | \"loopRequired\"\n    | \"loopEnum\"\n    | \"meta\"\n    | \"messages\"\n    | \"schemaId\"\n    | \"addUsedSchema\"\n    | \"validateSchema\"\n    | \"validateFormats\"\n    | \"int32range\"\n    | \"unicodeRegExp\"\n    | \"uriResolver\"]: NonNullable<Options[K]>\n} & {code: InstanceCodeOptions}\n\nexport type InstanceOptions = Options & RequiredInstanceOptions\n\nconst MAX_EXPRESSION = 200\n\n// eslint-disable-next-line complexity\nfunction requiredOptions(o: Options): RequiredInstanceOptions {\n  const s = o.strict\n  const _optz = o.code?.optimize\n  const optimize = _optz === true || _optz === undefined ? 1 : _optz || 0\n  const regExp = o.code?.regExp ?? defaultRegExp\n  const uriResolver = o.uriResolver ?? DefaultUriResolver\n  return {\n    strictSchema: o.strictSchema ?? s ?? true,\n    strictNumbers: o.strictNumbers ?? s ?? true,\n    strictTypes: o.strictTypes ?? s ?? \"log\",\n    strictTuples: o.strictTuples ?? s ?? \"log\",\n    strictRequired: o.strictRequired ?? s ?? false,\n    code: o.code ? {...o.code, optimize, regExp} : {optimize, regExp},\n    loopRequired: o.loopRequired ?? MAX_EXPRESSION,\n    loopEnum: o.loopEnum ?? MAX_EXPRESSION,\n    meta: o.meta ?? true,\n    messages: o.messages ?? true,\n    inlineRefs: o.inlineRefs ?? true,\n    schemaId: o.schemaId ?? \"$id\",\n    addUsedSchema: o.addUsedSchema ?? true,\n    validateSchema: o.validateSchema ?? true,\n    validateFormats: o.validateFormats ?? true,\n    unicodeRegExp: o.unicodeRegExp ?? true,\n    int32range: o.int32range ?? true,\n    uriResolver: uriResolver,\n  }\n}\n\nexport interface Logger {\n  log(...args: unknown[]): unknown\n  warn(...args: unknown[]): unknown\n  error(...args: unknown[]): unknown\n}\n\nexport default class Ajv {\n  opts: InstanceOptions\n  errors?: ErrorObject[] | null // errors from the last validation\n  logger: Logger\n  // shared external scope values for compiled functions\n  readonly scope: ValueScope\n  readonly schemas: {[Key in string]?: SchemaEnv} = {}\n  readonly refs: {[Ref in string]?: SchemaEnv | string} = {}\n  readonly formats: {[Name in string]?: AddedFormat} = {}\n  readonly RULES: ValidationRules\n  readonly _compilations: Set<SchemaEnv> = new Set()\n  private readonly _loading: {[Ref in string]?: Promise<AnySchemaObject>} = {}\n  private readonly _cache: Map<AnySchema, SchemaEnv> = new Map()\n  private readonly _metaOpts: InstanceOptions\n\n  static ValidationError = ValidationError\n  static MissingRefError = MissingRefError\n\n  constructor(opts: Options = {}) {\n    opts = this.opts = {...opts, ...requiredOptions(opts)}\n    const {es5, lines} = this.opts.code\n\n    this.scope = new ValueScope({scope: {}, prefixes: EXT_SCOPE_NAMES, es5, lines})\n    this.logger = getLogger(opts.logger)\n    const formatOpt = opts.validateFormats\n    opts.validateFormats = false\n\n    this.RULES = getRules()\n    checkOptions.call(this, removedOptions, opts, \"NOT SUPPORTED\")\n    checkOptions.call(this, deprecatedOptions, opts, \"DEPRECATED\", \"warn\")\n    this._metaOpts = getMetaSchemaOptions.call(this)\n\n    if (opts.formats) addInitialFormats.call(this)\n    this._addVocabularies()\n    this._addDefaultMetaSchema()\n    if (opts.keywords) addInitialKeywords.call(this, opts.keywords)\n    if (typeof opts.meta == \"object\") this.addMetaSchema(opts.meta)\n    addInitialSchemas.call(this)\n    opts.validateFormats = formatOpt\n  }\n\n  _addVocabularies(): void {\n    this.addKeyword(\"$async\")\n  }\n\n  _addDefaultMetaSchema(): void {\n    const {$data, meta, schemaId} = this.opts\n    let _dataRefSchema: SchemaObject = $dataRefSchema\n    if (schemaId === \"id\") {\n      _dataRefSchema = {...$dataRefSchema}\n      _dataRefSchema.id = _dataRefSchema.$id\n      delete _dataRefSchema.$id\n    }\n    if (meta && $data) this.addMetaSchema(_dataRefSchema, _dataRefSchema[schemaId], false)\n  }\n\n  defaultMeta(): string | AnySchemaObject | undefined {\n    const {meta, schemaId} = this.opts\n    return (this.opts.defaultMeta = typeof meta == \"object\" ? meta[schemaId] || meta : undefined)\n  }\n\n  // Validate data using schema\n  // AnySchema will be compiled and cached using schema itself as a key for Map\n  validate(schema: Schema | string, data: unknown): boolean\n  validate(schemaKeyRef: AnySchema | string, data: unknown): boolean | Promise<unknown>\n  validate<T>(schema: Schema | JSONSchemaType<T> | string, data: unknown): data is T\n  // Separated for type inference to work\n  // eslint-disable-next-line @typescript-eslint/unified-signatures\n  validate<T>(schema: JTDSchemaType<T>, data: unknown): data is T\n  // This overload is only intended for typescript inference, the first\n  // argument prevents manual type annotation from matching this overload\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  validate<N extends never, T extends SomeJTDSchemaType>(\n    schema: T,\n    data: unknown\n  ): data is JTDDataType<T>\n  // eslint-disable-next-line @typescript-eslint/no-redundant-type-constituents\n  validate<T>(schema: AsyncSchema, data: unknown | T): Promise<T>\n  validate<T>(schemaKeyRef: AnySchema | string, data: unknown): data is T | Promise<T>\n  validate<T>(\n    schemaKeyRef: AnySchema | string, // key, ref or schema object\n    // eslint-disable-next-line @typescript-eslint/no-redundant-type-constituents\n    data: unknown | T // to be validated\n  ): boolean | Promise<T> {\n    let v: AnyValidateFunction | undefined\n    if (typeof schemaKeyRef == \"string\") {\n      v = this.getSchema<T>(schemaKeyRef)\n      if (!v) throw new Error(`no schema with key or ref \"${schemaKeyRef}\"`)\n    } else {\n      v = this.compile<T>(schemaKeyRef)\n    }\n\n    const valid = v(data)\n    if (!(\"$async\" in v)) this.errors = v.errors\n    return valid\n  }\n\n  // Create validation function for passed schema\n  // _meta: true if schema is a meta-schema. Used internally to compile meta schemas of user-defined keywords.\n  compile<T = unknown>(schema: Schema | JSONSchemaType<T>, _meta?: boolean): ValidateFunction<T>\n  // Separated for type inference to work\n  // eslint-disable-next-line @typescript-eslint/unified-signatures\n  compile<T = unknown>(schema: JTDSchemaType<T>, _meta?: boolean): ValidateFunction<T>\n  // This overload is only intended for typescript inference, the first\n  // argument prevents manual type annotation from matching this overload\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  compile<N extends never, T extends SomeJTDSchemaType>(\n    schema: T,\n    _meta?: boolean\n  ): ValidateFunction<JTDDataType<T>>\n  compile<T = unknown>(schema: AsyncSchema, _meta?: boolean): AsyncValidateFunction<T>\n  compile<T = unknown>(schema: AnySchema, _meta?: boolean): AnyValidateFunction<T>\n  compile<T = unknown>(schema: AnySchema, _meta?: boolean): AnyValidateFunction<T> {\n    const sch = this._addSchema(schema, _meta)\n    return (sch.validate || this._compileSchemaEnv(sch)) as AnyValidateFunction<T>\n  }\n\n  // Creates validating function for passed schema with asynchronous loading of missing schemas.\n  // `loadSchema` option should be a function that accepts schema uri and returns promise that resolves with the schema.\n  // TODO allow passing schema URI\n  // meta - optional true to compile meta-schema\n  compileAsync<T = unknown>(\n    schema: SchemaObject | JSONSchemaType<T>,\n    _meta?: boolean\n  ): Promise<ValidateFunction<T>>\n  // Separated for type inference to work\n  // eslint-disable-next-line @typescript-eslint/unified-signatures\n  compileAsync<T = unknown>(schema: JTDSchemaType<T>, _meta?: boolean): Promise<ValidateFunction<T>>\n  compileAsync<T = unknown>(schema: AsyncSchema, meta?: boolean): Promise<AsyncValidateFunction<T>>\n  // eslint-disable-next-line @typescript-eslint/unified-signatures\n  compileAsync<T = unknown>(\n    schema: AnySchemaObject,\n    meta?: boolean\n  ): Promise<AnyValidateFunction<T>>\n  compileAsync<T = unknown>(\n    schema: AnySchemaObject,\n    meta?: boolean\n  ): Promise<AnyValidateFunction<T>> {\n    if (typeof this.opts.loadSchema != \"function\") {\n      throw new Error(\"options.loadSchema should be a function\")\n    }\n    const {loadSchema} = this.opts\n    return runCompileAsync.call(this, schema, meta)\n\n    async function runCompileAsync(\n      this: Ajv,\n      _schema: AnySchemaObject,\n      _meta?: boolean\n    ): Promise<AnyValidateFunction> {\n      await loadMetaSchema.call(this, _schema.$schema)\n      const sch = this._addSchema(_schema, _meta)\n      return sch.validate || _compileAsync.call(this, sch)\n    }\n\n    async function loadMetaSchema(this: Ajv, $ref?: string): Promise<void> {\n      if ($ref && !this.getSchema($ref)) {\n        await runCompileAsync.call(this, {$ref}, true)\n      }\n    }\n\n    async function _compileAsync(this: Ajv, sch: SchemaEnv): Promise<AnyValidateFunction> {\n      try {\n        return this._compileSchemaEnv(sch)\n      } catch (e) {\n        if (!(e instanceof MissingRefError)) throw e\n        checkLoaded.call(this, e)\n        await loadMissingSchema.call(this, e.missingSchema)\n        return _compileAsync.call(this, sch)\n      }\n    }\n\n    function checkLoaded(this: Ajv, {missingSchema: ref, missingRef}: MissingRefError): void {\n      if (this.refs[ref]) {\n        throw new Error(`AnySchema ${ref} is loaded but ${missingRef} cannot be resolved`)\n      }\n    }\n\n    async function loadMissingSchema(this: Ajv, ref: string): Promise<void> {\n      const _schema = await _loadSchema.call(this, ref)\n      if (!this.refs[ref]) await loadMetaSchema.call(this, _schema.$schema)\n      if (!this.refs[ref]) this.addSchema(_schema, ref, meta)\n    }\n\n    async function _loadSchema(this: Ajv, ref: string): Promise<AnySchemaObject> {\n      const p = this._loading[ref]\n      if (p) return p\n      try {\n        return await (this._loading[ref] = loadSchema(ref))\n      } finally {\n        delete this._loading[ref]\n      }\n    }\n  }\n\n  // Adds schema to the instance\n  addSchema(\n    schema: AnySchema | AnySchema[], // If array is passed, `key` will be ignored\n    key?: string, // Optional schema key. Can be passed to `validate` method instead of schema object or id/ref. One schema per instance can have empty `id` and `key`.\n    _meta?: boolean, // true if schema is a meta-schema. Used internally, addMetaSchema should be used instead.\n    _validateSchema = this.opts.validateSchema // false to skip schema validation. Used internally, option validateSchema should be used instead.\n  ): Ajv {\n    if (Array.isArray(schema)) {\n      for (const sch of schema) this.addSchema(sch, undefined, _meta, _validateSchema)\n      return this\n    }\n    let id: string | undefined\n    if (typeof schema === \"object\") {\n      const {schemaId} = this.opts\n      id = schema[schemaId]\n      if (id !== undefined && typeof id != \"string\") {\n        throw new Error(`schema ${schemaId} must be string`)\n      }\n    }\n    key = normalizeId(key || id)\n    this._checkUnique(key)\n    this.schemas[key] = this._addSchema(schema, _meta, key, _validateSchema, true)\n    return this\n  }\n\n  // Add schema that will be used to validate other schemas\n  // options in META_IGNORE_OPTIONS are alway set to false\n  addMetaSchema(\n    schema: AnySchemaObject,\n    key?: string, // schema key\n    _validateSchema = this.opts.validateSchema // false to skip schema validation, can be used to override validateSchema option for meta-schema\n  ): Ajv {\n    this.addSchema(schema, key, true, _validateSchema)\n    return this\n  }\n\n  //  Validate schema against its meta-schema\n  validateSchema(schema: AnySchema, throwOrLogError?: boolean): boolean | Promise<unknown> {\n    if (typeof schema == \"boolean\") return true\n    let $schema: string | AnySchemaObject | undefined\n    $schema = schema.$schema\n    if ($schema !== undefined && typeof $schema != \"string\") {\n      throw new Error(\"$schema must be a string\")\n    }\n    $schema = $schema || this.opts.defaultMeta || this.defaultMeta()\n    if (!$schema) {\n      this.logger.warn(\"meta-schema not available\")\n      this.errors = null\n      return true\n    }\n    const valid = this.validate($schema, schema)\n    if (!valid && throwOrLogError) {\n      const message = \"schema is invalid: \" + this.errorsText()\n      if (this.opts.validateSchema === \"log\") this.logger.error(message)\n      else throw new Error(message)\n    }\n    return valid\n  }\n\n  // Get compiled schema by `key` or `ref`.\n  // (`key` that was passed to `addSchema` or full schema reference - `schema.$id` or resolved id)\n  getSchema<T = unknown>(keyRef: string): AnyValidateFunction<T> | undefined {\n    let sch\n    while (typeof (sch = getSchEnv.call(this, keyRef)) == \"string\") keyRef = sch\n    if (sch === undefined) {\n      const {schemaId} = this.opts\n      const root = new SchemaEnv({schema: {}, schemaId})\n      sch = resolveSchema.call(this, root, keyRef)\n      if (!sch) return\n      this.refs[keyRef] = sch\n    }\n    return (sch.validate || this._compileSchemaEnv(sch)) as AnyValidateFunction<T> | undefined\n  }\n\n  // Remove cached schema(s).\n  // If no parameter is passed all schemas but meta-schemas are removed.\n  // If RegExp is passed all schemas with key/id matching pattern but meta-schemas are removed.\n  // Even if schema is referenced by other schemas it still can be removed as other schemas have local references.\n  removeSchema(schemaKeyRef?: AnySchema | string | RegExp): Ajv {\n    if (schemaKeyRef instanceof RegExp) {\n      this._removeAllSchemas(this.schemas, schemaKeyRef)\n      this._removeAllSchemas(this.refs, schemaKeyRef)\n      return this\n    }\n    switch (typeof schemaKeyRef) {\n      case \"undefined\":\n        this._removeAllSchemas(this.schemas)\n        this._removeAllSchemas(this.refs)\n        this._cache.clear()\n        return this\n      case \"string\": {\n        const sch = getSchEnv.call(this, schemaKeyRef)\n        if (typeof sch == \"object\") this._cache.delete(sch.schema)\n        delete this.schemas[schemaKeyRef]\n        delete this.refs[schemaKeyRef]\n        return this\n      }\n      case \"object\": {\n        const cacheKey = schemaKeyRef\n        this._cache.delete(cacheKey)\n        let id = schemaKeyRef[this.opts.schemaId]\n        if (id) {\n          id = normalizeId(id)\n          delete this.schemas[id]\n          delete this.refs[id]\n        }\n        return this\n      }\n      default:\n        throw new Error(\"ajv.removeSchema: invalid parameter\")\n    }\n  }\n\n  // add \"vocabulary\" - a collection of keywords\n  addVocabulary(definitions: Vocabulary): Ajv {\n    for (const def of definitions) this.addKeyword(def)\n    return this\n  }\n\n  addKeyword(\n    kwdOrDef: string | KeywordDefinition,\n    def?: KeywordDefinition // deprecated\n  ): Ajv {\n    let keyword: string | string[]\n    if (typeof kwdOrDef == \"string\") {\n      keyword = kwdOrDef\n      if (typeof def == \"object\") {\n        this.logger.warn(\"these parameters are deprecated, see docs for addKeyword\")\n        def.keyword = keyword\n      }\n    } else if (typeof kwdOrDef == \"object\" && def === undefined) {\n      def = kwdOrDef\n      keyword = def.keyword\n      if (Array.isArray(keyword) && !keyword.length) {\n        throw new Error(\"addKeywords: keyword must be string or non-empty array\")\n      }\n    } else {\n      throw new Error(\"invalid addKeywords parameters\")\n    }\n\n    checkKeyword.call(this, keyword, def)\n    if (!def) {\n      eachItem(keyword, (kwd) => addRule.call(this, kwd))\n      return this\n    }\n    keywordMetaschema.call(this, def)\n    const definition: AddedKeywordDefinition = {\n      ...def,\n      type: getJSONTypes(def.type),\n      schemaType: getJSONTypes(def.schemaType),\n    }\n    eachItem(\n      keyword,\n      definition.type.length === 0\n        ? (k) => addRule.call(this, k, definition)\n        : (k) => definition.type.forEach((t) => addRule.call(this, k, definition, t))\n    )\n    return this\n  }\n\n  getKeyword(keyword: string): AddedKeywordDefinition | boolean {\n    const rule = this.RULES.all[keyword]\n    return typeof rule == \"object\" ? rule.definition : !!rule\n  }\n\n  // Remove keyword\n  removeKeyword(keyword: string): Ajv {\n    // TODO return type should be Ajv\n    const {RULES} = this\n    delete RULES.keywords[keyword]\n    delete RULES.all[keyword]\n    for (const group of RULES.rules) {\n      const i = group.rules.findIndex((rule) => rule.keyword === keyword)\n      if (i >= 0) group.rules.splice(i, 1)\n    }\n    return this\n  }\n\n  // Add format\n  addFormat(name: string, format: Format): Ajv {\n    if (typeof format == \"string\") format = new RegExp(format)\n    this.formats[name] = format\n    return this\n  }\n\n  errorsText(\n    errors: ErrorObject[] | null | undefined = this.errors, // optional array of validation errors\n    {separator = \", \", dataVar = \"data\"}: ErrorsTextOptions = {} // optional options with properties `separator` and `dataVar`\n  ): string {\n    if (!errors || errors.length === 0) return \"No errors\"\n    return errors\n      .map((e) => `${dataVar}${e.instancePath} ${e.message}`)\n      .reduce((text, msg) => text + separator + msg)\n  }\n\n  $dataMetaSchema(metaSchema: AnySchemaObject, keywordsJsonPointers: string[]): AnySchemaObject {\n    const rules = this.RULES.all\n    metaSchema = JSON.parse(JSON.stringify(metaSchema))\n    for (const jsonPointer of keywordsJsonPointers) {\n      const segments = jsonPointer.split(\"/\").slice(1) // first segment is an empty string\n      let keywords = metaSchema\n      for (const seg of segments) keywords = keywords[seg] as AnySchemaObject\n\n      for (const key in rules) {\n        const rule = rules[key]\n        if (typeof rule != \"object\") continue\n        const {$data} = rule.definition\n        const schema = keywords[key] as AnySchemaObject | undefined\n        if ($data && schema) keywords[key] = schemaOrData(schema)\n      }\n    }\n\n    return metaSchema\n  }\n\n  private _removeAllSchemas(schemas: {[Ref in string]?: SchemaEnv | string}, regex?: RegExp): void {\n    for (const keyRef in schemas) {\n      const sch = schemas[keyRef]\n      if (!regex || regex.test(keyRef)) {\n        if (typeof sch == \"string\") {\n          delete schemas[keyRef]\n        } else if (sch && !sch.meta) {\n          this._cache.delete(sch.schema)\n          delete schemas[keyRef]\n        }\n      }\n    }\n  }\n\n  _addSchema(\n    schema: AnySchema,\n    meta?: boolean,\n    baseId?: string,\n    validateSchema = this.opts.validateSchema,\n    addSchema = this.opts.addUsedSchema\n  ): SchemaEnv {\n    let id: string | undefined\n    const {schemaId} = this.opts\n    if (typeof schema == \"object\") {\n      id = schema[schemaId]\n    } else {\n      if (this.opts.jtd) throw new Error(\"schema must be object\")\n      else if (typeof schema != \"boolean\") throw new Error(\"schema must be object or boolean\")\n    }\n    let sch = this._cache.get(schema)\n    if (sch !== undefined) return sch\n\n    baseId = normalizeId(id || baseId)\n    const localRefs = getSchemaRefs.call(this, schema, baseId)\n    sch = new SchemaEnv({schema, schemaId, meta, baseId, localRefs})\n    this._cache.set(sch.schema, sch)\n    if (addSchema && !baseId.startsWith(\"#\")) {\n      // TODO atm it is allowed to overwrite schemas without id (instead of not adding them)\n      if (baseId) this._checkUnique(baseId)\n      this.refs[baseId] = sch\n    }\n    if (validateSchema) this.validateSchema(schema, true)\n    return sch\n  }\n\n  private _checkUnique(id: string): void {\n    if (this.schemas[id] || this.refs[id]) {\n      throw new Error(`schema with key or id \"${id}\" already exists`)\n    }\n  }\n\n  private _compileSchemaEnv(sch: SchemaEnv): AnyValidateFunction {\n    if (sch.meta) this._compileMetaSchema(sch)\n    else compileSchema.call(this, sch)\n\n    /* istanbul ignore if */\n    if (!sch.validate) throw new Error(\"ajv implementation error\")\n    return sch.validate\n  }\n\n  private _compileMetaSchema(sch: SchemaEnv): void {\n    const currentOpts = this.opts\n    this.opts = this._metaOpts\n    try {\n      compileSchema.call(this, sch)\n    } finally {\n      this.opts = currentOpts\n    }\n  }\n}\n\nexport interface ErrorsTextOptions {\n  separator?: string\n  dataVar?: string\n}\n\nfunction checkOptions(\n  this: Ajv,\n  checkOpts: OptionsInfo<RemovedOptions | DeprecatedOptions>,\n  options: Options & RemovedOptions,\n  msg: string,\n  log: \"warn\" | \"error\" = \"error\"\n): void {\n  for (const key in checkOpts) {\n    const opt = key as keyof typeof checkOpts\n    if (opt in options) this.logger[log](`${msg}: option ${key}. ${checkOpts[opt]}`)\n  }\n}\n\nfunction getSchEnv(this: Ajv, keyRef: string): SchemaEnv | string | undefined {\n  keyRef = normalizeId(keyRef) // TODO tests fail without this line\n  return this.schemas[keyRef] || this.refs[keyRef]\n}\n\nfunction addInitialSchemas(this: Ajv): void {\n  const optsSchemas = this.opts.schemas\n  if (!optsSchemas) return\n  if (Array.isArray(optsSchemas)) this.addSchema(optsSchemas)\n  else for (const key in optsSchemas) this.addSchema(optsSchemas[key] as AnySchema, key)\n}\n\nfunction addInitialFormats(this: Ajv): void {\n  for (const name in this.opts.formats) {\n    const format = this.opts.formats[name]\n    if (format) this.addFormat(name, format)\n  }\n}\n\nfunction addInitialKeywords(\n  this: Ajv,\n  defs: Vocabulary | {[K in string]?: KeywordDefinition}\n): void {\n  if (Array.isArray(defs)) {\n    this.addVocabulary(defs)\n    return\n  }\n  this.logger.warn(\"keywords option as map is deprecated, pass array\")\n  for (const keyword in defs) {\n    const def = defs[keyword] as KeywordDefinition\n    if (!def.keyword) def.keyword = keyword\n    this.addKeyword(def)\n  }\n}\n\nfunction getMetaSchemaOptions(this: Ajv): InstanceOptions {\n  const metaOpts = {...this.opts}\n  for (const opt of META_IGNORE_OPTIONS) delete metaOpts[opt]\n  return metaOpts\n}\n\nconst noLogs = {log() {}, warn() {}, error() {}}\n\nfunction getLogger(logger?: Partial<Logger> | false): Logger {\n  if (logger === false) return noLogs\n  if (logger === undefined) return console\n  if (logger.log && logger.warn && logger.error) return logger as Logger\n  throw new Error(\"logger must implement log, warn and error methods\")\n}\n\nconst KEYWORD_NAME = /^[a-z_$][a-z0-9_$:-]*$/i\n\nfunction checkKeyword(this: Ajv, keyword: string | string[], def?: KeywordDefinition): void {\n  const {RULES} = this\n  eachItem(keyword, (kwd) => {\n    if (RULES.keywords[kwd]) throw new Error(`Keyword ${kwd} is already defined`)\n    if (!KEYWORD_NAME.test(kwd)) throw new Error(`Keyword ${kwd} has invalid name`)\n  })\n  if (!def) return\n  if (def.$data && !(\"code\" in def || \"validate\" in def)) {\n    throw new Error('$data keyword must have \"code\" or \"validate\" function')\n  }\n}\n\nfunction addRule(\n  this: Ajv,\n  keyword: string,\n  definition?: AddedKeywordDefinition,\n  dataType?: JSONType\n): void {\n  const post = definition?.post\n  if (dataType && post) throw new Error('keyword with \"post\" flag cannot have \"type\"')\n  const {RULES} = this\n  let ruleGroup = post ? RULES.post : RULES.rules.find(({type: t}) => t === dataType)\n  if (!ruleGroup) {\n    ruleGroup = {type: dataType, rules: []}\n    RULES.rules.push(ruleGroup)\n  }\n  RULES.keywords[keyword] = true\n  if (!definition) return\n\n  const rule: Rule = {\n    keyword,\n    definition: {\n      ...definition,\n      type: getJSONTypes(definition.type),\n      schemaType: getJSONTypes(definition.schemaType),\n    },\n  }\n  if (definition.before) addBeforeRule.call(this, ruleGroup, rule, definition.before)\n  else ruleGroup.rules.push(rule)\n  RULES.all[keyword] = rule\n  definition.implements?.forEach((kwd) => this.addKeyword(kwd))\n}\n\nfunction addBeforeRule(this: Ajv, ruleGroup: RuleGroup, rule: Rule, before: string): void {\n  const i = ruleGroup.rules.findIndex((_rule) => _rule.keyword === before)\n  if (i >= 0) {\n    ruleGroup.rules.splice(i, 0, rule)\n  } else {\n    ruleGroup.rules.push(rule)\n    this.logger.warn(`rule ${before} is not defined`)\n  }\n}\n\nfunction keywordMetaschema(this: Ajv, def: KeywordDefinition): void {\n  let {metaSchema} = def\n  if (metaSchema === undefined) return\n  if (def.$data && this.opts.$data) metaSchema = schemaOrData(metaSchema)\n  def.validateSchema = this.compile(metaSchema, true)\n}\n\nconst $dataRef = {\n  $ref: \"https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#\",\n}\n\nfunction schemaOrData(schema: AnySchema): AnySchemaObject {\n  return {anyOf: [schema, $dataRef]}\n}\n", "import type {CodeKeywordDefinition} from \"../../types\"\n\nconst def: CodeKeywordDefinition = {\n  keyword: \"id\",\n  code() {\n    throw new Error('NOT SUPPORTED: keyword \"id\", use \"$id\" for schema ID')\n  },\n}\n\nexport default def\n", "import type {CodeKeywordDefinition, AnySchema} from \"../../types\"\nimport type {KeywordCxt} from \"../../compile/validate\"\nimport MissingRefError from \"../../compile/ref_error\"\nimport {callValidateCode} from \"../code\"\nimport {_, nil, stringify, Code, Name} from \"../../compile/codegen\"\nimport N from \"../../compile/names\"\nimport {SchemaEnv, resolveRef} from \"../../compile\"\nimport {mergeEvaluated} from \"../../compile/util\"\n\nconst def: CodeKeywordDefinition = {\n  keyword: \"$ref\",\n  schemaType: \"string\",\n  code(cxt: KeywordCxt): void {\n    const {gen, schema: $ref, it} = cxt\n    const {baseId, schemaEnv: env, validateName, opts, self} = it\n    const {root} = env\n    if (($ref === \"#\" || $ref === \"#/\") && baseId === root.baseId) return callRootRef()\n    const schOrEnv = resolveRef.call(self, root, baseId, $ref)\n    if (schOrEnv === undefined) throw new MissingRefError(it.opts.uriResolver, baseId, $ref)\n    if (schOrEnv instanceof SchemaEnv) return callValidate(schOrEnv)\n    return inlineRefSchema(schOrEnv)\n\n    function callRootRef(): void {\n      if (env === root) return callRef(cxt, validateName, env, env.$async)\n      const rootName = gen.scopeValue(\"root\", {ref: root})\n      return callRef(cxt, _`${rootName}.validate`, root, root.$async)\n    }\n\n    function callValidate(sch: SchemaEnv): void {\n      const v = getValidate(cxt, sch)\n      callRef(cxt, v, sch, sch.$async)\n    }\n\n    function inlineRefSchema(sch: AnySchema): void {\n      const schName = gen.scopeValue(\n        \"schema\",\n        opts.code.source === true ? {ref: sch, code: stringify(sch)} : {ref: sch}\n      )\n      const valid = gen.name(\"valid\")\n      const schCxt = cxt.subschema(\n        {\n          schema: sch,\n          dataTypes: [],\n          schemaPath: nil,\n          topSchemaRef: schName,\n          errSchemaPath: $ref,\n        },\n        valid\n      )\n      cxt.mergeEvaluated(schCxt)\n      cxt.ok(valid)\n    }\n  },\n}\n\nexport function getValidate(cxt: KeywordCxt, sch: SchemaEnv): Code {\n  const {gen} = cxt\n  return sch.validate\n    ? gen.scopeValue(\"validate\", {ref: sch.validate})\n    : _`${gen.scopeValue(\"wrapper\", {ref: sch})}.validate`\n}\n\nexport function callRef(cxt: KeywordCxt, v: Code, sch?: SchemaEnv, $async?: boolean): void {\n  const {gen, it} = cxt\n  const {allErrors, schemaEnv: env, opts} = it\n  const passCxt = opts.passContext ? N.this : nil\n  if ($async) callAsyncRef()\n  else callSyncRef()\n\n  function callAsyncRef(): void {\n    if (!env.$async) throw new Error(\"async schema referenced by sync schema\")\n    const valid = gen.let(\"valid\")\n    gen.try(\n      () => {\n        gen.code(_`await ${callValidateCode(cxt, v, passCxt)}`)\n        addEvaluatedFrom(v) // TODO will not work with async, it has to be returned with the result\n        if (!allErrors) gen.assign(valid, true)\n      },\n      (e) => {\n        gen.if(_`!(${e} instanceof ${it.ValidationError as Name})`, () => gen.throw(e))\n        addErrorsFrom(e)\n        if (!allErrors) gen.assign(valid, false)\n      }\n    )\n    cxt.ok(valid)\n  }\n\n  function callSyncRef(): void {\n    cxt.result(\n      callValidateCode(cxt, v, passCxt),\n      () => addEvaluatedFrom(v),\n      () => addErrorsFrom(v)\n    )\n  }\n\n  function addErrorsFrom(source: Code): void {\n    const errs = _`${source}.errors`\n    gen.assign(N.vErrors, _`${N.vErrors} === null ? ${errs} : ${N.vErrors}.concat(${errs})`) // TODO tagged\n    gen.assign(N.errors, _`${N.vErrors}.length`)\n  }\n\n  function addEvaluatedFrom(source: Code): void {\n    if (!it.opts.unevaluated) return\n    const schEvaluated = sch?.validate?.evaluated\n    // TODO refactor\n    if (it.props !== true) {\n      if (schEvaluated && !schEvaluated.dynamicProps) {\n        if (schEvaluated.props !== undefined) {\n          it.props = mergeEvaluated.props(gen, schEvaluated.props, it.props)\n        }\n      } else {\n        const props = gen.var(\"props\", _`${source}.evaluated.props`)\n        it.props = mergeEvaluated.props(gen, props, it.props, Name)\n      }\n    }\n    if (it.items !== true) {\n      if (schEvaluated && !schEvaluated.dynamicItems) {\n        if (schEvaluated.items !== undefined) {\n          it.items = mergeEvaluated.items(gen, schEvaluated.items, it.items)\n        }\n      } else {\n        const items = gen.var(\"items\", _`${source}.evaluated.items`)\n        it.items = mergeEvaluated.items(gen, items, it.items, Name)\n      }\n    }\n  }\n}\n\nexport default def\n", "import type {Vocabulary} from \"../../types\"\nimport idKeyword from \"./id\"\nimport refKeyword from \"./ref\"\n\nconst core: Vocabulary = [\n  \"$schema\",\n  \"$id\",\n  \"$defs\",\n  \"$vocabulary\",\n  {keyword: \"$comment\"},\n  \"definitions\",\n  idKeyword,\n  refKeyword,\n]\n\nexport default core\n", "import type {CodeKeywordDefinition, ErrorObject, KeywordErrorDefinition} from \"../../types\"\nimport type {KeywordCxt} from \"../../compile/validate\"\nimport {_, str, operators, Code} from \"../../compile/codegen\"\n\nconst ops = operators\n\ntype Kwd = \"maximum\" | \"minimum\" | \"exclusiveMaximum\" | \"exclusiveMinimum\"\n\ntype Comparison = \"<=\" | \">=\" | \"<\" | \">\"\n\nconst KWDs: {[K in Kwd]: {okStr: Comparison; ok: Code; fail: Code}} = {\n  maximum: {okStr: \"<=\", ok: ops.LTE, fail: ops.GT},\n  minimum: {okStr: \">=\", ok: ops.GTE, fail: ops.LT},\n  exclusiveMaximum: {okStr: \"<\", ok: ops.LT, fail: ops.GTE},\n  exclusiveMinimum: {okStr: \">\", ok: ops.GT, fail: ops.LTE},\n}\n\nexport type LimitNumberError = ErrorObject<\n  Kwd,\n  {limit: number; comparison: Comparison},\n  number | {$data: string}\n>\n\nconst error: KeywordErrorDefinition = {\n  message: ({keyword, schemaCode}) => str`must be ${KWDs[keyword as Kwd].okStr} ${schemaCode}`,\n  params: ({keyword, schemaCode}) =>\n    _`{comparison: ${KWDs[keyword as Kwd].okStr}, limit: ${schemaCode}}`,\n}\n\nconst def: CodeKeywordDefinition = {\n  keyword: Object.keys(KWDs),\n  type: \"number\",\n  schemaType: \"number\",\n  $data: true,\n  error,\n  code(cxt: KeywordCxt) {\n    const {keyword, data, schemaCode} = cxt\n    cxt.fail$data(_`${data} ${KWDs[keyword as Kwd].fail} ${schemaCode} || isNaN(${data})`)\n  },\n}\n\nexport default def\n", "import type {CodeKeywordDefinition, ErrorObject, KeywordErrorDefinition} from \"../../types\"\nimport type {KeywordCxt} from \"../../compile/validate\"\nimport {_, str} from \"../../compile/codegen\"\n\nexport type MultipleOfError = ErrorObject<\n  \"multipleOf\",\n  {multipleOf: number},\n  number | {$data: string}\n>\n\nconst error: KeywordErrorDefinition = {\n  message: ({schemaCode}) => str`must be multiple of ${schemaCode}`,\n  params: ({schemaCode}) => _`{multipleOf: ${schemaCode}}`,\n}\n\nconst def: CodeKeywordDefinition = {\n  keyword: \"multipleOf\",\n  type: \"number\",\n  schemaType: \"number\",\n  $data: true,\n  error,\n  code(cxt: KeywordCxt) {\n    const {gen, data, schemaCode, it} = cxt\n    // const bdt = bad$DataType(schemaCode, <string>def.schemaType, $data)\n    const prec = it.opts.multipleOfPrecision\n    const res = gen.let(\"res\")\n    const invalid = prec\n      ? _`Math.abs(Math.round(${res}) - ${res}) > 1e-${prec}`\n      : _`${res} !== parseInt(${res})`\n    cxt.fail$data(_`(${schemaCode} === 0 || (${res} = ${data}/${schemaCode}, ${invalid}))`)\n  },\n}\n\nexport default def\n", "// https://mathiasbynens.be/notes/javascript-encoding\n// https://github.com/bestiejs/punycode.js - punycode.ucs2.decode\nexport default function ucs2length(str: string): number {\n  const len = str.length\n  let length = 0\n  let pos = 0\n  let value: number\n  while (pos < len) {\n    length++\n    value = str.charCodeAt(pos++)\n    if (value >= 0xd800 && value <= 0xdbff && pos < len) {\n      // high surrogate, and there is a next character\n      value = str.charCodeAt(pos)\n      if ((value & 0xfc00) === 0xdc00) pos++ // low surrogate\n    }\n  }\n  return length\n}\n\nucs2length.code = 'require(\"ajv/dist/runtime/ucs2length\").default'\n", "import type {CodeKeywordDefinition, KeywordErrorDefinition} from \"../../types\"\nimport type {KeywordCxt} from \"../../compile/validate\"\nimport {_, str, operators} from \"../../compile/codegen\"\nimport {useFunc} from \"../../compile/util\"\nimport ucs2length from \"../../runtime/ucs2length\"\n\nconst error: KeywordErrorDefinition = {\n  message({keyword, schemaCode}) {\n    const comp = keyword === \"maxLength\" ? \"more\" : \"fewer\"\n    return str`must NOT have ${comp} than ${schemaCode} characters`\n  },\n  params: ({schemaCode}) => _`{limit: ${schemaCode}}`,\n}\n\nconst def: CodeKeywordDefinition = {\n  keyword: [\"maxLength\", \"minLength\"],\n  type: \"string\",\n  schemaType: \"number\",\n  $data: true,\n  error,\n  code(cxt: KeywordCxt) {\n    const {keyword, data, schemaCode, it} = cxt\n    const op = keyword === \"maxLength\" ? operators.GT : operators.LT\n    const len =\n      it.opts.unicode === false ? _`${data}.length` : _`${useFunc(cxt.gen, ucs2length)}(${data})`\n    cxt.fail$data(_`${len} ${op} ${schemaCode}`)\n  },\n}\n\nexport default def\n", "import type {CodeKeywordDefinition, ErrorObject, KeywordErrorDefinition} from \"../../types\"\nimport type {KeywordCxt} from \"../../compile/validate\"\nimport {usePattern} from \"../code\"\nimport {_, str} from \"../../compile/codegen\"\n\nexport type PatternError = ErrorObject<\"pattern\", {pattern: string}, string | {$data: string}>\n\nconst error: KeywordErrorDefinition = {\n  message: ({schemaCode}) => str`must match pattern \"${schemaCode}\"`,\n  params: ({schemaCode}) => _`{pattern: ${schemaCode}}`,\n}\n\nconst def: CodeKeywordDefinition = {\n  keyword: \"pattern\",\n  type: \"string\",\n  schemaType: \"string\",\n  $data: true,\n  error,\n  code(cxt: KeywordCxt) {\n    const {data, $data, schema, schemaCode, it} = cxt\n    // TODO regexp should be wrapped in try/catchs\n    const u = it.opts.unicodeRegExp ? \"u\" : \"\"\n    const regExp = $data ? _`(new RegExp(${schemaCode}, ${u}))` : usePattern(cxt, schema)\n    cxt.fail$data(_`!${regExp}.test(${data})`)\n  },\n}\n\nexport default def\n", "import type {CodeKeywordDefinition, KeywordErrorDefinition} from \"../../types\"\nimport type {KeywordCxt} from \"../../compile/validate\"\nimport {_, str, operators} from \"../../compile/codegen\"\n\nconst error: KeywordErrorDefinition = {\n  message({keyword, schemaCode}) {\n    const comp = keyword === \"maxProperties\" ? \"more\" : \"fewer\"\n    return str`must NOT have ${comp} than ${schemaCode} properties`\n  },\n  params: ({schemaCode}) => _`{limit: ${schemaCode}}`,\n}\n\nconst def: CodeKeywordDefinition = {\n  keyword: [\"maxProperties\", \"minProperties\"],\n  type: \"object\",\n  schemaType: \"number\",\n  $data: true,\n  error,\n  code(cxt: KeywordCxt) {\n    const {keyword, data, schemaCode} = cxt\n    const op = keyword === \"maxProperties\" ? operators.GT : operators.LT\n    cxt.fail$data(_`Object.keys(${data}).length ${op} ${schemaCode}`)\n  },\n}\n\nexport default def\n", "import type {CodeKeywordDefinition, ErrorObject, KeywordErrorDefinition} from \"../../types\"\nimport type {KeywordCxt} from \"../../compile/validate\"\nimport {\n  checkReportMissingProp,\n  checkMissingProp,\n  reportMissingProp,\n  propertyInData,\n  noPropertyInData,\n} from \"../code\"\nimport {_, str, nil, not, Name, Code} from \"../../compile/codegen\"\nimport {checkStrictMode} from \"../../compile/util\"\n\nexport type RequiredError = ErrorObject<\n  \"required\",\n  {missingProperty: string},\n  string[] | {$data: string}\n>\n\nconst error: KeywordErrorDefinition = {\n  message: ({params: {missingProperty}}) => str`must have required property '${missingProperty}'`,\n  params: ({params: {missingProperty}}) => _`{missingProperty: ${missingProperty}}`,\n}\n\nconst def: CodeKeywordDefinition = {\n  keyword: \"required\",\n  type: \"object\",\n  schemaType: \"array\",\n  $data: true,\n  error,\n  code(cxt: KeywordCxt) {\n    const {gen, schema, schemaCode, data, $data, it} = cxt\n    const {opts} = it\n    if (!$data && schema.length === 0) return\n    const useLoop = schema.length >= opts.loopRequired\n    if (it.allErrors) allErrorsMode()\n    else exitOnErrorMode()\n\n    if (opts.strictRequired) {\n      const props = cxt.parentSchema.properties\n      const {definedProperties} = cxt.it\n      for (const requiredKey of schema) {\n        if (props?.[requiredKey] === undefined && !definedProperties.has(requiredKey)) {\n          const schemaPath = it.schemaEnv.baseId + it.errSchemaPath\n          const msg = `required property \"${requiredKey}\" is not defined at \"${schemaPath}\" (strictRequired)`\n          checkStrictMode(it, msg, it.opts.strictRequired)\n        }\n      }\n    }\n\n    function allErrorsMode(): void {\n      if (useLoop || $data) {\n        cxt.block$data(nil, loopAllRequired)\n      } else {\n        for (const prop of schema) {\n          checkReportMissingProp(cxt, prop)\n        }\n      }\n    }\n\n    function exitOnErrorMode(): void {\n      const missing = gen.let(\"missing\")\n      if (useLoop || $data) {\n        const valid = gen.let(\"valid\", true)\n        cxt.block$data(valid, () => loopUntilMissing(missing, valid))\n        cxt.ok(valid)\n      } else {\n        gen.if(checkMissingProp(cxt, schema, missing))\n        reportMissingProp(cxt, missing)\n        gen.else()\n      }\n    }\n\n    function loopAllRequired(): void {\n      gen.forOf(\"prop\", schemaCode as Code, (prop) => {\n        cxt.setParams({missingProperty: prop})\n        gen.if(noPropertyInData(gen, data, prop, opts.ownProperties), () => cxt.error())\n      })\n    }\n\n    function loopUntilMissing(missing: Name, valid: Name): void {\n      cxt.setParams({missingProperty: missing})\n      gen.forOf(\n        missing,\n        schemaCode as Code,\n        () => {\n          gen.assign(valid, propertyInData(gen, data, missing, opts.ownProperties))\n          gen.if(not(valid), () => {\n            cxt.error()\n            gen.break()\n          })\n        },\n        nil\n      )\n    }\n  },\n}\n\nexport default def\n", "import type {CodeKeywordDefinition, KeywordErrorDefinition} from \"../../types\"\nimport type {KeywordCxt} from \"../../compile/validate\"\nimport {_, str, operators} from \"../../compile/codegen\"\n\nconst error: KeywordErrorDefinition = {\n  message({keyword, schemaCode}) {\n    const comp = keyword === \"maxItems\" ? \"more\" : \"fewer\"\n    return str`must NOT have ${comp} than ${schemaCode} items`\n  },\n  params: ({schemaCode}) => _`{limit: ${schemaCode}}`,\n}\n\nconst def: CodeKeywordDefinition = {\n  keyword: [\"maxItems\", \"minItems\"],\n  type: \"array\",\n  schemaType: \"number\",\n  $data: true,\n  error,\n  code(cxt: KeywordCxt) {\n    const {keyword, data, schemaCode} = cxt\n    const op = keyword === \"maxItems\" ? operators.GT : operators.LT\n    cxt.fail$data(_`${data}.length ${op} ${schemaCode}`)\n  },\n}\n\nexport default def\n", "// https://github.com/ajv-validator/ajv/issues/889\nimport * as equal from \"fast-deep-equal\"\n\ntype Equal = typeof equal & {code: string}\n;(equal as Equal).code = 'require(\"ajv/dist/runtime/equal\").default'\n\nexport default equal as Equal\n", "import type {CodeKeywordDefinition, ErrorObject, KeywordErrorDefinition} from \"../../types\"\nimport type {KeywordCxt} from \"../../compile/validate\"\nimport {checkDataTypes, getSchemaTypes, DataType} from \"../../compile/validate/dataType\"\nimport {_, str, Name} from \"../../compile/codegen\"\nimport {useFunc} from \"../../compile/util\"\nimport equal from \"../../runtime/equal\"\n\nexport type UniqueItemsError = ErrorObject<\n  \"uniqueItems\",\n  {i: number; j: number},\n  boolean | {$data: string}\n>\n\nconst error: KeywordErrorDefinition = {\n  message: ({params: {i, j}}) =>\n    str`must NOT have duplicate items (items ## ${j} and ${i} are identical)`,\n  params: ({params: {i, j}}) => _`{i: ${i}, j: ${j}}`,\n}\n\nconst def: CodeKeywordDefinition = {\n  keyword: \"uniqueItems\",\n  type: \"array\",\n  schemaType: \"boolean\",\n  $data: true,\n  error,\n  code(cxt: KeywordCxt) {\n    const {gen, data, $data, schema, parentSchema, schemaCode, it} = cxt\n    if (!$data && !schema) return\n    const valid = gen.let(\"valid\")\n    const itemTypes = parentSchema.items ? getSchemaTypes(parentSchema.items) : []\n    cxt.block$data(valid, validateUniqueItems, _`${schemaCode} === false`)\n    cxt.ok(valid)\n\n    function validateUniqueItems(): void {\n      const i = gen.let(\"i\", _`${data}.length`)\n      const j = gen.let(\"j\")\n      cxt.setParams({i, j})\n      gen.assign(valid, true)\n      gen.if(_`${i} > 1`, () => (canOptimize() ? loopN : loopN2)(i, j))\n    }\n\n    function canOptimize(): boolean {\n      return itemTypes.length > 0 && !itemTypes.some((t) => t === \"object\" || t === \"array\")\n    }\n\n    function loopN(i: Name, j: Name): void {\n      const item = gen.name(\"item\")\n      const wrongType = checkDataTypes(itemTypes, item, it.opts.strictNumbers, DataType.Wrong)\n      const indices = gen.const(\"indices\", _`{}`)\n      gen.for(_`;${i}--;`, () => {\n        gen.let(item, _`${data}[${i}]`)\n        gen.if(wrongType, _`continue`)\n        if (itemTypes.length > 1) gen.if(_`typeof ${item} == \"string\"`, _`${item} += \"_\"`)\n        gen\n          .if(_`typeof ${indices}[${item}] == \"number\"`, () => {\n            gen.assign(j, _`${indices}[${item}]`)\n            cxt.error()\n            gen.assign(valid, false).break()\n          })\n          .code(_`${indices}[${item}] = ${i}`)\n      })\n    }\n\n    function loopN2(i: Name, j: Name): void {\n      const eql = useFunc(gen, equal)\n      const outer = gen.name(\"outer\")\n      gen.label(outer).for(_`;${i}--;`, () =>\n        gen.for(_`${j} = ${i}; ${j}--;`, () =>\n          gen.if(_`${eql}(${data}[${i}], ${data}[${j}])`, () => {\n            cxt.error()\n            gen.assign(valid, false).break(outer)\n          })\n        )\n      )\n    }\n  },\n}\n\nexport default def\n", "import type {CodeKeywordDefinition, ErrorObject, KeywordErrorDefinition} from \"../../types\"\nimport type {KeywordCxt} from \"../../compile/validate\"\nimport {_} from \"../../compile/codegen\"\nimport {useFunc} from \"../../compile/util\"\nimport equal from \"../../runtime/equal\"\n\nexport type ConstError = ErrorObject<\"const\", {allowedValue: any}>\n\nconst error: KeywordErrorDefinition = {\n  message: \"must be equal to constant\",\n  params: ({schemaCode}) => _`{allowedValue: ${schemaCode}}`,\n}\n\nconst def: CodeKeywordDefinition = {\n  keyword: \"const\",\n  $data: true,\n  error,\n  code(cxt: KeywordCxt) {\n    const {gen, data, $data, schemaCode, schema} = cxt\n    if ($data || (schema && typeof schema == \"object\")) {\n      cxt.fail$data(_`!${useFunc(gen, equal)}(${data}, ${schemaCode})`)\n    } else {\n      cxt.fail(_`${schema} !== ${data}`)\n    }\n  },\n}\n\nexport default def\n", "import type {CodeKeywordDefinition, ErrorObject, KeywordErrorDefinition} from \"../../types\"\nimport type {KeywordCxt} from \"../../compile/validate\"\nimport {_, or, Name, Code} from \"../../compile/codegen\"\nimport {useFunc} from \"../../compile/util\"\nimport equal from \"../../runtime/equal\"\n\nexport type EnumError = ErrorObject<\"enum\", {allowedValues: any[]}, any[] | {$data: string}>\n\nconst error: KeywordErrorDefinition = {\n  message: \"must be equal to one of the allowed values\",\n  params: ({schemaCode}) => _`{allowedValues: ${schemaCode}}`,\n}\n\nconst def: CodeKeywordDefinition = {\n  keyword: \"enum\",\n  schemaType: \"array\",\n  $data: true,\n  error,\n  code(cxt: KeywordCxt) {\n    const {gen, data, $data, schema, schemaCode, it} = cxt\n    if (!$data && schema.length === 0) throw new Error(\"enum must have non-empty array\")\n    const useLoop = schema.length >= it.opts.loopEnum\n    let eql: Name | undefined\n    const getEql = (): Name => (eql ??= useFunc(gen, equal))\n\n    let valid: Code\n    if (useLoop || $data) {\n      valid = gen.let(\"valid\")\n      cxt.block$data(valid, loopEnum)\n    } else {\n      /* istanbul ignore if */\n      if (!Array.isArray(schema)) throw new Error(\"ajv implementation error\")\n      const vSchema = gen.const(\"vSchema\", schemaCode)\n      valid = or(...schema.map((_x: unknown, i: number) => equalCode(vSchema, i)))\n    }\n    cxt.pass(valid)\n\n    function loopEnum(): void {\n      gen.assign(valid, false)\n      gen.forOf(\"v\", schemaCode as Code, (v) =>\n        gen.if(_`${getEql()}(${data}, ${v})`, () => gen.assign(valid, true).break())\n      )\n    }\n\n    function equalCode(vSchema: Name, i: number): Code {\n      const sch = schema[i]\n      return typeof sch === \"object\" && sch !== null\n        ? _`${getEql()}(${data}, ${vSchema}[${i}])`\n        : _`${data} === ${sch}`\n    }\n  },\n}\n\nexport default def\n", "import type {ErrorObject, Vocabulary} from \"../../types\"\nimport limitNumber, {LimitNumberError} from \"./limitNumber\"\nimport multipleOf, {MultipleOfError} from \"./multipleOf\"\nimport limitLength from \"./limitLength\"\nimport pattern, {PatternError} from \"./pattern\"\nimport limitProperties from \"./limitProperties\"\nimport required, {RequiredError} from \"./required\"\nimport limitItems from \"./limitItems\"\nimport uniqueItems, {UniqueItemsError} from \"./uniqueItems\"\nimport constKeyword, {ConstError} from \"./const\"\nimport enumKeyword, {EnumError} from \"./enum\"\n\nconst validation: Vocabulary = [\n  // number\n  limitNumber,\n  multipleOf,\n  // string\n  limitLength,\n  pattern,\n  // object\n  limitProperties,\n  required,\n  // array\n  limitItems,\n  uniqueItems,\n  // any\n  {keyword: \"type\", schemaType: [\"string\", \"array\"]},\n  {keyword: \"nullable\", schemaType: \"boolean\"},\n  constKeyword,\n  enumKeyword,\n]\n\nexport default validation\n\ntype LimitError = ErrorObject<\n  \"maxItems\" | \"minItems\" | \"minProperties\" | \"maxProperties\" | \"minLength\" | \"maxLength\",\n  {limit: number},\n  number | {$data: string}\n>\n\nexport type ValidationKeywordError =\n  | LimitError\n  | LimitNumberError\n  | MultipleOfError\n  | PatternError\n  | RequiredError\n  | UniqueItemsError\n  | ConstError\n  | EnumError\n", "import type {\n  CodeKeywordDefinition,\n  ErrorObject,\n  KeywordErrorDefinition,\n  AnySchema,\n} from \"../../types\"\nimport type {KeywordCxt} from \"../../compile/validate\"\nimport {_, str, not, Name} from \"../../compile/codegen\"\nimport {alwaysValidSchema, checkStrictMode, Type} from \"../../compile/util\"\n\nexport type AdditionalItemsError = ErrorObject<\"additionalItems\", {limit: number}, AnySchema>\n\nconst error: KeywordErrorDefinition = {\n  message: ({params: {len}}) => str`must NOT have more than ${len} items`,\n  params: ({params: {len}}) => _`{limit: ${len}}`,\n}\n\nconst def: CodeKeywordDefinition = {\n  keyword: \"additionalItems\" as const,\n  type: \"array\",\n  schemaType: [\"boolean\", \"object\"],\n  before: \"uniqueItems\",\n  error,\n  code(cxt: KeywordCxt) {\n    const {parentSchema, it} = cxt\n    const {items} = parentSchema\n    if (!Array.isArray(items)) {\n      checkStrictMode(it, '\"additionalItems\" is ignored when \"items\" is not an array of schemas')\n      return\n    }\n    validateAdditionalItems(cxt, items)\n  },\n}\n\nexport function validateAdditionalItems(cxt: KeywordCxt, items: AnySchema[]): void {\n  const {gen, schema, data, keyword, it} = cxt\n  it.items = true\n  const len = gen.const(\"len\", _`${data}.length`)\n  if (schema === false) {\n    cxt.setParams({len: items.length})\n    cxt.pass(_`${len} <= ${items.length}`)\n  } else if (typeof schema == \"object\" && !alwaysValidSchema(it, schema)) {\n    const valid = gen.var(\"valid\", _`${len} <= ${items.length}`) // TODO var\n    gen.if(not(valid), () => validateItems(valid))\n    cxt.ok(valid)\n  }\n\n  function validateItems(valid: Name): void {\n    gen.forRange(\"i\", items.length, len, (i) => {\n      cxt.subschema({keyword, dataProp: i, dataPropType: Type.Num}, valid)\n      if (!it.allErrors) gen.if(not(valid), () => gen.break())\n    })\n  }\n}\n\nexport default def\n", "import type {CodeKeywordDefinition, AnySchema, AnySchemaObject} from \"../../types\"\nimport type {KeywordCxt} from \"../../compile/validate\"\nimport {_} from \"../../compile/codegen\"\nimport {alwaysValidSchema, mergeEvaluated, checkStrictMode} from \"../../compile/util\"\nimport {validateArray} from \"../code\"\n\nconst def: CodeKeywordDefinition = {\n  keyword: \"items\",\n  type: \"array\",\n  schemaType: [\"object\", \"array\", \"boolean\"],\n  before: \"uniqueItems\",\n  code(cxt: KeywordCxt) {\n    const {schema, it} = cxt\n    if (Array.isArray(schema)) return validateTuple(cxt, \"additionalItems\", schema)\n    it.items = true\n    if (alwaysValidSchema(it, schema)) return\n    cxt.ok(validateArray(cxt))\n  },\n}\n\nexport function validateTuple(\n  cxt: KeywordCxt,\n  extraItems: string,\n  schArr: AnySchema[] = cxt.schema\n): void {\n  const {gen, parentSchema, data, keyword, it} = cxt\n  checkStrictTuple(parentSchema)\n  if (it.opts.unevaluated && schArr.length && it.items !== true) {\n    it.items = mergeEvaluated.items(gen, schArr.length, it.items)\n  }\n  const valid = gen.name(\"valid\")\n  const len = gen.const(\"len\", _`${data}.length`)\n  schArr.forEach((sch: AnySchema, i: number) => {\n    if (alwaysValidSchema(it, sch)) return\n    gen.if(_`${len} > ${i}`, () =>\n      cxt.subschema(\n        {\n          keyword,\n          schemaProp: i,\n          dataProp: i,\n        },\n        valid\n      )\n    )\n    cxt.ok(valid)\n  })\n\n  function checkStrictTuple(sch: AnySchemaObject): void {\n    const {opts, errSchemaPath} = it\n    const l = schArr.length\n    const fullTuple = l === sch.minItems && (l === sch.maxItems || sch[extraItems] === false)\n    if (opts.strictTuples && !fullTuple) {\n      const msg = `\"${keyword}\" is ${l}-tuple, but minItems or maxItems/${extraItems} are not specified or different at path \"${errSchemaPath}\"`\n      checkStrictMode(it, msg, opts.strictTuples)\n    }\n  }\n}\n\nexport default def\n", "import type {CodeKeywordDefinition} from \"../../types\"\nimport {validateTuple} from \"./items\"\n\nconst def: CodeKeywordDefinition = {\n  keyword: \"prefixItems\",\n  type: \"array\",\n  schemaType: [\"array\"],\n  before: \"uniqueItems\",\n  code: (cxt) => validateTuple(cxt, \"items\"),\n}\n\nexport default def\n", "import type {\n  CodeKeywordDefinition,\n  KeywordErrorDefinition,\n  ErrorObject,\n  AnySchema,\n} from \"../../types\"\nimport type {KeywordCxt} from \"../../compile/validate\"\nimport {_, str} from \"../../compile/codegen\"\nimport {alwaysValidSchema} from \"../../compile/util\"\nimport {validateArray} from \"../code\"\nimport {validateAdditionalItems} from \"./additionalItems\"\n\nexport type ItemsError = ErrorObject<\"items\", {limit: number}, AnySchema>\n\nconst error: KeywordErrorDefinition = {\n  message: ({params: {len}}) => str`must NOT have more than ${len} items`,\n  params: ({params: {len}}) => _`{limit: ${len}}`,\n}\n\nconst def: CodeKeywordDefinition = {\n  keyword: \"items\",\n  type: \"array\",\n  schemaType: [\"object\", \"boolean\"],\n  before: \"uniqueItems\",\n  error,\n  code(cxt: KeywordCxt) {\n    const {schema, parentSchema, it} = cxt\n    const {prefixItems} = parentSchema\n    it.items = true\n    if (alwaysValidSchema(it, schema)) return\n    if (prefixItems) validateAdditionalItems(cxt, prefixItems)\n    else cxt.ok(validateArray(cxt))\n  },\n}\n\nexport default def\n", "import type {\n  CodeKeywordDefinition,\n  KeywordErrorDefinition,\n  ErrorObject,\n  AnySchema,\n} from \"../../types\"\nimport type {KeywordCxt} from \"../../compile/validate\"\nimport {_, str, Name} from \"../../compile/codegen\"\nimport {alwaysValidSchema, checkStrictMode, Type} from \"../../compile/util\"\n\nexport type ContainsError = ErrorObject<\n  \"contains\",\n  {minContains: number; maxContains?: number},\n  AnySchema\n>\n\nconst error: KeywordErrorDefinition = {\n  message: ({params: {min, max}}) =>\n    max === undefined\n      ? str`must contain at least ${min} valid item(s)`\n      : str`must contain at least ${min} and no more than ${max} valid item(s)`,\n  params: ({params: {min, max}}) =>\n    max === undefined ? _`{minContains: ${min}}` : _`{minContains: ${min}, maxContains: ${max}}`,\n}\n\nconst def: CodeKeywordDefinition = {\n  keyword: \"contains\",\n  type: \"array\",\n  schemaType: [\"object\", \"boolean\"],\n  before: \"uniqueItems\",\n  trackErrors: true,\n  error,\n  code(cxt: KeywordCxt) {\n    const {gen, schema, parentSchema, data, it} = cxt\n    let min: number\n    let max: number | undefined\n    const {minContains, maxContains} = parentSchema\n    if (it.opts.next) {\n      min = minContains === undefined ? 1 : minContains\n      max = maxContains\n    } else {\n      min = 1\n    }\n    const len = gen.const(\"len\", _`${data}.length`)\n    cxt.setParams({min, max})\n    if (max === undefined && min === 0) {\n      checkStrictMode(it, `\"minContains\" == 0 without \"maxContains\": \"contains\" keyword ignored`)\n      return\n    }\n    if (max !== undefined && min > max) {\n      checkStrictMode(it, `\"minContains\" > \"maxContains\" is always invalid`)\n      cxt.fail()\n      return\n    }\n    if (alwaysValidSchema(it, schema)) {\n      let cond = _`${len} >= ${min}`\n      if (max !== undefined) cond = _`${cond} && ${len} <= ${max}`\n      cxt.pass(cond)\n      return\n    }\n\n    it.items = true\n    const valid = gen.name(\"valid\")\n    if (max === undefined && min === 1) {\n      validateItems(valid, () => gen.if(valid, () => gen.break()))\n    } else if (min === 0) {\n      gen.let(valid, true)\n      if (max !== undefined) gen.if(_`${data}.length > 0`, validateItemsWithCount)\n    } else {\n      gen.let(valid, false)\n      validateItemsWithCount()\n    }\n    cxt.result(valid, () => cxt.reset())\n\n    function validateItemsWithCount(): void {\n      const schValid = gen.name(\"_valid\")\n      const count = gen.let(\"count\", 0)\n      validateItems(schValid, () => gen.if(schValid, () => checkLimits(count)))\n    }\n\n    function validateItems(_valid: Name, block: () => void): void {\n      gen.forRange(\"i\", 0, len, (i) => {\n        cxt.subschema(\n          {\n            keyword: \"contains\",\n            dataProp: i,\n            dataPropType: Type.Num,\n            compositeRule: true,\n          },\n          _valid\n        )\n        block()\n      })\n    }\n\n    function checkLimits(count: Name): void {\n      gen.code(_`${count}++`)\n      if (max === undefined) {\n        gen.if(_`${count} >= ${min}`, () => gen.assign(valid, true).break())\n      } else {\n        gen.if(_`${count} > ${max}`, () => gen.assign(valid, false).break())\n        if (min === 1) gen.assign(valid, true)\n        else gen.if(_`${count} >= ${min}`, () => gen.assign(valid, true))\n      }\n    }\n  },\n}\n\nexport default def\n", "import type {\n  CodeKeywordDefinition,\n  ErrorObject,\n  KeywordErrorDefinition,\n  SchemaMap,\n  AnySchema,\n} from \"../../types\"\nimport type {KeywordCxt} from \"../../compile/validate\"\nimport {_, str} from \"../../compile/codegen\"\nimport {alwaysValidSchema} from \"../../compile/util\"\nimport {checkReportMissingProp, checkMissingProp, reportMissingProp, propertyInData} from \"../code\"\n\nexport type PropertyDependencies = {[K in string]?: string[]}\n\nexport interface DependenciesErrorParams {\n  property: string\n  missingProperty: string\n  depsCount: number\n  deps: string // TODO change to string[]\n}\n\ntype SchemaDependencies = SchemaMap\n\nexport type DependenciesError = ErrorObject<\n  \"dependencies\",\n  DependenciesErrorParams,\n  {[K in string]?: string[] | AnySchema}\n>\n\nexport const error: KeywordErrorDefinition = {\n  message: ({params: {property, depsCount, deps}}) => {\n    const property_ies = depsCount === 1 ? \"property\" : \"properties\"\n    return str`must have ${property_ies} ${deps} when property ${property} is present`\n  },\n  params: ({params: {property, depsCount, deps, missingProperty}}) =>\n    _`{property: ${property},\n    missingProperty: ${missingProperty},\n    depsCount: ${depsCount},\n    deps: ${deps}}`, // TODO change to reference\n}\n\nconst def: CodeKeywordDefinition = {\n  keyword: \"dependencies\",\n  type: \"object\",\n  schemaType: \"object\",\n  error,\n  code(cxt: KeywordCxt) {\n    const [propDeps, schDeps] = splitDependencies(cxt)\n    validatePropertyDeps(cxt, propDeps)\n    validateSchemaDeps(cxt, schDeps)\n  },\n}\n\nfunction splitDependencies({schema}: KeywordCxt): [PropertyDependencies, SchemaDependencies] {\n  const propertyDeps: PropertyDependencies = {}\n  const schemaDeps: SchemaDependencies = {}\n  for (const key in schema) {\n    if (key === \"__proto__\") continue\n    const deps = Array.isArray(schema[key]) ? propertyDeps : schemaDeps\n    deps[key] = schema[key]\n  }\n  return [propertyDeps, schemaDeps]\n}\n\nexport function validatePropertyDeps(\n  cxt: KeywordCxt,\n  propertyDeps: {[K in string]?: string[]} = cxt.schema\n): void {\n  const {gen, data, it} = cxt\n  if (Object.keys(propertyDeps).length === 0) return\n  const missing = gen.let(\"missing\")\n  for (const prop in propertyDeps) {\n    const deps = propertyDeps[prop] as string[]\n    if (deps.length === 0) continue\n    const hasProperty = propertyInData(gen, data, prop, it.opts.ownProperties)\n    cxt.setParams({\n      property: prop,\n      depsCount: deps.length,\n      deps: deps.join(\", \"),\n    })\n    if (it.allErrors) {\n      gen.if(hasProperty, () => {\n        for (const depProp of deps) {\n          checkReportMissingProp(cxt, depProp)\n        }\n      })\n    } else {\n      gen.if(_`${hasProperty} && (${checkMissingProp(cxt, deps, missing)})`)\n      reportMissingProp(cxt, missing)\n      gen.else()\n    }\n  }\n}\n\nexport function validateSchemaDeps(cxt: KeywordCxt, schemaDeps: SchemaMap = cxt.schema): void {\n  const {gen, data, keyword, it} = cxt\n  const valid = gen.name(\"valid\")\n  for (const prop in schemaDeps) {\n    if (alwaysValidSchema(it, schemaDeps[prop] as AnySchema)) continue\n    gen.if(\n      propertyInData(gen, data, prop, it.opts.ownProperties),\n      () => {\n        const schCxt = cxt.subschema({keyword, schemaProp: prop}, valid)\n        cxt.mergeValidEvaluated(schCxt, valid)\n      },\n      () => gen.var(valid, true) // TODO var\n    )\n    cxt.ok(valid)\n  }\n}\n\nexport default def\n", "import type {\n  CodeKeywordDefinition,\n  ErrorObject,\n  KeywordErrorDefinition,\n  AnySchema,\n} from \"../../types\"\nimport type {KeywordCxt} from \"../../compile/validate\"\nimport {_, not} from \"../../compile/codegen\"\nimport {alwaysValidSchema} from \"../../compile/util\"\n\nexport type PropertyNamesError = ErrorObject<\"propertyNames\", {propertyName: string}, AnySchema>\n\nconst error: KeywordErrorDefinition = {\n  message: \"property name must be valid\",\n  params: ({params}) => _`{propertyName: ${params.propertyName}}`,\n}\n\nconst def: CodeKeywordDefinition = {\n  keyword: \"propertyNames\",\n  type: \"object\",\n  schemaType: [\"object\", \"boolean\"],\n  error,\n  code(cxt: KeywordCxt) {\n    const {gen, schema, data, it} = cxt\n    if (alwaysValidSchema(it, schema)) return\n    const valid = gen.name(\"valid\")\n\n    gen.forIn(\"key\", data, (key) => {\n      cxt.setParams({propertyName: key})\n      cxt.subschema(\n        {\n          keyword: \"propertyNames\",\n          data: key,\n          dataTypes: [\"string\"],\n          propertyName: key,\n          compositeRule: true,\n        },\n        valid\n      )\n      gen.if(not(valid), () => {\n        cxt.error(true)\n        if (!it.allErrors) gen.break()\n      })\n    })\n\n    cxt.ok(valid)\n  },\n}\n\nexport default def\n", "import type {\n  CodeKeywordDefinition,\n  AddedKeywordDefinition,\n  ErrorObject,\n  KeywordErrorDefinition,\n  AnySchema,\n} from \"../../types\"\nimport {allSchemaProperties, usePattern, isOwnProperty} from \"../code\"\nimport {_, nil, or, not, Code, Name} from \"../../compile/codegen\"\nimport N from \"../../compile/names\"\nimport type {SubschemaArgs} from \"../../compile/validate/subschema\"\nimport {alwaysValidSchema, schemaRefOrVal, Type} from \"../../compile/util\"\n\nexport type AdditionalPropertiesError = ErrorObject<\n  \"additionalProperties\",\n  {additionalProperty: string},\n  AnySchema\n>\n\nconst error: KeywordErrorDefinition = {\n  message: \"must NOT have additional properties\",\n  params: ({params}) => _`{additionalProperty: ${params.additionalProperty}}`,\n}\n\nconst def: CodeKeywordDefinition & AddedKeywordDefinition = {\n  keyword: \"additionalProperties\",\n  type: [\"object\"],\n  schemaType: [\"boolean\", \"object\"],\n  allowUndefined: true,\n  trackErrors: true,\n  error,\n  code(cxt) {\n    const {gen, schema, parentSchema, data, errsCount, it} = cxt\n    /* istanbul ignore if */\n    if (!errsCount) throw new Error(\"ajv implementation error\")\n    const {allErrors, opts} = it\n    it.props = true\n    if (opts.removeAdditional !== \"all\" && alwaysValidSchema(it, schema)) return\n    const props = allSchemaProperties(parentSchema.properties)\n    const patProps = allSchemaProperties(parentSchema.patternProperties)\n    checkAdditionalProperties()\n    cxt.ok(_`${errsCount} === ${N.errors}`)\n\n    function checkAdditionalProperties(): void {\n      gen.forIn(\"key\", data, (key: Name) => {\n        if (!props.length && !patProps.length) additionalPropertyCode(key)\n        else gen.if(isAdditional(key), () => additionalPropertyCode(key))\n      })\n    }\n\n    function isAdditional(key: Name): Code {\n      let definedProp: Code\n      if (props.length > 8) {\n        // TODO maybe an option instead of hard-coded 8?\n        const propsSchema = schemaRefOrVal(it, parentSchema.properties, \"properties\")\n        definedProp = isOwnProperty(gen, propsSchema as Code, key)\n      } else if (props.length) {\n        definedProp = or(...props.map((p) => _`${key} === ${p}`))\n      } else {\n        definedProp = nil\n      }\n      if (patProps.length) {\n        definedProp = or(definedProp, ...patProps.map((p) => _`${usePattern(cxt, p)}.test(${key})`))\n      }\n      return not(definedProp)\n    }\n\n    function deleteAdditional(key: Name): void {\n      gen.code(_`delete ${data}[${key}]`)\n    }\n\n    function additionalPropertyCode(key: Name): void {\n      if (opts.removeAdditional === \"all\" || (opts.removeAdditional && schema === false)) {\n        deleteAdditional(key)\n        return\n      }\n\n      if (schema === false) {\n        cxt.setParams({additionalProperty: key})\n        cxt.error()\n        if (!allErrors) gen.break()\n        return\n      }\n\n      if (typeof schema == \"object\" && !alwaysValidSchema(it, schema)) {\n        const valid = gen.name(\"valid\")\n        if (opts.removeAdditional === \"failing\") {\n          applyAdditionalSchema(key, valid, false)\n          gen.if(not(valid), () => {\n            cxt.reset()\n            deleteAdditional(key)\n          })\n        } else {\n          applyAdditionalSchema(key, valid)\n          if (!allErrors) gen.if(not(valid), () => gen.break())\n        }\n      }\n    }\n\n    function applyAdditionalSchema(key: Name, valid: Name, errors?: false): void {\n      const subschema: SubschemaArgs = {\n        keyword: \"additionalProperties\",\n        dataProp: key,\n        dataPropType: Type.Str,\n      }\n      if (errors === false) {\n        Object.assign(subschema, {\n          compositeRule: true,\n          createErrors: false,\n          allErrors: false,\n        })\n      }\n      cxt.subschema(subschema, valid)\n    }\n  },\n}\n\nexport default def\n", "import type {CodeKeywordDefinition} from \"../../types\"\nimport {KeywordCxt} from \"../../compile/validate\"\nimport {propertyInData, allSchemaProperties} from \"../code\"\nimport {alwaysValidSchema, toHash, mergeEvaluated} from \"../../compile/util\"\nimport apDef from \"./additionalProperties\"\n\nconst def: CodeKeywordDefinition = {\n  keyword: \"properties\",\n  type: \"object\",\n  schemaType: \"object\",\n  code(cxt: KeywordCxt) {\n    const {gen, schema, parentSchema, data, it} = cxt\n    if (it.opts.removeAdditional === \"all\" && parentSchema.additionalProperties === undefined) {\n      apDef.code(new KeywordCxt(it, apDef, \"additionalProperties\"))\n    }\n    const allProps = allSchemaProperties(schema)\n    for (const prop of allProps) {\n      it.definedProperties.add(prop)\n    }\n    if (it.opts.unevaluated && allProps.length && it.props !== true) {\n      it.props = mergeEvaluated.props(gen, toHash(allProps), it.props)\n    }\n    const properties = allProps.filter((p) => !alwaysValidSchema(it, schema[p]))\n    if (properties.length === 0) return\n    const valid = gen.name(\"valid\")\n\n    for (const prop of properties) {\n      if (hasDefault(prop)) {\n        applyPropertySchema(prop)\n      } else {\n        gen.if(propertyInData(gen, data, prop, it.opts.ownProperties))\n        applyPropertySchema(prop)\n        if (!it.allErrors) gen.else().var(valid, true)\n        gen.endIf()\n      }\n      cxt.it.definedProperties.add(prop)\n      cxt.ok(valid)\n    }\n\n    function hasDefault(prop: string): boolean | undefined {\n      return it.opts.useDefaults && !it.compositeRule && schema[prop].default !== undefined\n    }\n\n    function applyPropertySchema(prop: string): void {\n      cxt.subschema(\n        {\n          keyword: \"properties\",\n          schemaProp: prop,\n          dataProp: prop,\n        },\n        valid\n      )\n    }\n  },\n}\n\nexport default def\n", "import type {CodeKeywordDefinition} from \"../../types\"\nimport type {KeywordCxt} from \"../../compile/validate\"\nimport {allSchemaProperties, usePattern} from \"../code\"\nimport {_, not, Name} from \"../../compile/codegen\"\nimport {alwaysValidSchema, checkStrictMode} from \"../../compile/util\"\nimport {evaluatedPropsToName, Type} from \"../../compile/util\"\nimport {AnySchema} from \"../../types\"\n\nconst def: CodeKeywordDefinition = {\n  keyword: \"patternProperties\",\n  type: \"object\",\n  schemaType: \"object\",\n  code(cxt: KeywordCxt) {\n    const {gen, schema, data, parentSchema, it} = cxt\n    const {opts} = it\n    const patterns = allSchemaProperties(schema)\n    const alwaysValidPatterns = patterns.filter((p) =>\n      alwaysValidSchema(it, schema[p] as AnySchema)\n    )\n\n    if (\n      patterns.length === 0 ||\n      (alwaysValidPatterns.length === patterns.length &&\n        (!it.opts.unevaluated || it.props === true))\n    ) {\n      return\n    }\n\n    const checkProperties =\n      opts.strictSchema && !opts.allowMatchingProperties && parentSchema.properties\n    const valid = gen.name(\"valid\")\n    if (it.props !== true && !(it.props instanceof Name)) {\n      it.props = evaluatedPropsToName(gen, it.props)\n    }\n    const {props} = it\n    validatePatternProperties()\n\n    function validatePatternProperties(): void {\n      for (const pat of patterns) {\n        if (checkProperties) checkMatchingProperties(pat)\n        if (it.allErrors) {\n          validateProperties(pat)\n        } else {\n          gen.var(valid, true) // TODO var\n          validateProperties(pat)\n          gen.if(valid)\n        }\n      }\n    }\n\n    function checkMatchingProperties(pat: string): void {\n      for (const prop in checkProperties) {\n        if (new RegExp(pat).test(prop)) {\n          checkStrictMode(\n            it,\n            `property ${prop} matches pattern ${pat} (use allowMatchingProperties)`\n          )\n        }\n      }\n    }\n\n    function validateProperties(pat: string): void {\n      gen.forIn(\"key\", data, (key) => {\n        gen.if(_`${usePattern(cxt, pat)}.test(${key})`, () => {\n          const alwaysValid = alwaysValidPatterns.includes(pat)\n          if (!alwaysValid) {\n            cxt.subschema(\n              {\n                keyword: \"patternProperties\",\n                schemaProp: pat,\n                dataProp: key,\n                dataPropType: Type.Str,\n              },\n              valid\n            )\n          }\n\n          if (it.opts.unevaluated && props !== true) {\n            gen.assign(_`${props}[${key}]`, true)\n          } else if (!alwaysValid && !it.allErrors) {\n            // can short-circuit if `unevaluatedProperties` is not supported (opts.next === false)\n            // or if all properties were evaluated (props === true)\n            gen.if(not(valid), () => gen.break())\n          }\n        })\n      })\n    }\n  },\n}\n\nexport default def\n", "import type {CodeKeywordDefinition, ErrorNoParams, AnySchema} from \"../../types\"\nimport type {KeywordCxt} from \"../../compile/validate\"\nimport {alwaysValidSchema} from \"../../compile/util\"\n\nexport type NotKeywordError = ErrorNoParams<\"not\", AnySchema>\n\nconst def: CodeKeywordDefinition = {\n  keyword: \"not\",\n  schemaType: [\"object\", \"boolean\"],\n  trackErrors: true,\n  code(cxt: KeywordCxt) {\n    const {gen, schema, it} = cxt\n    if (alwaysValidSchema(it, schema)) {\n      cxt.fail()\n      return\n    }\n\n    const valid = gen.name(\"valid\")\n    cxt.subschema(\n      {\n        keyword: \"not\",\n        compositeRule: true,\n        createErrors: false,\n        allErrors: false,\n      },\n      valid\n    )\n\n    cxt.failResult(\n      valid,\n      () => cxt.reset(),\n      () => cxt.error()\n    )\n  },\n  error: {message: \"must NOT be valid\"},\n}\n\nexport default def\n", "import type {CodeKeywordDefinition, ErrorNoParams, AnySchema} from \"../../types\"\nimport {validateUnion} from \"../code\"\n\nexport type AnyOfError = ErrorNoParams<\"anyOf\", AnySchema[]>\n\nconst def: CodeKeywordDefinition = {\n  keyword: \"anyOf\",\n  schemaType: \"array\",\n  trackErrors: true,\n  code: validateUnion,\n  error: {message: \"must match a schema in anyOf\"},\n}\n\nexport default def\n", "import type {\n  CodeKeywordDefinition,\n  ErrorObject,\n  KeywordErrorDefinition,\n  AnySchema,\n} from \"../../types\"\nimport type {KeywordCxt} from \"../../compile/validate\"\nimport {_, Name} from \"../../compile/codegen\"\nimport {alwaysValidSchema} from \"../../compile/util\"\nimport {SchemaCxt} from \"../../compile\"\n\nexport type OneOfError = ErrorObject<\n  \"oneOf\",\n  {passingSchemas: [number, number] | null},\n  AnySchema[]\n>\n\nconst error: KeywordErrorDefinition = {\n  message: \"must match exactly one schema in oneOf\",\n  params: ({params}) => _`{passingSchemas: ${params.passing}}`,\n}\n\nconst def: CodeKeywordDefinition = {\n  keyword: \"oneOf\",\n  schemaType: \"array\",\n  trackErrors: true,\n  error,\n  code(cxt: KeywordCxt) {\n    const {gen, schema, parentSchema, it} = cxt\n    /* istanbul ignore if */\n    if (!Array.isArray(schema)) throw new Error(\"ajv implementation error\")\n    if (it.opts.discriminator && parentSchema.discriminator) return\n    const schArr: AnySchema[] = schema\n    const valid = gen.let(\"valid\", false)\n    const passing = gen.let(\"passing\", null)\n    const schValid = gen.name(\"_valid\")\n    cxt.setParams({passing})\n    // TODO possibly fail straight away (with warning or exception) if there are two empty always valid schemas\n\n    gen.block(validateOneOf)\n\n    cxt.result(\n      valid,\n      () => cxt.reset(),\n      () => cxt.error(true)\n    )\n\n    function validateOneOf(): void {\n      schArr.forEach((sch: AnySchema, i: number) => {\n        let schCxt: SchemaCxt | undefined\n        if (alwaysValidSchema(it, sch)) {\n          gen.var(schValid, true)\n        } else {\n          schCxt = cxt.subschema(\n            {\n              keyword: \"oneOf\",\n              schemaProp: i,\n              compositeRule: true,\n            },\n            schValid\n          )\n        }\n\n        if (i > 0) {\n          gen\n            .if(_`${schValid} && ${valid}`)\n            .assign(valid, false)\n            .assign(passing, _`[${passing}, ${i}]`)\n            .else()\n        }\n\n        gen.if(schValid, () => {\n          gen.assign(valid, true)\n          gen.assign(passing, i)\n          if (schCxt) cxt.mergeEvaluated(schCxt, Name)\n        })\n      })\n    }\n  },\n}\n\nexport default def\n", "import type {CodeKeywordDefinition, AnySchema} from \"../../types\"\nimport type {KeywordCxt} from \"../../compile/validate\"\nimport {alwaysValidSchema} from \"../../compile/util\"\n\nconst def: CodeKeywordDefinition = {\n  keyword: \"allOf\",\n  schemaType: \"array\",\n  code(cxt: KeywordCxt) {\n    const {gen, schema, it} = cxt\n    /* istanbul ignore if */\n    if (!Array.isArray(schema)) throw new Error(\"ajv implementation error\")\n    const valid = gen.name(\"valid\")\n    schema.forEach((sch: AnySchema, i: number) => {\n      if (alwaysValidSchema(it, sch)) return\n      const schCxt = cxt.subschema({keyword: \"allOf\", schemaProp: i}, valid)\n      cxt.ok(valid)\n      cxt.mergeEvaluated(schCxt)\n    })\n  },\n}\n\nexport default def\n", "import type {\n  CodeKeywordDefinition,\n  ErrorObject,\n  KeywordErrorDefinition,\n  AnySchema,\n} from \"../../types\"\nimport type {SchemaObjCxt} from \"../../compile\"\nimport type {KeywordCxt} from \"../../compile/validate\"\nimport {_, str, not, Name} from \"../../compile/codegen\"\nimport {alwaysValidSchema, checkStrictMode} from \"../../compile/util\"\n\nexport type IfKeywordError = ErrorObject<\"if\", {failingKeyword: string}, AnySchema>\n\nconst error: KeywordErrorDefinition = {\n  message: ({params}) => str`must match \"${params.ifClause}\" schema`,\n  params: ({params}) => _`{failingKeyword: ${params.ifClause}}`,\n}\n\nconst def: CodeKeywordDefinition = {\n  keyword: \"if\",\n  schemaType: [\"object\", \"boolean\"],\n  trackErrors: true,\n  error,\n  code(cxt: KeywordCxt) {\n    const {gen, parentSchema, it} = cxt\n    if (parentSchema.then === undefined && parentSchema.else === undefined) {\n      checkStrictMode(it, '\"if\" without \"then\" and \"else\" is ignored')\n    }\n    const hasThen = hasSchema(it, \"then\")\n    const hasElse = hasSchema(it, \"else\")\n    if (!hasThen && !hasElse) return\n\n    const valid = gen.let(\"valid\", true)\n    const schValid = gen.name(\"_valid\")\n    validateIf()\n    cxt.reset()\n\n    if (hasThen && hasElse) {\n      const ifClause = gen.let(\"ifClause\")\n      cxt.setParams({ifClause})\n      gen.if(schValid, validateClause(\"then\", ifClause), validateClause(\"else\", ifClause))\n    } else if (hasThen) {\n      gen.if(schValid, validateClause(\"then\"))\n    } else {\n      gen.if(not(schValid), validateClause(\"else\"))\n    }\n\n    cxt.pass(valid, () => cxt.error(true))\n\n    function validateIf(): void {\n      const schCxt = cxt.subschema(\n        {\n          keyword: \"if\",\n          compositeRule: true,\n          createErrors: false,\n          allErrors: false,\n        },\n        schValid\n      )\n      cxt.mergeEvaluated(schCxt)\n    }\n\n    function validateClause(keyword: string, ifClause?: Name): () => void {\n      return () => {\n        const schCxt = cxt.subschema({keyword}, schValid)\n        gen.assign(valid, schValid)\n        cxt.mergeValidEvaluated(schCxt, valid)\n        if (ifClause) gen.assign(ifClause, _`${keyword}`)\n        else cxt.setParams({ifClause: keyword})\n      }\n    }\n  },\n}\n\nfunction hasSchema(it: SchemaObjCxt, keyword: string): boolean {\n  const schema = it.schema[keyword]\n  return schema !== undefined && !alwaysValidSchema(it, schema)\n}\n\nexport default def\n", "import type {CodeKeywordDefinition} from \"../../types\"\nimport type {KeywordCxt} from \"../../compile/validate\"\nimport {checkStrictMode} from \"../../compile/util\"\n\nconst def: CodeKeywordDefinition = {\n  keyword: [\"then\", \"else\"],\n  schemaType: [\"object\", \"boolean\"],\n  code({keyword, parentSchema, it}: KeywordCxt) {\n    if (parentSchema.if === undefined) checkStrictMode(it, `\"${keyword}\" without \"if\" is ignored`)\n  },\n}\n\nexport default def\n", "import type {ErrorNoParams, Vocabulary} from \"../../types\"\nimport additionalItems, {AdditionalItemsError} from \"./additionalItems\"\nimport prefixItems from \"./prefixItems\"\nimport items from \"./items\"\nimport items2020, {ItemsError} from \"./items2020\"\nimport contains, {ContainsError} from \"./contains\"\nimport dependencies, {DependenciesError} from \"./dependencies\"\nimport propertyNames, {PropertyNamesError} from \"./propertyNames\"\nimport additionalProperties, {AdditionalPropertiesError} from \"./additionalProperties\"\nimport properties from \"./properties\"\nimport patternProperties from \"./patternProperties\"\nimport notKeyword, {NotKeywordError} from \"./not\"\nimport anyOf, {AnyOfError} from \"./anyOf\"\nimport oneOf, {OneOfError} from \"./oneOf\"\nimport allOf from \"./allOf\"\nimport ifKeyword, {IfKeywordError} from \"./if\"\nimport thenElse from \"./thenElse\"\n\nexport default function getApplicator(draft2020 = false): Vocabulary {\n  const applicator = [\n    // any\n    notKeyword,\n    anyOf,\n    oneOf,\n    allOf,\n    ifKeyword,\n    thenElse,\n    // object\n    propertyNames,\n    additionalProperties,\n    dependencies,\n    properties,\n    patternProperties,\n  ]\n  // array\n  if (draft2020) applicator.push(prefixItems, items2020)\n  else applicator.push(additionalItems, items)\n  applicator.push(contains)\n  return applicator\n}\n\nexport type ApplicatorKeywordError =\n  | ErrorNoParams<\"false schema\">\n  | AdditionalItemsError\n  | ItemsError\n  | ContainsError\n  | AdditionalPropertiesError\n  | DependenciesError\n  | IfKeywordError\n  | AnyOfError\n  | OneOfError\n  | NotKeywordError\n  | PropertyNamesError\n", "import type {\n  AddedFormat,\n  FormatValidator,\n  AsyncFormatValidator,\n  CodeKeywordDefinition,\n  KeywordErrorDefinition,\n  ErrorObject,\n} from \"../../types\"\nimport type {KeywordCxt} from \"../../compile/validate\"\nimport {_, str, nil, or, Code, getProperty, regexpCode} from \"../../compile/codegen\"\n\ntype FormatValidate =\n  | FormatValidator<string>\n  | FormatValidator<number>\n  | AsyncFormatValidator<string>\n  | AsyncFormatValidator<number>\n  | RegExp\n  | string\n  | true\n\nexport type FormatError = ErrorObject<\"format\", {format: string}, string | {$data: string}>\n\nconst error: KeywordErrorDefinition = {\n  message: ({schemaCode}) => str`must match format \"${schemaCode}\"`,\n  params: ({schemaCode}) => _`{format: ${schemaCode}}`,\n}\n\nconst def: CodeKeywordDefinition = {\n  keyword: \"format\",\n  type: [\"number\", \"string\"],\n  schemaType: \"string\",\n  $data: true,\n  error,\n  code(cxt: KeywordCxt, ruleType?: string) {\n    const {gen, data, $data, schema, schemaCode, it} = cxt\n    const {opts, errSchemaPath, schemaEnv, self} = it\n    if (!opts.validateFormats) return\n\n    if ($data) validate$DataFormat()\n    else validateFormat()\n\n    function validate$DataFormat(): void {\n      const fmts = gen.scopeValue(\"formats\", {\n        ref: self.formats,\n        code: opts.code.formats,\n      })\n      const fDef = gen.const(\"fDef\", _`${fmts}[${schemaCode}]`)\n      const fType = gen.let(\"fType\")\n      const format = gen.let(\"format\")\n      // TODO simplify\n      gen.if(\n        _`typeof ${fDef} == \"object\" && !(${fDef} instanceof RegExp)`,\n        () => gen.assign(fType, _`${fDef}.type || \"string\"`).assign(format, _`${fDef}.validate`),\n        () => gen.assign(fType, _`\"string\"`).assign(format, fDef)\n      )\n      cxt.fail$data(or(unknownFmt(), invalidFmt()))\n\n      function unknownFmt(): Code {\n        if (opts.strictSchema === false) return nil\n        return _`${schemaCode} && !${format}`\n      }\n\n      function invalidFmt(): Code {\n        const callFormat = schemaEnv.$async\n          ? _`(${fDef}.async ? await ${format}(${data}) : ${format}(${data}))`\n          : _`${format}(${data})`\n        const validData = _`(typeof ${format} == \"function\" ? ${callFormat} : ${format}.test(${data}))`\n        return _`${format} && ${format} !== true && ${fType} === ${ruleType} && !${validData}`\n      }\n    }\n\n    function validateFormat(): void {\n      const formatDef: AddedFormat | undefined = self.formats[schema]\n      if (!formatDef) {\n        unknownFormat()\n        return\n      }\n      if (formatDef === true) return\n      const [fmtType, format, fmtRef] = getFormat(formatDef)\n      if (fmtType === ruleType) cxt.pass(validCondition())\n\n      function unknownFormat(): void {\n        if (opts.strictSchema === false) {\n          self.logger.warn(unknownMsg())\n          return\n        }\n        throw new Error(unknownMsg())\n\n        function unknownMsg(): string {\n          return `unknown format \"${schema as string}\" ignored in schema at path \"${errSchemaPath}\"`\n        }\n      }\n\n      function getFormat(fmtDef: AddedFormat): [string, FormatValidate, Code] {\n        const code =\n          fmtDef instanceof RegExp\n            ? regexpCode(fmtDef)\n            : opts.code.formats\n            ? _`${opts.code.formats}${getProperty(schema)}`\n            : undefined\n        const fmt = gen.scopeValue(\"formats\", {key: schema, ref: fmtDef, code})\n        if (typeof fmtDef == \"object\" && !(fmtDef instanceof RegExp)) {\n          return [fmtDef.type || \"string\", fmtDef.validate, _`${fmt}.validate`]\n        }\n\n        return [\"string\", fmtDef, fmt]\n      }\n\n      function validCondition(): Code {\n        if (typeof formatDef == \"object\" && !(formatDef instanceof RegExp) && formatDef.async) {\n          if (!schemaEnv.$async) throw new Error(\"async format in sync schema\")\n          return _`await ${fmtRef}(${data})`\n        }\n        return typeof format == \"function\" ? _`${fmtRef}(${data})` : _`${fmtRef}.test(${data})`\n      }\n    }\n  },\n}\n\nexport default def\n", "import type {Vocabulary} from \"../../types\"\nimport formatKeyword from \"./format\"\n\nconst format: Vocabulary = [formatKeyword]\n\nexport default format\n", "import type {Vocabulary} from \"../types\"\n\nexport const metadataVocabulary: Vocabulary = [\n  \"title\",\n  \"description\",\n  \"default\",\n  \"deprecated\",\n  \"readOnly\",\n  \"writeOnly\",\n  \"examples\",\n]\n\nexport const contentVocabulary: Vocabulary = [\n  \"contentMediaType\",\n  \"contentEncoding\",\n  \"contentSchema\",\n]\n", "import type {Vocabulary} from \"../types\"\nimport coreVocabulary from \"./core\"\nimport validationVocabulary from \"./validation\"\nimport getApplicatorVocabulary from \"./applicator\"\nimport formatVocabulary from \"./format\"\nimport {metadataVocabulary, contentVocabulary} from \"./metadata\"\n\nconst draft7Vocabularies: Vocabulary[] = [\n  coreVocabulary,\n  validationVocabulary,\n  getApplicatorVocabulary(),\n  formatVocabulary,\n  metadataVocabulary,\n  contentVocabulary,\n]\n\nexport default draft7Vocabularies\n", "import type {ErrorObject} from \"../../types\"\n\nexport enum DiscrError {\n  Tag = \"tag\",\n  Mapping = \"mapping\",\n}\n\nexport type DiscrErrorObj<E extends DiscrError> = ErrorObject<\n  \"discriminator\",\n  {error: E; tag: string; tagValue: unknown},\n  string\n>\n", "import type {CodeKeywordDefinition, AnySchemaObject, KeywordErrorDefinition} from \"../../types\"\nimport type {KeywordCxt} from \"../../compile/validate\"\nimport {_, getProperty, Name} from \"../../compile/codegen\"\nimport {DiscrError, DiscrErrorObj} from \"../discriminator/types\"\nimport {resolveRef, SchemaEnv} from \"../../compile\"\nimport MissingRefError from \"../../compile/ref_error\"\nimport {schemaHasRulesButRef} from \"../../compile/util\"\n\nexport type DiscriminatorError = DiscrErrorObj<DiscrError.Tag> | DiscrErrorObj<DiscrError.Mapping>\n\nconst error: KeywordErrorDefinition = {\n  message: ({params: {discrError, tagName}}) =>\n    discrError === DiscrError.Tag\n      ? `tag \"${tagName}\" must be string`\n      : `value of tag \"${tagName}\" must be in oneOf`,\n  params: ({params: {discrError, tag, tagName}}) =>\n    _`{error: ${discrError}, tag: ${tagName}, tagValue: ${tag}}`,\n}\n\nconst def: CodeKeywordDefinition = {\n  keyword: \"discriminator\",\n  type: \"object\",\n  schemaType: \"object\",\n  error,\n  code(cxt: KeywordCxt) {\n    const {gen, data, schema, parentSchema, it} = cxt\n    const {oneOf} = parentSchema\n    if (!it.opts.discriminator) {\n      throw new Error(\"discriminator: requires discriminator option\")\n    }\n    const tagName = schema.propertyName\n    if (typeof tagName != \"string\") throw new Error(\"discriminator: requires propertyName\")\n    if (schema.mapping) throw new Error(\"discriminator: mapping is not supported\")\n    if (!oneOf) throw new Error(\"discriminator: requires oneOf keyword\")\n    const valid = gen.let(\"valid\", false)\n    const tag = gen.const(\"tag\", _`${data}${getProperty(tagName)}`)\n    gen.if(\n      _`typeof ${tag} == \"string\"`,\n      () => validateMapping(),\n      () => cxt.error(false, {discrError: DiscrError.Tag, tag, tagName})\n    )\n    cxt.ok(valid)\n\n    function validateMapping(): void {\n      const mapping = getMapping()\n      gen.if(false)\n      for (const tagValue in mapping) {\n        gen.elseIf(_`${tag} === ${tagValue}`)\n        gen.assign(valid, applyTagSchema(mapping[tagValue]))\n      }\n      gen.else()\n      cxt.error(false, {discrError: DiscrError.Mapping, tag, tagName})\n      gen.endIf()\n    }\n\n    function applyTagSchema(schemaProp?: number): Name {\n      const _valid = gen.name(\"valid\")\n      const schCxt = cxt.subschema({keyword: \"oneOf\", schemaProp}, _valid)\n      cxt.mergeEvaluated(schCxt, Name)\n      return _valid\n    }\n\n    function getMapping(): {[T in string]?: number} {\n      const oneOfMapping: {[T in string]?: number} = {}\n      const topRequired = hasRequired(parentSchema)\n      let tagRequired = true\n      for (let i = 0; i < oneOf.length; i++) {\n        let sch = oneOf[i]\n        if (sch?.$ref && !schemaHasRulesButRef(sch, it.self.RULES)) {\n          const ref = sch.$ref\n          sch = resolveRef.call(it.self, it.schemaEnv.root, it.baseId, ref)\n          if (sch instanceof SchemaEnv) sch = sch.schema\n          if (sch === undefined) throw new MissingRefError(it.opts.uriResolver, it.baseId, ref)\n        }\n        const propSch = sch?.properties?.[tagName]\n        if (typeof propSch != \"object\") {\n          throw new Error(\n            `discriminator: oneOf subschemas (or referenced schemas) must have \"properties/${tagName}\"`\n          )\n        }\n        tagRequired = tagRequired && (topRequired || hasRequired(sch))\n        addMappings(propSch, i)\n      }\n      if (!tagRequired) throw new Error(`discriminator: \"${tagName}\" must be required`)\n      return oneOfMapping\n\n      function hasRequired({required}: AnySchemaObject): boolean {\n        return Array.isArray(required) && required.includes(tagName)\n      }\n\n      function addMappings(sch: AnySchemaObject, i: number): void {\n        if (sch.const) {\n          addMapping(sch.const, i)\n        } else if (sch.enum) {\n          for (const tagValue of sch.enum) {\n            addMapping(tagValue, i)\n          }\n        } else {\n          throw new Error(`discriminator: \"properties/${tagName}\" must have \"const\" or \"enum\"`)\n        }\n      }\n\n      function addMapping(tagValue: unknown, i: number): void {\n        if (typeof tagValue != \"string\" || tagValue in oneOfMapping) {\n          throw new Error(`discriminator: \"${tagName}\" values must be unique strings`)\n        }\n        oneOfMapping[tagValue] = i\n      }\n    }\n  },\n}\n\nexport default def\n", "{\n  \"$schema\": \"http://json-schema.org/draft-07/schema#\",\n  \"$id\": \"http://json-schema.org/draft-07/schema#\",\n  \"title\": \"Core schema meta-schema\",\n  \"definitions\": {\n    \"schemaArray\": {\n      \"type\": \"array\",\n      \"minItems\": 1,\n      \"items\": {\"$ref\": \"#\"}\n    },\n    \"nonNegativeInteger\": {\n      \"type\": \"integer\",\n      \"minimum\": 0\n    },\n    \"nonNegativeIntegerDefault0\": {\n      \"allOf\": [{\"$ref\": \"#/definitions/nonNegativeInteger\"}, {\"default\": 0}]\n    },\n    \"simpleTypes\": {\n      \"enum\": [\"array\", \"boolean\", \"integer\", \"null\", \"number\", \"object\", \"string\"]\n    },\n    \"stringArray\": {\n      \"type\": \"array\",\n      \"items\": {\"type\": \"string\"},\n      \"uniqueItems\": true,\n      \"default\": []\n    }\n  },\n  \"type\": [\"object\", \"boolean\"],\n  \"properties\": {\n    \"$id\": {\n      \"type\": \"string\",\n      \"format\": \"uri-reference\"\n    },\n    \"$schema\": {\n      \"type\": \"string\",\n      \"format\": \"uri\"\n    },\n    \"$ref\": {\n      \"type\": \"string\",\n      \"format\": \"uri-reference\"\n    },\n    \"$comment\": {\n      \"type\": \"string\"\n    },\n    \"title\": {\n      \"type\": \"string\"\n    },\n    \"description\": {\n      \"type\": \"string\"\n    },\n    \"default\": true,\n    \"readOnly\": {\n      \"type\": \"boolean\",\n      \"default\": false\n    },\n    \"examples\": {\n      \"type\": \"array\",\n      \"items\": true\n    },\n    \"multipleOf\": {\n      \"type\": \"number\",\n      \"exclusiveMinimum\": 0\n    },\n    \"maximum\": {\n      \"type\": \"number\"\n    },\n    \"exclusiveMaximum\": {\n      \"type\": \"number\"\n    },\n    \"minimum\": {\n      \"type\": \"number\"\n    },\n    \"exclusiveMinimum\": {\n      \"type\": \"number\"\n    },\n    \"maxLength\": {\"$ref\": \"#/definitions/nonNegativeInteger\"},\n    \"minLength\": {\"$ref\": \"#/definitions/nonNegativeIntegerDefault0\"},\n    \"pattern\": {\n      \"type\": \"string\",\n      \"format\": \"regex\"\n    },\n    \"additionalItems\": {\"$ref\": \"#\"},\n    \"items\": {\n      \"anyOf\": [{\"$ref\": \"#\"}, {\"$ref\": \"#/definitions/schemaArray\"}],\n      \"default\": true\n    },\n    \"maxItems\": {\"$ref\": \"#/definitions/nonNegativeInteger\"},\n    \"minItems\": {\"$ref\": \"#/definitions/nonNegativeIntegerDefault0\"},\n    \"uniqueItems\": {\n      \"type\": \"boolean\",\n      \"default\": false\n    },\n    \"contains\": {\"$ref\": \"#\"},\n    \"maxProperties\": {\"$ref\": \"#/definitions/nonNegativeInteger\"},\n    \"minProperties\": {\"$ref\": \"#/definitions/nonNegativeIntegerDefault0\"},\n    \"required\": {\"$ref\": \"#/definitions/stringArray\"},\n    \"additionalProperties\": {\"$ref\": \"#\"},\n    \"definitions\": {\n      \"type\": \"object\",\n      \"additionalProperties\": {\"$ref\": \"#\"},\n      \"default\": {}\n    },\n    \"properties\": {\n      \"type\": \"object\",\n      \"additionalProperties\": {\"$ref\": \"#\"},\n      \"default\": {}\n    },\n    \"patternProperties\": {\n      \"type\": \"object\",\n      \"additionalProperties\": {\"$ref\": \"#\"},\n      \"propertyNames\": {\"format\": \"regex\"},\n      \"default\": {}\n    },\n    \"dependencies\": {\n      \"type\": \"object\",\n      \"additionalProperties\": {\n        \"anyOf\": [{\"$ref\": \"#\"}, {\"$ref\": \"#/definitions/stringArray\"}]\n      }\n    },\n    \"propertyNames\": {\"$ref\": \"#\"},\n    \"const\": true,\n    \"enum\": {\n      \"type\": \"array\",\n      \"items\": true,\n      \"minItems\": 1,\n      \"uniqueItems\": true\n    },\n    \"type\": {\n      \"anyOf\": [\n        {\"$ref\": \"#/definitions/simpleTypes\"},\n        {\n          \"type\": \"array\",\n          \"items\": {\"$ref\": \"#/definitions/simpleTypes\"},\n          \"minItems\": 1,\n          \"uniqueItems\": true\n        }\n      ]\n    },\n    \"format\": {\"type\": \"string\"},\n    \"contentMediaType\": {\"type\": \"string\"},\n    \"contentEncoding\": {\"type\": \"string\"},\n    \"if\": {\"$ref\": \"#\"},\n    \"then\": {\"$ref\": \"#\"},\n    \"else\": {\"$ref\": \"#\"},\n    \"allOf\": {\"$ref\": \"#/definitions/schemaArray\"},\n    \"anyOf\": {\"$ref\": \"#/definitions/schemaArray\"},\n    \"oneOf\": {\"$ref\": \"#/definitions/schemaArray\"},\n    \"not\": {\"$ref\": \"#\"}\n  },\n  \"default\": true\n}\n", "import type {AnySchemaObject} from \"./types\"\nimport AjvCore from \"./core\"\nimport draft7Vocabularies from \"./vocabularies/draft7\"\nimport discriminator from \"./vocabularies/discriminator\"\nimport * as draft7MetaSchema from \"./refs/json-schema-draft-07.json\"\n\nconst META_SUPPORT_DATA = [\"/properties\"]\n\nconst META_SCHEMA_ID = \"http://json-schema.org/draft-07/schema\"\n\nexport class Ajv extends AjvCore {\n  _addVocabularies(): void {\n    super._addVocabularies()\n    draft7Vocabularies.forEach((v) => this.addVocabulary(v))\n    if (this.opts.discriminator) this.addKeyword(discriminator)\n  }\n\n  _addDefaultMetaSchema(): void {\n    super._addDefaultMetaSchema()\n    if (!this.opts.meta) return\n    const metaSchema = this.opts.$data\n      ? this.$dataMetaSchema(draft7MetaSchema, META_SUPPORT_DATA)\n      : draft7MetaSchema\n    this.addMetaSchema(metaSchema, META_SCHEMA_ID, false)\n    this.refs[\"http://json-schema.org/schema\"] = META_SCHEMA_ID\n  }\n\n  defaultMeta(): string | AnySchemaObject | undefined {\n    return (this.opts.defaultMeta =\n      super.defaultMeta() || (this.getSchema(META_SCHEMA_ID) ? META_SCHEMA_ID : undefined))\n  }\n}\n\nmodule.exports = exports = Ajv\nmodule.exports.Ajv = Ajv\nObject.defineProperty(exports, \"__esModule\", {value: true})\n\nexport default Ajv\n\nexport {\n  Format,\n  FormatDefinition,\n  AsyncFormatDefinition,\n  KeywordDefinition,\n  KeywordErrorDefinition,\n  CodeKeywordDefinition,\n  MacroKeywordDefinition,\n  FuncKeywordDefinition,\n  Vocabulary,\n  Schema,\n  SchemaObject,\n  AnySchemaObject,\n  AsyncSchema,\n  AnySchema,\n  ValidateFunction,\n  AsyncValidateFunction,\n  SchemaValidateFunction,\n  ErrorObject,\n  ErrorNoParams,\n} from \"./types\"\n\nexport {Plugin, Options, CodeOptions, InstanceOptions, Logger, ErrorsTextOptions} from \"./core\"\nexport {SchemaCxt, SchemaObjCxt} from \"./compile\"\nexport {KeywordCxt} from \"./compile/validate\"\nexport {DefinedError} from \"./vocabularies/errors\"\nexport {JSONType} from \"./compile/rules\"\nexport {JSONSchemaType} from \"./types/json-schema\"\nexport {_, str, stringify, nil, Name, Code, CodeGen, CodeGenOptions} from \"./compile/codegen\"\nexport {default as ValidationError} from \"./runtime/validation_error\"\nexport {default as MissingRefError} from \"./compile/ref_error\"\n"], "mappings": ";;;;;;;;;;AACA,QAAsB,cAAtB,MAAiC;;AAAjC,YAAA,cAAA;AAOa,YAAA,aAAa;AAE1B,QAAa,OAAb,cAA0B,YAAW;MAEnC,YAAY,GAAS;AACnB,cAAK;AACL,YAAI,CAAC,QAAA,WAAW,KAAK,CAAC;AAAG,gBAAM,IAAI,MAAM,0CAA0C;AACnF,aAAK,MAAM;MACb;MAEA,WAAQ;AACN,eAAO,KAAK;MACd;MAEA,WAAQ;AACN,eAAO;MACT;MAEA,IAAI,QAAK;AACP,eAAO,EAAC,CAAC,KAAK,GAAG,GAAG,EAAC;MACvB;;AAlBF,YAAA,OAAA;AAqBA,QAAa,QAAb,cAA2B,YAAW;MAKpC,YAAY,MAAkC;AAC5C,cAAK;AACL,aAAK,SAAS,OAAO,SAAS,WAAW,CAAC,IAAI,IAAI;MACpD;MAEA,WAAQ;AACN,eAAO,KAAK;MACd;MAEA,WAAQ;AACN,YAAI,KAAK,OAAO,SAAS;AAAG,iBAAO;AACnC,cAAM,OAAO,KAAK,OAAO,CAAC;AAC1B,eAAO,SAAS,MAAM,SAAS;MACjC;MAEA,IAAI,MAAG;;AACL,gBAAO,KAAC,KAAK,UAAI,QAAA,OAAA,SAAA,KAAT,KAAK,OAAS,KAAK,OAAO,OAAO,CAAC,GAAW,MAAgB,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE;MACrF;MAEA,IAAI,QAAK;;AACP,gBAAO,KAAC,KAAK,YAAM,QAAA,OAAA,SAAA,KAAX,KAAK,SAAW,KAAK,OAAO,OAAO,CAAC,OAAkB,MAAK;AACjE,cAAI,aAAa;AAAM,kBAAM,EAAE,GAAG,KAAK,MAAM,EAAE,GAAG,KAAK,KAAK;AAC5D,iBAAO;QACT,GAAG,CAAA,CAAE;MACP;;AA7BF,YAAA,QAAA;AAwCa,YAAA,MAAM,IAAI,MAAM,EAAE;AAI/B,aAAgB,EAAE,SAA+B,MAAe;AAC9D,YAAM,OAAmB,CAAC,KAAK,CAAC,CAAC;AACjC,UAAI,IAAI;AACR,aAAO,IAAI,KAAK,QAAQ;AACtB,mBAAW,MAAM,KAAK,CAAC,CAAC;AACxB,aAAK,KAAK,KAAK,EAAE,CAAC,CAAC;MACrB;AACA,aAAO,IAAI,MAAM,IAAI;IACvB;AARA,YAAA,IAAA;AAUA,QAAM,OAAO,IAAI,MAAM,GAAG;AAE1B,aAAgB,IAAI,SAA+B,MAA4B;AAC7E,YAAM,OAAmB,CAAC,cAAc,KAAK,CAAC,CAAC,CAAC;AAChD,UAAI,IAAI;AACR,aAAO,IAAI,KAAK,QAAQ;AACtB,aAAK,KAAK,IAAI;AACd,mBAAW,MAAM,KAAK,CAAC,CAAC;AACxB,aAAK,KAAK,MAAM,cAAc,KAAK,EAAE,CAAC,CAAC,CAAC;MAC1C;AACA,eAAS,IAAI;AACb,aAAO,IAAI,MAAM,IAAI;IACvB;AAVA,YAAA,MAAA;AAYA,aAAgB,WAAW,MAAkB,KAAuB;AAClE,UAAI,eAAe;AAAO,aAAK,KAAK,GAAG,IAAI,MAAM;eACxC,eAAe;AAAM,aAAK,KAAK,GAAG;;AACtC,aAAK,KAAK,YAAY,GAAG,CAAC;IACjC;AAJA,YAAA,aAAA;AAMA,aAAS,SAAS,MAAgB;AAChC,UAAI,IAAI;AACR,aAAO,IAAI,KAAK,SAAS,GAAG;AAC1B,YAAI,KAAK,CAAC,MAAM,MAAM;AACpB,gBAAM,MAAM,eAAe,KAAK,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,CAAC;AACnD,cAAI,QAAQ,QAAW;AACrB,iBAAK,OAAO,IAAI,GAAG,GAAG,GAAG;AACzB;UACF;AACA,eAAK,GAAG,IAAI;QACd;AACA;MACF;IACF;AAEA,aAAS,eAAe,GAAa,GAAW;AAC9C,UAAI,MAAM;AAAM,eAAO;AACvB,UAAI,MAAM;AAAM,eAAO;AACvB,UAAI,OAAO,KAAK,UAAU;AACxB,YAAI,aAAa,QAAQ,EAAE,EAAE,SAAS,CAAC,MAAM;AAAK;AAClD,YAAI,OAAO,KAAK;AAAU,iBAAO,GAAG,EAAE,MAAM,GAAG,EAAE,CAAC,GAAG,CAAC;AACtD,YAAI,EAAE,CAAC,MAAM;AAAK,iBAAO,EAAE,MAAM,GAAG,EAAE,IAAI,EAAE,MAAM,CAAC;AACnD;MACF;AACA,UAAI,OAAO,KAAK,YAAY,EAAE,CAAC,MAAM,OAAO,EAAE,aAAa;AAAO,eAAO,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;AAC3F;IACF;AAEA,aAAgB,UAAU,IAAU,IAAQ;AAC1C,aAAO,GAAG,SAAQ,IAAK,KAAK,GAAG,SAAQ,IAAK,KAAK,MAAM,EAAE,GAAG,EAAE;IAChE;AAFA,YAAA,YAAA;AAKA,aAAS,YAAY,GAA+C;AAClE,aAAO,OAAO,KAAK,YAAY,OAAO,KAAK,aAAa,MAAM,OAC1D,IACA,cAAc,MAAM,QAAQ,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI,CAAC;IACtD;AAEA,aAAgB,UAAU,GAAU;AAClC,aAAO,IAAI,MAAM,cAAc,CAAC,CAAC;IACnC;AAFA,YAAA,YAAA;AAIA,aAAgB,cAAc,GAAU;AACtC,aAAO,KAAK,UAAU,CAAC,EACpB,QAAQ,WAAW,SAAS,EAC5B,QAAQ,WAAW,SAAS;IACjC;AAJA,YAAA,gBAAA;AAMA,aAAgB,YAAY,KAA2B;AACrD,aAAO,OAAO,OAAO,YAAY,QAAA,WAAW,KAAK,GAAG,IAAI,IAAI,MAAM,IAAI,GAAG,EAAE,IAAI,KAAK,GAAG;IACzF;AAFA,YAAA,cAAA;AAKA,aAAgB,iBAAiB,KAA2B;AAC1D,UAAI,OAAO,OAAO,YAAY,QAAA,WAAW,KAAK,GAAG,GAAG;AAClD,eAAO,IAAI,MAAM,GAAG,GAAG,EAAE;MAC3B;AACA,YAAM,IAAI,MAAM,iCAAiC,GAAG,iCAAiC;IACvF;AALA,YAAA,mBAAA;AAOA,aAAgB,WAAW,IAAU;AACnC,aAAO,IAAI,MAAM,GAAG,SAAQ,CAAE;IAChC;AAFA,YAAA,aAAA;;;;;;;;;;ACtKA,QAAA,SAAA;AAeA,QAAM,aAAN,cAAyB,MAAK;MAE5B,YAAY,MAAoB;AAC9B,cAAM,uBAAuB,IAAI,cAAc;AAC/C,aAAK,QAAQ,KAAK;MACpB;;AAwBF,QAAY;AAAZ,KAAA,SAAYA,iBAAc;AACxB,MAAAA,gBAAAA,gBAAA,SAAA,IAAA,CAAA,IAAA;AACA,MAAAA,gBAAAA,gBAAA,WAAA,IAAA,CAAA,IAAA;IACF,GAHY,mBAAc,QAAA,iBAAd,iBAAc,CAAA,EAAA;AASb,YAAA,WAAW;MACtB,OAAO,IAAI,OAAA,KAAK,OAAO;MACvB,KAAK,IAAI,OAAA,KAAK,KAAK;MACnB,KAAK,IAAI,OAAA,KAAK,KAAK;;AAGrB,QAAa,QAAb,MAAkB;MAKhB,YAAY,EAAC,UAAU,OAAM,IAAkB,CAAA,GAAE;AAJ9B,aAAA,SAA2C,CAAA;AAK5D,aAAK,YAAY;AACjB,aAAK,UAAU;MACjB;MAEA,OAAO,cAA2B;AAChC,eAAO,wBAAwB,OAAA,OAAO,eAAe,KAAK,KAAK,YAAY;MAC7E;MAEA,KAAK,QAAc;AACjB,eAAO,IAAI,OAAA,KAAK,KAAK,SAAS,MAAM,CAAC;MACvC;MAEU,SAAS,QAAc;AAC/B,cAAM,KAAK,KAAK,OAAO,MAAM,KAAK,KAAK,WAAW,MAAM;AACxD,eAAO,GAAG,MAAM,GAAG,GAAG,OAAO;MAC/B;MAEQ,WAAW,QAAc;;AAC/B,cAAI,MAAA,KAAA,KAAK,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,eAAS,QAAA,OAAA,SAAA,SAAA,GAAE,IAAI,MAAM,MAAM,KAAK,aAAa,CAAC,KAAK,UAAU,IAAI,MAAM,GAAI;AAC3F,gBAAM,IAAI,MAAM,oBAAoB,MAAM,gCAAgC;QAC5E;AACA,eAAQ,KAAK,OAAO,MAAM,IAAI,EAAC,QAAQ,OAAO,EAAC;MACjD;;AA5BF,YAAA,QAAA;AAoCA,QAAa,iBAAb,cAAoC,OAAA,KAAI;MAKtC,YAAY,QAAgB,SAAe;AACzC,cAAM,OAAO;AACb,aAAK,SAAS;MAChB;MAEA,SAAS,OAAkB,EAAC,UAAU,UAAS,GAAY;AACzD,aAAK,QAAQ;AACb,aAAK,aAAY,GAAA,OAAA,MAAK,IAAI,OAAA,KAAK,QAAQ,CAAC,IAAI,SAAS;MACvD;;AAbF,YAAA,iBAAA;AAoBA,QAAM,QAAO,GAAA,OAAA;AAEb,QAAa,aAAb,cAAgC,MAAK;MAKnC,YAAY,MAAuB;AACjC,cAAM,IAAI;AALO,aAAA,UAAuB,CAAA;AAMxC,aAAK,SAAS,KAAK;AACnB,aAAK,OAAO,EAAC,GAAG,MAAM,IAAI,KAAK,QAAQ,OAAO,OAAA,IAAG;MACnD;MAEA,MAAG;AACD,eAAO,KAAK;MACd;MAEA,KAAK,QAAc;AACjB,eAAO,IAAI,eAAe,QAAQ,KAAK,SAAS,MAAM,CAAC;MACzD;MAEA,MAAM,cAAuC,OAAgB;;AAC3D,YAAI,MAAM,QAAQ;AAAW,gBAAM,IAAI,MAAM,sCAAsC;AACnF,cAAM,OAAO,KAAK,OAAO,YAAY;AACrC,cAAM,EAAC,OAAM,IAAI;AACjB,cAAM,YAAW,KAAA,MAAM,SAAG,QAAA,OAAA,SAAA,KAAI,MAAM;AACpC,YAAI,KAAK,KAAK,QAAQ,MAAM;AAC5B,YAAI,IAAI;AACN,gBAAM,QAAQ,GAAG,IAAI,QAAQ;AAC7B,cAAI;AAAO,mBAAO;QACpB,OAAO;AACL,eAAK,KAAK,QAAQ,MAAM,IAAI,oBAAI,IAAG;QACrC;AACA,WAAG,IAAI,UAAU,IAAI;AAErB,cAAM,IAAI,KAAK,OAAO,MAAM,MAAM,KAAK,OAAO,MAAM,IAAI,CAAA;AACxD,cAAM,YAAY,EAAE;AACpB,UAAE,SAAS,IAAI,MAAM;AACrB,aAAK,SAAS,OAAO,EAAC,UAAU,QAAQ,UAAS,CAAC;AAClD,eAAO;MACT;MAEA,SAAS,QAAgB,UAAiB;AACxC,cAAM,KAAK,KAAK,QAAQ,MAAM;AAC9B,YAAI,CAAC;AAAI;AACT,eAAO,GAAG,IAAI,QAAQ;MACxB;MAEA,UAAU,WAAiB,SAAuC,KAAK,SAAO;AAC5E,eAAO,KAAK,cAAc,QAAQ,CAAC,SAAwB;AACzD,cAAI,KAAK,cAAc;AAAW,kBAAM,IAAI,MAAM,kBAAkB,IAAI,gBAAgB;AACxF,kBAAO,GAAA,OAAA,KAAI,SAAS,GAAG,KAAK,SAAS;QACvC,CAAC;MACH;MAEA,UACE,SAAuC,KAAK,SAC5C,YACA,SAAiD;AAEjD,eAAO,KAAK,cACV,QACA,CAAC,SAAwB;AACvB,cAAI,KAAK,UAAU;AAAW,kBAAM,IAAI,MAAM,kBAAkB,IAAI,gBAAgB;AACpF,iBAAO,KAAK,MAAM;QACpB,GACA,YACA,OAAO;MAEX;MAEQ,cACN,QACA,WACA,aAA8B,CAAA,GAC9B,SAAiD;AAEjD,YAAI,OAAa,OAAA;AACjB,mBAAW,UAAU,QAAQ;AAC3B,gBAAM,KAAK,OAAO,MAAM;AACxB,cAAI,CAAC;AAAI;AACT,gBAAM,UAAW,WAAW,MAAM,IAAI,WAAW,MAAM,KAAK,oBAAI,IAAG;AACnE,aAAG,QAAQ,CAAC,SAAwB;AAClC,gBAAI,QAAQ,IAAI,IAAI;AAAG;AACvB,oBAAQ,IAAI,MAAM,eAAe,OAAO;AACxC,gBAAI,IAAI,UAAU,IAAI;AACtB,gBAAI,GAAG;AACL,oBAAM,MAAM,KAAK,KAAK,MAAM,QAAA,SAAS,MAAM,QAAA,SAAS;AACpD,sBAAO,GAAA,OAAA,KAAI,IAAI,GAAG,GAAG,IAAI,IAAI,MAAM,CAAC,IAAI,KAAK,KAAK,EAAE;YACtD,WAAY,IAAI,YAAO,QAAP,YAAO,SAAA,SAAP,QAAU,IAAI,GAAI;AAChC,sBAAO,GAAA,OAAA,KAAI,IAAI,GAAG,CAAC,GAAG,KAAK,KAAK,EAAE;YACpC,OAAO;AACL,oBAAM,IAAI,WAAW,IAAI;YAC3B;AACA,oBAAQ,IAAI,MAAM,eAAe,SAAS;UAC5C,CAAC;QACH;AACA,eAAO;MACT;;AAhGF,YAAA,aAAA;;;;;;;;;;ACpHA,QAAA,SAAA;AACA,QAAA,UAAA;AAEA,QAAA,SAAA;AAAQ,WAAA,eAAA,SAAA,KAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAAA,OAAA;IAAC,EAAA,CAAA;AAAE,WAAA,eAAA,SAAA,OAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAAA,OAAA;IAAG,EAAA,CAAA;AAAE,WAAA,eAAA,SAAA,aAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAAA,OAAA;IAAS,EAAA,CAAA;AAAE,WAAA,eAAA,SAAA,OAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAAA,OAAA;IAAG,EAAA,CAAA;AAAE,WAAA,eAAA,SAAA,eAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAAA,OAAA;IAAW,EAAA,CAAA;AAAE,WAAA,eAAA,SAAA,aAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAAA,OAAA;IAAS,EAAA,CAAA;AAAE,WAAA,eAAA,SAAA,cAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAAA,OAAA;IAAU,EAAA,CAAA;AAAE,WAAA,eAAA,SAAA,QAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAAA,OAAA;IAAI,EAAA,CAAA;AACxE,QAAA,UAAA;AAAQ,WAAA,eAAA,SAAA,SAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAAA,QAAA;IAAK,EAAA,CAAA;AAAc,WAAA,eAAA,SAAA,cAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAAA,QAAA;IAAU,EAAA,CAAA;AAAE,WAAA,eAAA,SAAA,kBAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAAA,QAAA;IAAc,EAAA,CAAA;AAAkB,WAAA,eAAA,SAAA,YAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAAA,QAAA;IAAQ,EAAA,CAAA;AAQlE,YAAA,YAAY;MACvB,IAAI,IAAI,OAAA,MAAM,GAAG;MACjB,KAAK,IAAI,OAAA,MAAM,IAAI;MACnB,IAAI,IAAI,OAAA,MAAM,GAAG;MACjB,KAAK,IAAI,OAAA,MAAM,IAAI;MACnB,IAAI,IAAI,OAAA,MAAM,KAAK;MACnB,KAAK,IAAI,OAAA,MAAM,KAAK;MACpB,KAAK,IAAI,OAAA,MAAM,GAAG;MAClB,IAAI,IAAI,OAAA,MAAM,IAAI;MAClB,KAAK,IAAI,OAAA,MAAM,IAAI;MACnB,KAAK,IAAI,OAAA,MAAM,GAAG;;AAGpB,QAAe,OAAf,MAAmB;MAGjB,gBAAa;AACX,eAAO;MACT;MAEA,cAAc,QAAmB,YAAqB;AACpD,eAAO;MACT;;AAOF,QAAM,MAAN,cAAkB,KAAI;MACpB,YACmB,SACA,MACT,KAAc;AAEtB,cAAK;AAJY,aAAA,UAAA;AACA,aAAA,OAAA;AACT,aAAA,MAAA;MAGV;MAEA,OAAO,EAAC,KAAK,GAAE,GAAY;AACzB,cAAM,UAAU,MAAM,QAAA,SAAS,MAAM,KAAK;AAC1C,cAAM,MAAM,KAAK,QAAQ,SAAY,KAAK,MAAM,KAAK,GAAG;AACxD,eAAO,GAAG,OAAO,IAAI,KAAK,IAAI,GAAG,GAAG,MAAM;MAC5C;MAEA,cAAc,OAAkB,WAAoB;AAClD,YAAI,CAAC,MAAM,KAAK,KAAK,GAAG;AAAG;AAC3B,YAAI,KAAK;AAAK,eAAK,MAAM,aAAa,KAAK,KAAK,OAAO,SAAS;AAChE,eAAO;MACT;MAEA,IAAI,QAAK;AACP,eAAO,KAAK,eAAe,OAAA,cAAc,KAAK,IAAI,QAAQ,CAAA;MAC5D;;AAGF,QAAM,SAAN,cAAqB,KAAI;MACvB,YACW,KACF,KACU,aAAqB;AAEtC,cAAK;AAJI,aAAA,MAAA;AACF,aAAA,MAAA;AACU,aAAA,cAAA;MAGnB;MAEA,OAAO,EAAC,GAAE,GAAY;AACpB,eAAO,GAAG,KAAK,GAAG,MAAM,KAAK,GAAG,MAAM;MACxC;MAEA,cAAc,OAAkB,WAAoB;AAClD,YAAI,KAAK,eAAe,OAAA,QAAQ,CAAC,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,KAAK;AAAa;AAC3E,aAAK,MAAM,aAAa,KAAK,KAAK,OAAO,SAAS;AAClD,eAAO;MACT;MAEA,IAAI,QAAK;AACP,cAAM,QAAQ,KAAK,eAAe,OAAA,OAAO,CAAA,IAAK,EAAC,GAAG,KAAK,IAAI,MAAK;AAChE,eAAO,aAAa,OAAO,KAAK,GAAG;MACrC;;AAGF,QAAM,WAAN,cAAuB,OAAM;MAC3B,YACE,KACiB,IACjB,KACA,aAAqB;AAErB,cAAM,KAAK,KAAK,WAAW;AAJV,aAAA,KAAA;MAKnB;MAEA,OAAO,EAAC,GAAE,GAAY;AACpB,eAAO,GAAG,KAAK,GAAG,IAAI,KAAK,EAAE,KAAK,KAAK,GAAG,MAAM;MAClD;;AAGF,QAAM,QAAN,cAAoB,KAAI;MAEtB,YAAqB,OAAW;AAC9B,cAAK;AADc,aAAA,QAAA;AADZ,aAAA,QAAmB,CAAA;MAG5B;MAEA,OAAO,EAAC,GAAE,GAAY;AACpB,eAAO,GAAG,KAAK,KAAK,MAAM;MAC5B;;AAGF,QAAM,QAAN,cAAoB,KAAI;MAEtB,YAAqB,OAAY;AAC/B,cAAK;AADc,aAAA,QAAA;AADZ,aAAA,QAAmB,CAAA;MAG5B;MAEA,OAAO,EAAC,GAAE,GAAY;AACpB,cAAM,QAAQ,KAAK,QAAQ,IAAI,KAAK,KAAK,KAAK;AAC9C,eAAO,QAAQ,KAAK,MAAM;MAC5B;;AAGF,QAAM,QAAN,cAAoB,KAAI;MACtB,YAAqB,OAAW;AAC9B,cAAK;AADc,aAAA,QAAA;MAErB;MAEA,OAAO,EAAC,GAAE,GAAY;AACpB,eAAO,SAAS,KAAK,KAAK,MAAM;MAClC;MAEA,IAAI,QAAK;AACP,eAAO,KAAK,MAAM;MACpB;;AAGF,QAAM,UAAN,cAAsB,KAAI;MACxB,YAAoB,MAAc;AAChC,cAAK;AADa,aAAA,OAAA;MAEpB;MAEA,OAAO,EAAC,GAAE,GAAY;AACpB,eAAO,GAAG,KAAK,IAAI,MAAM;MAC3B;MAEA,gBAAa;AACX,eAAO,GAAG,KAAK,IAAI,KAAK,OAAO;MACjC;MAEA,cAAc,OAAkB,WAAoB;AAClD,aAAK,OAAO,aAAa,KAAK,MAAM,OAAO,SAAS;AACpD,eAAO;MACT;MAEA,IAAI,QAAK;AACP,eAAO,KAAK,gBAAgB,OAAA,cAAc,KAAK,KAAK,QAAQ,CAAA;MAC9D;;AAGF,QAAe,aAAf,cAAkC,KAAI;MACpC,YAAqB,QAAqB,CAAA,GAAE;AAC1C,cAAK;AADc,aAAA,QAAA;MAErB;MAEA,OAAO,MAAe;AACpB,eAAO,KAAK,MAAM,OAAO,CAAC,MAAM,MAAM,OAAO,EAAE,OAAO,IAAI,GAAG,EAAE;MACjE;MAEA,gBAAa;AACX,cAAM,EAAC,MAAK,IAAI;AAChB,YAAI,IAAI,MAAM;AACd,eAAO,KAAK;AACV,gBAAM,IAAI,MAAM,CAAC,EAAE,cAAa;AAChC,cAAI,MAAM,QAAQ,CAAC;AAAG,kBAAM,OAAO,GAAG,GAAG,GAAG,CAAC;mBACpC;AAAG,kBAAM,CAAC,IAAI;;AAClB,kBAAM,OAAO,GAAG,CAAC;QACxB;AACA,eAAO,MAAM,SAAS,IAAI,OAAO;MACnC;MAEA,cAAc,OAAkB,WAAoB;AAClD,cAAM,EAAC,MAAK,IAAI;AAChB,YAAI,IAAI,MAAM;AACd,eAAO,KAAK;AAEV,gBAAM,IAAI,MAAM,CAAC;AACjB,cAAI,EAAE,cAAc,OAAO,SAAS;AAAG;AACvC,wBAAc,OAAO,EAAE,KAAK;AAC5B,gBAAM,OAAO,GAAG,CAAC;QACnB;AACA,eAAO,MAAM,SAAS,IAAI,OAAO;MACnC;MAEA,IAAI,QAAK;AACP,eAAO,KAAK,MAAM,OAAO,CAAC,OAAkB,MAAM,SAAS,OAAO,EAAE,KAAK,GAAG,CAAA,CAAE;MAChF;;AAOF,QAAe,YAAf,cAAiC,WAAU;MACzC,OAAO,MAAe;AACpB,eAAO,MAAM,KAAK,KAAK,MAAM,OAAO,IAAI,IAAI,MAAM,KAAK;MACzD;;AAGF,QAAM,OAAN,cAAmB,WAAU;;AAE7B,QAAM,OAAN,cAAmB,UAAS;;AACV,SAAA,OAAO;AAGzB,QAAM,KAAN,MAAM,YAAW,UAAS;MAGxB,YACU,WACR,OAAmB;AAEnB,cAAM,KAAK;AAHH,aAAA,YAAA;MAIV;MAEA,OAAO,MAAe;AACpB,YAAI,OAAO,MAAM,KAAK,SAAS,MAAM,MAAM,OAAO,IAAI;AACtD,YAAI,KAAK;AAAM,kBAAQ,UAAU,KAAK,KAAK,OAAO,IAAI;AACtD,eAAO;MACT;MAEA,gBAAa;AACX,cAAM,cAAa;AACnB,cAAM,OAAO,KAAK;AAClB,YAAI,SAAS;AAAM,iBAAO,KAAK;AAC/B,YAAI,IAAI,KAAK;AACb,YAAI,GAAG;AACL,gBAAM,KAAK,EAAE,cAAa;AAC1B,cAAI,KAAK,OAAO,MAAM,QAAQ,EAAE,IAAI,IAAI,KAAK,EAAE,IAAK;QACtD;AACA,YAAI,GAAG;AACL,cAAI,SAAS;AAAO,mBAAO,aAAa,MAAK,IAAI,EAAE;AACnD,cAAI,KAAK,MAAM;AAAQ,mBAAO;AAC9B,iBAAO,IAAI,IAAG,IAAI,IAAI,GAAG,aAAa,MAAK,CAAC,CAAC,IAAI,EAAE,KAAK;QAC1D;AACA,YAAI,SAAS,SAAS,CAAC,KAAK,MAAM;AAAQ,iBAAO;AACjD,eAAO;MACT;MAEA,cAAc,OAAkB,WAAoB;;AAClD,aAAK,QAAO,KAAA,KAAK,UAAI,QAAA,OAAA,SAAA,SAAA,GAAE,cAAc,OAAO,SAAS;AACrD,YAAI,EAAE,MAAM,cAAc,OAAO,SAAS,KAAK,KAAK;AAAO;AAC3D,aAAK,YAAY,aAAa,KAAK,WAAW,OAAO,SAAS;AAC9D,eAAO;MACT;MAEA,IAAI,QAAK;AACP,cAAM,QAAQ,MAAM;AACpB,qBAAa,OAAO,KAAK,SAAS;AAClC,YAAI,KAAK;AAAM,mBAAS,OAAO,KAAK,KAAK,KAAK;AAC9C,eAAO;MACT;;AA7CgB,OAAA,OAAO;AAoDzB,QAAe,MAAf,cAA2B,UAAS;;AAClB,QAAA,OAAO;AAGzB,QAAM,UAAN,cAAsB,IAAG;MACvB,YAAoB,WAAe;AACjC,cAAK;AADa,aAAA,YAAA;MAEpB;MAEA,OAAO,MAAe;AACpB,eAAO,OAAO,KAAK,SAAS,MAAM,MAAM,OAAO,IAAI;MACrD;MAEA,cAAc,OAAkB,WAAoB;AAClD,YAAI,CAAC,MAAM,cAAc,OAAO,SAAS;AAAG;AAC5C,aAAK,YAAY,aAAa,KAAK,WAAW,OAAO,SAAS;AAC9D,eAAO;MACT;MAEA,IAAI,QAAK;AACP,eAAO,SAAS,MAAM,OAAO,KAAK,UAAU,KAAK;MACnD;;AAGF,QAAM,WAAN,cAAuB,IAAG;MACxB,YACmB,SACA,MACA,MACA,IAAY;AAE7B,cAAK;AALY,aAAA,UAAA;AACA,aAAA,OAAA;AACA,aAAA,OAAA;AACA,aAAA,KAAA;MAGnB;MAEA,OAAO,MAAe;AACpB,cAAM,UAAU,KAAK,MAAM,QAAA,SAAS,MAAM,KAAK;AAC/C,cAAM,EAAC,MAAM,MAAM,GAAE,IAAI;AACzB,eAAO,OAAO,OAAO,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,EAAE,KAAK,IAAI,QAAQ,MAAM,OAAO,IAAI;MACxF;MAEA,IAAI,QAAK;AACP,cAAM,QAAQ,aAAa,MAAM,OAAO,KAAK,IAAI;AACjD,eAAO,aAAa,OAAO,KAAK,EAAE;MACpC;;AAGF,QAAM,UAAN,cAAsB,IAAG;MACvB,YACmB,MACA,SACA,MACT,UAAc;AAEtB,cAAK;AALY,aAAA,OAAA;AACA,aAAA,UAAA;AACA,aAAA,OAAA;AACT,aAAA,WAAA;MAGV;MAEA,OAAO,MAAe;AACpB,eAAO,OAAO,KAAK,OAAO,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK,QAAQ,MAAM,MAAM,OAAO,IAAI;MAC9F;MAEA,cAAc,OAAkB,WAAoB;AAClD,YAAI,CAAC,MAAM,cAAc,OAAO,SAAS;AAAG;AAC5C,aAAK,WAAW,aAAa,KAAK,UAAU,OAAO,SAAS;AAC5D,eAAO;MACT;MAEA,IAAI,QAAK;AACP,eAAO,SAAS,MAAM,OAAO,KAAK,SAAS,KAAK;MAClD;;AAGF,QAAM,OAAN,cAAmB,UAAS;MAE1B,YACS,MACA,MACA,OAAe;AAEtB,cAAK;AAJE,aAAA,OAAA;AACA,aAAA,OAAA;AACA,aAAA,QAAA;MAGT;MAEA,OAAO,MAAe;AACpB,cAAM,SAAS,KAAK,QAAQ,WAAW;AACvC,eAAO,GAAG,MAAM,YAAY,KAAK,IAAI,IAAI,KAAK,IAAI,MAAM,MAAM,OAAO,IAAI;MAC3E;;AAZgB,SAAA,OAAO;AAezB,QAAM,SAAN,cAAqB,WAAU;MAG7B,OAAO,MAAe;AACpB,eAAO,YAAY,MAAM,OAAO,IAAI;MACtC;;AAJgB,WAAA,OAAO;AAOzB,QAAM,MAAN,cAAkB,UAAS;MAIzB,OAAO,MAAe;AACpB,YAAI,OAAO,QAAQ,MAAM,OAAO,IAAI;AACpC,YAAI,KAAK;AAAO,kBAAQ,KAAK,MAAM,OAAO,IAAI;AAC9C,YAAI,KAAK;AAAS,kBAAQ,KAAK,QAAQ,OAAO,IAAI;AAClD,eAAO;MACT;MAEA,gBAAa;;AACX,cAAM,cAAa;AACnB,SAAA,KAAA,KAAK,WAAK,QAAA,OAAA,SAAA,SAAA,GAAE,cAAa;AACzB,SAAA,KAAA,KAAK,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,cAAa;AAC3B,eAAO;MACT;MAEA,cAAc,OAAkB,WAAoB;;AAClD,cAAM,cAAc,OAAO,SAAS;AACpC,SAAA,KAAA,KAAK,WAAK,QAAA,OAAA,SAAA,SAAA,GAAE,cAAc,OAAO,SAAS;AAC1C,SAAA,KAAA,KAAK,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,cAAc,OAAO,SAAS;AAC5C,eAAO;MACT;MAEA,IAAI,QAAK;AACP,cAAM,QAAQ,MAAM;AACpB,YAAI,KAAK;AAAO,mBAAS,OAAO,KAAK,MAAM,KAAK;AAChD,YAAI,KAAK;AAAS,mBAAS,OAAO,KAAK,QAAQ,KAAK;AACpD,eAAO;MACT;;AAOF,QAAM,QAAN,cAAoB,UAAS;MAE3B,YAAqB,OAAW;AAC9B,cAAK;AADc,aAAA,QAAA;MAErB;MAEA,OAAO,MAAe;AACpB,eAAO,SAAS,KAAK,KAAK,MAAM,MAAM,OAAO,IAAI;MACnD;;AAPgB,UAAA,OAAO;AAUzB,QAAM,UAAN,cAAsB,UAAS;MAE7B,OAAO,MAAe;AACpB,eAAO,YAAY,MAAM,OAAO,IAAI;MACtC;;AAHgB,YAAA,OAAO;AAiCzB,QAAa,UAAb,MAAoB;MASlB,YAAY,UAAsB,OAAuB,CAAA,GAAE;AANlD,aAAA,UAA0B,CAAA;AAElB,aAAA,eAAyB,CAAA;AACzB,aAAA,aAAwB,CAAA;AAIvC,aAAK,OAAO,EAAC,GAAG,MAAM,IAAI,KAAK,QAAQ,OAAO,GAAE;AAChD,aAAK,YAAY;AACjB,aAAK,SAAS,IAAI,QAAA,MAAM,EAAC,QAAQ,SAAQ,CAAC;AAC1C,aAAK,SAAS,CAAC,IAAI,KAAI,CAAE;MAC3B;MAEA,WAAQ;AACN,eAAO,KAAK,MAAM,OAAO,KAAK,IAAI;MACpC;;MAGA,KAAK,QAAc;AACjB,eAAO,KAAK,OAAO,KAAK,MAAM;MAChC;;MAGA,UAAU,QAAc;AACtB,eAAO,KAAK,UAAU,KAAK,MAAM;MACnC;;MAGA,WAAW,cAAuC,OAAgB;AAChE,cAAM,OAAO,KAAK,UAAU,MAAM,cAAc,KAAK;AACrD,cAAM,KAAK,KAAK,QAAQ,KAAK,MAAM,MAAM,KAAK,QAAQ,KAAK,MAAM,IAAI,oBAAI,IAAG;AAC5E,WAAG,IAAI,IAAI;AACX,eAAO;MACT;MAEA,cAAc,QAAgB,UAAiB;AAC7C,eAAO,KAAK,UAAU,SAAS,QAAQ,QAAQ;MACjD;;;MAIA,UAAU,WAAe;AACvB,eAAO,KAAK,UAAU,UAAU,WAAW,KAAK,OAAO;MACzD;MAEA,YAAS;AACP,eAAO,KAAK,UAAU,UAAU,KAAK,OAAO;MAC9C;MAEQ,KACN,SACA,cACA,KACA,UAAkB;AAElB,cAAM,OAAO,KAAK,OAAO,OAAO,YAAY;AAC5C,YAAI,QAAQ,UAAa;AAAU,eAAK,WAAW,KAAK,GAAG,IAAI;AAC/D,aAAK,UAAU,IAAI,IAAI,SAAS,MAAM,GAAG,CAAC;AAC1C,eAAO;MACT;;MAGA,MAAM,cAA6B,KAAe,WAAmB;AACnE,eAAO,KAAK,KAAK,QAAA,SAAS,OAAO,cAAc,KAAK,SAAS;MAC/D;;MAGA,IAAI,cAA6B,KAAgB,WAAmB;AAClE,eAAO,KAAK,KAAK,QAAA,SAAS,KAAK,cAAc,KAAK,SAAS;MAC7D;;MAGA,IAAI,cAA6B,KAAgB,WAAmB;AAClE,eAAO,KAAK,KAAK,QAAA,SAAS,KAAK,cAAc,KAAK,SAAS;MAC7D;;MAGA,OAAO,KAAW,KAAe,aAAqB;AACpD,eAAO,KAAK,UAAU,IAAI,OAAO,KAAK,KAAK,WAAW,CAAC;MACzD;;MAGA,IAAI,KAAW,KAAa;AAC1B,eAAO,KAAK,UAAU,IAAI,SAAS,KAAK,QAAA,UAAU,KAAK,GAAG,CAAC;MAC7D;;MAGA,KAAK,GAAmB;AACtB,YAAI,OAAO,KAAK;AAAY,YAAC;iBACpB,MAAM,OAAA;AAAK,eAAK,UAAU,IAAI,QAAQ,CAAC,CAAC;AACjD,eAAO;MACT;;MAGA,UAAU,WAA+C;AACvD,cAAM,OAAmB,CAAC,GAAG;AAC7B,mBAAW,CAAC,KAAK,KAAK,KAAK,WAAW;AACpC,cAAI,KAAK,SAAS;AAAG,iBAAK,KAAK,GAAG;AAClC,eAAK,KAAK,GAAG;AACb,cAAI,QAAQ,SAAS,KAAK,KAAK,KAAK;AAClC,iBAAK,KAAK,GAAG;AACb,aAAA,GAAA,OAAA,YAAW,MAAM,KAAK;UACxB;QACF;AACA,aAAK,KAAK,GAAG;AACb,eAAO,IAAI,OAAA,MAAM,IAAI;MACvB;;MAGA,GAAG,WAA2B,UAAkB,UAAgB;AAC9D,aAAK,WAAW,IAAI,GAAG,SAAS,CAAC;AAEjC,YAAI,YAAY,UAAU;AACxB,eAAK,KAAK,QAAQ,EAAE,KAAI,EAAG,KAAK,QAAQ,EAAE,MAAK;QACjD,WAAW,UAAU;AACnB,eAAK,KAAK,QAAQ,EAAE,MAAK;QAC3B,WAAW,UAAU;AACnB,gBAAM,IAAI,MAAM,0CAA0C;QAC5D;AACA,eAAO;MACT;;MAGA,OAAO,WAAyB;AAC9B,eAAO,KAAK,UAAU,IAAI,GAAG,SAAS,CAAC;MACzC;;MAGA,OAAI;AACF,eAAO,KAAK,UAAU,IAAI,KAAI,CAAE;MAClC;;MAGA,QAAK;AACH,eAAO,KAAK,cAAc,IAAI,IAAI;MACpC;MAEQ,KAAK,MAAW,SAAe;AACrC,aAAK,WAAW,IAAI;AACpB,YAAI;AAAS,eAAK,KAAK,OAAO,EAAE,OAAM;AACtC,eAAO;MACT;;MAGA,IAAI,WAAiB,SAAe;AAClC,eAAO,KAAK,KAAK,IAAI,QAAQ,SAAS,GAAG,OAAO;MAClD;;MAGA,SACE,cACA,MACA,IACA,SACA,UAAgB,KAAK,KAAK,MAAM,QAAA,SAAS,MAAM,QAAA,SAAS,KAAG;AAE3D,cAAM,OAAO,KAAK,OAAO,OAAO,YAAY;AAC5C,eAAO,KAAK,KAAK,IAAI,SAAS,SAAS,MAAM,MAAM,EAAE,GAAG,MAAM,QAAQ,IAAI,CAAC;MAC7E;;MAGA,MACE,cACA,UACA,SACA,UAAgB,QAAA,SAAS,OAAK;AAE9B,cAAM,OAAO,KAAK,OAAO,OAAO,YAAY;AAC5C,YAAI,KAAK,KAAK,KAAK;AACjB,gBAAM,MAAM,oBAAoB,OAAA,OAAO,WAAW,KAAK,IAAI,QAAQ,QAAQ;AAC3E,iBAAO,KAAK,SAAS,MAAM,IAAG,GAAA,OAAA,KAAI,GAAG,WAAW,CAAC,MAAK;AACpD,iBAAK,IAAI,OAAM,GAAA,OAAA,KAAI,GAAG,IAAI,CAAC,GAAG;AAC9B,oBAAQ,IAAI;UACd,CAAC;QACH;AACA,eAAO,KAAK,KAAK,IAAI,QAAQ,MAAM,SAAS,MAAM,QAAQ,GAAG,MAAM,QAAQ,IAAI,CAAC;MAClF;;;MAIA,MACE,cACA,KACA,SACA,UAAgB,KAAK,KAAK,MAAM,QAAA,SAAS,MAAM,QAAA,SAAS,OAAK;AAE7D,YAAI,KAAK,KAAK,eAAe;AAC3B,iBAAO,KAAK,MAAM,eAAc,GAAA,OAAA,iBAAgB,GAAG,KAAK,OAAO;QACjE;AACA,cAAM,OAAO,KAAK,OAAO,OAAO,YAAY;AAC5C,eAAO,KAAK,KAAK,IAAI,QAAQ,MAAM,SAAS,MAAM,GAAG,GAAG,MAAM,QAAQ,IAAI,CAAC;MAC7E;;MAGA,SAAM;AACJ,eAAO,KAAK,cAAc,GAAG;MAC/B;;MAGA,MAAM,OAAW;AACf,eAAO,KAAK,UAAU,IAAI,MAAM,KAAK,CAAC;MACxC;;MAGA,MAAM,OAAY;AAChB,eAAO,KAAK,UAAU,IAAI,MAAM,KAAK,CAAC;MACxC;;MAGA,OAAO,OAAuB;AAC5B,cAAM,OAAO,IAAI,OAAM;AACvB,aAAK,WAAW,IAAI;AACpB,aAAK,KAAK,KAAK;AACf,YAAI,KAAK,MAAM,WAAW;AAAG,gBAAM,IAAI,MAAM,wCAAwC;AACrF,eAAO,KAAK,cAAc,MAAM;MAClC;;MAGA,IAAI,SAAgB,WAA+B,aAAmB;AACpE,YAAI,CAAC,aAAa,CAAC;AAAa,gBAAM,IAAI,MAAM,8CAA8C;AAC9F,cAAM,OAAO,IAAI,IAAG;AACpB,aAAK,WAAW,IAAI;AACpB,aAAK,KAAK,OAAO;AACjB,YAAI,WAAW;AACb,gBAAM,QAAQ,KAAK,KAAK,GAAG;AAC3B,eAAK,YAAY,KAAK,QAAQ,IAAI,MAAM,KAAK;AAC7C,oBAAU,KAAK;QACjB;AACA,YAAI,aAAa;AACf,eAAK,YAAY,KAAK,UAAU,IAAI,QAAO;AAC3C,eAAK,KAAK,WAAW;QACvB;AACA,eAAO,KAAK,cAAc,OAAO,OAAO;MAC1C;;MAGA,MAAM,OAAW;AACf,eAAO,KAAK,UAAU,IAAI,MAAM,KAAK,CAAC;MACxC;;MAGA,MAAM,MAAc,WAAkB;AACpC,aAAK,aAAa,KAAK,KAAK,OAAO,MAAM;AACzC,YAAI;AAAM,eAAK,KAAK,IAAI,EAAE,SAAS,SAAS;AAC5C,eAAO;MACT;;MAGA,SAAS,WAAkB;AACzB,cAAM,MAAM,KAAK,aAAa,IAAG;AACjC,YAAI,QAAQ;AAAW,gBAAM,IAAI,MAAM,sCAAsC;AAC7E,cAAM,UAAU,KAAK,OAAO,SAAS;AACrC,YAAI,UAAU,KAAM,cAAc,UAAa,YAAY,WAAY;AACrE,gBAAM,IAAI,MAAM,mCAAmC,OAAO,OAAO,SAAS,WAAW;QACvF;AACA,aAAK,OAAO,SAAS;AACrB,eAAO;MACT;;MAGA,KAAK,MAAY,OAAa,OAAA,KAAK,OAAiB,UAAgB;AAClE,aAAK,WAAW,IAAI,KAAK,MAAM,MAAM,KAAK,CAAC;AAC3C,YAAI;AAAU,eAAK,KAAK,QAAQ,EAAE,QAAO;AACzC,eAAO;MACT;;MAGA,UAAO;AACL,eAAO,KAAK,cAAc,IAAI;MAChC;MAEA,SAAS,IAAI,GAAC;AACZ,eAAO,MAAM,GAAG;AACd,eAAK,MAAM,cAAa;AACxB,eAAK,MAAM,cAAc,KAAK,MAAM,OAAO,KAAK,UAAU;QAC5D;MACF;MAEQ,UAAU,MAAc;AAC9B,aAAK,UAAU,MAAM,KAAK,IAAI;AAC9B,eAAO;MACT;MAEQ,WAAW,MAAoB;AACrC,aAAK,UAAU,MAAM,KAAK,IAAI;AAC9B,aAAK,OAAO,KAAK,IAAI;MACvB;MAEQ,cAAc,IAAsB,IAAqB;AAC/D,cAAM,IAAI,KAAK;AACf,YAAI,aAAa,MAAO,MAAM,aAAa,IAAK;AAC9C,eAAK,OAAO,IAAG;AACf,iBAAO;QACT;AACA,cAAM,IAAI,MAAM,0BAA0B,KAAK,GAAG,GAAG,IAAI,IAAI,GAAG,IAAI,KAAK,GAAG,IAAI,GAAG;MACrF;MAEQ,UAAU,MAAe;AAC/B,cAAM,IAAI,KAAK;AACf,YAAI,EAAE,aAAa,KAAK;AACtB,gBAAM,IAAI,MAAM,8BAA8B;QAChD;AACA,aAAK,YAAY,EAAE,OAAO;AAC1B,eAAO;MACT;MAEA,IAAY,QAAK;AACf,eAAO,KAAK,OAAO,CAAC;MACtB;MAEA,IAAY,YAAS;AACnB,cAAM,KAAK,KAAK;AAChB,eAAO,GAAG,GAAG,SAAS,CAAC;MACzB;MAEA,IAAY,UAAU,MAAgB;AACpC,cAAM,KAAK,KAAK;AAChB,WAAG,GAAG,SAAS,CAAC,IAAI;MACtB;;AAjUF,YAAA,UAAA;AAwUA,aAAS,SAAS,OAAkB,MAAe;AACjD,iBAAW,KAAK;AAAM,cAAM,CAAC,KAAK,MAAM,CAAC,KAAK,MAAM,KAAK,CAAC,KAAK;AAC/D,aAAO;IACT;AAEA,aAAS,aAAa,OAAkB,MAAc;AACpD,aAAO,gBAAgB,OAAA,cAAc,SAAS,OAAO,KAAK,KAAK,IAAI;IACrE;AAGA,aAAS,aAAa,MAAgB,OAAkB,WAAoB;AAC1E,UAAI,gBAAgB,OAAA;AAAM,eAAO,YAAY,IAAI;AACjD,UAAI,CAAC,YAAY,IAAI;AAAG,eAAO;AAC/B,aAAO,IAAI,OAAA,MACT,KAAK,OAAO,OAAO,CAAC,OAAmB,MAAwB;AAC7D,YAAI,aAAa,OAAA;AAAM,cAAI,YAAY,CAAC;AACxC,YAAI,aAAa,OAAA;AAAO,gBAAM,KAAK,GAAG,EAAE,MAAM;;AACzC,gBAAM,KAAK,CAAC;AACjB,eAAO;MACT,GAAG,CAAA,CAAE,CAAC;AAGR,eAAS,YAAY,GAAO;AAC1B,cAAM,IAAI,UAAU,EAAE,GAAG;AACzB,YAAI,MAAM,UAAa,MAAM,EAAE,GAAG,MAAM;AAAG,iBAAO;AAClD,eAAO,MAAM,EAAE,GAAG;AAClB,eAAO;MACT;AAEA,eAAS,YAAY,GAAW;AAC9B,eACE,aAAa,OAAA,SACb,EAAE,OAAO,KACP,CAAC,MAAM,aAAa,OAAA,QAAQ,MAAM,EAAE,GAAG,MAAM,KAAK,UAAU,EAAE,GAAG,MAAM,MAAS;MAGtF;IACF;AAEA,aAAS,cAAc,OAAkB,MAAe;AACtD,iBAAW,KAAK;AAAM,cAAM,CAAC,KAAK,MAAM,CAAC,KAAK,MAAM,KAAK,CAAC,KAAK;IACjE;AAGA,aAAgB,IAAI,GAAkB;AACpC,aAAO,OAAO,KAAK,aAAa,OAAO,KAAK,YAAY,MAAM,OAAO,CAAC,KAAI,GAAA,OAAA,MAAK,IAAI,CAAC,CAAC;IACvF;AAFA,YAAA,MAAA;AAIA,QAAM,UAAU,QAAQ,QAAA,UAAU,GAAG;AAGrC,aAAgB,OAAO,MAAY;AACjC,aAAO,KAAK,OAAO,OAAO;IAC5B;AAFA,YAAA,MAAA;AAIA,QAAM,SAAS,QAAQ,QAAA,UAAU,EAAE;AAGnC,aAAgB,MAAM,MAAY;AAChC,aAAO,KAAK,OAAO,MAAM;IAC3B;AAFA,YAAA,KAAA;AAMA,aAAS,QAAQ,IAAQ;AACvB,aAAO,CAAC,GAAG,MAAO,MAAM,OAAA,MAAM,IAAI,MAAM,OAAA,MAAM,KAAI,GAAA,OAAA,KAAI,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,IAAI,CAAC,CAAC;IAC9E;AAEA,aAAS,IAAI,GAAO;AAClB,aAAO,aAAa,OAAA,OAAO,KAAI,GAAA,OAAA,MAAK,CAAC;IACvC;;;;;;;;;;ACj1BA,QAAA,YAAA;AACA,QAAA,SAAA;AAIA,aAAgB,OAAkC,KAAQ;AACxD,YAAM,OAA0B,CAAA;AAChC,iBAAW,QAAQ;AAAK,aAAK,IAAI,IAAI;AACrC,aAAO;IACT;AAJA,YAAA,SAAA;AAMA,aAAgB,kBAAkB,IAAe,QAAiB;AAChE,UAAI,OAAO,UAAU;AAAW,eAAO;AACvC,UAAI,OAAO,KAAK,MAAM,EAAE,WAAW;AAAG,eAAO;AAC7C,wBAAkB,IAAI,MAAM;AAC5B,aAAO,CAAC,eAAe,QAAQ,GAAG,KAAK,MAAM,GAAG;IAClD;AALA,YAAA,oBAAA;AAOA,aAAgB,kBAAkB,IAAe,SAAoB,GAAG,QAAM;AAC5E,YAAM,EAAC,MAAM,KAAI,IAAI;AACrB,UAAI,CAAC,KAAK;AAAc;AACxB,UAAI,OAAO,WAAW;AAAW;AACjC,YAAM,QAAQ,KAAK,MAAM;AACzB,iBAAW,OAAO,QAAQ;AACxB,YAAI,CAAC,MAAM,GAAG;AAAG,0BAAgB,IAAI,qBAAqB,GAAG,GAAG;MAClE;IACF;AARA,YAAA,oBAAA;AAUA,aAAgB,eACd,QACA,OAAyC;AAEzC,UAAI,OAAO,UAAU;AAAW,eAAO,CAAC;AACxC,iBAAW,OAAO;AAAQ,YAAI,MAAM,GAAG;AAAG,iBAAO;AACjD,aAAO;IACT;AAPA,YAAA,iBAAA;AASA,aAAgB,qBAAqB,QAAmB,OAAsB;AAC5E,UAAI,OAAO,UAAU;AAAW,eAAO,CAAC;AACxC,iBAAW,OAAO;AAAQ,YAAI,QAAQ,UAAU,MAAM,IAAI,GAAG;AAAG,iBAAO;AACvE,aAAO;IACT;AAJA,YAAA,uBAAA;AAMA,aAAgB,eACd,EAAC,cAAc,WAAU,GACzB,QACA,SACA,OAAsB;AAEtB,UAAI,CAAC,OAAO;AACV,YAAI,OAAO,UAAU,YAAY,OAAO,UAAU;AAAW,iBAAO;AACpE,YAAI,OAAO,UAAU;AAAU,kBAAO,GAAA,UAAA,KAAI,MAAM;MAClD;AACA,cAAO,GAAA,UAAA,KAAI,YAAY,GAAG,UAAU,IAAG,GAAA,UAAA,aAAY,OAAO,CAAC;IAC7D;AAXA,YAAA,iBAAA;AAaA,aAAgB,iBAAiB,KAAW;AAC1C,aAAO,oBAAoB,mBAAmB,GAAG,CAAC;IACpD;AAFA,YAAA,mBAAA;AAIA,aAAgB,eAAe,KAAoB;AACjD,aAAO,mBAAmB,kBAAkB,GAAG,CAAC;IAClD;AAFA,YAAA,iBAAA;AAIA,aAAgB,kBAAkB,KAAoB;AACpD,UAAI,OAAO,OAAO;AAAU,eAAO,GAAG,GAAG;AACzC,aAAO,IAAI,QAAQ,MAAM,IAAI,EAAE,QAAQ,OAAO,IAAI;IACpD;AAHA,YAAA,oBAAA;AAKA,aAAgB,oBAAoB,KAAW;AAC7C,aAAO,IAAI,QAAQ,OAAO,GAAG,EAAE,QAAQ,OAAO,GAAG;IACnD;AAFA,YAAA,sBAAA;AAIA,aAAgB,SAAY,IAAa,GAAiB;AACxD,UAAI,MAAM,QAAQ,EAAE,GAAG;AACrB,mBAAW,KAAK;AAAI,YAAE,CAAC;MACzB,OAAO;AACL,UAAE,EAAE;MACN;IACF;AANA,YAAA,WAAA;AAwBA,aAAS,mBAA4C,EACnD,YACA,aACA,aACA,aAAY,GACS;AACrB,aAAO,CAAC,KAAK,MAAM,IAAI,WAAU;AAC/B,cAAM,MACJ,OAAO,SACH,OACA,cAAc,UAAA,QACb,gBAAgB,UAAA,OAAO,WAAW,KAAK,MAAM,EAAE,IAAI,YAAY,KAAK,MAAM,EAAE,GAAG,MAChF,gBAAgB,UAAA,QACf,YAAY,KAAK,IAAI,IAAI,GAAG,QAC7B,YAAY,MAAM,EAAE;AAC1B,eAAO,WAAW,UAAA,QAAQ,EAAE,eAAe,UAAA,QAAQ,aAAa,KAAK,GAAG,IAAI;MAC9E;IACF;AAOa,YAAA,iBAAiC;MAC5C,OAAO,mBAAmB;QACxB,YAAY,CAAC,KAAK,MAAM,OACtB,IAAI,IAAG,GAAA,UAAA,KAAI,EAAE,gBAAgB,IAAI,kBAAkB,MAAK;AACtD,cAAI,IACF,GAAA,UAAA,KAAI,IAAI,aACR,MAAM,IAAI,OAAO,IAAI,IAAI,GACzB,MAAM,IAAI,OAAO,KAAI,GAAA,UAAA,KAAI,EAAE,QAAQ,EAAE,MAAK,GAAA,UAAA,mBAAkB,EAAE,KAAK,IAAI,GAAG,CAAC;QAE/E,CAAC;QACH,aAAa,CAAC,KAAK,MAAM,OACvB,IAAI,IAAG,GAAA,UAAA,KAAI,EAAE,aAAa,MAAK;AAC7B,cAAI,SAAS,MAAM;AACjB,gBAAI,OAAO,IAAI,IAAI;UACrB,OAAO;AACL,gBAAI,OAAO,KAAI,GAAA,UAAA,KAAI,EAAE,QAAQ;AAC7B,yBAAa,KAAK,IAAI,IAAI;UAC5B;QACF,CAAC;QACH,aAAa,CAAC,MAAM,OAAQ,SAAS,OAAO,OAAO,EAAC,GAAG,MAAM,GAAG,GAAE;QAClE,cAAc;OACf;MACD,OAAO,mBAAmB;QACxB,YAAY,CAAC,KAAK,MAAM,OACtB,IAAI,IAAG,GAAA,UAAA,KAAI,EAAE,gBAAgB,IAAI,kBAAkB,MACjD,IAAI,OAAO,KAAI,GAAA,UAAA,KAAI,IAAI,sBAAsB,EAAE,MAAM,IAAI,MAAM,EAAE,MAAM,IAAI,EAAE,CAAC;QAElF,aAAa,CAAC,KAAK,MAAM,OACvB,IAAI,IAAG,GAAA,UAAA,KAAI,EAAE,aAAa,MACxB,IAAI,OAAO,IAAI,SAAS,OAAO,QAAO,GAAA,UAAA,KAAI,EAAE,MAAM,IAAI,MAAM,EAAE,MAAM,IAAI,EAAE,CAAC;QAE/E,aAAa,CAAC,MAAM,OAAQ,SAAS,OAAO,OAAO,KAAK,IAAI,MAAM,EAAE;QACpE,cAAc,CAAC,KAAK,UAAU,IAAI,IAAI,SAAS,KAAK;OACrD;;AAGH,aAAgB,qBAAqB,KAAc,IAAwB;AACzE,UAAI,OAAO;AAAM,eAAO,IAAI,IAAI,SAAS,IAAI;AAC7C,YAAM,QAAQ,IAAI,IAAI,UAAS,GAAA,UAAA,MAAK;AACpC,UAAI,OAAO;AAAW,qBAAa,KAAK,OAAO,EAAE;AACjD,aAAO;IACT;AALA,YAAA,uBAAA;AAOA,aAAgB,aAAa,KAAc,OAAa,IAA0B;AAChF,aAAO,KAAK,EAAE,EAAE,QAAQ,CAAC,MAAM,IAAI,QAAO,GAAA,UAAA,KAAI,KAAK,IAAG,GAAA,UAAA,aAAY,CAAC,CAAC,IAAI,IAAI,CAAC;IAC/E;AAFA,YAAA,eAAA;AAIA,QAAM,WAAoC,CAAA;AAE1C,aAAgB,QAAQ,KAAc,GAAiB;AACrD,aAAO,IAAI,WAAW,QAAQ;QAC5B,KAAK;QACL,MAAM,SAAS,EAAE,IAAI,MAAM,SAAS,EAAE,IAAI,IAAI,IAAI,OAAA,MAAM,EAAE,IAAI;OAC/D;IACH;AALA,YAAA,UAAA;AAOA,QAAY;AAAZ,KAAA,SAAYC,OAAI;AACd,MAAAA,MAAAA,MAAA,KAAA,IAAA,CAAA,IAAA;AACA,MAAAA,MAAAA,MAAA,KAAA,IAAA,CAAA,IAAA;IACF,GAHY,SAAI,QAAA,OAAJ,OAAI,CAAA,EAAA;AAKhB,aAAgB,aACd,UACA,cACA,kBAA0B;AAG1B,UAAI,oBAAoB,UAAA,MAAM;AAC5B,cAAM,WAAW,iBAAiB,KAAK;AACvC,eAAO,mBACH,YACE,GAAA,UAAA,WAAU,QAAQ,YAClB,GAAA,UAAA,YAAW,QAAQ,YACrB,YACA,GAAA,UAAA,WAAU,QAAQ,MAClB,GAAA,UAAA,WAAU,QAAQ;MACxB;AACA,aAAO,oBAAmB,GAAA,UAAA,aAAY,QAAQ,EAAE,SAAQ,IAAK,MAAM,kBAAkB,QAAQ;IAC/F;AAjBA,YAAA,eAAA;AAmBA,aAAgB,gBACd,IACA,KACA,OAAwB,GAAG,KAAK,cAAY;AAE5C,UAAI,CAAC;AAAM;AACX,YAAM,gBAAgB,GAAG;AACzB,UAAI,SAAS;AAAM,cAAM,IAAI,MAAM,GAAG;AACtC,SAAG,KAAK,OAAO,KAAK,GAAG;IACzB;AATA,YAAA,kBAAA;;;;;;;;;AC3MA,QAAA,YAAA;AAEA,QAAM,QAAQ;;MAEZ,MAAM,IAAI,UAAA,KAAK,MAAM;;;MAErB,QAAQ,IAAI,UAAA,KAAK,QAAQ;;MACzB,cAAc,IAAI,UAAA,KAAK,cAAc;MACrC,YAAY,IAAI,UAAA,KAAK,YAAY;MACjC,oBAAoB,IAAI,UAAA,KAAK,oBAAoB;MACjD,UAAU,IAAI,UAAA,KAAK,UAAU;;MAC7B,gBAAgB,IAAI,UAAA,KAAK,gBAAgB;;;MAEzC,SAAS,IAAI,UAAA,KAAK,SAAS;;MAC3B,QAAQ,IAAI,UAAA,KAAK,QAAQ;;MACzB,MAAM,IAAI,UAAA,KAAK,MAAM;;MAErB,MAAM,IAAI,UAAA,KAAK,MAAM;MACrB,OAAO,IAAI,UAAA,KAAK,OAAO;;MAEvB,MAAM,IAAI,UAAA,KAAK,MAAM;MACrB,SAAS,IAAI,UAAA,KAAK,SAAS;MAC3B,SAAS,IAAI,UAAA,KAAK,SAAS;MAC3B,UAAU,IAAI,UAAA,KAAK,UAAU;;AAG/B,YAAA,UAAe;;;;;;;;;;ACxBf,QAAA,YAAA;AAEA,QAAA,SAAA;AACA,QAAA,UAAA;AAEa,YAAA,eAAuC;MAClD,SAAS,CAAC,EAAC,QAAO,OAAM,GAAA,UAAA,kBAAiB,OAAO;;AAGrC,YAAA,oBAA4C;MACvD,SAAS,CAAC,EAAC,SAAS,WAAU,MAC5B,cACI,GAAA,UAAA,QAAO,OAAO,qBAAqB,UAAU,cAC7C,GAAA,UAAA,QAAO,OAAO;;AAStB,aAAgB,YACd,KACA,QAAgC,QAAA,cAChC,YACA,mBAA2B;AAE3B,YAAM,EAAC,GAAE,IAAI;AACb,YAAM,EAAC,KAAK,eAAe,UAAS,IAAI;AACxC,YAAM,SAAS,gBAAgB,KAAK,OAAO,UAAU;AACrD,UAAI,sBAAiB,QAAjB,sBAAiB,SAAjB,oBAAsB,iBAAiB,WAAY;AACrD,iBAAS,KAAK,MAAM;MACtB,OAAO;AACL,qBAAa,KAAI,GAAA,UAAA,MAAK,MAAM,GAAG;MACjC;IACF;AAdA,YAAA,cAAA;AAgBA,aAAgB,iBACd,KACA,QAAgC,QAAA,cAChC,YAAuB;AAEvB,YAAM,EAAC,GAAE,IAAI;AACb,YAAM,EAAC,KAAK,eAAe,UAAS,IAAI;AACxC,YAAM,SAAS,gBAAgB,KAAK,OAAO,UAAU;AACrD,eAAS,KAAK,MAAM;AACpB,UAAI,EAAE,iBAAiB,YAAY;AACjC,qBAAa,IAAI,QAAA,QAAE,OAAO;MAC5B;IACF;AAZA,YAAA,mBAAA;AAcA,aAAgB,iBAAiB,KAAc,WAAe;AAC5D,UAAI,OAAO,QAAA,QAAE,QAAQ,SAAS;AAC9B,UAAI,IAAG,GAAA,UAAA,KAAI,QAAA,QAAE,OAAO,aAAa,MAC/B,IAAI,GACF,WACA,MAAM,IAAI,QAAO,GAAA,UAAA,KAAI,QAAA,QAAE,OAAO,WAAW,SAAS,GAClD,MAAM,IAAI,OAAO,QAAA,QAAE,SAAS,IAAI,CAAC,CAClC;IAEL;AATA,YAAA,mBAAA;AAWA,aAAgB,aAAa,EAC3B,KACA,SACA,aACA,MACA,WACA,GAAE,GACc;AAEhB,UAAI,cAAc;AAAW,cAAM,IAAI,MAAM,0BAA0B;AACvE,YAAM,MAAM,IAAI,KAAK,KAAK;AAC1B,UAAI,SAAS,KAAK,WAAW,QAAA,QAAE,QAAQ,CAAC,MAAK;AAC3C,YAAI,MAAM,MAAK,GAAA,UAAA,KAAI,QAAA,QAAE,OAAO,IAAI,CAAC,GAAG;AACpC,YAAI,IAAG,GAAA,UAAA,KAAI,GAAG,+BAA+B,MAC3C,IAAI,QAAO,GAAA,UAAA,KAAI,GAAG,kBAAiB,GAAA,UAAA,WAAU,QAAA,QAAE,cAAc,GAAG,SAAS,CAAC,CAAC;AAE7E,YAAI,QAAO,GAAA,UAAA,KAAI,GAAG,gBAAe,GAAA,UAAA,OAAM,GAAG,aAAa,IAAI,OAAO,EAAE;AACpE,YAAI,GAAG,KAAK,SAAS;AACnB,cAAI,QAAO,GAAA,UAAA,KAAI,GAAG,WAAW,WAAW;AACxC,cAAI,QAAO,GAAA,UAAA,KAAI,GAAG,SAAS,IAAI;QACjC;MACF,CAAC;IACH;AAtBA,YAAA,eAAA;AAwBA,aAAS,SAAS,KAAc,QAAY;AAC1C,YAAM,MAAM,IAAI,MAAM,OAAO,MAAM;AACnC,UAAI,IACF,GAAA,UAAA,KAAI,QAAA,QAAE,OAAO,aACb,MAAM,IAAI,OAAO,QAAA,QAAE,UAAS,GAAA,UAAA,MAAK,GAAG,GAAG,IACvC,GAAA,UAAA,KAAI,QAAA,QAAE,OAAO,SAAS,GAAG,GAAG;AAE9B,UAAI,MAAK,GAAA,UAAA,KAAI,QAAA,QAAE,MAAM,IAAI;IAC3B;AAEA,aAAS,aAAa,IAAe,MAAU;AAC7C,YAAM,EAAC,KAAK,cAAc,UAAS,IAAI;AACvC,UAAI,UAAU,QAAQ;AACpB,YAAI,OAAM,GAAA,UAAA,SAAQ,GAAG,eAAuB,IAAI,IAAI,GAAG;MACzD,OAAO;AACL,YAAI,QAAO,GAAA,UAAA,KAAI,YAAY,WAAW,IAAI;AAC1C,YAAI,OAAO,KAAK;MAClB;IACF;AAEA,QAAM,IAAI;MACR,SAAS,IAAI,UAAA,KAAK,SAAS;MAC3B,YAAY,IAAI,UAAA,KAAK,YAAY;;MACjC,QAAQ,IAAI,UAAA,KAAK,QAAQ;MACzB,cAAc,IAAI,UAAA,KAAK,cAAc;MACrC,SAAS,IAAI,UAAA,KAAK,SAAS;MAC3B,QAAQ,IAAI,UAAA,KAAK,QAAQ;MACzB,cAAc,IAAI,UAAA,KAAK,cAAc;;AAGvC,aAAS,gBACP,KACA,OACA,YAAuB;AAEvB,YAAM,EAAC,aAAY,IAAI,IAAI;AAC3B,UAAI,iBAAiB;AAAO,gBAAO,GAAA,UAAA;AACnC,aAAO,YAAY,KAAK,OAAO,UAAU;IAC3C;AAEA,aAAS,YACP,KACA,OACA,aAAyB,CAAA,GAAE;AAE3B,YAAM,EAAC,KAAK,GAAE,IAAI;AAClB,YAAM,YAAyC;QAC7C,kBAAkB,IAAI,UAAU;QAChC,gBAAgB,KAAK,UAAU;;AAEjC,sBAAgB,KAAK,OAAO,SAAS;AACrC,aAAO,IAAI,OAAO,GAAG,SAAS;IAChC;AAEA,aAAS,kBAAkB,EAAC,UAAS,GAAc,EAAC,aAAY,GAAa;AAC3E,YAAM,WAAW,gBACb,GAAA,UAAA,OAAM,SAAS,IAAG,GAAA,OAAA,cAAa,cAAc,OAAA,KAAK,GAAG,CAAC,KACtD;AACJ,aAAO,CAAC,QAAA,QAAE,eAAc,GAAA,UAAA,WAAU,QAAA,QAAE,cAAc,QAAQ,CAAC;IAC7D;AAEA,aAAS,gBACP,EAAC,SAAS,IAAI,EAAC,cAAa,EAAC,GAC7B,EAAC,YAAY,aAAY,GAAa;AAEtC,UAAI,UAAU,eAAe,iBAAgB,GAAA,UAAA,OAAM,aAAa,IAAI,OAAO;AAC3E,UAAI,YAAY;AACd,mBAAU,GAAA,UAAA,OAAM,OAAO,IAAG,GAAA,OAAA,cAAa,YAAY,OAAA,KAAK,GAAG,CAAC;MAC9D;AACA,aAAO,CAAC,EAAE,YAAY,OAAO;IAC/B;AAEA,aAAS,gBACP,KACA,EAAC,QAAQ,QAAO,GAChB,WAAsC;AAEtC,YAAM,EAAC,SAAS,MAAM,aAAa,GAAE,IAAI;AACzC,YAAM,EAAC,MAAM,cAAc,cAAc,WAAU,IAAI;AACvD,gBAAU,KACR,CAAC,EAAE,SAAS,OAAO,GACnB,CAAC,EAAE,QAAQ,OAAO,UAAU,aAAa,OAAO,GAAG,IAAI,WAAU,GAAA,UAAA,MAAK,CAAC;AAEzE,UAAI,KAAK,UAAU;AACjB,kBAAU,KAAK,CAAC,EAAE,SAAS,OAAO,WAAW,aAAa,QAAQ,GAAG,IAAI,OAAO,CAAC;MACnF;AACA,UAAI,KAAK,SAAS;AAChB,kBAAU,KACR,CAAC,EAAE,QAAQ,WAAW,GACtB,CAAC,EAAE,eAAc,GAAA,UAAA,KAAI,YAAY,GAAG,UAAU,EAAE,GAChD,CAAC,QAAA,QAAE,MAAM,IAAI,CAAC;MAElB;AACA,UAAI;AAAc,kBAAU,KAAK,CAAC,EAAE,cAAc,YAAY,CAAC;IACjE;;;;;;;;;;ACrLA,QAAA,WAAA;AACA,QAAA,YAAA;AACA,QAAA,UAAA;AAEA,QAAM,YAAoC;MACxC,SAAS;;AAGX,aAAgB,qBAAqB,IAAa;AAChD,YAAM,EAAC,KAAK,QAAQ,aAAY,IAAI;AACpC,UAAI,WAAW,OAAO;AACpB,yBAAiB,IAAI,KAAK;MAC5B,WAAW,OAAO,UAAU,YAAY,OAAO,WAAW,MAAM;AAC9D,YAAI,OAAO,QAAA,QAAE,IAAI;MACnB,OAAO;AACL,YAAI,QAAO,GAAA,UAAA,KAAI,YAAY,WAAW,IAAI;AAC1C,YAAI,OAAO,IAAI;MACjB;IACF;AAVA,YAAA,uBAAA;AAYA,aAAgB,kBAAkB,IAAe,OAAW;AAC1D,YAAM,EAAC,KAAK,OAAM,IAAI;AACtB,UAAI,WAAW,OAAO;AACpB,YAAI,IAAI,OAAO,KAAK;AACpB,yBAAiB,EAAE;MACrB,OAAO;AACL,YAAI,IAAI,OAAO,IAAI;MACrB;IACF;AARA,YAAA,oBAAA;AAUA,aAAS,iBAAiB,IAAe,mBAA2B;AAClE,YAAM,EAAC,KAAK,KAAI,IAAI;AAEpB,YAAM,MAAuB;QAC3B;QACA,SAAS;QACT;QACA,QAAQ;QACR,YAAY;QACZ,aAAa;QACb,QAAQ,CAAA;QACR;;AAEF,OAAA,GAAA,SAAA,aAAY,KAAK,WAAW,QAAW,iBAAiB;IAC1D;;;;;;;;;;AC5CA,QAAM,aAAa,CAAC,UAAU,UAAU,WAAW,WAAW,QAAQ,UAAU,OAAO;AAIvF,QAAM,YAAyB,IAAI,IAAI,UAAU;AAEjD,aAAgB,WAAW,GAAU;AACnC,aAAO,OAAO,KAAK,YAAY,UAAU,IAAI,CAAC;IAChD;AAFA,YAAA,aAAA;AA2BA,aAAgB,WAAQ;AACtB,YAAM,SAAsE;QAC1E,QAAQ,EAAC,MAAM,UAAU,OAAO,CAAA,EAAE;QAClC,QAAQ,EAAC,MAAM,UAAU,OAAO,CAAA,EAAE;QAClC,OAAO,EAAC,MAAM,SAAS,OAAO,CAAA,EAAE;QAChC,QAAQ,EAAC,MAAM,UAAU,OAAO,CAAA,EAAE;;AAEpC,aAAO;QACL,OAAO,EAAC,GAAG,QAAQ,SAAS,MAAM,SAAS,MAAM,MAAM,KAAI;QAC3D,OAAO,CAAC,EAAC,OAAO,CAAA,EAAE,GAAG,OAAO,QAAQ,OAAO,QAAQ,OAAO,OAAO,OAAO,MAAM;QAC9E,MAAM,EAAC,OAAO,CAAA,EAAE;QAChB,KAAK,CAAA;QACL,UAAU,CAAA;;IAEd;AAdA,YAAA,WAAA;;;;;;;;;;AC/BA,aAAgB,sBACd,EAAC,QAAQ,KAAI,GACb,MAAc;AAEd,YAAM,QAAQ,KAAK,MAAM,MAAM,IAAI;AACnC,aAAO,SAAS,UAAU,QAAQ,eAAe,QAAQ,KAAK;IAChE;AANA,YAAA,wBAAA;AAQA,aAAgB,eAAe,QAAyB,OAAgB;AACtE,aAAO,MAAM,MAAM,KAAK,CAAC,SAAS,cAAc,QAAQ,IAAI,CAAC;IAC/D;AAFA,YAAA,iBAAA;AAIA,aAAgB,cAAc,QAAyB,MAAU;;AAC/D,aACE,OAAO,KAAK,OAAO,MAAM,YACzB,KAAA,KAAK,WAAW,gBAAU,QAAA,OAAA,SAAA,SAAA,GAAE,KAAK,CAAC,QAAQ,OAAO,GAAG,MAAM,MAAS;IAEvE;AALA,YAAA,gBAAA;;;;;;;;;;ACTA,QAAA,UAAA;AACA,QAAA,kBAAA;AACA,QAAA,WAAA;AACA,QAAA,YAAA;AACA,QAAA,SAAA;AAEA,QAAY;AAAZ,KAAA,SAAYC,WAAQ;AAClB,MAAAA,UAAAA,UAAA,SAAA,IAAA,CAAA,IAAA;AACA,MAAAA,UAAAA,UAAA,OAAA,IAAA,CAAA,IAAA;IACF,GAHY,aAAQ,QAAA,WAAR,WAAQ,CAAA,EAAA;AAKpB,aAAgB,eAAe,QAAuB;AACpD,YAAM,QAAQ,aAAa,OAAO,IAAI;AACtC,YAAM,UAAU,MAAM,SAAS,MAAM;AACrC,UAAI,SAAS;AACX,YAAI,OAAO,aAAa;AAAO,gBAAM,IAAI,MAAM,wCAAwC;MACzF,OAAO;AACL,YAAI,CAAC,MAAM,UAAU,OAAO,aAAa,QAAW;AAClD,gBAAM,IAAI,MAAM,0CAA0C;QAC5D;AACA,YAAI,OAAO,aAAa;AAAM,gBAAM,KAAK,MAAM;MACjD;AACA,aAAO;IACT;AAZA,YAAA,iBAAA;AAeA,aAAgB,aAAa,IAAuB;AAClD,YAAM,QAAmB,MAAM,QAAQ,EAAE,IAAI,KAAK,KAAK,CAAC,EAAE,IAAI,CAAA;AAC9D,UAAI,MAAM,MAAM,QAAA,UAAU;AAAG,eAAO;AACpC,YAAM,IAAI,MAAM,0CAA0C,MAAM,KAAK,GAAG,CAAC;IAC3E;AAJA,YAAA,eAAA;AAMA,aAAgB,uBAAuB,IAAkB,OAAiB;AACxE,YAAM,EAAC,KAAK,MAAM,KAAI,IAAI;AAC1B,YAAM,WAAW,cAAc,OAAO,KAAK,WAAW;AACtD,YAAM,aACJ,MAAM,SAAS,KACf,EAAE,SAAS,WAAW,KAAK,MAAM,WAAW,MAAK,GAAA,gBAAA,uBAAsB,IAAI,MAAM,CAAC,CAAC;AACrF,UAAI,YAAY;AACd,cAAM,YAAY,eAAe,OAAO,MAAM,KAAK,eAAe,SAAS,KAAK;AAChF,YAAI,GAAG,WAAW,MAAK;AACrB,cAAI,SAAS;AAAQ,uBAAW,IAAI,OAAO,QAAQ;;AAC9C,4BAAgB,EAAE;QACzB,CAAC;MACH;AACA,aAAO;IACT;AAdA,YAAA,yBAAA;AAgBA,QAAM,YAA2B,oBAAI,IAAI,CAAC,UAAU,UAAU,WAAW,WAAW,MAAM,CAAC;AAC3F,aAAS,cAAc,OAAmB,aAA+B;AACvE,aAAO,cACH,MAAM,OAAO,CAAC,MAAM,UAAU,IAAI,CAAC,KAAM,gBAAgB,WAAW,MAAM,OAAQ,IAClF,CAAA;IACN;AAEA,aAAS,WAAW,IAAkB,OAAmB,UAAoB;AAC3E,YAAM,EAAC,KAAK,MAAM,KAAI,IAAI;AAC1B,YAAM,WAAW,IAAI,IAAI,aAAY,GAAA,UAAA,YAAW,IAAI,EAAE;AACtD,YAAM,UAAU,IAAI,IAAI,YAAW,GAAA,UAAA,aAAY;AAC/C,UAAI,KAAK,gBAAgB,SAAS;AAChC,YAAI,IAAG,GAAA,UAAA,KAAI,QAAQ,iCAAiC,IAAI,QAAQ,IAAI,gBAAgB,MAClF,IACG,OAAO,OAAM,GAAA,UAAA,KAAI,IAAI,KAAK,EAC1B,OAAO,WAAU,GAAA,UAAA,YAAW,IAAI,EAAE,EAClC,GAAG,eAAe,OAAO,MAAM,KAAK,aAAa,GAAG,MAAM,IAAI,OAAO,SAAS,IAAI,CAAC,CAAC;MAE3F;AACA,UAAI,IAAG,GAAA,UAAA,KAAI,OAAO,gBAAgB;AAClC,iBAAW,KAAK,UAAU;AACxB,YAAI,UAAU,IAAI,CAAC,KAAM,MAAM,WAAW,KAAK,gBAAgB,SAAU;AACvE,6BAAmB,CAAC;QACtB;MACF;AACA,UAAI,KAAI;AACR,sBAAgB,EAAE;AAClB,UAAI,MAAK;AAET,UAAI,IAAG,GAAA,UAAA,KAAI,OAAO,kBAAkB,MAAK;AACvC,YAAI,OAAO,MAAM,OAAO;AACxB,yBAAiB,IAAI,OAAO;MAC9B,CAAC;AAED,eAAS,mBAAmB,GAAS;AACnC,gBAAQ,GAAG;UACT,KAAK;AACH,gBACG,QAAO,GAAA,UAAA,KAAI,QAAQ,mBAAmB,QAAQ,eAAe,EAC7D,OAAO,UAAS,GAAA,UAAA,UAAS,IAAI,EAAE,EAC/B,QAAO,GAAA,UAAA,KAAI,IAAI,WAAW,EAC1B,OAAO,UAAS,GAAA,UAAA,MAAK;AACxB;UACF,KAAK;AACH,gBACG,QACC,GAAA,UAAA,KAAI,QAAQ,oBAAoB,IAAI;oBAC5B,QAAQ,mBAAmB,IAAI,OAAO,IAAI,QAAQ,IAAI,GAAG,EAElE,OAAO,UAAS,GAAA,UAAA,MAAK,IAAI,EAAE;AAC9B;UACF,KAAK;AACH,gBACG,QACC,GAAA,UAAA,KAAI,QAAQ,qBAAqB,IAAI;oBAC7B,QAAQ,oBAAoB,IAAI,OAAO,IAAI,QAAQ,IAAI,SAAS,IAAI,QAAQ,EAErF,OAAO,UAAS,GAAA,UAAA,MAAK,IAAI,EAAE;AAC9B;UACF,KAAK;AACH,gBACG,QAAO,GAAA,UAAA,KAAI,IAAI,mBAAmB,IAAI,aAAa,IAAI,WAAW,EAClE,OAAO,SAAS,KAAK,EACrB,QAAO,GAAA,UAAA,KAAI,IAAI,kBAAkB,IAAI,QAAQ,EAC7C,OAAO,SAAS,IAAI;AACvB;UACF,KAAK;AACH,gBAAI,QAAO,GAAA,UAAA,KAAI,IAAI,cAAc,IAAI,aAAa,IAAI,YAAY;AAClE,gBAAI,OAAO,SAAS,IAAI;AACxB;UAEF,KAAK;AACH,gBACG,QACC,GAAA,UAAA,KAAI,QAAQ,oBAAoB,QAAQ;mBACjC,QAAQ,qBAAqB,IAAI,WAAW,EAEpD,OAAO,UAAS,GAAA,UAAA,MAAK,IAAI,GAAG;QACnC;MACF;IACF;AAEA,aAAS,iBAAiB,EAAC,KAAK,YAAY,mBAAkB,GAAiB,MAAU;AAEvF,UAAI,IAAG,GAAA,UAAA,KAAI,UAAU,kBAAkB,MACrC,IAAI,QAAO,GAAA,UAAA,KAAI,UAAU,IAAI,kBAAkB,KAAK,IAAI,CAAC;IAE7D;AAEA,aAAgB,cACd,UACA,MACA,YACA,UAAU,SAAS,SAAO;AAE1B,YAAM,KAAK,YAAY,SAAS,UAAU,UAAA,UAAU,KAAK,UAAA,UAAU;AACnE,UAAI;AACJ,cAAQ,UAAU;QAChB,KAAK;AACH,kBAAO,GAAA,UAAA,KAAI,IAAI,IAAI,EAAE;QACvB,KAAK;AACH,kBAAO,GAAA,UAAA,mBAAkB,IAAI;AAC7B;QACF,KAAK;AACH,kBAAO,GAAA,UAAA,KAAI,IAAI,cAAc,IAAI,kCAAkC,IAAI;AACvE;QACF,KAAK;AACH,iBAAO,SAAQ,GAAA,UAAA,OAAM,IAAI,mBAAmB,IAAI,GAAG;AACnD;QACF,KAAK;AACH,iBAAO,QAAO;AACd;QACF;AACE,kBAAO,GAAA,UAAA,YAAW,IAAI,IAAI,EAAE,IAAI,QAAQ;MAC5C;AACA,aAAO,YAAY,SAAS,UAAU,QAAO,GAAA,UAAA,KAAI,IAAI;AAErD,eAAS,QAAQ,QAAc,UAAA,KAAG;AAChC,gBAAO,GAAA,UAAA,MAAI,GAAA,UAAA,YAAW,IAAI,gBAAgB,OAAO,cAAa,GAAA,UAAA,cAAa,IAAI,MAAM,UAAA,GAAG;MAC1F;IACF;AA/BA,YAAA,gBAAA;AAiCA,aAAgB,eACd,WACA,MACA,YACA,SAAkB;AAElB,UAAI,UAAU,WAAW,GAAG;AAC1B,eAAO,cAAc,UAAU,CAAC,GAAG,MAAM,YAAY,OAAO;MAC9D;AACA,UAAI;AACJ,YAAM,SAAQ,GAAA,OAAA,QAAO,SAAS;AAC9B,UAAI,MAAM,SAAS,MAAM,QAAQ;AAC/B,cAAM,UAAS,GAAA,UAAA,YAAW,IAAI;AAC9B,eAAO,MAAM,OAAO,UAAS,GAAA,UAAA,MAAK,IAAI,OAAO,MAAM;AACnD,eAAO,MAAM;AACb,eAAO,MAAM;AACb,eAAO,MAAM;MACf,OAAO;AACL,eAAO,UAAA;MACT;AACA,UAAI,MAAM;AAAQ,eAAO,MAAM;AAC/B,iBAAW,KAAK;AAAO,gBAAO,GAAA,UAAA,KAAI,MAAM,cAAc,GAAe,MAAM,YAAY,OAAO,CAAC;AAC/F,aAAO;IACT;AAvBA,YAAA,iBAAA;AA2BA,QAAM,YAAoC;MACxC,SAAS,CAAC,EAAC,OAAM,MAAM,WAAW,MAAM;MACxC,QAAQ,CAAC,EAAC,QAAQ,YAAW,MAC3B,OAAO,UAAU,YAAW,GAAA,UAAA,YAAW,MAAM,OAAM,GAAA,UAAA,YAAW,WAAW;;AAG7E,aAAgB,gBAAgB,IAAgB;AAC9C,YAAM,MAAM,oBAAoB,EAAE;AAClC,OAAA,GAAA,SAAA,aAAY,KAAK,SAAS;IAC5B;AAHA,YAAA,kBAAA;AAKA,aAAS,oBAAoB,IAAgB;AAC3C,YAAM,EAAC,KAAK,MAAM,OAAM,IAAI;AAC5B,YAAM,cAAa,GAAA,OAAA,gBAAe,IAAI,QAAQ,MAAM;AACpD,aAAO;QACL;QACA,SAAS;QACT;QACA,QAAQ,OAAO;QACf;QACA,aAAa;QACb,cAAc;QACd,QAAQ,CAAA;QACR;;IAEJ;;;;;;;;;;ACpOA,QAAA,YAAA;AACA,QAAA,SAAA;AAEA,aAAgB,eAAe,IAAkB,IAAW;AAC1D,YAAM,EAAC,YAAY,MAAK,IAAI,GAAG;AAC/B,UAAI,OAAO,YAAY,YAAY;AACjC,mBAAW,OAAO,YAAY;AAC5B,wBAAc,IAAI,KAAK,WAAW,GAAG,EAAE,OAAO;QAChD;MACF,WAAW,OAAO,WAAW,MAAM,QAAQ,KAAK,GAAG;AACjD,cAAM,QAAQ,CAAC,KAAK,MAAc,cAAc,IAAI,GAAG,IAAI,OAAO,CAAC;MACrE;IACF;AATA,YAAA,iBAAA;AAWA,aAAS,cAAc,IAAkB,MAAuB,cAAqB;AACnF,YAAM,EAAC,KAAK,eAAe,MAAM,KAAI,IAAI;AACzC,UAAI,iBAAiB;AAAW;AAChC,YAAM,aAAY,GAAA,UAAA,KAAI,IAAI,IAAG,GAAA,UAAA,aAAY,IAAI,CAAC;AAC9C,UAAI,eAAe;AACjB,SAAA,GAAA,OAAA,iBAAgB,IAAI,2BAA2B,SAAS,EAAE;AAC1D;MACF;AAEA,UAAI,aAAY,GAAA,UAAA,KAAI,SAAS;AAC7B,UAAI,KAAK,gBAAgB,SAAS;AAChC,qBAAY,GAAA,UAAA,KAAI,SAAS,OAAO,SAAS,gBAAgB,SAAS;MACpE;AAGA,UAAI,GAAG,YAAW,GAAA,UAAA,KAAI,SAAS,OAAM,GAAA,UAAA,WAAU,YAAY,CAAC,EAAE;IAChE;;;;;;;;;;AC5BA,QAAA,YAAA;AACA,QAAA,SAAA;AACA,QAAA,UAAA;AACA,QAAA,SAAA;AACA,aAAgB,uBAAuB,KAAiB,MAAY;AAClE,YAAM,EAAC,KAAK,MAAM,GAAE,IAAI;AACxB,UAAI,GAAG,iBAAiB,KAAK,MAAM,MAAM,GAAG,KAAK,aAAa,GAAG,MAAK;AACpE,YAAI,UAAU,EAAC,kBAAiB,GAAA,UAAA,KAAI,IAAI,GAAE,GAAG,IAAI;AACjD,YAAI,MAAK;MACX,CAAC;IACH;AANA,YAAA,yBAAA;AAQA,aAAgB,iBACd,EAAC,KAAK,MAAM,IAAI,EAAC,KAAI,EAAC,GACtB,YACA,SAAa;AAEb,cAAO,GAAA,UAAA,IACL,GAAG,WAAW,IAAI,CAAC,UACjB,GAAA,UAAA,KAAI,iBAAiB,KAAK,MAAM,MAAM,KAAK,aAAa,IAAG,GAAA,UAAA,KAAI,OAAO,MAAM,IAAI,EAAE,CAAC,CACpF;IAEL;AAVA,YAAA,mBAAA;AAYA,aAAgB,kBAAkB,KAAiB,SAAa;AAC9D,UAAI,UAAU,EAAC,iBAAiB,QAAO,GAAG,IAAI;AAC9C,UAAI,MAAK;IACX;AAHA,YAAA,oBAAA;AAKA,aAAgB,YAAY,KAAY;AACtC,aAAO,IAAI,WAAW,QAAQ;;QAE5B,KAAK,OAAO,UAAU;QACtB,OAAM,GAAA,UAAA;OACP;IACH;AANA,YAAA,cAAA;AAQA,aAAgB,cAAc,KAAc,MAAY,UAAuB;AAC7E,cAAO,GAAA,UAAA,KAAI,YAAY,GAAG,CAAC,SAAS,IAAI,KAAK,QAAQ;IACvD;AAFA,YAAA,gBAAA;AAIA,aAAgB,eACd,KACA,MACA,UACA,eAAuB;AAEvB,YAAM,QAAO,GAAA,UAAA,KAAI,IAAI,IAAG,GAAA,UAAA,aAAY,QAAQ,CAAC;AAC7C,aAAO,iBAAgB,GAAA,UAAA,KAAI,IAAI,OAAO,cAAc,KAAK,MAAM,QAAQ,CAAC,KAAK;IAC/E;AARA,YAAA,iBAAA;AAUA,aAAgB,iBACd,KACA,MACA,UACA,eAAuB;AAEvB,YAAM,QAAO,GAAA,UAAA,KAAI,IAAI,IAAG,GAAA,UAAA,aAAY,QAAQ,CAAC;AAC7C,aAAO,iBAAgB,GAAA,UAAA,IAAG,OAAM,GAAA,UAAA,KAAI,cAAc,KAAK,MAAM,QAAQ,CAAC,CAAC,IAAI;IAC7E;AARA,YAAA,mBAAA;AAUA,aAAgB,oBAAoB,WAAqB;AACvD,aAAO,YAAY,OAAO,KAAK,SAAS,EAAE,OAAO,CAAC,MAAM,MAAM,WAAW,IAAI,CAAA;IAC/E;AAFA,YAAA,sBAAA;AAIA,aAAgB,iBAAiB,IAAe,WAAoB;AAClE,aAAO,oBAAoB,SAAS,EAAE,OACpC,CAAC,MAAM,EAAC,GAAA,OAAA,mBAAkB,IAAI,UAAU,CAAC,CAAc,CAAC;IAE5D;AAJA,YAAA,mBAAA;AAMA,aAAgB,iBACd,EAAC,YAAY,MAAM,IAAI,EAAC,KAAK,cAAc,YAAY,UAAS,GAAG,GAAE,GACrE,MACA,SACA,YAAoB;AAEpB,YAAM,gBAAgB,cAAa,GAAA,UAAA,KAAI,UAAU,KAAK,IAAI,KAAK,YAAY,GAAG,UAAU,KAAK;AAC7F,YAAM,SAAkC;QACtC,CAAC,QAAA,QAAE,eAAc,GAAA,UAAA,WAAU,QAAA,QAAE,cAAc,SAAS,CAAC;QACrD,CAAC,QAAA,QAAE,YAAY,GAAG,UAAU;QAC5B,CAAC,QAAA,QAAE,oBAAoB,GAAG,kBAAkB;QAC5C,CAAC,QAAA,QAAE,UAAU,QAAA,QAAE,QAAQ;;AAEzB,UAAI,GAAG,KAAK;AAAY,eAAO,KAAK,CAAC,QAAA,QAAE,gBAAgB,QAAA,QAAE,cAAc,CAAC;AACxE,YAAM,QAAO,GAAA,UAAA,KAAI,aAAa,KAAK,IAAI,OAAO,GAAG,MAAM,CAAC;AACxD,aAAO,YAAY,UAAA,OAAM,GAAA,UAAA,KAAI,IAAI,SAAS,OAAO,KAAK,IAAI,OAAM,GAAA,UAAA,KAAI,IAAI,IAAI,IAAI;IAClF;AAhBA,YAAA,mBAAA;AAkBA,QAAM,aAAY,GAAA,UAAA;AAElB,aAAgB,WAAW,EAAC,KAAK,IAAI,EAAC,KAAI,EAAC,GAAe,SAAe;AACvE,YAAM,IAAI,KAAK,gBAAgB,MAAM;AACrC,YAAM,EAAC,OAAM,IAAI,KAAK;AACtB,YAAM,KAAK,OAAO,SAAS,CAAC;AAE5B,aAAO,IAAI,WAAW,WAAW;QAC/B,KAAK,GAAG,SAAQ;QAChB,KAAK;QACL,OAAM,GAAA,UAAA,KAAI,OAAO,SAAS,eAAe,aAAY,GAAA,OAAA,SAAQ,KAAK,MAAM,CAAC,IAAI,OAAO,KAAK,CAAC;OAC3F;IACH;AAVA,YAAA,aAAA;AAYA,aAAgB,cAAc,KAAe;AAC3C,YAAM,EAAC,KAAK,MAAM,SAAS,GAAE,IAAI;AACjC,YAAM,QAAQ,IAAI,KAAK,OAAO;AAC9B,UAAI,GAAG,WAAW;AAChB,cAAM,WAAW,IAAI,IAAI,SAAS,IAAI;AACtC,sBAAc,MAAM,IAAI,OAAO,UAAU,KAAK,CAAC;AAC/C,eAAO;MACT;AACA,UAAI,IAAI,OAAO,IAAI;AACnB,oBAAc,MAAM,IAAI,MAAK,CAAE;AAC/B,aAAO;AAEP,eAAS,cAAc,UAAoB;AACzC,cAAM,MAAM,IAAI,MAAM,QAAO,GAAA,UAAA,KAAI,IAAI,SAAS;AAC9C,YAAI,SAAS,KAAK,GAAG,KAAK,CAAC,MAAK;AAC9B,cAAI,UACF;YACE;YACA,UAAU;YACV,cAAc,OAAA,KAAK;aAErB,KAAK;AAEP,cAAI,IAAG,GAAA,UAAA,KAAI,KAAK,GAAG,QAAQ;QAC7B,CAAC;MACH;IACF;AA1BA,YAAA,gBAAA;AA4BA,aAAgB,cAAc,KAAe;AAC3C,YAAM,EAAC,KAAK,QAAQ,SAAS,GAAE,IAAI;AAEnC,UAAI,CAAC,MAAM,QAAQ,MAAM;AAAG,cAAM,IAAI,MAAM,0BAA0B;AACtE,YAAM,cAAc,OAAO,KAAK,CAAC,SAAmB,GAAA,OAAA,mBAAkB,IAAI,GAAG,CAAC;AAC9E,UAAI,eAAe,CAAC,GAAG,KAAK;AAAa;AAEzC,YAAM,QAAQ,IAAI,IAAI,SAAS,KAAK;AACpC,YAAM,WAAW,IAAI,KAAK,QAAQ;AAElC,UAAI,MAAM,MACR,OAAO,QAAQ,CAAC,MAAiB,MAAa;AAC5C,cAAM,SAAS,IAAI,UACjB;UACE;UACA,YAAY;UACZ,eAAe;WAEjB,QAAQ;AAEV,YAAI,OAAO,QAAO,GAAA,UAAA,KAAI,KAAK,OAAO,QAAQ,EAAE;AAC5C,cAAM,SAAS,IAAI,oBAAoB,QAAQ,QAAQ;AAGvD,YAAI,CAAC;AAAQ,cAAI,IAAG,GAAA,UAAA,KAAI,KAAK,CAAC;MAChC,CAAC,CAAC;AAGJ,UAAI,OACF,OACA,MAAM,IAAI,MAAK,GACf,MAAM,IAAI,MAAM,IAAI,CAAC;IAEzB;AAjCA,YAAA,gBAAA;;;;;;;;;;AC5HA,QAAA,YAAA;AACA,QAAA,UAAA;AAEA,QAAA,SAAA;AACA,QAAA,WAAA;AAIA,aAAgB,iBAAiB,KAAiB,KAA2B;AAC3E,YAAM,EAAC,KAAK,SAAS,QAAQ,cAAc,GAAE,IAAI;AACjD,YAAM,cAAc,IAAI,MAAM,KAAK,GAAG,MAAM,QAAQ,cAAc,EAAE;AACpE,YAAM,YAAY,WAAW,KAAK,SAAS,WAAW;AACtD,UAAI,GAAG,KAAK,mBAAmB;AAAO,WAAG,KAAK,eAAe,aAAa,IAAI;AAE9E,YAAM,QAAQ,IAAI,KAAK,OAAO;AAC9B,UAAI,UACF;QACE,QAAQ;QACR,YAAY,UAAA;QACZ,eAAe,GAAG,GAAG,aAAa,IAAI,OAAO;QAC7C,cAAc;QACd,eAAe;SAEjB,KAAK;AAEP,UAAI,KAAK,OAAO,MAAM,IAAI,MAAM,IAAI,CAAC;IACvC;AAlBA,YAAA,mBAAA;AAoBA,aAAgB,gBAAgB,KAAiB,KAA0B;;AACzE,YAAM,EAAC,KAAK,SAAS,QAAQ,cAAc,OAAO,GAAE,IAAI;AACxD,wBAAkB,IAAI,GAAG;AACzB,YAAM,WACJ,CAAC,SAAS,IAAI,UAAU,IAAI,QAAQ,KAAK,GAAG,MAAM,QAAQ,cAAc,EAAE,IAAI,IAAI;AACpF,YAAM,cAAc,WAAW,KAAK,SAAS,QAAQ;AACrD,YAAM,QAAQ,IAAI,IAAI,OAAO;AAC7B,UAAI,WAAW,OAAO,eAAe;AACrC,UAAI,IAAG,KAAA,IAAI,WAAK,QAAA,OAAA,SAAA,KAAI,KAAK;AAEzB,eAAS,kBAAe;AACtB,YAAI,IAAI,WAAW,OAAO;AACxB,sBAAW;AACX,cAAI,IAAI;AAAW,uBAAW,GAAG;AACjC,qBAAW,MAAM,IAAI,MAAK,CAAE;QAC9B,OAAO;AACL,gBAAM,WAAW,IAAI,QAAQ,cAAa,IAAK,aAAY;AAC3D,cAAI,IAAI;AAAW,uBAAW,GAAG;AACjC,qBAAW,MAAM,QAAQ,KAAK,QAAQ,CAAC;QACzC;MACF;AAEA,eAAS,gBAAa;AACpB,cAAM,WAAW,IAAI,IAAI,YAAY,IAAI;AACzC,YAAI,IACF,MAAM,aAAY,GAAA,UAAA,UAAS,GAC3B,CAAC,MACC,IAAI,OAAO,OAAO,KAAK,EAAE,IACvB,GAAA,UAAA,KAAI,CAAC,eAAe,GAAG,eAAuB,IAC9C,MAAM,IAAI,OAAO,WAAU,GAAA,UAAA,KAAI,CAAC,SAAS,GACzC,MAAM,IAAI,MAAM,CAAC,CAAC,CACnB;AAEL,eAAO;MACT;AAEA,eAAS,eAAY;AACnB,cAAM,gBAAe,GAAA,UAAA,KAAI,WAAW;AACpC,YAAI,OAAO,cAAc,IAAI;AAC7B,oBAAY,UAAA,GAAG;AACf,eAAO;MACT;AAEA,eAAS,YAAY,SAAe,IAAI,SAAQ,GAAA,UAAA,aAAY,UAAA,KAAG;AAC7D,cAAM,UAAU,GAAG,KAAK,cAAc,QAAA,QAAE,OAAO,QAAA,QAAE;AACjD,cAAM,aAAa,EAAG,aAAa,OAAO,CAAC,SAAU,IAAI,WAAW;AACpE,YAAI,OACF,QACA,GAAA,UAAA,KAAI,MAAM,IAAG,GAAA,OAAA,kBAAiB,KAAK,aAAa,SAAS,UAAU,CAAC,IACpE,IAAI,SAAS;MAEjB;AAEA,eAAS,WAAW,QAAkB;;AACpC,YAAI,IAAG,GAAA,UAAA,MAAIC,MAAA,IAAI,WAAK,QAAAA,QAAA,SAAAA,MAAI,KAAK,GAAG,MAAM;MACxC;IACF;AAxDA,YAAA,kBAAA;AA0DA,aAAS,WAAW,KAAe;AACjC,YAAM,EAAC,KAAK,MAAM,GAAE,IAAI;AACxB,UAAI,GAAG,GAAG,YAAY,MAAM,IAAI,OAAO,OAAM,GAAA,UAAA,KAAI,GAAG,UAAU,IAAI,GAAG,kBAAkB,GAAG,CAAC;IAC7F;AAEA,aAAS,QAAQ,KAAiB,MAAU;AAC1C,YAAM,EAAC,IAAG,IAAI;AACd,UAAI,IACF,GAAA,UAAA,mBAAkB,IAAI,KACtB,MAAK;AACH,YACG,OAAO,QAAA,QAAE,UAAS,GAAA,UAAA,KAAI,QAAA,QAAE,OAAO,eAAe,IAAI,MAAM,QAAA,QAAE,OAAO,WAAW,IAAI,GAAG,EACnF,OAAO,QAAA,QAAE,SAAQ,GAAA,UAAA,KAAI,QAAA,QAAE,OAAO,SAAS;AAC1C,SAAA,GAAA,SAAA,cAAa,GAAG;MAClB,GACA,MAAM,IAAI,MAAK,CAAE;IAErB;AAEA,aAAS,kBAAkB,EAAC,UAAS,GAAiB,KAA0B;AAC9E,UAAI,IAAI,SAAS,CAAC,UAAU;AAAQ,cAAM,IAAI,MAAM,8BAA8B;IACpF;AAEA,aAAS,WAAW,KAAc,SAAiB,QAAiC;AAClF,UAAI,WAAW;AAAW,cAAM,IAAI,MAAM,YAAY,OAAO,qBAAqB;AAClF,aAAO,IAAI,WACT,WACA,OAAO,UAAU,aAAa,EAAC,KAAK,OAAM,IAAI,EAAC,KAAK,QAAQ,OAAM,GAAA,UAAA,WAAU,MAAM,EAAC,CAAC;IAExF;AAEA,aAAgB,gBACd,QACA,YACA,iBAAiB,OAAK;AAGtB,aACE,CAAC,WAAW,UACZ,WAAW,KAAK,CAAC,OACf,OAAO,UACH,MAAM,QAAQ,MAAM,IACpB,OAAO,WACP,UAAU,OAAO,UAAU,YAAY,CAAC,MAAM,QAAQ,MAAM,IAC5D,OAAO,UAAU,MAAO,kBAAkB,OAAO,UAAU,WAAY;IAGjF;AAhBA,YAAA,kBAAA;AAkBA,aAAgB,qBACd,EAAC,QAAQ,MAAM,MAAM,cAAa,GAClC,KACA,SAAe;AAGf,UAAI,MAAM,QAAQ,IAAI,OAAO,IAAI,CAAC,IAAI,QAAQ,SAAS,OAAO,IAAI,IAAI,YAAY,SAAS;AACzF,cAAM,IAAI,MAAM,0BAA0B;MAC5C;AAEA,YAAM,OAAO,IAAI;AACjB,UAAI,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,KAAK,CAAC,QAAQ,CAAC,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,CAAC,GAAG;AAC3E,cAAM,IAAI,MAAM,2CAA2C,OAAO,KAAK,KAAK,KAAK,GAAG,CAAC,EAAE;MACzF;AAEA,UAAI,IAAI,gBAAgB;AACtB,cAAM,QAAQ,IAAI,eAAe,OAAO,OAAO,CAAC;AAChD,YAAI,CAAC,OAAO;AACV,gBAAM,MACJ,YAAY,OAAO,+BAA+B,aAAa,QAC/D,KAAK,WAAW,IAAI,eAAe,MAAM;AAC3C,cAAI,KAAK,mBAAmB;AAAO,iBAAK,OAAO,MAAM,GAAG;;AACnD,kBAAM,IAAI,MAAM,GAAG;QAC1B;MACF;IACF;AAzBA,YAAA,uBAAA;;;;;;;;;;AC/IA,QAAA,YAAA;AACA,QAAA,SAAA;AA6CA,aAAgB,aACd,IACA,EAAC,SAAS,YAAY,QAAQ,YAAY,eAAe,aAAY,GAAgB;AAErF,UAAI,YAAY,UAAa,WAAW,QAAW;AACjD,cAAM,IAAI,MAAM,sDAAsD;MACxE;AAEA,UAAI,YAAY,QAAW;AACzB,cAAM,MAAM,GAAG,OAAO,OAAO;AAC7B,eAAO,eAAe,SAClB;UACE,QAAQ;UACR,aAAY,GAAA,UAAA,KAAI,GAAG,UAAU,IAAG,GAAA,UAAA,aAAY,OAAO,CAAC;UACpD,eAAe,GAAG,GAAG,aAAa,IAAI,OAAO;YAE/C;UACE,QAAQ,IAAI,UAAU;UACtB,aAAY,GAAA,UAAA,KAAI,GAAG,UAAU,IAAG,GAAA,UAAA,aAAY,OAAO,CAAC,IAAG,GAAA,UAAA,aAAY,UAAU,CAAC;UAC9E,eAAe,GAAG,GAAG,aAAa,IAAI,OAAO,KAAI,GAAA,OAAA,gBAAe,UAAU,CAAC;;MAEnF;AAEA,UAAI,WAAW,QAAW;AACxB,YAAI,eAAe,UAAa,kBAAkB,UAAa,iBAAiB,QAAW;AACzF,gBAAM,IAAI,MAAM,6EAA6E;QAC/F;AACA,eAAO;UACL;UACA;UACA;UACA;;MAEJ;AAEA,YAAM,IAAI,MAAM,6CAA6C;IAC/D;AApCA,YAAA,eAAA;AAsCA,aAAgB,oBACd,WACA,IACA,EAAC,UAAU,cAAc,QAAQ,MAAM,WAAW,aAAY,GAAgB;AAE9E,UAAI,SAAS,UAAa,aAAa,QAAW;AAChD,cAAM,IAAI,MAAM,qDAAqD;MACvE;AAEA,YAAM,EAAC,IAAG,IAAI;AAEd,UAAI,aAAa,QAAW;AAC1B,cAAM,EAAC,WAAW,aAAa,KAAI,IAAI;AACvC,cAAM,WAAW,IAAI,IAAI,SAAQ,GAAA,UAAA,KAAI,GAAG,IAAI,IAAG,GAAA,UAAA,aAAY,QAAQ,CAAC,IAAI,IAAI;AAC5E,yBAAiB,QAAQ;AACzB,kBAAU,aAAY,GAAA,UAAA,OAAM,SAAS,IAAG,GAAA,OAAA,cAAa,UAAU,QAAQ,KAAK,gBAAgB,CAAC;AAC7F,kBAAU,sBAAqB,GAAA,UAAA,KAAI,QAAQ;AAC3C,kBAAU,cAAc,CAAC,GAAG,aAAa,UAAU,kBAAkB;MACvE;AAEA,UAAI,SAAS,QAAW;AACtB,cAAM,WAAW,gBAAgB,UAAA,OAAO,OAAO,IAAI,IAAI,QAAQ,MAAM,IAAI;AACzE,yBAAiB,QAAQ;AACzB,YAAI,iBAAiB;AAAW,oBAAU,eAAe;MAE3D;AAEA,UAAI;AAAW,kBAAU,YAAY;AAErC,eAAS,iBAAiB,WAAe;AACvC,kBAAU,OAAO;AACjB,kBAAU,YAAY,GAAG,YAAY;AACrC,kBAAU,YAAY,CAAA;AACtB,WAAG,oBAAoB,oBAAI,IAAG;AAC9B,kBAAU,aAAa,GAAG;AAC1B,kBAAU,YAAY,CAAC,GAAG,GAAG,WAAW,SAAS;MACnD;IACF;AArCA,YAAA,sBAAA;AAuCA,aAAgB,oBACd,WACA,EAAC,kBAAkB,aAAa,eAAe,cAAc,UAAS,GAAgB;AAEtF,UAAI,kBAAkB;AAAW,kBAAU,gBAAgB;AAC3D,UAAI,iBAAiB;AAAW,kBAAU,eAAe;AACzD,UAAI,cAAc;AAAW,kBAAU,YAAY;AACnD,gBAAU,mBAAmB;AAC7B,gBAAU,cAAc;IAC1B;AATA,YAAA,sBAAA;;;;;AC7HA;AAAA;AAAA;AAMA,WAAO,UAAU,SAAS,MAAM,GAAG,GAAG;AACpC,UAAI,MAAM,EAAG,QAAO;AAEpB,UAAI,KAAK,KAAK,OAAO,KAAK,YAAY,OAAO,KAAK,UAAU;AAC1D,YAAI,EAAE,gBAAgB,EAAE,YAAa,QAAO;AAE5C,YAAI,QAAQ,GAAG;AACf,YAAI,MAAM,QAAQ,CAAC,GAAG;AACpB,mBAAS,EAAE;AACX,cAAI,UAAU,EAAE,OAAQ,QAAO;AAC/B,eAAK,IAAI,QAAQ,QAAQ;AACvB,gBAAI,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,EAAG,QAAO;AACjC,iBAAO;AAAA,QACT;AAIA,YAAI,EAAE,gBAAgB,OAAQ,QAAO,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE;AAC5E,YAAI,EAAE,YAAY,OAAO,UAAU,QAAS,QAAO,EAAE,QAAQ,MAAM,EAAE,QAAQ;AAC7E,YAAI,EAAE,aAAa,OAAO,UAAU,SAAU,QAAO,EAAE,SAAS,MAAM,EAAE,SAAS;AAEjF,eAAO,OAAO,KAAK,CAAC;AACpB,iBAAS,KAAK;AACd,YAAI,WAAW,OAAO,KAAK,CAAC,EAAE,OAAQ,QAAO;AAE7C,aAAK,IAAI,QAAQ,QAAQ;AACvB,cAAI,CAAC,OAAO,UAAU,eAAe,KAAK,GAAG,KAAK,CAAC,CAAC,EAAG,QAAO;AAEhE,aAAK,IAAI,QAAQ,QAAQ,KAAI;AAC3B,cAAI,MAAM,KAAK,CAAC;AAEhB,cAAI,CAAC,MAAM,EAAE,GAAG,GAAG,EAAE,GAAG,CAAC,EAAG,QAAO;AAAA,QACrC;AAEA,eAAO;AAAA,MACT;AAGA,aAAO,MAAI,KAAK,MAAI;AAAA,IACtB;AAAA;AAAA;;;AC7CA;AAAA;AAAA;AAEA,QAAI,WAAW,OAAO,UAAU,SAAU,QAAQ,MAAM,IAAI;AAE1D,UAAI,OAAO,QAAQ,YAAY;AAC7B,aAAK;AACL,eAAO,CAAC;AAAA,MACV;AAEA,WAAK,KAAK,MAAM;AAChB,UAAI,MAAO,OAAO,MAAM,aAAc,KAAK,GAAG,OAAO,WAAW;AAAA,MAAC;AACjE,UAAI,OAAO,GAAG,QAAQ,WAAW;AAAA,MAAC;AAElC,gBAAU,MAAM,KAAK,MAAM,QAAQ,IAAI,MAAM;AAAA,IAC/C;AAGA,aAAS,WAAW;AAAA,MAClB,iBAAiB;AAAA,MACjB,OAAO;AAAA,MACP,UAAU;AAAA,MACV,sBAAsB;AAAA,MACtB,eAAe;AAAA,MACf,KAAK;AAAA,MACL,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,MAAM;AAAA,IACR;AAEA,aAAS,gBAAgB;AAAA,MACvB,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AAEA,aAAS,gBAAgB;AAAA,MACvB,OAAO;AAAA,MACP,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,mBAAmB;AAAA,MACnB,cAAc;AAAA,IAChB;AAEA,aAAS,eAAe;AAAA,MACtB,SAAS;AAAA,MACT,MAAM;AAAA,MACN,OAAO;AAAA,MACP,UAAU;AAAA,MACV,SAAS;AAAA,MACT,SAAS;AAAA,MACT,kBAAkB;AAAA,MAClB,kBAAkB;AAAA,MAClB,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,WAAW;AAAA,MACX,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,UAAU;AAAA,MACV,aAAa;AAAA,MACb,eAAe;AAAA,MACf,eAAe;AAAA,IACjB;AAGA,aAAS,UAAU,MAAM,KAAK,MAAM,QAAQ,SAAS,YAAY,eAAe,eAAe,cAAc,UAAU;AACrH,UAAI,UAAU,OAAO,UAAU,YAAY,CAAC,MAAM,QAAQ,MAAM,GAAG;AACjE,YAAI,QAAQ,SAAS,YAAY,eAAe,eAAe,cAAc,QAAQ;AACrF,iBAAS,OAAO,QAAQ;AACtB,cAAI,MAAM,OAAO,GAAG;AACpB,cAAI,MAAM,QAAQ,GAAG,GAAG;AACtB,gBAAI,OAAO,SAAS,eAAe;AACjC,uBAAS,IAAE,GAAG,IAAE,IAAI,QAAQ;AAC1B,0BAAU,MAAM,KAAK,MAAM,IAAI,CAAC,GAAG,UAAU,MAAM,MAAM,MAAM,GAAG,YAAY,SAAS,KAAK,QAAQ,CAAC;AAAA,YACzG;AAAA,UACF,WAAW,OAAO,SAAS,eAAe;AACxC,gBAAI,OAAO,OAAO,OAAO,UAAU;AACjC,uBAAS,QAAQ;AACf,0BAAU,MAAM,KAAK,MAAM,IAAI,IAAI,GAAG,UAAU,MAAM,MAAM,MAAM,cAAc,IAAI,GAAG,YAAY,SAAS,KAAK,QAAQ,IAAI;AAAA,YACjI;AAAA,UACF,WAAW,OAAO,SAAS,YAAa,KAAK,WAAW,EAAE,OAAO,SAAS,eAAgB;AACxF,sBAAU,MAAM,KAAK,MAAM,KAAK,UAAU,MAAM,KAAK,YAAY,SAAS,KAAK,MAAM;AAAA,UACvF;AAAA,QACF;AACA,aAAK,QAAQ,SAAS,YAAY,eAAe,eAAe,cAAc,QAAQ;AAAA,MACxF;AAAA,IACF;AAGA,aAAS,cAAc,KAAK;AAC1B,aAAO,IAAI,QAAQ,MAAM,IAAI,EAAE,QAAQ,OAAO,IAAI;AAAA,IACpD;AAAA;AAAA;;;;;;;;ACzFA,QAAA,SAAA;AACA,QAAA,QAAA;AACA,QAAA,WAAA;AAMA,QAAM,iBAAiB,oBAAI,IAAI;MAC7B;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;KACD;AAED,aAAgB,UAAU,QAAmB,QAA0B,MAAI;AACzE,UAAI,OAAO,UAAU;AAAW,eAAO;AACvC,UAAI,UAAU;AAAM,eAAO,CAAC,OAAO,MAAM;AACzC,UAAI,CAAC;AAAO,eAAO;AACnB,aAAO,UAAU,MAAM,KAAK;IAC9B;AALA,YAAA,YAAA;AAOA,QAAM,eAAe,oBAAI,IAAI;MAC3B;MACA;MACA;MACA;MACA;KACD;AAED,aAAS,OAAO,QAAuB;AACrC,iBAAW,OAAO,QAAQ;AACxB,YAAI,aAAa,IAAI,GAAG;AAAG,iBAAO;AAClC,cAAM,MAAM,OAAO,GAAG;AACtB,YAAI,MAAM,QAAQ,GAAG,KAAK,IAAI,KAAK,MAAM;AAAG,iBAAO;AACnD,YAAI,OAAO,OAAO,YAAY,OAAO,GAAG;AAAG,iBAAO;MACpD;AACA,aAAO;IACT;AAEA,aAAS,UAAU,QAAuB;AACxC,UAAI,QAAQ;AACZ,iBAAW,OAAO,QAAQ;AACxB,YAAI,QAAQ;AAAQ,iBAAO;AAC3B;AACA,YAAI,eAAe,IAAI,GAAG;AAAG;AAC7B,YAAI,OAAO,OAAO,GAAG,KAAK,UAAU;AAClC,WAAA,GAAA,OAAA,UAAS,OAAO,GAAG,GAAG,CAAC,QAAS,SAAS,UAAU,GAAG,CAAE;QAC1D;AACA,YAAI,UAAU;AAAU,iBAAO;MACjC;AACA,aAAO;IACT;AAEA,aAAgB,YAAY,UAAuB,KAAK,IAAI,WAAmB;AAC7E,UAAI,cAAc;AAAO,aAAK,YAAY,EAAE;AAC5C,YAAM,IAAI,SAAS,MAAM,EAAE;AAC3B,aAAO,aAAa,UAAU,CAAC;IACjC;AAJA,YAAA,cAAA;AAMA,aAAgB,aAAa,UAAuB,GAAe;AACjE,YAAM,aAAa,SAAS,UAAU,CAAC;AACvC,aAAO,WAAW,MAAM,GAAG,EAAE,CAAC,IAAI;IACpC;AAHA,YAAA,eAAA;AAKA,QAAM,sBAAsB;AAC5B,aAAgB,YAAY,IAAsB;AAChD,aAAO,KAAK,GAAG,QAAQ,qBAAqB,EAAE,IAAI;IACpD;AAFA,YAAA,cAAA;AAIA,aAAgB,WAAW,UAAuB,QAAgB,IAAU;AAC1E,WAAK,YAAY,EAAE;AACnB,aAAO,SAAS,QAAQ,QAAQ,EAAE;IACpC;AAHA,YAAA,aAAA;AAKA,QAAM,SAAS;AAEf,aAAgB,cAAyB,QAAmB,QAAc;AACxE,UAAI,OAAO,UAAU;AAAW,eAAO,CAAA;AACvC,YAAM,EAAC,UAAU,YAAW,IAAI,KAAK;AACrC,YAAM,QAAQ,YAAY,OAAO,QAAQ,KAAK,MAAM;AACpD,YAAM,UAA0C,EAAC,IAAI,MAAK;AAC1D,YAAM,aAAa,YAAY,aAAa,OAAO,KAAK;AACxD,YAAM,YAAuB,CAAA;AAC7B,YAAM,aAA0B,oBAAI,IAAG;AAEvC,eAAS,QAAQ,EAAC,SAAS,KAAI,GAAG,CAAC,KAAK,SAAS,GAAG,kBAAiB;AACnE,YAAI,kBAAkB;AAAW;AACjC,cAAM,WAAW,aAAa;AAC9B,YAAI,cAAc,QAAQ,aAAa;AACvC,YAAI,OAAO,IAAI,QAAQ,KAAK;AAAU,wBAAc,OAAO,KAAK,MAAM,IAAI,QAAQ,CAAC;AACnF,kBAAU,KAAK,MAAM,IAAI,OAAO;AAChC,kBAAU,KAAK,MAAM,IAAI,cAAc;AACvC,gBAAQ,OAAO,IAAI;AAEnB,iBAAS,OAAkB,KAAW;AAEpC,gBAAM,WAAW,KAAK,KAAK,YAAY;AACvC,gBAAM,YAAY,cAAc,SAAS,aAAa,GAAG,IAAI,GAAG;AAChE,cAAI,WAAW,IAAI,GAAG;AAAG,kBAAM,SAAS,GAAG;AAC3C,qBAAW,IAAI,GAAG;AAClB,cAAI,WAAW,KAAK,KAAK,GAAG;AAC5B,cAAI,OAAO,YAAY;AAAU,uBAAW,KAAK,KAAK,QAAQ;AAC9D,cAAI,OAAO,YAAY,UAAU;AAC/B,6BAAiB,KAAK,SAAS,QAAQ,GAAG;UAC5C,WAAW,QAAQ,YAAY,QAAQ,GAAG;AACxC,gBAAI,IAAI,CAAC,MAAM,KAAK;AAClB,+BAAiB,KAAK,UAAU,GAAG,GAAG,GAAG;AACzC,wBAAU,GAAG,IAAI;YACnB,OAAO;AACL,mBAAK,KAAK,GAAG,IAAI;YACnB;UACF;AACA,iBAAO;QACT;AAEA,iBAAS,UAAqB,QAAe;AAC3C,cAAI,OAAO,UAAU,UAAU;AAC7B,gBAAI,CAAC,OAAO,KAAK,MAAM;AAAG,oBAAM,IAAI,MAAM,mBAAmB,MAAM,GAAG;AACtE,mBAAO,KAAK,MAAM,IAAI,MAAM,EAAE;UAChC;QACF;MACF,CAAC;AAED,aAAO;AAEP,eAAS,iBAAiB,MAAiB,MAA6B,KAAW;AACjF,YAAI,SAAS,UAAa,CAAC,MAAM,MAAM,IAAI;AAAG,gBAAM,SAAS,GAAG;MAClE;AAEA,eAAS,SAAS,KAAW;AAC3B,eAAO,IAAI,MAAM,cAAc,GAAG,oCAAoC;MACxE;IACF;AAxDA,YAAA,gBAAA;;;;;;;;;;ACnFA,QAAA,eAAA;AACA,QAAA,aAAA;AACA,QAAA,kBAAA;AACA,QAAA,aAAA;AACA,QAAA,aAAA;AACA,QAAA,YAAA;AACA,QAAA,cAAA;AACA,QAAA,YAAA;AACA,QAAA,UAAA;AACA,QAAA,YAAA;AACA,QAAA,SAAA;AASA,QAAA,WAAA;AASA,aAAgB,qBAAqB,IAAa;AAChD,UAAI,YAAY,EAAE,GAAG;AACnB,sBAAc,EAAE;AAChB,YAAI,kBAAkB,EAAE,GAAG;AACzB,2BAAiB,EAAE;AACnB;QACF;MACF;AACA,uBAAiB,IAAI,OAAM,GAAA,aAAA,sBAAqB,EAAE,CAAC;IACrD;AATA,YAAA,uBAAA;AAWA,aAAS,iBACP,EAAC,KAAK,cAAc,QAAQ,WAAW,KAAI,GAC3C,MAAW;AAEX,UAAI,KAAK,KAAK,KAAK;AACjB,YAAI,KAAK,eAAc,GAAA,UAAA,KAAI,QAAA,QAAE,IAAI,KAAK,QAAA,QAAE,MAAM,IAAI,UAAU,QAAQ,MAAK;AACvE,cAAI,MAAK,GAAA,UAAA,mBAAkB,cAAc,QAAQ,IAAI,CAAC,EAAE;AACxD,+BAAqB,KAAK,IAAI;AAC9B,cAAI,KAAK,IAAI;QACf,CAAC;MACH,OAAO;AACL,YAAI,KAAK,eAAc,GAAA,UAAA,KAAI,QAAA,QAAE,IAAI,KAAK,kBAAkB,IAAI,CAAC,IAAI,UAAU,QAAQ,MACjF,IAAI,KAAK,cAAc,QAAQ,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC;MAEpD;IACF;AAEA,aAAS,kBAAkB,MAAqB;AAC9C,cAAO,GAAA,UAAA,MAAK,QAAA,QAAE,YAAY,QAAQ,QAAA,QAAE,UAAU,KAAK,QAAA,QAAE,kBAAkB,KAAK,QAAA,QAAE,QAAQ,IACpF,QAAA,QAAE,IACJ,GAAG,KAAK,cAAa,GAAA,UAAA,OAAM,QAAA,QAAE,cAAc,QAAQ,UAAA,GAAG;IACxD;AAEA,aAAS,qBAAqB,KAAc,MAAqB;AAC/D,UAAI,GACF,QAAA,QAAE,QACF,MAAK;AACH,YAAI,IAAI,QAAA,QAAE,eAAc,GAAA,UAAA,KAAI,QAAA,QAAE,MAAM,IAAI,QAAA,QAAE,YAAY,EAAE;AACxD,YAAI,IAAI,QAAA,QAAE,aAAY,GAAA,UAAA,KAAI,QAAA,QAAE,MAAM,IAAI,QAAA,QAAE,UAAU,EAAE;AACpD,YAAI,IAAI,QAAA,QAAE,qBAAoB,GAAA,UAAA,KAAI,QAAA,QAAE,MAAM,IAAI,QAAA,QAAE,kBAAkB,EAAE;AACpE,YAAI,IAAI,QAAA,QAAE,WAAU,GAAA,UAAA,KAAI,QAAA,QAAE,MAAM,IAAI,QAAA,QAAE,QAAQ,EAAE;AAChD,YAAI,KAAK;AAAY,cAAI,IAAI,QAAA,QAAE,iBAAgB,GAAA,UAAA,KAAI,QAAA,QAAE,MAAM,IAAI,QAAA,QAAE,cAAc,EAAE;MACnF,GACA,MAAK;AACH,YAAI,IAAI,QAAA,QAAE,eAAc,GAAA,UAAA,MAAK;AAC7B,YAAI,IAAI,QAAA,QAAE,aAAY,GAAA,UAAA,aAAY;AAClC,YAAI,IAAI,QAAA,QAAE,qBAAoB,GAAA,UAAA,aAAY;AAC1C,YAAI,IAAI,QAAA,QAAE,UAAU,QAAA,QAAE,IAAI;AAC1B,YAAI,KAAK;AAAY,cAAI,IAAI,QAAA,QAAE,iBAAgB,GAAA,UAAA,MAAK;MACtD,CAAC;IAEL;AAEA,aAAS,iBAAiB,IAAgB;AACxC,YAAM,EAAC,QAAQ,MAAM,IAAG,IAAI;AAC5B,uBAAiB,IAAI,MAAK;AACxB,YAAI,KAAK,YAAY,OAAO;AAAU,yBAAe,EAAE;AACvD,uBAAe,EAAE;AACjB,YAAI,IAAI,QAAA,QAAE,SAAS,IAAI;AACvB,YAAI,IAAI,QAAA,QAAE,QAAQ,CAAC;AACnB,YAAI,KAAK;AAAa,yBAAe,EAAE;AACvC,wBAAgB,EAAE;AAClB,sBAAc,EAAE;MAClB,CAAC;AACD;IACF;AAEA,aAAS,eAAe,IAAgB;AAEtC,YAAM,EAAC,KAAK,aAAY,IAAI;AAC5B,SAAG,YAAY,IAAI,MAAM,cAAa,GAAA,UAAA,KAAI,YAAY,YAAY;AAClE,UAAI,IAAG,GAAA,UAAA,KAAI,GAAG,SAAS,iBAAiB,MAAM,IAAI,QAAO,GAAA,UAAA,KAAI,GAAG,SAAS,WAAU,GAAA,UAAA,aAAY,CAAC;AAChG,UAAI,IAAG,GAAA,UAAA,KAAI,GAAG,SAAS,iBAAiB,MAAM,IAAI,QAAO,GAAA,UAAA,KAAI,GAAG,SAAS,WAAU,GAAA,UAAA,aAAY,CAAC;IAClG;AAEA,aAAS,cAAc,QAAmB,MAAqB;AAC7D,YAAM,QAAQ,OAAO,UAAU,YAAY,OAAO,KAAK,QAAQ;AAC/D,aAAO,UAAU,KAAK,KAAK,UAAU,KAAK,KAAK,YAAW,GAAA,UAAA,mBAAkB,KAAK,QAAQ,UAAA;IAC3F;AAGA,aAAS,cAAc,IAAe,OAAW;AAC/C,UAAI,YAAY,EAAE,GAAG;AACnB,sBAAc,EAAE;AAChB,YAAI,kBAAkB,EAAE,GAAG;AACzB,2BAAiB,IAAI,KAAK;AAC1B;QACF;MACF;AACA,OAAA,GAAA,aAAA,mBAAkB,IAAI,KAAK;IAC7B;AAEA,aAAS,kBAAkB,EAAC,QAAQ,KAAI,GAAY;AAClD,UAAI,OAAO,UAAU;AAAW,eAAO,CAAC;AACxC,iBAAW,OAAO;AAAQ,YAAI,KAAK,MAAM,IAAI,GAAG;AAAG,iBAAO;AAC1D,aAAO;IACT;AAEA,aAAS,YAAY,IAAa;AAChC,aAAO,OAAO,GAAG,UAAU;IAC7B;AAEA,aAAS,iBAAiB,IAAkB,OAAW;AACrD,YAAM,EAAC,QAAQ,KAAK,KAAI,IAAI;AAC5B,UAAI,KAAK,YAAY,OAAO;AAAU,uBAAe,EAAE;AACvD,oBAAc,EAAE;AAChB,uBAAiB,EAAE;AACnB,YAAM,YAAY,IAAI,MAAM,SAAS,QAAA,QAAE,MAAM;AAC7C,sBAAgB,IAAI,SAAS;AAE7B,UAAI,IAAI,QAAO,GAAA,UAAA,KAAI,SAAS,QAAQ,QAAA,QAAE,MAAM,EAAE;IAChD;AAEA,aAAS,cAAc,IAAgB;AACrC,OAAA,GAAA,OAAA,mBAAkB,EAAE;AACpB,2BAAqB,EAAE;IACzB;AAEA,aAAS,gBAAgB,IAAkB,WAAgB;AACzD,UAAI,GAAG,KAAK;AAAK,eAAO,eAAe,IAAI,CAAA,GAAI,OAAO,SAAS;AAC/D,YAAM,SAAQ,GAAA,WAAA,gBAAe,GAAG,MAAM;AACtC,YAAM,gBAAe,GAAA,WAAA,wBAAuB,IAAI,KAAK;AACrD,qBAAe,IAAI,OAAO,CAAC,cAAc,SAAS;IACpD;AAEA,aAAS,qBAAqB,IAAgB;AAC5C,YAAM,EAAC,QAAQ,eAAe,MAAM,KAAI,IAAI;AAC5C,UAAI,OAAO,QAAQ,KAAK,0BAAyB,GAAA,OAAA,sBAAqB,QAAQ,KAAK,KAAK,GAAG;AACzF,aAAK,OAAO,KAAK,6CAA6C,aAAa,GAAG;MAChF;IACF;AAEA,aAAS,eAAe,IAAgB;AACtC,YAAM,EAAC,QAAQ,KAAI,IAAI;AACvB,UAAI,OAAO,YAAY,UAAa,KAAK,eAAe,KAAK,cAAc;AACzE,SAAA,GAAA,OAAA,iBAAgB,IAAI,uCAAuC;MAC7D;IACF;AAEA,aAAS,cAAc,IAAgB;AACrC,YAAM,QAAQ,GAAG,OAAO,GAAG,KAAK,QAAQ;AACxC,UAAI;AAAO,WAAG,UAAS,GAAA,UAAA,YAAW,GAAG,KAAK,aAAa,GAAG,QAAQ,KAAK;IACzE;AAEA,aAAS,iBAAiB,IAAgB;AACxC,UAAI,GAAG,OAAO,UAAU,CAAC,GAAG,UAAU;AAAQ,cAAM,IAAI,MAAM,6BAA6B;IAC7F;AAEA,aAAS,eAAe,EAAC,KAAK,WAAW,QAAQ,eAAe,KAAI,GAAe;AACjF,YAAM,MAAM,OAAO;AACnB,UAAI,KAAK,aAAa,MAAM;AAC1B,YAAI,MAAK,GAAA,UAAA,KAAI,QAAA,QAAE,IAAI,eAAe,GAAG,GAAG;MAC1C,WAAW,OAAO,KAAK,YAAY,YAAY;AAC7C,cAAM,cAAa,GAAA,UAAA,OAAM,aAAa;AACtC,cAAM,WAAW,IAAI,WAAW,QAAQ,EAAC,KAAK,UAAU,KAAI,CAAC;AAC7D,YAAI,MAAK,GAAA,UAAA,KAAI,QAAA,QAAE,IAAI,kBAAkB,GAAG,KAAK,UAAU,KAAK,QAAQ,UAAU;MAChF;IACF;AAEA,aAAS,cAAc,IAAa;AAClC,YAAM,EAAC,KAAK,WAAW,cAAc,iBAAiB,KAAI,IAAI;AAC9D,UAAI,UAAU,QAAQ;AAEpB,YAAI,IACF,GAAA,UAAA,KAAI,QAAA,QAAE,MAAM,UACZ,MAAM,IAAI,OAAO,QAAA,QAAE,IAAI,GACvB,MAAM,IAAI,OAAM,GAAA,UAAA,SAAQ,eAAuB,IAAI,QAAA,QAAE,OAAO,GAAG,CAAC;MAEpE,OAAO;AACL,YAAI,QAAO,GAAA,UAAA,KAAI,YAAY,WAAW,QAAA,QAAE,OAAO;AAC/C,YAAI,KAAK;AAAa,0BAAgB,EAAE;AACxC,YAAI,QAAO,GAAA,UAAA,KAAI,QAAA,QAAE,MAAM,QAAQ;MACjC;IACF;AAEA,aAAS,gBAAgB,EAAC,KAAK,WAAW,OAAO,MAAK,GAAY;AAChE,UAAI,iBAAiB,UAAA;AAAM,YAAI,QAAO,GAAA,UAAA,KAAI,SAAS,UAAU,KAAK;AAClE,UAAI,iBAAiB,UAAA;AAAM,YAAI,QAAO,GAAA,UAAA,KAAI,SAAS,UAAU,KAAK;IACpE;AAEA,aAAS,eACP,IACA,OACA,YACA,WAAgB;AAEhB,YAAM,EAAC,KAAK,QAAQ,MAAM,WAAW,MAAM,KAAI,IAAI;AACnD,YAAM,EAAC,MAAK,IAAI;AAChB,UAAI,OAAO,SAAS,KAAK,yBAAyB,EAAC,GAAA,OAAA,sBAAqB,QAAQ,KAAK,IAAI;AACvF,YAAI,MAAM,MAAM,YAAY,IAAI,QAAS,MAAM,IAAI,KAAc,UAAU,CAAC;AAC5E;MACF;AACA,UAAI,CAAC,KAAK;AAAK,yBAAiB,IAAI,KAAK;AACzC,UAAI,MAAM,MAAK;AACb,mBAAW,SAAS,MAAM;AAAO,wBAAc,KAAK;AACpD,sBAAc,MAAM,IAAI;MAC1B,CAAC;AAED,eAAS,cAAc,OAAgB;AACrC,YAAI,EAAC,GAAA,gBAAA,gBAAe,QAAQ,KAAK;AAAG;AACpC,YAAI,MAAM,MAAM;AACd,cAAI,IAAG,GAAA,WAAA,eAAc,MAAM,MAAM,MAAM,KAAK,aAAa,CAAC;AAC1D,0BAAgB,IAAI,KAAK;AACzB,cAAI,MAAM,WAAW,KAAK,MAAM,CAAC,MAAM,MAAM,QAAQ,YAAY;AAC/D,gBAAI,KAAI;AACR,aAAA,GAAA,WAAA,iBAAgB,EAAE;UACpB;AACA,cAAI,MAAK;QACX,OAAO;AACL,0BAAgB,IAAI,KAAK;QAC3B;AAEA,YAAI,CAAC;AAAW,cAAI,IAAG,GAAA,UAAA,KAAI,QAAA,QAAE,MAAM,QAAQ,aAAa,CAAC,EAAE;MAC7D;IACF;AAEA,aAAS,gBAAgB,IAAkB,OAAgB;AACzD,YAAM,EACJ,KACA,QACA,MAAM,EAAC,YAAW,EAAC,IACjB;AACJ,UAAI;AAAa,SAAA,GAAA,WAAA,gBAAe,IAAI,MAAM,IAAI;AAC9C,UAAI,MAAM,MAAK;AACb,mBAAW,QAAQ,MAAM,OAAO;AAC9B,eAAI,GAAA,gBAAA,eAAc,QAAQ,IAAI,GAAG;AAC/B,wBAAY,IAAI,KAAK,SAAS,KAAK,YAAY,MAAM,IAAI;UAC3D;QACF;MACF,CAAC;IACH;AAEA,aAAS,iBAAiB,IAAkB,OAAiB;AAC3D,UAAI,GAAG,UAAU,QAAQ,CAAC,GAAG,KAAK;AAAa;AAC/C,wBAAkB,IAAI,KAAK;AAC3B,UAAI,CAAC,GAAG,KAAK;AAAiB,2BAAmB,IAAI,KAAK;AAC1D,wBAAkB,IAAI,GAAG,SAAS;IACpC;AAEA,aAAS,kBAAkB,IAAkB,OAAiB;AAC5D,UAAI,CAAC,MAAM;AAAQ;AACnB,UAAI,CAAC,GAAG,UAAU,QAAQ;AACxB,WAAG,YAAY;AACf;MACF;AACA,YAAM,QAAQ,CAAC,MAAK;AAClB,YAAI,CAAC,aAAa,GAAG,WAAW,CAAC,GAAG;AAClC,2BAAiB,IAAI,SAAS,CAAC,6BAA6B,GAAG,UAAU,KAAK,GAAG,CAAC,GAAG;QACvF;MACF,CAAC;AACD,wBAAkB,IAAI,KAAK;IAC7B;AAEA,aAAS,mBAAmB,IAAkB,IAAc;AAC1D,UAAI,GAAG,SAAS,KAAK,EAAE,GAAG,WAAW,KAAK,GAAG,SAAS,MAAM,IAAI;AAC9D,yBAAiB,IAAI,iDAAiD;MACxE;IACF;AAEA,aAAS,kBAAkB,IAAkB,IAAc;AACzD,YAAM,QAAQ,GAAG,KAAK,MAAM;AAC5B,iBAAW,WAAW,OAAO;AAC3B,cAAM,OAAO,MAAM,OAAO;AAC1B,YAAI,OAAO,QAAQ,aAAY,GAAA,gBAAA,eAAc,GAAG,QAAQ,IAAI,GAAG;AAC7D,gBAAM,EAAC,KAAI,IAAI,KAAK;AACpB,cAAI,KAAK,UAAU,CAAC,KAAK,KAAK,CAAC,MAAM,kBAAkB,IAAI,CAAC,CAAC,GAAG;AAC9D,6BAAiB,IAAI,iBAAiB,KAAK,KAAK,GAAG,CAAC,kBAAkB,OAAO,GAAG;UAClF;QACF;MACF;IACF;AAEA,aAAS,kBAAkB,OAAmB,MAAc;AAC1D,aAAO,MAAM,SAAS,IAAI,KAAM,SAAS,YAAY,MAAM,SAAS,SAAS;IAC/E;AAEA,aAAS,aAAa,IAAgB,GAAW;AAC/C,aAAO,GAAG,SAAS,CAAC,KAAM,MAAM,aAAa,GAAG,SAAS,QAAQ;IACnE;AAEA,aAAS,kBAAkB,IAAkB,WAAqB;AAChE,YAAM,KAAiB,CAAA;AACvB,iBAAW,KAAK,GAAG,WAAW;AAC5B,YAAI,aAAa,WAAW,CAAC;AAAG,aAAG,KAAK,CAAC;iBAChC,UAAU,SAAS,SAAS,KAAK,MAAM;AAAU,aAAG,KAAK,SAAS;MAC7E;AACA,SAAG,YAAY;IACjB;AAEA,aAAS,iBAAiB,IAAkB,KAAW;AACrD,YAAM,aAAa,GAAG,UAAU,SAAS,GAAG;AAC5C,aAAO,QAAQ,UAAU;AACzB,OAAA,GAAA,OAAA,iBAAgB,IAAI,KAAK,GAAG,KAAK,WAAW;IAC9C;AAEA,QAAa,aAAb,MAAuB;MAiBrB,YAAY,IAAkB,KAA6B,SAAe;AACxE,SAAA,GAAA,UAAA,sBAAqB,IAAI,KAAK,OAAO;AACrC,aAAK,MAAM,GAAG;AACd,aAAK,YAAY,GAAG;AACpB,aAAK,UAAU;AACf,aAAK,OAAO,GAAG;AACf,aAAK,SAAS,GAAG,OAAO,OAAO;AAC/B,aAAK,QAAQ,IAAI,SAAS,GAAG,KAAK,SAAS,KAAK,UAAU,KAAK,OAAO;AACtE,aAAK,eAAc,GAAA,OAAA,gBAAe,IAAI,KAAK,QAAQ,SAAS,KAAK,KAAK;AACtE,aAAK,aAAa,IAAI;AACtB,aAAK,eAAe,GAAG;AACvB,aAAK,SAAS,CAAA;AACd,aAAK,KAAK;AACV,aAAK,MAAM;AAEX,YAAI,KAAK,OAAO;AACd,eAAK,aAAa,GAAG,IAAI,MAAM,WAAW,QAAQ,KAAK,OAAO,EAAE,CAAC;QACnE,OAAO;AACL,eAAK,aAAa,KAAK;AACvB,cAAI,EAAC,GAAA,UAAA,iBAAgB,KAAK,QAAQ,IAAI,YAAY,IAAI,cAAc,GAAG;AACrE,kBAAM,IAAI,MAAM,GAAG,OAAO,kBAAkB,KAAK,UAAU,IAAI,UAAU,CAAC,EAAE;UAC9E;QACF;AAEA,YAAI,UAAU,MAAM,IAAI,cAAc,IAAI,WAAW,OAAO;AAC1D,eAAK,YAAY,GAAG,IAAI,MAAM,SAAS,QAAA,QAAE,MAAM;QACjD;MACF;MAEA,OAAO,WAAiB,eAA4B,YAAuB;AACzE,aAAK,YAAW,GAAA,UAAA,KAAI,SAAS,GAAG,eAAe,UAAU;MAC3D;MAEA,WAAW,WAAiB,eAA4B,YAAuB;AAC7E,aAAK,IAAI,GAAG,SAAS;AACrB,YAAI;AAAY,qBAAU;;AACrB,eAAK,MAAK;AACf,YAAI,eAAe;AACjB,eAAK,IAAI,KAAI;AACb,wBAAa;AACb,cAAI,KAAK;AAAW,iBAAK,IAAI,MAAK;QACpC,OAAO;AACL,cAAI,KAAK;AAAW,iBAAK,IAAI,MAAK;;AAC7B,iBAAK,IAAI,KAAI;QACpB;MACF;MAEA,KAAK,WAAiB,YAAuB;AAC3C,aAAK,YAAW,GAAA,UAAA,KAAI,SAAS,GAAG,QAAW,UAAU;MACvD;MAEA,KAAK,WAAgB;AACnB,YAAI,cAAc,QAAW;AAC3B,eAAK,MAAK;AACV,cAAI,CAAC,KAAK;AAAW,iBAAK,IAAI,GAAG,KAAK;AACtC;QACF;AACA,aAAK,IAAI,GAAG,SAAS;AACrB,aAAK,MAAK;AACV,YAAI,KAAK;AAAW,eAAK,IAAI,MAAK;;AAC7B,eAAK,IAAI,KAAI;MACpB;MAEA,UAAU,WAAe;AACvB,YAAI,CAAC,KAAK;AAAO,iBAAO,KAAK,KAAK,SAAS;AAC3C,cAAM,EAAC,WAAU,IAAI;AACrB,aAAK,MAAK,GAAA,UAAA,KAAI,UAAU,uBAAsB,GAAA,UAAA,IAAG,KAAK,aAAY,GAAI,SAAS,CAAC,GAAG;MACrF;MAEA,MAAM,QAAkB,aAAgC,YAAuB;AAC7E,YAAI,aAAa;AACf,eAAK,UAAU,WAAW;AAC1B,eAAK,OAAO,QAAQ,UAAU;AAC9B,eAAK,UAAU,CAAA,CAAE;AACjB;QACF;AACA,aAAK,OAAO,QAAQ,UAAU;MAChC;MAEQ,OAAO,QAAkB,YAAuB;AACtD;AAAC,SAAC,SAAS,SAAA,mBAAmB,SAAA,aAAa,MAAM,KAAK,IAAI,OAAO,UAAU;MAC7E;MAEA,aAAU;AACR,SAAA,GAAA,SAAA,aAAY,MAAM,KAAK,IAAI,cAAc,SAAA,iBAAiB;MAC5D;MAEA,QAAK;AACH,YAAI,KAAK,cAAc;AAAW,gBAAM,IAAI,MAAM,yCAAyC;AAC3F,SAAA,GAAA,SAAA,kBAAiB,KAAK,KAAK,KAAK,SAAS;MAC3C;MAEA,GAAG,MAAoB;AACrB,YAAI,CAAC,KAAK;AAAW,eAAK,IAAI,GAAG,IAAI;MACvC;MAEA,UAAU,KAAuB,QAAa;AAC5C,YAAI;AAAQ,iBAAO,OAAO,KAAK,QAAQ,GAAG;;AACrC,eAAK,SAAS;MACrB;MAEA,WAAW,OAAa,WAAuB,aAAmB,UAAA,KAAG;AACnE,aAAK,IAAI,MAAM,MAAK;AAClB,eAAK,WAAW,OAAO,UAAU;AACjC,oBAAS;QACX,CAAC;MACH;MAEA,WAAW,QAAc,UAAA,KAAK,aAAmB,UAAA,KAAG;AAClD,YAAI,CAAC,KAAK;AAAO;AACjB,cAAM,EAAC,KAAK,YAAY,YAAY,IAAG,IAAI;AAC3C,YAAI,IAAG,GAAA,UAAA,KAAG,GAAA,UAAA,KAAI,UAAU,kBAAkB,UAAU,CAAC;AACrD,YAAI,UAAU,UAAA;AAAK,cAAI,OAAO,OAAO,IAAI;AACzC,YAAI,WAAW,UAAU,IAAI,gBAAgB;AAC3C,cAAI,OAAO,KAAK,aAAY,CAAE;AAC9B,eAAK,WAAU;AACf,cAAI,UAAU,UAAA;AAAK,gBAAI,OAAO,OAAO,KAAK;QAC5C;AACA,YAAI,KAAI;MACV;MAEA,eAAY;AACV,cAAM,EAAC,KAAK,YAAY,YAAY,KAAK,GAAE,IAAI;AAC/C,gBAAO,GAAA,UAAA,IAAG,eAAc,GAAI,mBAAkB,CAAE;AAEhD,iBAAS,iBAAc;AACrB,cAAI,WAAW,QAAQ;AAErB,gBAAI,EAAE,sBAAsB,UAAA;AAAO,oBAAM,IAAI,MAAM,0BAA0B;AAC7E,kBAAM,KAAK,MAAM,QAAQ,UAAU,IAAI,aAAa,CAAC,UAAU;AAC/D,oBAAO,GAAA,UAAA,MAAI,GAAA,WAAA,gBAAe,IAAI,YAAY,GAAG,KAAK,eAAe,WAAA,SAAS,KAAK,CAAC;UAClF;AACA,iBAAO,UAAA;QACT;AAEA,iBAAS,qBAAkB;AACzB,cAAI,IAAI,gBAAgB;AACtB,kBAAM,oBAAoB,IAAI,WAAW,iBAAiB,EAAC,KAAK,IAAI,eAAc,CAAC;AACnF,oBAAO,GAAA,UAAA,MAAK,iBAAiB,IAAI,UAAU;UAC7C;AACA,iBAAO,UAAA;QACT;MACF;MAEA,UAAU,MAAqB,OAAW;AACxC,cAAM,aAAY,GAAA,YAAA,cAAa,KAAK,IAAI,IAAI;AAC5C,SAAA,GAAA,YAAA,qBAAoB,WAAW,KAAK,IAAI,IAAI;AAC5C,SAAA,GAAA,YAAA,qBAAoB,WAAW,IAAI;AACnC,cAAM,cAAc,EAAC,GAAG,KAAK,IAAI,GAAG,WAAW,OAAO,QAAW,OAAO,OAAS;AACjF,sBAAc,aAAa,KAAK;AAChC,eAAO;MACT;MAEA,eAAe,WAAsB,QAAoB;AACvD,cAAM,EAAC,IAAI,IAAG,IAAI;AAClB,YAAI,CAAC,GAAG,KAAK;AAAa;AAC1B,YAAI,GAAG,UAAU,QAAQ,UAAU,UAAU,QAAW;AACtD,aAAG,QAAQ,OAAA,eAAe,MAAM,KAAK,UAAU,OAAO,GAAG,OAAO,MAAM;QACxE;AACA,YAAI,GAAG,UAAU,QAAQ,UAAU,UAAU,QAAW;AACtD,aAAG,QAAQ,OAAA,eAAe,MAAM,KAAK,UAAU,OAAO,GAAG,OAAO,MAAM;QACxE;MACF;MAEA,oBAAoB,WAAsB,OAAW;AACnD,cAAM,EAAC,IAAI,IAAG,IAAI;AAClB,YAAI,GAAG,KAAK,gBAAgB,GAAG,UAAU,QAAQ,GAAG,UAAU,OAAO;AACnE,cAAI,GAAG,OAAO,MAAM,KAAK,eAAe,WAAW,UAAA,IAAI,CAAC;AACxD,iBAAO;QACT;MACF;;AA3LF,YAAA,aAAA;AA8LA,aAAS,YACP,IACA,SACA,KACA,UAAmB;AAEnB,YAAM,MAAM,IAAI,WAAW,IAAI,KAAK,OAAO;AAC3C,UAAI,UAAU,KAAK;AACjB,YAAI,KAAK,KAAK,QAAQ;MACxB,WAAW,IAAI,SAAS,IAAI,UAAU;AACpC,SAAA,GAAA,UAAA,iBAAgB,KAAK,GAAG;MAC1B,WAAW,WAAW,KAAK;AACzB,SAAA,GAAA,UAAA,kBAAiB,KAAK,GAAG;MAC3B,WAAW,IAAI,WAAW,IAAI,UAAU;AACtC,SAAA,GAAA,UAAA,iBAAgB,KAAK,GAAG;MAC1B;IACF;AAEA,QAAM,eAAe;AACrB,QAAM,wBAAwB;AAC9B,aAAgB,QACd,OACA,EAAC,WAAW,WAAW,YAAW,GAAY;AAE9C,UAAI;AACJ,UAAI;AACJ,UAAI,UAAU;AAAI,eAAO,QAAA,QAAE;AAC3B,UAAI,MAAM,CAAC,MAAM,KAAK;AACpB,YAAI,CAAC,aAAa,KAAK,KAAK;AAAG,gBAAM,IAAI,MAAM,yBAAyB,KAAK,EAAE;AAC/E,sBAAc;AACd,eAAO,QAAA,QAAE;MACX,OAAO;AACL,cAAM,UAAU,sBAAsB,KAAK,KAAK;AAChD,YAAI,CAAC;AAAS,gBAAM,IAAI,MAAM,yBAAyB,KAAK,EAAE;AAC9D,cAAM,KAAa,CAAC,QAAQ,CAAC;AAC7B,sBAAc,QAAQ,CAAC;AACvB,YAAI,gBAAgB,KAAK;AACvB,cAAI,MAAM;AAAW,kBAAM,IAAI,MAAM,SAAS,kBAAkB,EAAE,CAAC;AACnE,iBAAO,YAAY,YAAY,EAAE;QACnC;AACA,YAAI,KAAK;AAAW,gBAAM,IAAI,MAAM,SAAS,QAAQ,EAAE,CAAC;AACxD,eAAO,UAAU,YAAY,EAAE;AAC/B,YAAI,CAAC;AAAa,iBAAO;MAC3B;AAEA,UAAI,OAAO;AACX,YAAM,WAAW,YAAY,MAAM,GAAG;AACtC,iBAAW,WAAW,UAAU;AAC9B,YAAI,SAAS;AACX,kBAAO,GAAA,UAAA,KAAI,IAAI,IAAG,GAAA,UAAA,cAAY,GAAA,OAAA,qBAAoB,OAAO,CAAC,CAAC;AAC3D,kBAAO,GAAA,UAAA,KAAI,IAAI,OAAO,IAAI;QAC5B;MACF;AACA,aAAO;AAEP,eAAS,SAAS,aAAqB,IAAU;AAC/C,eAAO,iBAAiB,WAAW,IAAI,EAAE,gCAAgC,SAAS;MACpF;IACF;AAtCA,YAAA,UAAA;;;;;;;;;AC7hBA,QAAqB,kBAArB,cAA6C,MAAK;MAKhD,YAAY,QAA8B;AACxC,cAAM,mBAAmB;AACzB,aAAK,SAAS;AACd,aAAK,MAAM,KAAK,aAAa;MAC/B;;AATF,YAAA,UAAA;;;;;;;;;ACFA,QAAA,YAAA;AAGA,QAAqB,kBAArB,cAA6C,MAAK;MAIhD,YAAY,UAAuB,QAAgB,KAAa,KAAY;AAC1E,cAAM,OAAO,2BAA2B,GAAG,YAAY,MAAM,EAAE;AAC/D,aAAK,cAAa,GAAA,UAAA,YAAW,UAAU,QAAQ,GAAG;AAClD,aAAK,iBAAgB,GAAA,UAAA,cAAY,GAAA,UAAA,aAAY,UAAU,KAAK,UAAU,CAAC;MACzE;;AARF,YAAA,UAAA;;;;;;;;;;ACOA,QAAA,YAAA;AACA,QAAA,qBAAA;AACA,QAAA,UAAA;AACA,QAAA,YAAA;AACA,QAAA,SAAA;AACA,QAAA,aAAA;AA0DA,QAAa,YAAb,MAAsB;MAkBpB,YAAY,KAAkB;;AATrB,aAAA,OAAmB,CAAA;AACnB,aAAA,iBAA2C,CAAA;AASlD,YAAI;AACJ,YAAI,OAAO,IAAI,UAAU;AAAU,mBAAS,IAAI;AAChD,aAAK,SAAS,IAAI;AAClB,aAAK,WAAW,IAAI;AACpB,aAAK,OAAO,IAAI,QAAQ;AACxB,aAAK,UAAS,KAAA,IAAI,YAAM,QAAA,OAAA,SAAA,MAAI,GAAA,UAAA,aAAY,WAAM,QAAN,WAAM,SAAA,SAAN,OAAS,IAAI,YAAY,KAAK,CAAC;AACvE,aAAK,aAAa,IAAI;AACtB,aAAK,YAAY,IAAI;AACrB,aAAK,OAAO,IAAI;AAChB,aAAK,SAAS,WAAM,QAAN,WAAM,SAAA,SAAN,OAAQ;AACtB,aAAK,OAAO,CAAA;MACd;;AA9BF,YAAA,YAAA;AAqCA,aAAgB,cAAyB,KAAc;AAErD,YAAM,OAAO,mBAAmB,KAAK,MAAM,GAAG;AAC9C,UAAI;AAAM,eAAO;AACjB,YAAM,UAAS,GAAA,UAAA,aAAY,KAAK,KAAK,aAAa,IAAI,KAAK,MAAM;AACjE,YAAM,EAAC,KAAK,MAAK,IAAI,KAAK,KAAK;AAC/B,YAAM,EAAC,cAAa,IAAI,KAAK;AAC7B,YAAM,MAAM,IAAI,UAAA,QAAQ,KAAK,OAAO,EAAC,KAAK,OAAO,cAAa,CAAC;AAC/D,UAAI;AACJ,UAAI,IAAI,QAAQ;AACd,2BAAmB,IAAI,WAAW,SAAS;UACzC,KAAK,mBAAA;UACL,OAAM,GAAA,UAAA;SACP;MACH;AAEA,YAAM,eAAe,IAAI,UAAU,UAAU;AAC7C,UAAI,eAAe;AAEnB,YAAM,YAAuB;QAC3B;QACA,WAAW,KAAK,KAAK;QACrB,MAAM,QAAA,QAAE;QACR,YAAY,QAAA,QAAE;QACd,oBAAoB,QAAA,QAAE;QACtB,WAAW,CAAC,QAAA,QAAE,IAAI;QAClB,aAAa,CAAC,UAAA,GAAG;;QACjB,WAAW;QACX,WAAW,CAAA;QACX,mBAAmB,oBAAI,IAAG;QAC1B,cAAc,IAAI,WAChB,UACA,KAAK,KAAK,KAAK,WAAW,OACtB,EAAC,KAAK,IAAI,QAAQ,OAAM,GAAA,UAAA,WAAU,IAAI,MAAM,EAAC,IAC7C,EAAC,KAAK,IAAI,OAAM,CAAC;QAEvB;QACA,iBAAiB;QACjB,QAAQ,IAAI;QACZ,WAAW;QACX;QACA,QAAQ,IAAI,UAAU;QACtB,YAAY,UAAA;QACZ,eAAe,IAAI,eAAe,KAAK,KAAK,MAAM,KAAK;QACvD,YAAW,GAAA,UAAA;QACX,MAAM,KAAK;QACX,MAAM;;AAGR,UAAI;AACJ,UAAI;AACF,aAAK,cAAc,IAAI,GAAG;AAC1B,SAAA,GAAA,WAAA,sBAAqB,SAAS;AAC9B,YAAI,SAAS,KAAK,KAAK,KAAK,QAAQ;AAEpC,cAAM,eAAe,IAAI,SAAQ;AACjC,qBAAa,GAAG,IAAI,UAAU,QAAA,QAAE,KAAK,CAAC,UAAU,YAAY;AAE5D,YAAI,KAAK,KAAK,KAAK;AAAS,uBAAa,KAAK,KAAK,KAAK,QAAQ,YAAY,GAAG;AAE/E,cAAM,eAAe,IAAI,SAAS,GAAG,QAAA,QAAE,IAAI,IAAI,GAAG,QAAA,QAAE,KAAK,IAAI,UAAU;AACvE,cAAM,WAAgC,aAAa,MAAM,KAAK,MAAM,IAAG,CAAE;AACzE,aAAK,MAAM,MAAM,cAAc,EAAC,KAAK,SAAQ,CAAC;AAE9C,iBAAS,SAAS;AAClB,iBAAS,SAAS,IAAI;AACtB,iBAAS,YAAY;AACrB,YAAI,IAAI;AAAS,mBAAmC,SAAS;AAC7D,YAAI,KAAK,KAAK,KAAK,WAAW,MAAM;AAClC,mBAAS,SAAS,EAAC,cAAc,cAAc,aAAa,IAAI,QAAO;QACzE;AACA,YAAI,KAAK,KAAK,aAAa;AACzB,gBAAM,EAAC,OAAO,MAAK,IAAI;AACvB,mBAAS,YAAY;YACnB,OAAO,iBAAiB,UAAA,OAAO,SAAY;YAC3C,OAAO,iBAAiB,UAAA,OAAO,SAAY;YAC3C,cAAc,iBAAiB,UAAA;YAC/B,cAAc,iBAAiB,UAAA;;AAEjC,cAAI,SAAS;AAAQ,qBAAS,OAAO,aAAY,GAAA,UAAA,WAAU,SAAS,SAAS;QAC/E;AACA,YAAI,WAAW;AACf,eAAO;MACT,SAAS,GAAG;AACV,eAAO,IAAI;AACX,eAAO,IAAI;AACX,YAAI;AAAY,eAAK,OAAO,MAAM,0CAA0C,UAAU;AAEtF,cAAM;MACR;AACE,aAAK,cAAc,OAAO,GAAG;MAC/B;IACF;AA5FA,YAAA,gBAAA;AA8FA,aAAgB,WAEd,MACA,QACA,KAAW;;AAEX,aAAM,GAAA,UAAA,YAAW,KAAK,KAAK,aAAa,QAAQ,GAAG;AACnD,YAAM,YAAY,KAAK,KAAK,GAAG;AAC/B,UAAI;AAAW,eAAO;AAEtB,UAAI,OAAO,QAAQ,KAAK,MAAM,MAAM,GAAG;AACvC,UAAI,SAAS,QAAW;AACtB,cAAM,UAAS,KAAA,KAAK,eAAS,QAAA,OAAA,SAAA,SAAA,GAAG,GAAG;AACnC,cAAM,EAAC,SAAQ,IAAI,KAAK;AACxB,YAAI;AAAQ,iBAAO,IAAI,UAAU,EAAC,QAAQ,UAAU,MAAM,OAAM,CAAC;MACnE;AAEA,UAAI,SAAS;AAAW;AACxB,aAAQ,KAAK,KAAK,GAAG,IAAI,gBAAgB,KAAK,MAAM,IAAI;IAC1D;AAnBA,YAAA,aAAA;AAqBA,aAAS,gBAA2B,KAAc;AAChD,WAAI,GAAA,UAAA,WAAU,IAAI,QAAQ,KAAK,KAAK,UAAU;AAAG,eAAO,IAAI;AAC5D,aAAO,IAAI,WAAW,MAAM,cAAc,KAAK,MAAM,GAAG;IAC1D;AAGA,aAAgB,mBAA8B,QAAiB;AAC7D,iBAAW,OAAO,KAAK,eAAe;AACpC,YAAI,cAAc,KAAK,MAAM;AAAG,iBAAO;MACzC;IACF;AAJA,YAAA,qBAAA;AAMA,aAAS,cAAc,IAAe,IAAa;AACjD,aAAO,GAAG,WAAW,GAAG,UAAU,GAAG,SAAS,GAAG,QAAQ,GAAG,WAAW,GAAG;IAC5E;AAIA,aAAS,QAEP,MACA;AAEA,UAAI;AACJ,aAAO,QAAQ,MAAM,KAAK,KAAK,GAAG,MAAM;AAAU,cAAM;AACxD,aAAO,OAAO,KAAK,QAAQ,GAAG,KAAK,cAAc,KAAK,MAAM,MAAM,GAAG;IACvE;AAGA,aAAgB,cAEd,MACA;AAEA,YAAM,IAAI,KAAK,KAAK,YAAY,MAAM,GAAG;AACzC,YAAM,WAAU,GAAA,UAAA,cAAa,KAAK,KAAK,aAAa,CAAC;AACrD,UAAI,UAAS,GAAA,UAAA,aAAY,KAAK,KAAK,aAAa,KAAK,QAAQ,MAAS;AAEtE,UAAI,OAAO,KAAK,KAAK,MAAM,EAAE,SAAS,KAAK,YAAY,QAAQ;AAC7D,eAAO,eAAe,KAAK,MAAM,GAAG,IAAI;MAC1C;AAEA,YAAM,MAAK,GAAA,UAAA,aAAY,OAAO;AAC9B,YAAM,WAAW,KAAK,KAAK,EAAE,KAAK,KAAK,QAAQ,EAAE;AACjD,UAAI,OAAO,YAAY,UAAU;AAC/B,cAAM,MAAM,cAAc,KAAK,MAAM,MAAM,QAAQ;AACnD,YAAI,QAAO,QAAG,QAAH,QAAG,SAAA,SAAH,IAAK,YAAW;AAAU;AACrC,eAAO,eAAe,KAAK,MAAM,GAAG,GAAG;MACzC;AAEA,UAAI,QAAO,aAAQ,QAAR,aAAQ,SAAA,SAAR,SAAU,YAAW;AAAU;AAC1C,UAAI,CAAC,SAAS;AAAU,sBAAc,KAAK,MAAM,QAAQ;AACzD,UAAI,QAAO,GAAA,UAAA,aAAY,GAAG,GAAG;AAC3B,cAAM,EAAC,OAAM,IAAI;AACjB,cAAM,EAAC,SAAQ,IAAI,KAAK;AACxB,cAAM,QAAQ,OAAO,QAAQ;AAC7B,YAAI;AAAO,oBAAS,GAAA,UAAA,YAAW,KAAK,KAAK,aAAa,QAAQ,KAAK;AACnE,eAAO,IAAI,UAAU,EAAC,QAAQ,UAAU,MAAM,OAAM,CAAC;MACvD;AACA,aAAO,eAAe,KAAK,MAAM,GAAG,QAAQ;IAC9C;AA/BA,YAAA,gBAAA;AAiCA,QAAM,uBAAuB,oBAAI,IAAI;MACnC;MACA;MACA;MACA;MACA;KACD;AAED,aAAS,eAEP,WACA,EAAC,QAAQ,QAAQ,KAAI,GAAY;;AAEjC,YAAI,KAAA,UAAU,cAAQ,QAAA,OAAA,SAAA,SAAA,GAAG,CAAC,OAAM;AAAK;AACrC,iBAAW,QAAQ,UAAU,SAAS,MAAM,CAAC,EAAE,MAAM,GAAG,GAAG;AACzD,YAAI,OAAO,WAAW;AAAW;AACjC,cAAM,aAAa,QAAO,GAAA,OAAA,kBAAiB,IAAI,CAAC;AAChD,YAAI,eAAe;AAAW;AAC9B,iBAAS;AAET,cAAM,QAAQ,OAAO,WAAW,YAAY,OAAO,KAAK,KAAK,QAAQ;AACrE,YAAI,CAAC,qBAAqB,IAAI,IAAI,KAAK,OAAO;AAC5C,oBAAS,GAAA,UAAA,YAAW,KAAK,KAAK,aAAa,QAAQ,KAAK;QAC1D;MACF;AACA,UAAI;AACJ,UAAI,OAAO,UAAU,aAAa,OAAO,QAAQ,EAAC,GAAA,OAAA,sBAAqB,QAAQ,KAAK,KAAK,GAAG;AAC1F,cAAM,QAAO,GAAA,UAAA,YAAW,KAAK,KAAK,aAAa,QAAQ,OAAO,IAAI;AAClE,cAAM,cAAc,KAAK,MAAM,MAAM,IAAI;MAC3C;AAGA,YAAM,EAAC,SAAQ,IAAI,KAAK;AACxB,YAAM,OAAO,IAAI,UAAU,EAAC,QAAQ,UAAU,MAAM,OAAM,CAAC;AAC3D,UAAI,IAAI,WAAW,IAAI,KAAK;AAAQ,eAAO;AAC3C,aAAO;IACT;;;;;ACnUA;AAAA;AAAA;AAAA,MACE,KAAO;AAAA,MACP,aAAe;AAAA,MACf,MAAQ;AAAA,MACR,UAAY,CAAC,OAAO;AAAA,MACpB,YAAc;AAAA,QACZ,OAAS;AAAA,UACP,MAAQ;AAAA,UACR,OAAS,CAAC,EAAC,QAAU,wBAAuB,GAAG,EAAC,QAAU,eAAc,CAAC;AAAA,QAC3E;AAAA,MACF;AAAA,MACA,sBAAwB;AAAA,IAC1B;AAAA;AAAA;;;ACZA;AAAA;AAAA;AAEA,QAAM,MAAM;AAAA,MACV,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAEA,WAAO,UAAU;AAAA,MACf;AAAA,IACF;AAAA;AAAA;;;AC7BA;AAAA;AAAA;AAEA,QAAM,EAAE,IAAI,IAAI;AAEhB,QAAM,WAAW;AAEjB,aAAS,cAAe,MAAM;AAC5B,UAAI,UAAU,MAAM,GAAG,IAAI,GAAG;AAAE,eAAO,EAAE,MAAM,QAAQ,MAAM;AAAA,MAAE;AAC/D,YAAM,UAAU,KAAK,MAAM,QAAQ,KAAK,CAAC;AACzC,YAAM,CAAC,OAAO,IAAI;AAClB,UAAI,SAAS;AACX,eAAO,EAAE,MAAM,kBAAkB,SAAS,GAAG,GAAG,QAAQ,KAAK;AAAA,MAC/D,OAAO;AACL,eAAO,EAAE,MAAM,QAAQ,MAAM;AAAA,MAC/B;AAAA,IACF;AAOA,aAAS,yBAA0B,OAAO,WAAW,OAAO;AAC1D,UAAI,MAAM;AACV,UAAI,QAAQ;AACZ,iBAAW,KAAK,OAAO;AACrB,YAAI,IAAI,CAAC,MAAM,OAAW,QAAO;AACjC,YAAI,MAAM,OAAO,UAAU,KAAM,SAAQ;AACzC,YAAI,CAAC,MAAO,QAAO;AAAA,MACrB;AACA,UAAI,YAAY,IAAI,WAAW,EAAG,OAAM;AACxC,aAAO;AAAA,IACT;AAEA,aAAS,QAAS,OAAO;AACvB,UAAI,aAAa;AACjB,YAAM,SAAS,EAAE,OAAO,OAAO,SAAS,IAAI,MAAM,GAAG;AACrD,YAAM,UAAU,CAAC;AACjB,YAAM,SAAS,CAAC;AAChB,UAAI,SAAS;AACb,UAAI,qBAAqB;AACzB,UAAI,UAAU;AAEd,eAAS,UAAW;AAClB,YAAI,OAAO,QAAQ;AACjB,cAAI,WAAW,OAAO;AACpB,kBAAM,MAAM,yBAAyB,MAAM;AAC3C,gBAAI,QAAQ,QAAW;AACrB,sBAAQ,KAAK,GAAG;AAAA,YAClB,OAAO;AACL,qBAAO,QAAQ;AACf,qBAAO;AAAA,YACT;AAAA,UACF;AACA,iBAAO,SAAS;AAAA,QAClB;AACA,eAAO;AAAA,MACT;AAEA,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,cAAM,SAAS,MAAM,CAAC;AACtB,YAAI,WAAW,OAAO,WAAW,KAAK;AAAE;AAAA,QAAS;AACjD,YAAI,WAAW,KAAK;AAClB,cAAI,uBAAuB,MAAM;AAC/B,sBAAU;AAAA,UACZ;AACA,cAAI,CAAC,QAAQ,GAAG;AAAE;AAAA,UAAM;AACxB;AACA,kBAAQ,KAAK,GAAG;AAChB,cAAI,aAAa,GAAG;AAElB,mBAAO,QAAQ;AACf;AAAA,UACF;AACA,cAAI,IAAI,KAAK,KAAK,MAAM,IAAI,CAAC,MAAM,KAAK;AACtC,iCAAqB;AAAA,UACvB;AACA;AAAA,QACF,WAAW,WAAW,KAAK;AACzB,cAAI,CAAC,QAAQ,GAAG;AAAE;AAAA,UAAM;AAExB,mBAAS;AAAA,QACX,OAAO;AACL,iBAAO,KAAK,MAAM;AAClB;AAAA,QACF;AAAA,MACF;AACA,UAAI,OAAO,QAAQ;AACjB,YAAI,QAAQ;AACV,iBAAO,OAAO,OAAO,KAAK,EAAE;AAAA,QAC9B,WAAW,SAAS;AAClB,kBAAQ,KAAK,OAAO,KAAK,EAAE,CAAC;AAAA,QAC9B,OAAO;AACL,kBAAQ,KAAK,yBAAyB,MAAM,CAAC;AAAA,QAC/C;AAAA,MACF;AACA,aAAO,UAAU,QAAQ,KAAK,EAAE;AAChC,aAAO;AAAA,IACT;AAEA,aAAS,cAAe,MAAM;AAC5B,UAAI,UAAU,MAAM,GAAG,IAAI,GAAG;AAAE,eAAO,EAAE,MAAM,QAAQ,MAAM;AAAA,MAAE;AAC/D,YAAM,OAAO,QAAQ,IAAI;AAEzB,UAAI,CAAC,KAAK,OAAO;AACf,YAAI,UAAU,KAAK;AACnB,YAAI,cAAc,KAAK;AACvB,YAAI,KAAK,MAAM;AACb,qBAAW,MAAM,KAAK;AACtB,yBAAe,QAAQ,KAAK;AAAA,QAC9B;AACA,eAAO,EAAE,MAAM,SAAS,aAAa,QAAQ,KAAK;AAAA,MACpD,OAAO;AACL,eAAO,EAAE,MAAM,QAAQ,MAAM;AAAA,MAC/B;AAAA,IACF;AAEA,aAAS,kBAAmB,KAAK,OAAO;AACtC,UAAI,MAAM;AACV,UAAI,OAAO;AACX,YAAM,IAAI,IAAI;AACd,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,cAAM,IAAI,IAAI,CAAC;AACf,YAAI,MAAM,OAAO,MAAM;AACrB,cAAK,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC,MAAM,SAAU,IAAI,MAAM,GAAG;AACvD,mBAAO;AACP,mBAAO;AAAA,UACT;AAAA,QACF,OAAO;AACL,cAAI,MAAM,OAAO;AACf,mBAAO;AAAA,UACT,OAAO;AACL,mBAAO;AAAA,UACT;AACA,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,aAAS,UAAW,KAAK,OAAO;AAC9B,UAAI,MAAM;AACV,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,YAAI,IAAI,CAAC,MAAM,MAAO;AAAA,MACxB;AACA,aAAO;AAAA,IACT;AAEA,QAAM,OAAO;AACb,QAAM,OAAO;AACb,QAAM,OAAO;AACb,QAAM,OAAO;AAEb,aAAS,kBAAmB,OAAO;AACjC,YAAM,SAAS,CAAC;AAEhB,aAAO,MAAM,QAAQ;AACnB,YAAI,MAAM,MAAM,IAAI,GAAG;AACrB,kBAAQ,MAAM,QAAQ,MAAM,EAAE;AAAA,QAChC,WAAW,MAAM,MAAM,IAAI,GAAG;AAC5B,kBAAQ,MAAM,QAAQ,MAAM,GAAG;AAAA,QACjC,WAAW,MAAM,MAAM,IAAI,GAAG;AAC5B,kBAAQ,MAAM,QAAQ,MAAM,GAAG;AAC/B,iBAAO,IAAI;AAAA,QACb,WAAW,UAAU,OAAO,UAAU,MAAM;AAC1C,kBAAQ;AAAA,QACV,OAAO;AACL,gBAAM,KAAK,MAAM,MAAM,IAAI;AAC3B,cAAI,IAAI;AACN,kBAAM,IAAI,GAAG,CAAC;AACd,oBAAQ,MAAM,MAAM,EAAE,MAAM;AAC5B,mBAAO,KAAK,CAAC;AAAA,UACf,OAAO;AACL,kBAAM,IAAI,MAAM,kCAAkC;AAAA,UACpD;AAAA,QACF;AAAA,MACF;AACA,aAAO,OAAO,KAAK,EAAE;AAAA,IACvB;AAEA,aAAS,2BAA4B,YAAY,KAAK;AACpD,YAAM,OAAO,QAAQ,OAAO,SAAS;AACrC,UAAI,WAAW,WAAW,QAAW;AACnC,mBAAW,SAAS,KAAK,WAAW,MAAM;AAAA,MAC5C;AACA,UAAI,WAAW,aAAa,QAAW;AACrC,mBAAW,WAAW,KAAK,WAAW,QAAQ;AAAA,MAChD;AACA,UAAI,WAAW,SAAS,QAAW;AACjC,mBAAW,OAAO,KAAK,WAAW,IAAI;AAAA,MACxC;AACA,UAAI,WAAW,SAAS,QAAW;AACjC,mBAAW,OAAO,KAAK,WAAW,IAAI;AAAA,MACxC;AACA,UAAI,WAAW,UAAU,QAAW;AAClC,mBAAW,QAAQ,KAAK,WAAW,KAAK;AAAA,MAC1C;AACA,UAAI,WAAW,aAAa,QAAW;AACrC,mBAAW,WAAW,KAAK,WAAW,QAAQ;AAAA,MAChD;AACA,aAAO;AAAA,IACT;AAEA,aAAS,mBAAoB,YAAY;AACvC,YAAM,YAAY,CAAC;AAEnB,UAAI,WAAW,aAAa,QAAW;AACrC,kBAAU,KAAK,WAAW,QAAQ;AAClC,kBAAU,KAAK,GAAG;AAAA,MACpB;AAEA,UAAI,WAAW,SAAS,QAAW;AACjC,YAAI,OAAO,SAAS,WAAW,IAAI;AACnC,cAAM,UAAU,cAAc,IAAI;AAElC,YAAI,QAAQ,QAAQ;AAClB,iBAAO,QAAQ;AAAA,QACjB,OAAO;AACL,gBAAM,UAAU,cAAc,QAAQ,IAAI;AAC1C,cAAI,QAAQ,WAAW,MAAM;AAC3B,mBAAO,IAAI,QAAQ,WAAW;AAAA,UAChC,OAAO;AACL,mBAAO,WAAW;AAAA,UACpB;AAAA,QACF;AACA,kBAAU,KAAK,IAAI;AAAA,MACrB;AAEA,UAAI,OAAO,WAAW,SAAS,YAAY,OAAO,WAAW,SAAS,UAAU;AAC9E,kBAAU,KAAK,GAAG;AAClB,kBAAU,KAAK,OAAO,WAAW,IAAI,CAAC;AAAA,MACxC;AAEA,aAAO,UAAU,SAAS,UAAU,KAAK,EAAE,IAAI;AAAA,IACjD;AAEA,WAAO,UAAU;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA;AAAA;;;ACnPA;AAAA;AAAA;AAEA,QAAM,WAAW;AACjB,QAAM,UAAU;AAEhB,aAAS,SAAU,cAAc;AAC/B,aAAO,OAAO,aAAa,WAAW,YAAY,aAAa,SAAS,OAAO,aAAa,MAAM,EAAE,YAAY,MAAM;AAAA,IACxH;AAEA,aAAS,UAAW,YAAY;AAC9B,UAAI,CAAC,WAAW,MAAM;AACpB,mBAAW,QAAQ,WAAW,SAAS;AAAA,MACzC;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,cAAe,YAAY;AAClC,YAAM,SAAS,OAAO,WAAW,MAAM,EAAE,YAAY,MAAM;AAG3D,UAAI,WAAW,UAAU,SAAS,MAAM,OAAO,WAAW,SAAS,IAAI;AACrE,mBAAW,OAAO;AAAA,MACpB;AAGA,UAAI,CAAC,WAAW,MAAM;AACpB,mBAAW,OAAO;AAAA,MACpB;AAMA,aAAO;AAAA,IACT;AAEA,aAAS,QAAS,cAAc;AAE9B,mBAAa,SAAS,SAAS,YAAY;AAG3C,mBAAa,gBAAgB,aAAa,QAAQ,QAAQ,aAAa,QAAQ,MAAM,aAAa,QAAQ;AAC1G,mBAAa,OAAO;AACpB,mBAAa,QAAQ;AAErB,aAAO;AAAA,IACT;AAEA,aAAS,YAAa,cAAc;AAElC,UAAI,aAAa,UAAU,SAAS,YAAY,IAAI,MAAM,OAAO,aAAa,SAAS,IAAI;AACzF,qBAAa,OAAO;AAAA,MACtB;AAGA,UAAI,OAAO,aAAa,WAAW,WAAW;AAC5C,qBAAa,SAAU,aAAa,SAAS,QAAQ;AACrD,qBAAa,SAAS;AAAA,MACxB;AAGA,UAAI,aAAa,cAAc;AAC7B,cAAM,CAAC,MAAM,KAAK,IAAI,aAAa,aAAa,MAAM,GAAG;AACzD,qBAAa,OAAQ,QAAQ,SAAS,MAAM,OAAO;AACnD,qBAAa,QAAQ;AACrB,qBAAa,eAAe;AAAA,MAC9B;AAGA,mBAAa,WAAW;AAExB,aAAO;AAAA,IACT;AAEA,aAAS,SAAU,eAAe,SAAS;AACzC,UAAI,CAAC,cAAc,MAAM;AACvB,sBAAc,QAAQ;AACtB,eAAO;AAAA,MACT;AACA,YAAM,UAAU,cAAc,KAAK,MAAM,OAAO;AAChD,UAAI,SAAS;AACX,cAAM,SAAS,QAAQ,UAAU,cAAc,UAAU;AACzD,sBAAc,MAAM,QAAQ,CAAC,EAAE,YAAY;AAC3C,sBAAc,MAAM,QAAQ,CAAC;AAC7B,cAAM,YAAY,GAAG,MAAM,IAAI,QAAQ,OAAO,cAAc,GAAG;AAC/D,cAAM,gBAAgB,QAAQ,SAAS;AACvC,sBAAc,OAAO;AAErB,YAAI,eAAe;AACjB,0BAAgB,cAAc,MAAM,eAAe,OAAO;AAAA,QAC5D;AAAA,MACF,OAAO;AACL,sBAAc,QAAQ,cAAc,SAAS;AAAA,MAC/C;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,aAAc,eAAe,SAAS;AAC7C,YAAM,SAAS,QAAQ,UAAU,cAAc,UAAU;AACzD,YAAM,MAAM,cAAc,IAAI,YAAY;AAC1C,YAAM,YAAY,GAAG,MAAM,IAAI,QAAQ,OAAO,GAAG;AACjD,YAAM,gBAAgB,QAAQ,SAAS;AAEvC,UAAI,eAAe;AACjB,wBAAgB,cAAc,UAAU,eAAe,OAAO;AAAA,MAChE;AAEA,YAAM,gBAAgB;AACtB,YAAM,MAAM,cAAc;AAC1B,oBAAc,OAAO,GAAG,OAAO,QAAQ,GAAG,IAAI,GAAG;AAEjD,cAAQ,aAAa;AACrB,aAAO;AAAA,IACT;AAEA,aAAS,aAAc,eAAe,SAAS;AAC7C,YAAM,iBAAiB;AACvB,qBAAe,OAAO,eAAe;AACrC,qBAAe,MAAM;AAErB,UAAI,CAAC,QAAQ,aAAa,CAAC,eAAe,QAAQ,CAAC,SAAS,KAAK,eAAe,IAAI,IAAI;AACtF,uBAAe,QAAQ,eAAe,SAAS;AAAA,MACjD;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,iBAAkB,gBAAgB;AACzC,YAAM,gBAAgB;AAEtB,oBAAc,OAAO,eAAe,QAAQ,IAAI,YAAY;AAC5D,aAAO;AAAA,IACT;AAEA,QAAM,OAAO;AAAA,MACX,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,WAAW;AAAA,IACb;AAEA,QAAM,QAAQ;AAAA,MACZ,QAAQ;AAAA,MACR,YAAY,KAAK;AAAA,MACjB,OAAO;AAAA,MACP,WAAW;AAAA,IACb;AAEA,QAAM,KAAK;AAAA,MACT,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,WAAW;AAAA,IACb;AAEA,QAAM,MAAM;AAAA,MACV,QAAQ;AAAA,MACR,YAAY,GAAG;AAAA,MACf,OAAO,GAAG;AAAA,MACV,WAAW,GAAG;AAAA,IAChB;AAEA,QAAM,MAAM;AAAA,MACV,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,WAAW;AAAA,MACX,eAAe;AAAA,IACjB;AAEA,QAAM,UAAU;AAAA,MACd,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,WAAW;AAAA,MACX,eAAe;AAAA,IACjB;AAEA,QAAM,UAAU;AAAA,MACd;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,YAAY;AAAA,IACd;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC3LjB;AAAA;AAAA;AAEA,QAAM,EAAE,eAAe,eAAe,mBAAmB,oBAAoB,2BAA2B,IAAI;AAC5G,QAAM,UAAU;AAEhB,aAAS,UAAW,KAAK,SAAS;AAChC,UAAI,OAAO,QAAQ,UAAU;AAC3B,cAAM,UAAU,MAAM,KAAK,OAAO,GAAG,OAAO;AAAA,MAC9C,WAAW,OAAO,QAAQ,UAAU;AAClC,cAAM,MAAM,UAAU,KAAK,OAAO,GAAG,OAAO;AAAA,MAC9C;AACA,aAAO;AAAA,IACT;AAEA,aAAS,QAAS,SAAS,aAAa,SAAS;AAC/C,YAAM,oBAAoB,OAAO,OAAO,EAAE,QAAQ,OAAO,GAAG,OAAO;AACnE,YAAM,WAAW,kBAAkB,MAAM,SAAS,iBAAiB,GAAG,MAAM,aAAa,iBAAiB,GAAG,mBAAmB,IAAI;AACpI,aAAO,UAAU,UAAU,EAAE,GAAG,mBAAmB,YAAY,KAAK,CAAC;AAAA,IACvE;AAEA,aAAS,kBAAmB,MAAM,UAAU,SAAS,mBAAmB;AACtE,YAAM,SAAS,CAAC;AAChB,UAAI,CAAC,mBAAmB;AACtB,eAAO,MAAM,UAAU,MAAM,OAAO,GAAG,OAAO;AAC9C,mBAAW,MAAM,UAAU,UAAU,OAAO,GAAG,OAAO;AAAA,MACxD;AACA,gBAAU,WAAW,CAAC;AAEtB,UAAI,CAAC,QAAQ,YAAY,SAAS,QAAQ;AACxC,eAAO,SAAS,SAAS;AAEzB,eAAO,WAAW,SAAS;AAC3B,eAAO,OAAO,SAAS;AACvB,eAAO,OAAO,SAAS;AACvB,eAAO,OAAO,kBAAkB,SAAS,QAAQ,EAAE;AACnD,eAAO,QAAQ,SAAS;AAAA,MAC1B,OAAO;AACL,YAAI,SAAS,aAAa,UAAa,SAAS,SAAS,UAAa,SAAS,SAAS,QAAW;AAEjG,iBAAO,WAAW,SAAS;AAC3B,iBAAO,OAAO,SAAS;AACvB,iBAAO,OAAO,SAAS;AACvB,iBAAO,OAAO,kBAAkB,SAAS,QAAQ,EAAE;AACnD,iBAAO,QAAQ,SAAS;AAAA,QAC1B,OAAO;AACL,cAAI,CAAC,SAAS,MAAM;AAClB,mBAAO,OAAO,KAAK;AACnB,gBAAI,SAAS,UAAU,QAAW;AAChC,qBAAO,QAAQ,SAAS;AAAA,YAC1B,OAAO;AACL,qBAAO,QAAQ,KAAK;AAAA,YACtB;AAAA,UACF,OAAO;AACL,gBAAI,SAAS,KAAK,OAAO,CAAC,MAAM,KAAK;AACnC,qBAAO,OAAO,kBAAkB,SAAS,IAAI;AAAA,YAC/C,OAAO;AACL,mBAAK,KAAK,aAAa,UAAa,KAAK,SAAS,UAAa,KAAK,SAAS,WAAc,CAAC,KAAK,MAAM;AACrG,uBAAO,OAAO,MAAM,SAAS;AAAA,cAC/B,WAAW,CAAC,KAAK,MAAM;AACrB,uBAAO,OAAO,SAAS;AAAA,cACzB,OAAO;AACL,uBAAO,OAAO,KAAK,KAAK,MAAM,GAAG,KAAK,KAAK,YAAY,GAAG,IAAI,CAAC,IAAI,SAAS;AAAA,cAC9E;AACA,qBAAO,OAAO,kBAAkB,OAAO,IAAI;AAAA,YAC7C;AACA,mBAAO,QAAQ,SAAS;AAAA,UAC1B;AAEA,iBAAO,WAAW,KAAK;AACvB,iBAAO,OAAO,KAAK;AACnB,iBAAO,OAAO,KAAK;AAAA,QACrB;AACA,eAAO,SAAS,KAAK;AAAA,MACvB;AAEA,aAAO,WAAW,SAAS;AAE3B,aAAO;AAAA,IACT;AAEA,aAAS,MAAO,MAAM,MAAM,SAAS;AACnC,UAAI,OAAO,SAAS,UAAU;AAC5B,eAAO,SAAS,IAAI;AACpB,eAAO,UAAU,2BAA2B,MAAM,MAAM,OAAO,GAAG,IAAI,GAAG,EAAE,GAAG,SAAS,YAAY,KAAK,CAAC;AAAA,MAC3G,WAAW,OAAO,SAAS,UAAU;AACnC,eAAO,UAAU,2BAA2B,MAAM,IAAI,GAAG,EAAE,GAAG,SAAS,YAAY,KAAK,CAAC;AAAA,MAC3F;AAEA,UAAI,OAAO,SAAS,UAAU;AAC5B,eAAO,SAAS,IAAI;AACpB,eAAO,UAAU,2BAA2B,MAAM,MAAM,OAAO,GAAG,IAAI,GAAG,EAAE,GAAG,SAAS,YAAY,KAAK,CAAC;AAAA,MAC3G,WAAW,OAAO,SAAS,UAAU;AACnC,eAAO,UAAU,2BAA2B,MAAM,IAAI,GAAG,EAAE,GAAG,SAAS,YAAY,KAAK,CAAC;AAAA,MAC3F;AAEA,aAAO,KAAK,YAAY,MAAM,KAAK,YAAY;AAAA,IACjD;AAEA,aAAS,UAAW,OAAO,MAAM;AAC/B,YAAM,aAAa;AAAA,QACjB,MAAM,MAAM;AAAA,QACZ,QAAQ,MAAM;AAAA,QACd,UAAU,MAAM;AAAA,QAChB,MAAM,MAAM;AAAA,QACZ,MAAM,MAAM;AAAA,QACZ,OAAO,MAAM;AAAA,QACb,KAAK,MAAM;AAAA,QACX,KAAK,MAAM;AAAA,QACX,MAAM,MAAM;AAAA,QACZ,UAAU,MAAM;AAAA,QAChB,WAAW,MAAM;AAAA,QACjB,cAAc,MAAM;AAAA,QACpB,QAAQ,MAAM;AAAA,QACd,OAAO;AAAA,MACT;AACA,YAAM,UAAU,OAAO,OAAO,CAAC,GAAG,IAAI;AACtC,YAAM,YAAY,CAAC;AAGnB,YAAM,gBAAgB,SAAS,QAAQ,UAAU,WAAW,UAAU,IAAI,YAAY,CAAC;AAGvF,UAAI,iBAAiB,cAAc,UAAW,eAAc,UAAU,YAAY,OAAO;AAEzF,UAAI,WAAW,SAAS,QAAW;AACjC,YAAI,CAAC,QAAQ,YAAY;AACvB,qBAAW,OAAO,OAAO,WAAW,IAAI;AAExC,cAAI,WAAW,WAAW,QAAW;AACnC,uBAAW,OAAO,WAAW,KAAK,MAAM,KAAK,EAAE,KAAK,GAAG;AAAA,UACzD;AAAA,QACF,OAAO;AACL,qBAAW,OAAO,SAAS,WAAW,IAAI;AAAA,QAC5C;AAAA,MACF;AAEA,UAAI,QAAQ,cAAc,YAAY,WAAW,QAAQ;AACvD,kBAAU,KAAK,WAAW,QAAQ,GAAG;AAAA,MACvC;AAEA,YAAM,YAAY,mBAAmB,UAAU;AAC/C,UAAI,cAAc,QAAW;AAC3B,YAAI,QAAQ,cAAc,UAAU;AAClC,oBAAU,KAAK,IAAI;AAAA,QACrB;AAEA,kBAAU,KAAK,SAAS;AAExB,YAAI,WAAW,QAAQ,WAAW,KAAK,OAAO,CAAC,MAAM,KAAK;AACxD,oBAAU,KAAK,GAAG;AAAA,QACpB;AAAA,MACF;AACA,UAAI,WAAW,SAAS,QAAW;AACjC,YAAI,IAAI,WAAW;AAEnB,YAAI,CAAC,QAAQ,iBAAiB,CAAC,iBAAiB,CAAC,cAAc,eAAe;AAC5E,cAAI,kBAAkB,CAAC;AAAA,QACzB;AAEA,YAAI,cAAc,QAAW;AAC3B,cAAI,EAAE,QAAQ,UAAU,MAAM;AAAA,QAChC;AAEA,kBAAU,KAAK,CAAC;AAAA,MAClB;AAEA,UAAI,WAAW,UAAU,QAAW;AAClC,kBAAU,KAAK,KAAK,WAAW,KAAK;AAAA,MACtC;AAEA,UAAI,WAAW,aAAa,QAAW;AACrC,kBAAU,KAAK,KAAK,WAAW,QAAQ;AAAA,MACzC;AACA,aAAO,UAAU,KAAK,EAAE;AAAA,IAC1B;AAEA,QAAM,YAAY,MAAM,KAAK,EAAE,QAAQ,IAAI,GAAG,CAAC,IAAI,MAAM,8BAA8B,KAAK,OAAO,aAAa,CAAC,CAAC,CAAC;AAEnH,aAAS,gBAAiB,OAAO;AAC/B,UAAI,OAAO;AACX,eAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,EAAE,GAAG;AAChD,eAAO,MAAM,WAAW,CAAC;AACzB,YAAI,OAAO,OAAO,UAAU,IAAI,GAAG;AACjC,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,QAAM,YAAY;AAElB,aAAS,MAAO,KAAK,MAAM;AACzB,YAAM,UAAU,OAAO,OAAO,CAAC,GAAG,IAAI;AACtC,YAAM,SAAS;AAAA,QACb,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,OAAO;AAAA,QACP,UAAU;AAAA,MACZ;AACA,YAAM,cAAc,IAAI,QAAQ,GAAG,MAAM;AACzC,UAAI,OAAO;AACX,UAAI,QAAQ,cAAc,SAAU,QAAO,QAAQ,SAAS,QAAQ,SAAS,MAAM,MAAM,OAAO;AAEhG,YAAM,UAAU,IAAI,MAAM,SAAS;AAEnC,UAAI,SAAS;AAEX,eAAO,SAAS,QAAQ,CAAC;AACzB,eAAO,WAAW,QAAQ,CAAC;AAC3B,eAAO,OAAO,QAAQ,CAAC;AACvB,eAAO,OAAO,SAAS,QAAQ,CAAC,GAAG,EAAE;AACrC,eAAO,OAAO,QAAQ,CAAC,KAAK;AAC5B,eAAO,QAAQ,QAAQ,CAAC;AACxB,eAAO,WAAW,QAAQ,CAAC;AAG3B,YAAI,MAAM,OAAO,IAAI,GAAG;AACtB,iBAAO,OAAO,QAAQ,CAAC;AAAA,QACzB;AACA,YAAI,OAAO,MAAM;AACf,gBAAM,aAAa,cAAc,OAAO,IAAI;AAC5C,cAAI,WAAW,WAAW,OAAO;AAC/B,kBAAM,aAAa,cAAc,WAAW,IAAI;AAChD,mBAAO,OAAO,WAAW,KAAK,YAAY;AAC1C,mBAAO,WAAW;AAAA,UACpB,OAAO;AACL,mBAAO,OAAO,WAAW;AACzB,mBAAO;AAAA,UACT;AAAA,QACF;AACA,YAAI,OAAO,WAAW,UAAa,OAAO,aAAa,UAAa,OAAO,SAAS,UAAa,OAAO,SAAS,UAAa,OAAO,UAAU,UAAa,CAAC,OAAO,MAAM;AACxK,iBAAO,YAAY;AAAA,QACrB,WAAW,OAAO,WAAW,QAAW;AACtC,iBAAO,YAAY;AAAA,QACrB,WAAW,OAAO,aAAa,QAAW;AACxC,iBAAO,YAAY;AAAA,QACrB,OAAO;AACL,iBAAO,YAAY;AAAA,QACrB;AAGA,YAAI,QAAQ,aAAa,QAAQ,cAAc,YAAY,QAAQ,cAAc,OAAO,WAAW;AACjG,iBAAO,QAAQ,OAAO,SAAS,kBAAkB,QAAQ,YAAY;AAAA,QACvE;AAGA,cAAM,gBAAgB,SAAS,QAAQ,UAAU,OAAO,UAAU,IAAI,YAAY,CAAC;AAGnF,YAAI,CAAC,QAAQ,mBAAmB,CAAC,iBAAiB,CAAC,cAAc,iBAAiB;AAEhF,cAAI,OAAO,SAAS,QAAQ,cAAe,iBAAiB,cAAc,eAAgB,SAAS,SAAS,gBAAgB,OAAO,IAAI,GAAG;AAExI,gBAAI;AACF,qBAAO,OAAO,IAAI,cAAc,OAAO,KAAK,YAAY,CAAC;AAAA,YAC3D,SAAS,GAAG;AACV,qBAAO,QAAQ,OAAO,SAAS,uDAAuD;AAAA,YACxF;AAAA,UACF;AAAA,QAEF;AAEA,YAAI,CAAC,iBAAkB,iBAAiB,CAAC,cAAc,eAAgB;AACrE,cAAI,eAAe,OAAO,WAAW,QAAW;AAC9C,mBAAO,SAAS,SAAS,OAAO,MAAM;AAAA,UACxC;AACA,cAAI,eAAe,OAAO,SAAS,QAAW;AAC5C,mBAAO,OAAO,SAAS,OAAO,IAAI;AAAA,UACpC;AACA,cAAI,OAAO,MAAM;AACf,mBAAO,OAAO,OAAO,SAAS,OAAO,IAAI,CAAC;AAAA,UAC5C;AACA,cAAI,OAAO,UAAU;AACnB,mBAAO,WAAW,UAAU,mBAAmB,OAAO,QAAQ,CAAC;AAAA,UACjE;AAAA,QACF;AAGA,YAAI,iBAAiB,cAAc,OAAO;AACxC,wBAAc,MAAM,QAAQ,OAAO;AAAA,QACrC;AAAA,MACF,OAAO;AACL,eAAO,QAAQ,OAAO,SAAS;AAAA,MACjC;AACA,aAAO;AAAA,IACT;AAEA,QAAM,UAAU;AAAA,MACd;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,WAAO,UAAU;AACjB,WAAO,QAAQ,UAAU;AACzB,WAAO,QAAQ,UAAU;AAAA;AAAA;;;;;;;AC9SzB,QAAA,MAAA;AAGE,QAAY,OAAO;AAErB,YAAA,UAAe;;;;;;;;;;ACuBf,QAAA,aAAA;AAAQ,WAAA,eAAA,SAAA,cAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAAA,WAAA;IAAU,EAAA,CAAA;AAKlB,QAAA,YAAA;AAAQ,WAAA,eAAA,SAAA,KAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAAA,UAAA;IAAC,EAAA,CAAA;AAAE,WAAA,eAAA,SAAA,OAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAAA,UAAA;IAAG,EAAA,CAAA;AAAE,WAAA,eAAA,SAAA,aAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAAA,UAAA;IAAS,EAAA,CAAA;AAAE,WAAA,eAAA,SAAA,OAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAAA,UAAA;IAAG,EAAA,CAAA;AAAE,WAAA,eAAA,SAAA,QAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAAA,UAAA;IAAI,EAAA,CAAA;AAAQ,WAAA,eAAA,SAAA,WAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAAA,UAAA;IAAO,EAAA,CAAA;AAsBnD,QAAA,qBAAA;AACA,QAAA,cAAA;AACA,QAAA,UAAA;AACA,QAAA,YAAA;AACA,QAAA,YAAA;AACA,QAAA,YAAA;AACA,QAAA,aAAA;AACA,QAAA,SAAA;AACA,QAAA,iBAAA;AAEA,QAAA,QAAA;AAEA,QAAM,gBAA8B,CAAC,KAAK,UAAU,IAAI,OAAO,KAAK,KAAK;AACzE,kBAAc,OAAO;AAErB,QAAM,sBAAyC,CAAC,oBAAoB,eAAe,aAAa;AAChG,QAAM,kBAAkB,oBAAI,IAAI;MAC9B;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;KACD;AAyGD,QAAM,iBAA8C;MAClD,eAAe;MACf,QAAQ;MACR,UAAU;MACV,cAAc;MACd,YAAY;MACZ,aAAa;MACb,aAAa;MACb,YAAY;MACZ,gBAAgB;MAChB,gBAAgB;MAChB,aAAa;MACb,gBAAgB;MAChB,OAAO;MACP,WAAW;MACX,WAAW;;AAGb,QAAM,oBAAoD;MACxD,uBAAuB;MACvB,kBAAkB;MAClB,SAAS;;AA0BX,QAAM,iBAAiB;AAGvB,aAAS,gBAAgB,GAAU;;AACjC,YAAM,IAAI,EAAE;AACZ,YAAM,SAAQ,KAAA,EAAE,UAAI,QAAA,OAAA,SAAA,SAAA,GAAE;AACtB,YAAM,WAAW,UAAU,QAAQ,UAAU,SAAY,IAAI,SAAS;AACtE,YAAM,UAAS,MAAA,KAAA,EAAE,UAAI,QAAA,OAAA,SAAA,SAAA,GAAE,YAAM,QAAA,OAAA,SAAA,KAAI;AACjC,YAAM,eAAc,KAAA,EAAE,iBAAW,QAAA,OAAA,SAAA,KAAI,MAAA;AACrC,aAAO;QACL,eAAc,MAAA,KAAA,EAAE,kBAAY,QAAA,OAAA,SAAA,KAAI,OAAC,QAAA,OAAA,SAAA,KAAI;QACrC,gBAAe,MAAA,KAAA,EAAE,mBAAa,QAAA,OAAA,SAAA,KAAI,OAAC,QAAA,OAAA,SAAA,KAAI;QACvC,cAAa,MAAA,KAAA,EAAE,iBAAW,QAAA,OAAA,SAAA,KAAI,OAAC,QAAA,OAAA,SAAA,KAAI;QACnC,eAAc,MAAA,KAAA,EAAE,kBAAY,QAAA,OAAA,SAAA,KAAI,OAAC,QAAA,OAAA,SAAA,KAAI;QACrC,iBAAgB,MAAA,KAAA,EAAE,oBAAc,QAAA,OAAA,SAAA,KAAI,OAAC,QAAA,OAAA,SAAA,KAAI;QACzC,MAAM,EAAE,OAAO,EAAC,GAAG,EAAE,MAAM,UAAU,OAAM,IAAI,EAAC,UAAU,OAAM;QAChE,eAAc,KAAA,EAAE,kBAAY,QAAA,OAAA,SAAA,KAAI;QAChC,WAAU,KAAA,EAAE,cAAQ,QAAA,OAAA,SAAA,KAAI;QACxB,OAAM,KAAA,EAAE,UAAI,QAAA,OAAA,SAAA,KAAI;QAChB,WAAU,KAAA,EAAE,cAAQ,QAAA,OAAA,SAAA,KAAI;QACxB,aAAY,KAAA,EAAE,gBAAU,QAAA,OAAA,SAAA,KAAI;QAC5B,WAAU,KAAA,EAAE,cAAQ,QAAA,OAAA,SAAA,KAAI;QACxB,gBAAe,KAAA,EAAE,mBAAa,QAAA,OAAA,SAAA,KAAI;QAClC,iBAAgB,KAAA,EAAE,oBAAc,QAAA,OAAA,SAAA,KAAI;QACpC,kBAAiB,KAAA,EAAE,qBAAe,QAAA,OAAA,SAAA,KAAI;QACtC,gBAAe,KAAA,EAAE,mBAAa,QAAA,OAAA,SAAA,KAAI;QAClC,aAAY,KAAA,EAAE,gBAAU,QAAA,OAAA,SAAA,KAAI;QAC5B;;IAEJ;AAQA,QAAqB,MAArB,MAAwB;MAkBtB,YAAY,OAAgB,CAAA,GAAE;AAZrB,aAAA,UAAyC,CAAA;AACzC,aAAA,OAA+C,CAAA;AAC/C,aAAA,UAA4C,CAAA;AAE5C,aAAA,gBAAgC,oBAAI,IAAG;AAC/B,aAAA,WAAyD,CAAA;AACzD,aAAA,SAAoC,oBAAI,IAAG;AAO1D,eAAO,KAAK,OAAO,EAAC,GAAG,MAAM,GAAG,gBAAgB,IAAI,EAAC;AACrD,cAAM,EAAC,KAAK,MAAK,IAAI,KAAK,KAAK;AAE/B,aAAK,QAAQ,IAAI,UAAA,WAAW,EAAC,OAAO,CAAA,GAAI,UAAU,iBAAiB,KAAK,MAAK,CAAC;AAC9E,aAAK,SAAS,UAAU,KAAK,MAAM;AACnC,cAAM,YAAY,KAAK;AACvB,aAAK,kBAAkB;AAEvB,aAAK,SAAQ,GAAA,QAAA,UAAQ;AACrB,qBAAa,KAAK,MAAM,gBAAgB,MAAM,eAAe;AAC7D,qBAAa,KAAK,MAAM,mBAAmB,MAAM,cAAc,MAAM;AACrE,aAAK,YAAY,qBAAqB,KAAK,IAAI;AAE/C,YAAI,KAAK;AAAS,4BAAkB,KAAK,IAAI;AAC7C,aAAK,iBAAgB;AACrB,aAAK,sBAAqB;AAC1B,YAAI,KAAK;AAAU,6BAAmB,KAAK,MAAM,KAAK,QAAQ;AAC9D,YAAI,OAAO,KAAK,QAAQ;AAAU,eAAK,cAAc,KAAK,IAAI;AAC9D,0BAAkB,KAAK,IAAI;AAC3B,aAAK,kBAAkB;MACzB;MAEA,mBAAgB;AACd,aAAK,WAAW,QAAQ;MAC1B;MAEA,wBAAqB;AACnB,cAAM,EAAC,OAAO,MAAM,SAAQ,IAAI,KAAK;AACrC,YAAI,iBAA+B;AACnC,YAAI,aAAa,MAAM;AACrB,2BAAiB,EAAC,GAAG,eAAc;AACnC,yBAAe,KAAK,eAAe;AACnC,iBAAO,eAAe;QACxB;AACA,YAAI,QAAQ;AAAO,eAAK,cAAc,gBAAgB,eAAe,QAAQ,GAAG,KAAK;MACvF;MAEA,cAAW;AACT,cAAM,EAAC,MAAM,SAAQ,IAAI,KAAK;AAC9B,eAAQ,KAAK,KAAK,cAAc,OAAO,QAAQ,WAAW,KAAK,QAAQ,KAAK,OAAO;MACrF;MAoBA,SACE,cAEA;AAEA,YAAI;AACJ,YAAI,OAAO,gBAAgB,UAAU;AACnC,cAAI,KAAK,UAAa,YAAY;AAClC,cAAI,CAAC;AAAG,kBAAM,IAAI,MAAM,8BAA8B,YAAY,GAAG;QACvE,OAAO;AACL,cAAI,KAAK,QAAW,YAAY;QAClC;AAEA,cAAM,QAAQ,EAAE,IAAI;AACpB,YAAI,EAAE,YAAY;AAAI,eAAK,SAAS,EAAE;AACtC,eAAO;MACT;MAiBA,QAAqB,QAAmB,OAAe;AACrD,cAAM,MAAM,KAAK,WAAW,QAAQ,KAAK;AACzC,eAAQ,IAAI,YAAY,KAAK,kBAAkB,GAAG;MACpD;MAmBA,aACE,QACA,MAAc;AAEd,YAAI,OAAO,KAAK,KAAK,cAAc,YAAY;AAC7C,gBAAM,IAAI,MAAM,yCAAyC;QAC3D;AACA,cAAM,EAAC,WAAU,IAAI,KAAK;AAC1B,eAAO,gBAAgB,KAAK,MAAM,QAAQ,IAAI;AAE9C,uBAAe,gBAEb,SACA,OAAe;AAEf,gBAAM,eAAe,KAAK,MAAM,QAAQ,OAAO;AAC/C,gBAAM,MAAM,KAAK,WAAW,SAAS,KAAK;AAC1C,iBAAO,IAAI,YAAY,cAAc,KAAK,MAAM,GAAG;QACrD;AAEA,uBAAe,eAA0B,MAAa;AACpD,cAAI,QAAQ,CAAC,KAAK,UAAU,IAAI,GAAG;AACjC,kBAAM,gBAAgB,KAAK,MAAM,EAAC,KAAI,GAAG,IAAI;UAC/C;QACF;AAEA,uBAAe,cAAyB,KAAc;AACpD,cAAI;AACF,mBAAO,KAAK,kBAAkB,GAAG;UACnC,SAAS,GAAG;AACV,gBAAI,EAAE,aAAa,YAAA;AAAkB,oBAAM;AAC3C,wBAAY,KAAK,MAAM,CAAC;AACxB,kBAAM,kBAAkB,KAAK,MAAM,EAAE,aAAa;AAClD,mBAAO,cAAc,KAAK,MAAM,GAAG;UACrC;QACF;AAEA,iBAAS,YAAuB,EAAC,eAAe,KAAK,WAAU,GAAkB;AAC/E,cAAI,KAAK,KAAK,GAAG,GAAG;AAClB,kBAAM,IAAI,MAAM,aAAa,GAAG,kBAAkB,UAAU,qBAAqB;UACnF;QACF;AAEA,uBAAe,kBAA6B,KAAW;AACrD,gBAAM,UAAU,MAAM,YAAY,KAAK,MAAM,GAAG;AAChD,cAAI,CAAC,KAAK,KAAK,GAAG;AAAG,kBAAM,eAAe,KAAK,MAAM,QAAQ,OAAO;AACpE,cAAI,CAAC,KAAK,KAAK,GAAG;AAAG,iBAAK,UAAU,SAAS,KAAK,IAAI;QACxD;AAEA,uBAAe,YAAuB,KAAW;AAC/C,gBAAM,IAAI,KAAK,SAAS,GAAG;AAC3B,cAAI;AAAG,mBAAO;AACd,cAAI;AACF,mBAAO,OAAO,KAAK,SAAS,GAAG,IAAI,WAAW,GAAG;UACnD;AACE,mBAAO,KAAK,SAAS,GAAG;UAC1B;QACF;MACF;;MAGA,UACE,QACA,KACA,OACA,kBAAkB,KAAK,KAAK;AAE5B,YAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,qBAAW,OAAO;AAAQ,iBAAK,UAAU,KAAK,QAAW,OAAO,eAAe;AAC/E,iBAAO;QACT;AACA,YAAI;AACJ,YAAI,OAAO,WAAW,UAAU;AAC9B,gBAAM,EAAC,SAAQ,IAAI,KAAK;AACxB,eAAK,OAAO,QAAQ;AACpB,cAAI,OAAO,UAAa,OAAO,MAAM,UAAU;AAC7C,kBAAM,IAAI,MAAM,UAAU,QAAQ,iBAAiB;UACrD;QACF;AACA,eAAM,GAAA,UAAA,aAAY,OAAO,EAAE;AAC3B,aAAK,aAAa,GAAG;AACrB,aAAK,QAAQ,GAAG,IAAI,KAAK,WAAW,QAAQ,OAAO,KAAK,iBAAiB,IAAI;AAC7E,eAAO;MACT;;;MAIA,cACE,QACA,KACA,kBAAkB,KAAK,KAAK;AAE5B,aAAK,UAAU,QAAQ,KAAK,MAAM,eAAe;AACjD,eAAO;MACT;;MAGA,eAAe,QAAmB,iBAAyB;AACzD,YAAI,OAAO,UAAU;AAAW,iBAAO;AACvC,YAAI;AACJ,kBAAU,OAAO;AACjB,YAAI,YAAY,UAAa,OAAO,WAAW,UAAU;AACvD,gBAAM,IAAI,MAAM,0BAA0B;QAC5C;AACA,kBAAU,WAAW,KAAK,KAAK,eAAe,KAAK,YAAW;AAC9D,YAAI,CAAC,SAAS;AACZ,eAAK,OAAO,KAAK,2BAA2B;AAC5C,eAAK,SAAS;AACd,iBAAO;QACT;AACA,cAAM,QAAQ,KAAK,SAAS,SAAS,MAAM;AAC3C,YAAI,CAAC,SAAS,iBAAiB;AAC7B,gBAAM,UAAU,wBAAwB,KAAK,WAAU;AACvD,cAAI,KAAK,KAAK,mBAAmB;AAAO,iBAAK,OAAO,MAAM,OAAO;;AAC5D,kBAAM,IAAI,MAAM,OAAO;QAC9B;AACA,eAAO;MACT;;;MAIA,UAAuB,QAAc;AACnC,YAAI;AACJ,eAAO,QAAQ,MAAM,UAAU,KAAK,MAAM,MAAM,MAAM;AAAU,mBAAS;AACzE,YAAI,QAAQ,QAAW;AACrB,gBAAM,EAAC,SAAQ,IAAI,KAAK;AACxB,gBAAM,OAAO,IAAI,UAAA,UAAU,EAAC,QAAQ,CAAA,GAAI,SAAQ,CAAC;AACjD,gBAAM,UAAA,cAAc,KAAK,MAAM,MAAM,MAAM;AAC3C,cAAI,CAAC;AAAK;AACV,eAAK,KAAK,MAAM,IAAI;QACtB;AACA,eAAQ,IAAI,YAAY,KAAK,kBAAkB,GAAG;MACpD;;;;;MAMA,aAAa,cAA0C;AACrD,YAAI,wBAAwB,QAAQ;AAClC,eAAK,kBAAkB,KAAK,SAAS,YAAY;AACjD,eAAK,kBAAkB,KAAK,MAAM,YAAY;AAC9C,iBAAO;QACT;AACA,gBAAQ,OAAO,cAAc;UAC3B,KAAK;AACH,iBAAK,kBAAkB,KAAK,OAAO;AACnC,iBAAK,kBAAkB,KAAK,IAAI;AAChC,iBAAK,OAAO,MAAK;AACjB,mBAAO;UACT,KAAK,UAAU;AACb,kBAAM,MAAM,UAAU,KAAK,MAAM,YAAY;AAC7C,gBAAI,OAAO,OAAO;AAAU,mBAAK,OAAO,OAAO,IAAI,MAAM;AACzD,mBAAO,KAAK,QAAQ,YAAY;AAChC,mBAAO,KAAK,KAAK,YAAY;AAC7B,mBAAO;UACT;UACA,KAAK,UAAU;AACb,kBAAM,WAAW;AACjB,iBAAK,OAAO,OAAO,QAAQ;AAC3B,gBAAI,KAAK,aAAa,KAAK,KAAK,QAAQ;AACxC,gBAAI,IAAI;AACN,oBAAK,GAAA,UAAA,aAAY,EAAE;AACnB,qBAAO,KAAK,QAAQ,EAAE;AACtB,qBAAO,KAAK,KAAK,EAAE;YACrB;AACA,mBAAO;UACT;UACA;AACE,kBAAM,IAAI,MAAM,qCAAqC;QACzD;MACF;;MAGA,cAAc,aAAuB;AACnC,mBAAW,OAAO;AAAa,eAAK,WAAW,GAAG;AAClD,eAAO;MACT;MAEA,WACE,UACA;AAEA,YAAI;AACJ,YAAI,OAAO,YAAY,UAAU;AAC/B,oBAAU;AACV,cAAI,OAAO,OAAO,UAAU;AAC1B,iBAAK,OAAO,KAAK,0DAA0D;AAC3E,gBAAI,UAAU;UAChB;QACF,WAAW,OAAO,YAAY,YAAY,QAAQ,QAAW;AAC3D,gBAAM;AACN,oBAAU,IAAI;AACd,cAAI,MAAM,QAAQ,OAAO,KAAK,CAAC,QAAQ,QAAQ;AAC7C,kBAAM,IAAI,MAAM,wDAAwD;UAC1E;QACF,OAAO;AACL,gBAAM,IAAI,MAAM,gCAAgC;QAClD;AAEA,qBAAa,KAAK,MAAM,SAAS,GAAG;AACpC,YAAI,CAAC,KAAK;AACR,WAAA,GAAA,OAAA,UAAS,SAAS,CAAC,QAAQ,QAAQ,KAAK,MAAM,GAAG,CAAC;AAClD,iBAAO;QACT;AACA,0BAAkB,KAAK,MAAM,GAAG;AAChC,cAAM,aAAqC;UACzC,GAAG;UACH,OAAM,GAAA,WAAA,cAAa,IAAI,IAAI;UAC3B,aAAY,GAAA,WAAA,cAAa,IAAI,UAAU;;AAEzC,SAAA,GAAA,OAAA,UACE,SACA,WAAW,KAAK,WAAW,IACvB,CAAC,MAAM,QAAQ,KAAK,MAAM,GAAG,UAAU,IACvC,CAAC,MAAM,WAAW,KAAK,QAAQ,CAAC,MAAM,QAAQ,KAAK,MAAM,GAAG,YAAY,CAAC,CAAC,CAAC;AAEjF,eAAO;MACT;MAEA,WAAW,SAAe;AACxB,cAAM,OAAO,KAAK,MAAM,IAAI,OAAO;AACnC,eAAO,OAAO,QAAQ,WAAW,KAAK,aAAa,CAAC,CAAC;MACvD;;MAGA,cAAc,SAAe;AAE3B,cAAM,EAAC,MAAK,IAAI;AAChB,eAAO,MAAM,SAAS,OAAO;AAC7B,eAAO,MAAM,IAAI,OAAO;AACxB,mBAAW,SAAS,MAAM,OAAO;AAC/B,gBAAM,IAAI,MAAM,MAAM,UAAU,CAAC,SAAS,KAAK,YAAY,OAAO;AAClE,cAAI,KAAK;AAAG,kBAAM,MAAM,OAAO,GAAG,CAAC;QACrC;AACA,eAAO;MACT;;MAGA,UAAU,MAAc,QAAc;AACpC,YAAI,OAAO,UAAU;AAAU,mBAAS,IAAI,OAAO,MAAM;AACzD,aAAK,QAAQ,IAAI,IAAI;AACrB,eAAO;MACT;MAEA,WACE,SAA2C,KAAK,QAChD,EAAC,YAAY,MAAM,UAAU,OAAM,IAAuB,CAAA;AAE1D,YAAI,CAAC,UAAU,OAAO,WAAW;AAAG,iBAAO;AAC3C,eAAO,OACJ,IAAI,CAAC,MAAM,GAAG,OAAO,GAAG,EAAE,YAAY,IAAI,EAAE,OAAO,EAAE,EACrD,OAAO,CAAC,MAAM,QAAQ,OAAO,YAAY,GAAG;MACjD;MAEA,gBAAgB,YAA6B,sBAA8B;AACzE,cAAM,QAAQ,KAAK,MAAM;AACzB,qBAAa,KAAK,MAAM,KAAK,UAAU,UAAU,CAAC;AAClD,mBAAW,eAAe,sBAAsB;AAC9C,gBAAM,WAAW,YAAY,MAAM,GAAG,EAAE,MAAM,CAAC;AAC/C,cAAI,WAAW;AACf,qBAAW,OAAO;AAAU,uBAAW,SAAS,GAAG;AAEnD,qBAAW,OAAO,OAAO;AACvB,kBAAM,OAAO,MAAM,GAAG;AACtB,gBAAI,OAAO,QAAQ;AAAU;AAC7B,kBAAM,EAAC,MAAK,IAAI,KAAK;AACrB,kBAAM,SAAS,SAAS,GAAG;AAC3B,gBAAI,SAAS;AAAQ,uBAAS,GAAG,IAAI,aAAa,MAAM;UAC1D;QACF;AAEA,eAAO;MACT;MAEQ,kBAAkB,SAAiD,OAAc;AACvF,mBAAW,UAAU,SAAS;AAC5B,gBAAM,MAAM,QAAQ,MAAM;AAC1B,cAAI,CAAC,SAAS,MAAM,KAAK,MAAM,GAAG;AAChC,gBAAI,OAAO,OAAO,UAAU;AAC1B,qBAAO,QAAQ,MAAM;YACvB,WAAW,OAAO,CAAC,IAAI,MAAM;AAC3B,mBAAK,OAAO,OAAO,IAAI,MAAM;AAC7B,qBAAO,QAAQ,MAAM;YACvB;UACF;QACF;MACF;MAEA,WACE,QACA,MACA,QACA,iBAAiB,KAAK,KAAK,gBAC3B,YAAY,KAAK,KAAK,eAAa;AAEnC,YAAI;AACJ,cAAM,EAAC,SAAQ,IAAI,KAAK;AACxB,YAAI,OAAO,UAAU,UAAU;AAC7B,eAAK,OAAO,QAAQ;QACtB,OAAO;AACL,cAAI,KAAK,KAAK;AAAK,kBAAM,IAAI,MAAM,uBAAuB;mBACjD,OAAO,UAAU;AAAW,kBAAM,IAAI,MAAM,kCAAkC;QACzF;AACA,YAAI,MAAM,KAAK,OAAO,IAAI,MAAM;AAChC,YAAI,QAAQ;AAAW,iBAAO;AAE9B,kBAAS,GAAA,UAAA,aAAY,MAAM,MAAM;AACjC,cAAM,YAAY,UAAA,cAAc,KAAK,MAAM,QAAQ,MAAM;AACzD,cAAM,IAAI,UAAA,UAAU,EAAC,QAAQ,UAAU,MAAM,QAAQ,UAAS,CAAC;AAC/D,aAAK,OAAO,IAAI,IAAI,QAAQ,GAAG;AAC/B,YAAI,aAAa,CAAC,OAAO,WAAW,GAAG,GAAG;AAExC,cAAI;AAAQ,iBAAK,aAAa,MAAM;AACpC,eAAK,KAAK,MAAM,IAAI;QACtB;AACA,YAAI;AAAgB,eAAK,eAAe,QAAQ,IAAI;AACpD,eAAO;MACT;MAEQ,aAAa,IAAU;AAC7B,YAAI,KAAK,QAAQ,EAAE,KAAK,KAAK,KAAK,EAAE,GAAG;AACrC,gBAAM,IAAI,MAAM,0BAA0B,EAAE,kBAAkB;QAChE;MACF;MAEQ,kBAAkB,KAAc;AACtC,YAAI,IAAI;AAAM,eAAK,mBAAmB,GAAG;;AACpC,oBAAA,cAAc,KAAK,MAAM,GAAG;AAGjC,YAAI,CAAC,IAAI;AAAU,gBAAM,IAAI,MAAM,0BAA0B;AAC7D,eAAO,IAAI;MACb;MAEQ,mBAAmB,KAAc;AACvC,cAAM,cAAc,KAAK;AACzB,aAAK,OAAO,KAAK;AACjB,YAAI;AACF,oBAAA,cAAc,KAAK,MAAM,GAAG;QAC9B;AACE,eAAK,OAAO;QACd;MACF;;AA9cO,QAAA,kBAAkB,mBAAA;AAClB,QAAA,kBAAkB,YAAA;sBAhBN;AAqerB,aAAS,aAEP,WACA,SACA,KACA,MAAwB,SAAO;AAE/B,iBAAW,OAAO,WAAW;AAC3B,cAAM,MAAM;AACZ,YAAI,OAAO;AAAS,eAAK,OAAO,GAAG,EAAE,GAAG,GAAG,YAAY,GAAG,KAAK,UAAU,GAAG,CAAC,EAAE;MACjF;IACF;AAEA,aAAS,UAAqB,QAAc;AAC1C,gBAAS,GAAA,UAAA,aAAY,MAAM;AAC3B,aAAO,KAAK,QAAQ,MAAM,KAAK,KAAK,KAAK,MAAM;IACjD;AAEA,aAAS,oBAAiB;AACxB,YAAM,cAAc,KAAK,KAAK;AAC9B,UAAI,CAAC;AAAa;AAClB,UAAI,MAAM,QAAQ,WAAW;AAAG,aAAK,UAAU,WAAW;;AACrD,mBAAW,OAAO;AAAa,eAAK,UAAU,YAAY,GAAG,GAAgB,GAAG;IACvF;AAEA,aAAS,oBAAiB;AACxB,iBAAW,QAAQ,KAAK,KAAK,SAAS;AACpC,cAAM,SAAS,KAAK,KAAK,QAAQ,IAAI;AACrC,YAAI;AAAQ,eAAK,UAAU,MAAM,MAAM;MACzC;IACF;AAEA,aAAS,mBAEP,MAAsD;AAEtD,UAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,aAAK,cAAc,IAAI;AACvB;MACF;AACA,WAAK,OAAO,KAAK,kDAAkD;AACnE,iBAAW,WAAW,MAAM;AAC1B,cAAM,MAAM,KAAK,OAAO;AACxB,YAAI,CAAC,IAAI;AAAS,cAAI,UAAU;AAChC,aAAK,WAAW,GAAG;MACrB;IACF;AAEA,aAAS,uBAAoB;AAC3B,YAAM,WAAW,EAAC,GAAG,KAAK,KAAI;AAC9B,iBAAW,OAAO;AAAqB,eAAO,SAAS,GAAG;AAC1D,aAAO;IACT;AAEA,QAAM,SAAS,EAAC,MAAG;IAAI,GAAG,OAAI;IAAI,GAAG,QAAK;IAAI,EAAC;AAE/C,aAAS,UAAU,QAAgC;AACjD,UAAI,WAAW;AAAO,eAAO;AAC7B,UAAI,WAAW;AAAW,eAAO;AACjC,UAAI,OAAO,OAAO,OAAO,QAAQ,OAAO;AAAO,eAAO;AACtD,YAAM,IAAI,MAAM,mDAAmD;IACrE;AAEA,QAAM,eAAe;AAErB,aAAS,aAAwB,SAA4B,KAAuB;AAClF,YAAM,EAAC,MAAK,IAAI;AAChB,OAAA,GAAA,OAAA,UAAS,SAAS,CAAC,QAAO;AACxB,YAAI,MAAM,SAAS,GAAG;AAAG,gBAAM,IAAI,MAAM,WAAW,GAAG,qBAAqB;AAC5E,YAAI,CAAC,aAAa,KAAK,GAAG;AAAG,gBAAM,IAAI,MAAM,WAAW,GAAG,mBAAmB;MAChF,CAAC;AACD,UAAI,CAAC;AAAK;AACV,UAAI,IAAI,SAAS,EAAE,UAAU,OAAO,cAAc,MAAM;AACtD,cAAM,IAAI,MAAM,uDAAuD;MACzE;IACF;AAEA,aAAS,QAEP,SACA,YACA,UAAmB;;AAEnB,YAAM,OAAO,eAAU,QAAV,eAAU,SAAA,SAAV,WAAY;AACzB,UAAI,YAAY;AAAM,cAAM,IAAI,MAAM,6CAA6C;AACnF,YAAM,EAAC,MAAK,IAAI;AAChB,UAAI,YAAY,OAAO,MAAM,OAAO,MAAM,MAAM,KAAK,CAAC,EAAC,MAAM,EAAC,MAAM,MAAM,QAAQ;AAClF,UAAI,CAAC,WAAW;AACd,oBAAY,EAAC,MAAM,UAAU,OAAO,CAAA,EAAE;AACtC,cAAM,MAAM,KAAK,SAAS;MAC5B;AACA,YAAM,SAAS,OAAO,IAAI;AAC1B,UAAI,CAAC;AAAY;AAEjB,YAAM,OAAa;QACjB;QACA,YAAY;UACV,GAAG;UACH,OAAM,GAAA,WAAA,cAAa,WAAW,IAAI;UAClC,aAAY,GAAA,WAAA,cAAa,WAAW,UAAU;;;AAGlD,UAAI,WAAW;AAAQ,sBAAc,KAAK,MAAM,WAAW,MAAM,WAAW,MAAM;;AAC7E,kBAAU,MAAM,KAAK,IAAI;AAC9B,YAAM,IAAI,OAAO,IAAI;AACrB,OAAA,KAAA,WAAW,gBAAU,QAAA,OAAA,SAAA,SAAA,GAAE,QAAQ,CAAC,QAAQ,KAAK,WAAW,GAAG,CAAC;IAC9D;AAEA,aAAS,cAAyB,WAAsB,MAAY,QAAc;AAChF,YAAM,IAAI,UAAU,MAAM,UAAU,CAAC,UAAU,MAAM,YAAY,MAAM;AACvE,UAAI,KAAK,GAAG;AACV,kBAAU,MAAM,OAAO,GAAG,GAAG,IAAI;MACnC,OAAO;AACL,kBAAU,MAAM,KAAK,IAAI;AACzB,aAAK,OAAO,KAAK,QAAQ,MAAM,iBAAiB;MAClD;IACF;AAEA,aAAS,kBAA6B,KAAsB;AAC1D,UAAI,EAAC,WAAU,IAAI;AACnB,UAAI,eAAe;AAAW;AAC9B,UAAI,IAAI,SAAS,KAAK,KAAK;AAAO,qBAAa,aAAa,UAAU;AACtE,UAAI,iBAAiB,KAAK,QAAQ,YAAY,IAAI;IACpD;AAEA,QAAM,WAAW;MACf,MAAM;;AAGR,aAAS,aAAa,QAAiB;AACrC,aAAO,EAAC,OAAO,CAAC,QAAQ,QAAQ,EAAC;IACnC;;;;;;;;;ACx3BA,QAAM,MAA6B;MACjC,SAAS;MACT,OAAI;AACF,cAAM,IAAI,MAAM,sDAAsD;MACxE;;AAGF,YAAA,UAAe;;;;;;;;;;ACPf,QAAA,cAAA;AACA,QAAA,SAAA;AACA,QAAA,YAAA;AACA,QAAA,UAAA;AACA,QAAA,YAAA;AACA,QAAA,SAAA;AAEA,QAAM,MAA6B;MACjC,SAAS;MACT,YAAY;MACZ,KAAK,KAAe;AAClB,cAAM,EAAC,KAAK,QAAQ,MAAM,GAAE,IAAI;AAChC,cAAM,EAAC,QAAQ,WAAW,KAAK,cAAc,MAAM,KAAI,IAAI;AAC3D,cAAM,EAAC,KAAI,IAAI;AACf,aAAK,SAAS,OAAO,SAAS,SAAS,WAAW,KAAK;AAAQ,iBAAO,YAAW;AACjF,cAAM,WAAW,UAAA,WAAW,KAAK,MAAM,MAAM,QAAQ,IAAI;AACzD,YAAI,aAAa;AAAW,gBAAM,IAAI,YAAA,QAAgB,GAAG,KAAK,aAAa,QAAQ,IAAI;AACvF,YAAI,oBAAoB,UAAA;AAAW,iBAAO,aAAa,QAAQ;AAC/D,eAAO,gBAAgB,QAAQ;AAE/B,iBAAS,cAAW;AAClB,cAAI,QAAQ;AAAM,mBAAO,QAAQ,KAAK,cAAc,KAAK,IAAI,MAAM;AACnE,gBAAM,WAAW,IAAI,WAAW,QAAQ,EAAC,KAAK,KAAI,CAAC;AACnD,iBAAO,QAAQ,MAAK,GAAA,UAAA,KAAI,QAAQ,aAAa,MAAM,KAAK,MAAM;QAChE;AAEA,iBAAS,aAAa,KAAc;AAClC,gBAAM,IAAI,YAAY,KAAK,GAAG;AAC9B,kBAAQ,KAAK,GAAG,KAAK,IAAI,MAAM;QACjC;AAEA,iBAAS,gBAAgB,KAAc;AACrC,gBAAM,UAAU,IAAI,WAClB,UACA,KAAK,KAAK,WAAW,OAAO,EAAC,KAAK,KAAK,OAAM,GAAA,UAAA,WAAU,GAAG,EAAC,IAAI,EAAC,KAAK,IAAG,CAAC;AAE3E,gBAAM,QAAQ,IAAI,KAAK,OAAO;AAC9B,gBAAM,SAAS,IAAI,UACjB;YACE,QAAQ;YACR,WAAW,CAAA;YACX,YAAY,UAAA;YACZ,cAAc;YACd,eAAe;aAEjB,KAAK;AAEP,cAAI,eAAe,MAAM;AACzB,cAAI,GAAG,KAAK;QACd;MACF;;AAGF,aAAgB,YAAY,KAAiB,KAAc;AACzD,YAAM,EAAC,IAAG,IAAI;AACd,aAAO,IAAI,WACP,IAAI,WAAW,YAAY,EAAC,KAAK,IAAI,SAAQ,CAAC,KAC9C,GAAA,UAAA,KAAI,IAAI,WAAW,WAAW,EAAC,KAAK,IAAG,CAAC,CAAC;IAC/C;AALA,YAAA,cAAA;AAOA,aAAgB,QAAQ,KAAiB,GAAS,KAAiB,QAAgB;AACjF,YAAM,EAAC,KAAK,GAAE,IAAI;AAClB,YAAM,EAAC,WAAW,WAAW,KAAK,KAAI,IAAI;AAC1C,YAAM,UAAU,KAAK,cAAc,QAAA,QAAE,OAAO,UAAA;AAC5C,UAAI;AAAQ,qBAAY;;AACnB,oBAAW;AAEhB,eAAS,eAAY;AACnB,YAAI,CAAC,IAAI;AAAQ,gBAAM,IAAI,MAAM,wCAAwC;AACzE,cAAM,QAAQ,IAAI,IAAI,OAAO;AAC7B,YAAI,IACF,MAAK;AACH,cAAI,MAAK,GAAA,UAAA,YAAU,GAAA,OAAA,kBAAiB,KAAK,GAAG,OAAO,CAAC,EAAE;AACtD,2BAAiB,CAAC;AAClB,cAAI,CAAC;AAAW,gBAAI,OAAO,OAAO,IAAI;QACxC,GACA,CAAC,MAAK;AACJ,cAAI,IAAG,GAAA,UAAA,OAAM,CAAC,eAAe,GAAG,eAAuB,KAAK,MAAM,IAAI,MAAM,CAAC,CAAC;AAC9E,wBAAc,CAAC;AACf,cAAI,CAAC;AAAW,gBAAI,OAAO,OAAO,KAAK;QACzC,CAAC;AAEH,YAAI,GAAG,KAAK;MACd;AAEA,eAAS,cAAW;AAClB,YAAI,QACF,GAAA,OAAA,kBAAiB,KAAK,GAAG,OAAO,GAChC,MAAM,iBAAiB,CAAC,GACxB,MAAM,cAAc,CAAC,CAAC;MAE1B;AAEA,eAAS,cAAc,QAAY;AACjC,cAAM,QAAO,GAAA,UAAA,KAAI,MAAM;AACvB,YAAI,OAAO,QAAA,QAAE,UAAS,GAAA,UAAA,KAAI,QAAA,QAAE,OAAO,eAAe,IAAI,MAAM,QAAA,QAAE,OAAO,WAAW,IAAI,GAAG;AACvF,YAAI,OAAO,QAAA,QAAE,SAAQ,GAAA,UAAA,KAAI,QAAA,QAAE,OAAO,SAAS;MAC7C;AAEA,eAAS,iBAAiB,QAAY;;AACpC,YAAI,CAAC,GAAG,KAAK;AAAa;AAC1B,cAAM,gBAAe,KAAA,QAAG,QAAH,QAAG,SAAA,SAAH,IAAK,cAAQ,QAAA,OAAA,SAAA,SAAA,GAAE;AAEpC,YAAI,GAAG,UAAU,MAAM;AACrB,cAAI,gBAAgB,CAAC,aAAa,cAAc;AAC9C,gBAAI,aAAa,UAAU,QAAW;AACpC,iBAAG,QAAQ,OAAA,eAAe,MAAM,KAAK,aAAa,OAAO,GAAG,KAAK;YACnE;UACF,OAAO;AACL,kBAAM,QAAQ,IAAI,IAAI,UAAS,GAAA,UAAA,KAAI,MAAM,kBAAkB;AAC3D,eAAG,QAAQ,OAAA,eAAe,MAAM,KAAK,OAAO,GAAG,OAAO,UAAA,IAAI;UAC5D;QACF;AACA,YAAI,GAAG,UAAU,MAAM;AACrB,cAAI,gBAAgB,CAAC,aAAa,cAAc;AAC9C,gBAAI,aAAa,UAAU,QAAW;AACpC,iBAAG,QAAQ,OAAA,eAAe,MAAM,KAAK,aAAa,OAAO,GAAG,KAAK;YACnE;UACF,OAAO;AACL,kBAAM,QAAQ,IAAI,IAAI,UAAS,GAAA,UAAA,KAAI,MAAM,kBAAkB;AAC3D,eAAG,QAAQ,OAAA,eAAe,MAAM,KAAK,OAAO,GAAG,OAAO,UAAA,IAAI;UAC5D;QACF;MACF;IACF;AAhEA,YAAA,UAAA;AAkEA,YAAA,UAAe;;;;;;;;;AC/Hf,QAAA,OAAA;AACA,QAAA,QAAA;AAEA,QAAM,OAAmB;MACvB;MACA;MACA;MACA;MACA,EAAC,SAAS,WAAU;MACpB;MACA,KAAA;MACA,MAAA;;AAGF,YAAA,UAAe;;;;;;;;;ACbf,QAAA,YAAA;AAEA,QAAM,MAAM,UAAA;AAMZ,QAAM,OAAgE;MACpE,SAAS,EAAC,OAAO,MAAM,IAAI,IAAI,KAAK,MAAM,IAAI,GAAE;MAChD,SAAS,EAAC,OAAO,MAAM,IAAI,IAAI,KAAK,MAAM,IAAI,GAAE;MAChD,kBAAkB,EAAC,OAAO,KAAK,IAAI,IAAI,IAAI,MAAM,IAAI,IAAG;MACxD,kBAAkB,EAAC,OAAO,KAAK,IAAI,IAAI,IAAI,MAAM,IAAI,IAAG;;AAS1D,QAAM,QAAgC;MACpC,SAAS,CAAC,EAAC,SAAS,WAAU,OAAM,GAAA,UAAA,eAAc,KAAK,OAAc,EAAE,KAAK,IAAI,UAAU;MAC1F,QAAQ,CAAC,EAAC,SAAS,WAAU,OAC3B,GAAA,UAAA,kBAAiB,KAAK,OAAc,EAAE,KAAK,YAAY,UAAU;;AAGrE,QAAM,MAA6B;MACjC,SAAS,OAAO,KAAK,IAAI;MACzB,MAAM;MACN,YAAY;MACZ,OAAO;MACP;MACA,KAAK,KAAe;AAClB,cAAM,EAAC,SAAS,MAAM,WAAU,IAAI;AACpC,YAAI,WAAU,GAAA,UAAA,KAAI,IAAI,IAAI,KAAK,OAAc,EAAE,IAAI,IAAI,UAAU,aAAa,IAAI,GAAG;MACvF;;AAGF,YAAA,UAAe;;;;;;;;;ACvCf,QAAA,YAAA;AAQA,QAAM,QAAgC;MACpC,SAAS,CAAC,EAAC,WAAU,OAAM,GAAA,UAAA,2BAA0B,UAAU;MAC/D,QAAQ,CAAC,EAAC,WAAU,OAAM,GAAA,UAAA,kBAAiB,UAAU;;AAGvD,QAAM,MAA6B;MACjC,SAAS;MACT,MAAM;MACN,YAAY;MACZ,OAAO;MACP;MACA,KAAK,KAAe;AAClB,cAAM,EAAC,KAAK,MAAM,YAAY,GAAE,IAAI;AAEpC,cAAM,OAAO,GAAG,KAAK;AACrB,cAAM,MAAM,IAAI,IAAI,KAAK;AACzB,cAAM,UAAU,QACZ,GAAA,UAAA,yBAAwB,GAAG,OAAO,GAAG,UAAU,IAAI,MACnD,GAAA,UAAA,KAAI,GAAG,iBAAiB,GAAG;AAC/B,YAAI,WAAU,GAAA,UAAA,MAAK,UAAU,cAAc,GAAG,MAAM,IAAI,IAAI,UAAU,KAAK,OAAO,IAAI;MACxF;;AAGF,YAAA,UAAe;;;;;;;;;AC/Bf,aAAwB,WAAW,KAAW;AAC5C,YAAM,MAAM,IAAI;AAChB,UAAI,SAAS;AACb,UAAI,MAAM;AACV,UAAI;AACJ,aAAO,MAAM,KAAK;AAChB;AACA,gBAAQ,IAAI,WAAW,KAAK;AAC5B,YAAI,SAAS,SAAU,SAAS,SAAU,MAAM,KAAK;AAEnD,kBAAQ,IAAI,WAAW,GAAG;AAC1B,eAAK,QAAQ,WAAY;AAAQ;QACnC;MACF;AACA,aAAO;IACT;AAfA,YAAA,UAAA;AAiBA,eAAW,OAAO;;;;;;;;;ACjBlB,QAAA,YAAA;AACA,QAAA,SAAA;AACA,QAAA,eAAA;AAEA,QAAM,QAAgC;MACpC,QAAQ,EAAC,SAAS,WAAU,GAAC;AAC3B,cAAM,OAAO,YAAY,cAAc,SAAS;AAChD,gBAAO,GAAA,UAAA,qBAAoB,IAAI,SAAS,UAAU;MACpD;MACA,QAAQ,CAAC,EAAC,WAAU,OAAM,GAAA,UAAA,aAAY,UAAU;;AAGlD,QAAM,MAA6B;MACjC,SAAS,CAAC,aAAa,WAAW;MAClC,MAAM;MACN,YAAY;MACZ,OAAO;MACP;MACA,KAAK,KAAe;AAClB,cAAM,EAAC,SAAS,MAAM,YAAY,GAAE,IAAI;AACxC,cAAM,KAAK,YAAY,cAAc,UAAA,UAAU,KAAK,UAAA,UAAU;AAC9D,cAAM,MACJ,GAAG,KAAK,YAAY,SAAQ,GAAA,UAAA,KAAI,IAAI,aAAY,GAAA,UAAA,MAAI,GAAA,OAAA,SAAQ,IAAI,KAAK,aAAA,OAAU,CAAC,IAAI,IAAI;AAC1F,YAAI,WAAU,GAAA,UAAA,KAAI,GAAG,IAAI,EAAE,IAAI,UAAU,EAAE;MAC7C;;AAGF,YAAA,UAAe;;;;;;;;;AC3Bf,QAAA,SAAA;AACA,QAAA,YAAA;AAIA,QAAM,QAAgC;MACpC,SAAS,CAAC,EAAC,WAAU,OAAM,GAAA,UAAA,2BAA0B,UAAU;MAC/D,QAAQ,CAAC,EAAC,WAAU,OAAM,GAAA,UAAA,eAAc,UAAU;;AAGpD,QAAM,MAA6B;MACjC,SAAS;MACT,MAAM;MACN,YAAY;MACZ,OAAO;MACP;MACA,KAAK,KAAe;AAClB,cAAM,EAAC,MAAM,OAAO,QAAQ,YAAY,GAAE,IAAI;AAE9C,cAAM,IAAI,GAAG,KAAK,gBAAgB,MAAM;AACxC,cAAM,SAAS,SAAQ,GAAA,UAAA,iBAAgB,UAAU,KAAK,CAAC,QAAO,GAAA,OAAA,YAAW,KAAK,MAAM;AACpF,YAAI,WAAU,GAAA,UAAA,MAAK,MAAM,SAAS,IAAI,GAAG;MAC3C;;AAGF,YAAA,UAAe;;;;;;;;;ACzBf,QAAA,YAAA;AAEA,QAAM,QAAgC;MACpC,QAAQ,EAAC,SAAS,WAAU,GAAC;AAC3B,cAAM,OAAO,YAAY,kBAAkB,SAAS;AACpD,gBAAO,GAAA,UAAA,qBAAoB,IAAI,SAAS,UAAU;MACpD;MACA,QAAQ,CAAC,EAAC,WAAU,OAAM,GAAA,UAAA,aAAY,UAAU;;AAGlD,QAAM,MAA6B;MACjC,SAAS,CAAC,iBAAiB,eAAe;MAC1C,MAAM;MACN,YAAY;MACZ,OAAO;MACP;MACA,KAAK,KAAe;AAClB,cAAM,EAAC,SAAS,MAAM,WAAU,IAAI;AACpC,cAAM,KAAK,YAAY,kBAAkB,UAAA,UAAU,KAAK,UAAA,UAAU;AAClE,YAAI,WAAU,GAAA,UAAA,iBAAgB,IAAI,YAAY,EAAE,IAAI,UAAU,EAAE;MAClE;;AAGF,YAAA,UAAe;;;;;;;;;ACvBf,QAAA,SAAA;AAOA,QAAA,YAAA;AACA,QAAA,SAAA;AAQA,QAAM,QAAgC;MACpC,SAAS,CAAC,EAAC,QAAQ,EAAC,gBAAe,EAAC,OAAM,GAAA,UAAA,oCAAmC,eAAe;MAC5F,QAAQ,CAAC,EAAC,QAAQ,EAAC,gBAAe,EAAC,OAAM,GAAA,UAAA,uBAAsB,eAAe;;AAGhF,QAAM,MAA6B;MACjC,SAAS;MACT,MAAM;MACN,YAAY;MACZ,OAAO;MACP;MACA,KAAK,KAAe;AAClB,cAAM,EAAC,KAAK,QAAQ,YAAY,MAAM,OAAO,GAAE,IAAI;AACnD,cAAM,EAAC,KAAI,IAAI;AACf,YAAI,CAAC,SAAS,OAAO,WAAW;AAAG;AACnC,cAAM,UAAU,OAAO,UAAU,KAAK;AACtC,YAAI,GAAG;AAAW,wBAAa;;AAC1B,0BAAe;AAEpB,YAAI,KAAK,gBAAgB;AACvB,gBAAM,QAAQ,IAAI,aAAa;AAC/B,gBAAM,EAAC,kBAAiB,IAAI,IAAI;AAChC,qBAAW,eAAe,QAAQ;AAChC,iBAAI,UAAK,QAAL,UAAK,SAAA,SAAL,MAAQ,WAAW,OAAM,UAAa,CAAC,kBAAkB,IAAI,WAAW,GAAG;AAC7E,oBAAM,aAAa,GAAG,UAAU,SAAS,GAAG;AAC5C,oBAAM,MAAM,sBAAsB,WAAW,wBAAwB,UAAU;AAC/E,eAAA,GAAA,OAAA,iBAAgB,IAAI,KAAK,GAAG,KAAK,cAAc;YACjD;UACF;QACF;AAEA,iBAAS,gBAAa;AACpB,cAAI,WAAW,OAAO;AACpB,gBAAI,WAAW,UAAA,KAAK,eAAe;UACrC,OAAO;AACL,uBAAW,QAAQ,QAAQ;AACzB,eAAA,GAAA,OAAA,wBAAuB,KAAK,IAAI;YAClC;UACF;QACF;AAEA,iBAAS,kBAAe;AACtB,gBAAM,UAAU,IAAI,IAAI,SAAS;AACjC,cAAI,WAAW,OAAO;AACpB,kBAAM,QAAQ,IAAI,IAAI,SAAS,IAAI;AACnC,gBAAI,WAAW,OAAO,MAAM,iBAAiB,SAAS,KAAK,CAAC;AAC5D,gBAAI,GAAG,KAAK;UACd,OAAO;AACL,gBAAI,IAAG,GAAA,OAAA,kBAAiB,KAAK,QAAQ,OAAO,CAAC;AAC7C,aAAA,GAAA,OAAA,mBAAkB,KAAK,OAAO;AAC9B,gBAAI,KAAI;UACV;QACF;AAEA,iBAAS,kBAAe;AACtB,cAAI,MAAM,QAAQ,YAAoB,CAAC,SAAQ;AAC7C,gBAAI,UAAU,EAAC,iBAAiB,KAAI,CAAC;AACrC,gBAAI,IAAG,GAAA,OAAA,kBAAiB,KAAK,MAAM,MAAM,KAAK,aAAa,GAAG,MAAM,IAAI,MAAK,CAAE;UACjF,CAAC;QACH;AAEA,iBAAS,iBAAiB,SAAe,OAAW;AAClD,cAAI,UAAU,EAAC,iBAAiB,QAAO,CAAC;AACxC,cAAI,MACF,SACA,YACA,MAAK;AACH,gBAAI,OAAO,QAAO,GAAA,OAAA,gBAAe,KAAK,MAAM,SAAS,KAAK,aAAa,CAAC;AACxE,gBAAI,IAAG,GAAA,UAAA,KAAI,KAAK,GAAG,MAAK;AACtB,kBAAI,MAAK;AACT,kBAAI,MAAK;YACX,CAAC;UACH,GACA,UAAA,GAAG;QAEP;MACF;;AAGF,YAAA,UAAe;;;;;;;;;AC/Ff,QAAA,YAAA;AAEA,QAAM,QAAgC;MACpC,QAAQ,EAAC,SAAS,WAAU,GAAC;AAC3B,cAAM,OAAO,YAAY,aAAa,SAAS;AAC/C,gBAAO,GAAA,UAAA,qBAAoB,IAAI,SAAS,UAAU;MACpD;MACA,QAAQ,CAAC,EAAC,WAAU,OAAM,GAAA,UAAA,aAAY,UAAU;;AAGlD,QAAM,MAA6B;MACjC,SAAS,CAAC,YAAY,UAAU;MAChC,MAAM;MACN,YAAY;MACZ,OAAO;MACP;MACA,KAAK,KAAe;AAClB,cAAM,EAAC,SAAS,MAAM,WAAU,IAAI;AACpC,cAAM,KAAK,YAAY,aAAa,UAAA,UAAU,KAAK,UAAA,UAAU;AAC7D,YAAI,WAAU,GAAA,UAAA,KAAI,IAAI,WAAW,EAAE,IAAI,UAAU,EAAE;MACrD;;AAGF,YAAA,UAAe;;;;;;;;;ACxBf,QAAA,QAAA;AAGE,UAAgB,OAAO;AAEzB,YAAA,UAAe;;;;;;;;;ACJf,QAAA,aAAA;AACA,QAAA,YAAA;AACA,QAAA,SAAA;AACA,QAAA,UAAA;AAQA,QAAM,QAAgC;MACpC,SAAS,CAAC,EAAC,QAAQ,EAAC,GAAG,EAAC,EAAC,OACvB,GAAA,UAAA,+CAA8C,CAAC,QAAQ,CAAC;MAC1D,QAAQ,CAAC,EAAC,QAAQ,EAAC,GAAG,EAAC,EAAC,OAAM,GAAA,UAAA,SAAQ,CAAC,QAAQ,CAAC;;AAGlD,QAAM,MAA6B;MACjC,SAAS;MACT,MAAM;MACN,YAAY;MACZ,OAAO;MACP;MACA,KAAK,KAAe;AAClB,cAAM,EAAC,KAAK,MAAM,OAAO,QAAQ,cAAc,YAAY,GAAE,IAAI;AACjE,YAAI,CAAC,SAAS,CAAC;AAAQ;AACvB,cAAM,QAAQ,IAAI,IAAI,OAAO;AAC7B,cAAM,YAAY,aAAa,SAAQ,GAAA,WAAA,gBAAe,aAAa,KAAK,IAAI,CAAA;AAC5E,YAAI,WAAW,OAAO,sBAAqB,GAAA,UAAA,KAAI,UAAU,YAAY;AACrE,YAAI,GAAG,KAAK;AAEZ,iBAAS,sBAAmB;AAC1B,gBAAM,IAAI,IAAI,IAAI,MAAK,GAAA,UAAA,KAAI,IAAI,SAAS;AACxC,gBAAM,IAAI,IAAI,IAAI,GAAG;AACrB,cAAI,UAAU,EAAC,GAAG,EAAC,CAAC;AACpB,cAAI,OAAO,OAAO,IAAI;AACtB,cAAI,IAAG,GAAA,UAAA,KAAI,CAAC,QAAQ,OAAO,YAAW,IAAK,QAAQ,QAAQ,GAAG,CAAC,CAAC;QAClE;AAEA,iBAAS,cAAW;AAClB,iBAAO,UAAU,SAAS,KAAK,CAAC,UAAU,KAAK,CAAC,MAAM,MAAM,YAAY,MAAM,OAAO;QACvF;AAEA,iBAAS,MAAM,GAAS,GAAO;AAC7B,gBAAM,OAAO,IAAI,KAAK,MAAM;AAC5B,gBAAM,aAAY,GAAA,WAAA,gBAAe,WAAW,MAAM,GAAG,KAAK,eAAe,WAAA,SAAS,KAAK;AACvF,gBAAM,UAAU,IAAI,MAAM,YAAW,GAAA,UAAA,MAAK;AAC1C,cAAI,KAAI,GAAA,UAAA,MAAK,CAAC,OAAO,MAAK;AACxB,gBAAI,IAAI,OAAM,GAAA,UAAA,KAAI,IAAI,IAAI,CAAC,GAAG;AAC9B,gBAAI,GAAG,YAAW,GAAA,UAAA,YAAW;AAC7B,gBAAI,UAAU,SAAS;AAAG,kBAAI,IAAG,GAAA,UAAA,YAAW,IAAI,iBAAgB,GAAA,UAAA,KAAI,IAAI,SAAS;AACjF,gBACG,IAAG,GAAA,UAAA,YAAW,OAAO,IAAI,IAAI,iBAAiB,MAAK;AAClD,kBAAI,OAAO,IAAG,GAAA,UAAA,KAAI,OAAO,IAAI,IAAI,GAAG;AACpC,kBAAI,MAAK;AACT,kBAAI,OAAO,OAAO,KAAK,EAAE,MAAK;YAChC,CAAC,EACA,MAAK,GAAA,UAAA,KAAI,OAAO,IAAI,IAAI,OAAO,CAAC,EAAE;UACvC,CAAC;QACH;AAEA,iBAAS,OAAO,GAAS,GAAO;AAC9B,gBAAM,OAAM,GAAA,OAAA,SAAQ,KAAK,QAAA,OAAK;AAC9B,gBAAM,QAAQ,IAAI,KAAK,OAAO;AAC9B,cAAI,MAAM,KAAK,EAAE,KAAI,GAAA,UAAA,MAAK,CAAC,OAAO,MAChC,IAAI,KAAI,GAAA,UAAA,KAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,MAC/B,IAAI,IAAG,GAAA,UAAA,KAAI,GAAG,IAAI,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,MAAK;AACnD,gBAAI,MAAK;AACT,gBAAI,OAAO,OAAO,KAAK,EAAE,MAAM,KAAK;UACtC,CAAC,CAAC,CACH;QAEL;MACF;;AAGF,YAAA,UAAe;;;;;;;;;AC5Ef,QAAA,YAAA;AACA,QAAA,SAAA;AACA,QAAA,UAAA;AAIA,QAAM,QAAgC;MACpC,SAAS;MACT,QAAQ,CAAC,EAAC,WAAU,OAAM,GAAA,UAAA,oBAAmB,UAAU;;AAGzD,QAAM,MAA6B;MACjC,SAAS;MACT,OAAO;MACP;MACA,KAAK,KAAe;AAClB,cAAM,EAAC,KAAK,MAAM,OAAO,YAAY,OAAM,IAAI;AAC/C,YAAI,SAAU,UAAU,OAAO,UAAU,UAAW;AAClD,cAAI,WAAU,GAAA,UAAA,OAAK,GAAA,OAAA,SAAQ,KAAK,QAAA,OAAK,CAAC,IAAI,IAAI,KAAK,UAAU,GAAG;QAClE,OAAO;AACL,cAAI,MAAK,GAAA,UAAA,KAAI,MAAM,QAAQ,IAAI,EAAE;QACnC;MACF;;AAGF,YAAA,UAAe;;;;;;;;;ACzBf,QAAA,YAAA;AACA,QAAA,SAAA;AACA,QAAA,UAAA;AAIA,QAAM,QAAgC;MACpC,SAAS;MACT,QAAQ,CAAC,EAAC,WAAU,OAAM,GAAA,UAAA,qBAAoB,UAAU;;AAG1D,QAAM,MAA6B;MACjC,SAAS;MACT,YAAY;MACZ,OAAO;MACP;MACA,KAAK,KAAe;AAClB,cAAM,EAAC,KAAK,MAAM,OAAO,QAAQ,YAAY,GAAE,IAAI;AACnD,YAAI,CAAC,SAAS,OAAO,WAAW;AAAG,gBAAM,IAAI,MAAM,gCAAgC;AACnF,cAAM,UAAU,OAAO,UAAU,GAAG,KAAK;AACzC,YAAI;AACJ,cAAM,SAAS,MAAa,QAAG,QAAH,QAAG,SAAH,MAAA,OAAQ,GAAA,OAAA,SAAQ,KAAK,QAAA,OAAK;AAEtD,YAAI;AACJ,YAAI,WAAW,OAAO;AACpB,kBAAQ,IAAI,IAAI,OAAO;AACvB,cAAI,WAAW,OAAO,QAAQ;QAChC,OAAO;AAEL,cAAI,CAAC,MAAM,QAAQ,MAAM;AAAG,kBAAM,IAAI,MAAM,0BAA0B;AACtE,gBAAM,UAAU,IAAI,MAAM,WAAW,UAAU;AAC/C,mBAAQ,GAAA,UAAA,IAAG,GAAG,OAAO,IAAI,CAAC,IAAa,MAAc,UAAU,SAAS,CAAC,CAAC,CAAC;QAC7E;AACA,YAAI,KAAK,KAAK;AAEd,iBAAS,WAAQ;AACf,cAAI,OAAO,OAAO,KAAK;AACvB,cAAI,MAAM,KAAK,YAAoB,CAAC,MAClC,IAAI,IAAG,GAAA,UAAA,KAAI,OAAM,CAAE,IAAI,IAAI,KAAK,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,IAAI,EAAE,MAAK,CAAE,CAAC;QAEhF;AAEA,iBAAS,UAAU,SAAe,GAAS;AACzC,gBAAM,MAAM,OAAO,CAAC;AACpB,iBAAO,OAAO,QAAQ,YAAY,QAAQ,QACtC,GAAA,UAAA,KAAI,OAAM,CAAE,IAAI,IAAI,KAAK,OAAO,IAAI,CAAC,QACrC,GAAA,UAAA,KAAI,IAAI,QAAQ,GAAG;QACzB;MACF;;AAGF,YAAA,UAAe;;;;;;;;;ACpDf,QAAA,gBAAA;AACA,QAAA,eAAA;AACA,QAAA,gBAAA;AACA,QAAA,YAAA;AACA,QAAA,oBAAA;AACA,QAAA,aAAA;AACA,QAAA,eAAA;AACA,QAAA,gBAAA;AACA,QAAA,UAAA;AACA,QAAA,SAAA;AAEA,QAAM,aAAyB;;MAE7B,cAAA;MACA,aAAA;;MAEA,cAAA;MACA,UAAA;;MAEA,kBAAA;MACA,WAAA;;MAEA,aAAA;MACA,cAAA;;MAEA,EAAC,SAAS,QAAQ,YAAY,CAAC,UAAU,OAAO,EAAC;MACjD,EAAC,SAAS,YAAY,YAAY,UAAS;MAC3C,QAAA;MACA,OAAA;;AAGF,YAAA,UAAe;;;;;;;;;;ACzBf,QAAA,YAAA;AACA,QAAA,SAAA;AAIA,QAAM,QAAgC;MACpC,SAAS,CAAC,EAAC,QAAQ,EAAC,IAAG,EAAC,OAAM,GAAA,UAAA,+BAA8B,GAAG;MAC/D,QAAQ,CAAC,EAAC,QAAQ,EAAC,IAAG,EAAC,OAAM,GAAA,UAAA,aAAY,GAAG;;AAG9C,QAAM,MAA6B;MACjC,SAAS;MACT,MAAM;MACN,YAAY,CAAC,WAAW,QAAQ;MAChC,QAAQ;MACR;MACA,KAAK,KAAe;AAClB,cAAM,EAAC,cAAc,GAAE,IAAI;AAC3B,cAAM,EAAC,MAAK,IAAI;AAChB,YAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACzB,WAAA,GAAA,OAAA,iBAAgB,IAAI,sEAAsE;AAC1F;QACF;AACA,gCAAwB,KAAK,KAAK;MACpC;;AAGF,aAAgB,wBAAwB,KAAiB,OAAkB;AACzE,YAAM,EAAC,KAAK,QAAQ,MAAM,SAAS,GAAE,IAAI;AACzC,SAAG,QAAQ;AACX,YAAM,MAAM,IAAI,MAAM,QAAO,GAAA,UAAA,KAAI,IAAI,SAAS;AAC9C,UAAI,WAAW,OAAO;AACpB,YAAI,UAAU,EAAC,KAAK,MAAM,OAAM,CAAC;AACjC,YAAI,MAAK,GAAA,UAAA,KAAI,GAAG,OAAO,MAAM,MAAM,EAAE;MACvC,WAAW,OAAO,UAAU,YAAY,EAAC,GAAA,OAAA,mBAAkB,IAAI,MAAM,GAAG;AACtE,cAAM,QAAQ,IAAI,IAAI,UAAS,GAAA,UAAA,KAAI,GAAG,OAAO,MAAM,MAAM,EAAE;AAC3D,YAAI,IAAG,GAAA,UAAA,KAAI,KAAK,GAAG,MAAM,cAAc,KAAK,CAAC;AAC7C,YAAI,GAAG,KAAK;MACd;AAEA,eAAS,cAAc,OAAW;AAChC,YAAI,SAAS,KAAK,MAAM,QAAQ,KAAK,CAAC,MAAK;AACzC,cAAI,UAAU,EAAC,SAAS,UAAU,GAAG,cAAc,OAAA,KAAK,IAAG,GAAG,KAAK;AACnE,cAAI,CAAC,GAAG;AAAW,gBAAI,IAAG,GAAA,UAAA,KAAI,KAAK,GAAG,MAAM,IAAI,MAAK,CAAE;QACzD,CAAC;MACH;IACF;AAnBA,YAAA,0BAAA;AAqBA,YAAA,UAAe;;;;;;;;;;ACrDf,QAAA,YAAA;AACA,QAAA,SAAA;AACA,QAAA,SAAA;AAEA,QAAM,MAA6B;MACjC,SAAS;MACT,MAAM;MACN,YAAY,CAAC,UAAU,SAAS,SAAS;MACzC,QAAQ;MACR,KAAK,KAAe;AAClB,cAAM,EAAC,QAAQ,GAAE,IAAI;AACrB,YAAI,MAAM,QAAQ,MAAM;AAAG,iBAAO,cAAc,KAAK,mBAAmB,MAAM;AAC9E,WAAG,QAAQ;AACX,aAAI,GAAA,OAAA,mBAAkB,IAAI,MAAM;AAAG;AACnC,YAAI,IAAG,GAAA,OAAA,eAAc,GAAG,CAAC;MAC3B;;AAGF,aAAgB,cACd,KACA,YACA,SAAsB,IAAI,QAAM;AAEhC,YAAM,EAAC,KAAK,cAAc,MAAM,SAAS,GAAE,IAAI;AAC/C,uBAAiB,YAAY;AAC7B,UAAI,GAAG,KAAK,eAAe,OAAO,UAAU,GAAG,UAAU,MAAM;AAC7D,WAAG,QAAQ,OAAA,eAAe,MAAM,KAAK,OAAO,QAAQ,GAAG,KAAK;MAC9D;AACA,YAAM,QAAQ,IAAI,KAAK,OAAO;AAC9B,YAAM,MAAM,IAAI,MAAM,QAAO,GAAA,UAAA,KAAI,IAAI,SAAS;AAC9C,aAAO,QAAQ,CAAC,KAAgB,MAAa;AAC3C,aAAI,GAAA,OAAA,mBAAkB,IAAI,GAAG;AAAG;AAChC,YAAI,IAAG,GAAA,UAAA,KAAI,GAAG,MAAM,CAAC,IAAI,MACvB,IAAI,UACF;UACE;UACA,YAAY;UACZ,UAAU;WAEZ,KAAK,CACN;AAEH,YAAI,GAAG,KAAK;MACd,CAAC;AAED,eAAS,iBAAiB,KAAoB;AAC5C,cAAM,EAAC,MAAM,cAAa,IAAI;AAC9B,cAAM,IAAI,OAAO;AACjB,cAAM,YAAY,MAAM,IAAI,aAAa,MAAM,IAAI,YAAY,IAAI,UAAU,MAAM;AACnF,YAAI,KAAK,gBAAgB,CAAC,WAAW;AACnC,gBAAM,MAAM,IAAI,OAAO,QAAQ,CAAC,oCAAoC,UAAU,4CAA4C,aAAa;AACvI,WAAA,GAAA,OAAA,iBAAgB,IAAI,KAAK,KAAK,YAAY;QAC5C;MACF;IACF;AApCA,YAAA,gBAAA;AAsCA,YAAA,UAAe;;;;;;;;;ACzDf,QAAA,UAAA;AAEA,QAAM,MAA6B;MACjC,SAAS;MACT,MAAM;MACN,YAAY,CAAC,OAAO;MACpB,QAAQ;MACR,MAAM,CAAC,SAAQ,GAAA,QAAA,eAAc,KAAK,OAAO;;AAG3C,YAAA,UAAe;;;;;;;;;ACJf,QAAA,YAAA;AACA,QAAA,SAAA;AACA,QAAA,SAAA;AACA,QAAA,oBAAA;AAIA,QAAM,QAAgC;MACpC,SAAS,CAAC,EAAC,QAAQ,EAAC,IAAG,EAAC,OAAM,GAAA,UAAA,+BAA8B,GAAG;MAC/D,QAAQ,CAAC,EAAC,QAAQ,EAAC,IAAG,EAAC,OAAM,GAAA,UAAA,aAAY,GAAG;;AAG9C,QAAM,MAA6B;MACjC,SAAS;MACT,MAAM;MACN,YAAY,CAAC,UAAU,SAAS;MAChC,QAAQ;MACR;MACA,KAAK,KAAe;AAClB,cAAM,EAAC,QAAQ,cAAc,GAAE,IAAI;AACnC,cAAM,EAAC,YAAW,IAAI;AACtB,WAAG,QAAQ;AACX,aAAI,GAAA,OAAA,mBAAkB,IAAI,MAAM;AAAG;AACnC,YAAI;AAAa,WAAA,GAAA,kBAAA,yBAAwB,KAAK,WAAW;;AACpD,cAAI,IAAG,GAAA,OAAA,eAAc,GAAG,CAAC;MAChC;;AAGF,YAAA,UAAe;;;;;;;;;AC5Bf,QAAA,YAAA;AACA,QAAA,SAAA;AAQA,QAAM,QAAgC;MACpC,SAAS,CAAC,EAAC,QAAQ,EAAC,KAAK,IAAG,EAAC,MAC3B,QAAQ,UACJ,GAAA,UAAA,6BAA4B,GAAG,oBAC/B,GAAA,UAAA,6BAA4B,GAAG,qBAAqB,GAAG;MAC7D,QAAQ,CAAC,EAAC,QAAQ,EAAC,KAAK,IAAG,EAAC,MAC1B,QAAQ,UAAY,GAAA,UAAA,mBAAkB,GAAG,OAAM,GAAA,UAAA,mBAAkB,GAAG,kBAAkB,GAAG;;AAG7F,QAAM,MAA6B;MACjC,SAAS;MACT,MAAM;MACN,YAAY,CAAC,UAAU,SAAS;MAChC,QAAQ;MACR,aAAa;MACb;MACA,KAAK,KAAe;AAClB,cAAM,EAAC,KAAK,QAAQ,cAAc,MAAM,GAAE,IAAI;AAC9C,YAAI;AACJ,YAAI;AACJ,cAAM,EAAC,aAAa,YAAW,IAAI;AACnC,YAAI,GAAG,KAAK,MAAM;AAChB,gBAAM,gBAAgB,SAAY,IAAI;AACtC,gBAAM;QACR,OAAO;AACL,gBAAM;QACR;AACA,cAAM,MAAM,IAAI,MAAM,QAAO,GAAA,UAAA,KAAI,IAAI,SAAS;AAC9C,YAAI,UAAU,EAAC,KAAK,IAAG,CAAC;AACxB,YAAI,QAAQ,UAAa,QAAQ,GAAG;AAClC,WAAA,GAAA,OAAA,iBAAgB,IAAI,sEAAsE;AAC1F;QACF;AACA,YAAI,QAAQ,UAAa,MAAM,KAAK;AAClC,WAAA,GAAA,OAAA,iBAAgB,IAAI,iDAAiD;AACrE,cAAI,KAAI;AACR;QACF;AACA,aAAI,GAAA,OAAA,mBAAkB,IAAI,MAAM,GAAG;AACjC,cAAI,QAAO,GAAA,UAAA,KAAI,GAAG,OAAO,GAAG;AAC5B,cAAI,QAAQ;AAAW,oBAAO,GAAA,UAAA,KAAI,IAAI,OAAO,GAAG,OAAO,GAAG;AAC1D,cAAI,KAAK,IAAI;AACb;QACF;AAEA,WAAG,QAAQ;AACX,cAAM,QAAQ,IAAI,KAAK,OAAO;AAC9B,YAAI,QAAQ,UAAa,QAAQ,GAAG;AAClC,wBAAc,OAAO,MAAM,IAAI,GAAG,OAAO,MAAM,IAAI,MAAK,CAAE,CAAC;QAC7D,WAAW,QAAQ,GAAG;AACpB,cAAI,IAAI,OAAO,IAAI;AACnB,cAAI,QAAQ;AAAW,gBAAI,IAAG,GAAA,UAAA,KAAI,IAAI,eAAe,sBAAsB;QAC7E,OAAO;AACL,cAAI,IAAI,OAAO,KAAK;AACpB,iCAAsB;QACxB;AACA,YAAI,OAAO,OAAO,MAAM,IAAI,MAAK,CAAE;AAEnC,iBAAS,yBAAsB;AAC7B,gBAAM,WAAW,IAAI,KAAK,QAAQ;AAClC,gBAAM,QAAQ,IAAI,IAAI,SAAS,CAAC;AAChC,wBAAc,UAAU,MAAM,IAAI,GAAG,UAAU,MAAM,YAAY,KAAK,CAAC,CAAC;QAC1E;AAEA,iBAAS,cAAc,QAAc,OAAiB;AACpD,cAAI,SAAS,KAAK,GAAG,KAAK,CAAC,MAAK;AAC9B,gBAAI,UACF;cACE,SAAS;cACT,UAAU;cACV,cAAc,OAAA,KAAK;cACnB,eAAe;eAEjB,MAAM;AAER,kBAAK;UACP,CAAC;QACH;AAEA,iBAAS,YAAY,OAAW;AAC9B,cAAI,MAAK,GAAA,UAAA,KAAI,KAAK,IAAI;AACtB,cAAI,QAAQ,QAAW;AACrB,gBAAI,IAAG,GAAA,UAAA,KAAI,KAAK,OAAO,GAAG,IAAI,MAAM,IAAI,OAAO,OAAO,IAAI,EAAE,MAAK,CAAE;UACrE,OAAO;AACL,gBAAI,IAAG,GAAA,UAAA,KAAI,KAAK,MAAM,GAAG,IAAI,MAAM,IAAI,OAAO,OAAO,KAAK,EAAE,MAAK,CAAE;AACnE,gBAAI,QAAQ;AAAG,kBAAI,OAAO,OAAO,IAAI;;AAChC,kBAAI,IAAG,GAAA,UAAA,KAAI,KAAK,OAAO,GAAG,IAAI,MAAM,IAAI,OAAO,OAAO,IAAI,CAAC;UAClE;QACF;MACF;;AAGF,YAAA,UAAe;;;;;;;;;;ACpGf,QAAA,YAAA;AACA,QAAA,SAAA;AACA,QAAA,SAAA;AAmBa,YAAA,QAAgC;MAC3C,SAAS,CAAC,EAAC,QAAQ,EAAC,UAAU,WAAW,KAAI,EAAC,MAAK;AACjD,cAAM,eAAe,cAAc,IAAI,aAAa;AACpD,gBAAO,GAAA,UAAA,iBAAgB,YAAY,IAAI,IAAI,kBAAkB,QAAQ;MACvE;MACA,QAAQ,CAAC,EAAC,QAAQ,EAAC,UAAU,WAAW,MAAM,gBAAe,EAAC,OAC5D,GAAA,UAAA,gBAAe,QAAQ;uBACJ,eAAe;iBACrB,SAAS;YACd,IAAI;;;AAGhB,QAAM,MAA6B;MACjC,SAAS;MACT,MAAM;MACN,YAAY;MACZ,OAAA,QAAA;MACA,KAAK,KAAe;AAClB,cAAM,CAAC,UAAU,OAAO,IAAI,kBAAkB,GAAG;AACjD,6BAAqB,KAAK,QAAQ;AAClC,2BAAmB,KAAK,OAAO;MACjC;;AAGF,aAAS,kBAAkB,EAAC,OAAM,GAAa;AAC7C,YAAM,eAAqC,CAAA;AAC3C,YAAM,aAAiC,CAAA;AACvC,iBAAW,OAAO,QAAQ;AACxB,YAAI,QAAQ;AAAa;AACzB,cAAM,OAAO,MAAM,QAAQ,OAAO,GAAG,CAAC,IAAI,eAAe;AACzD,aAAK,GAAG,IAAI,OAAO,GAAG;MACxB;AACA,aAAO,CAAC,cAAc,UAAU;IAClC;AAEA,aAAgB,qBACd,KACA,eAA2C,IAAI,QAAM;AAErD,YAAM,EAAC,KAAK,MAAM,GAAE,IAAI;AACxB,UAAI,OAAO,KAAK,YAAY,EAAE,WAAW;AAAG;AAC5C,YAAM,UAAU,IAAI,IAAI,SAAS;AACjC,iBAAW,QAAQ,cAAc;AAC/B,cAAM,OAAO,aAAa,IAAI;AAC9B,YAAI,KAAK,WAAW;AAAG;AACvB,cAAM,eAAc,GAAA,OAAA,gBAAe,KAAK,MAAM,MAAM,GAAG,KAAK,aAAa;AACzE,YAAI,UAAU;UACZ,UAAU;UACV,WAAW,KAAK;UAChB,MAAM,KAAK,KAAK,IAAI;SACrB;AACD,YAAI,GAAG,WAAW;AAChB,cAAI,GAAG,aAAa,MAAK;AACvB,uBAAW,WAAW,MAAM;AAC1B,eAAA,GAAA,OAAA,wBAAuB,KAAK,OAAO;YACrC;UACF,CAAC;QACH,OAAO;AACL,cAAI,IAAG,GAAA,UAAA,KAAI,WAAW,SAAQ,GAAA,OAAA,kBAAiB,KAAK,MAAM,OAAO,CAAC,GAAG;AACrE,WAAA,GAAA,OAAA,mBAAkB,KAAK,OAAO;AAC9B,cAAI,KAAI;QACV;MACF;IACF;AA5BA,YAAA,uBAAA;AA8BA,aAAgB,mBAAmB,KAAiB,aAAwB,IAAI,QAAM;AACpF,YAAM,EAAC,KAAK,MAAM,SAAS,GAAE,IAAI;AACjC,YAAM,QAAQ,IAAI,KAAK,OAAO;AAC9B,iBAAW,QAAQ,YAAY;AAC7B,aAAI,GAAA,OAAA,mBAAkB,IAAI,WAAW,IAAI,CAAc;AAAG;AAC1D,YAAI;WACF,GAAA,OAAA,gBAAe,KAAK,MAAM,MAAM,GAAG,KAAK,aAAa;UACrD,MAAK;AACH,kBAAM,SAAS,IAAI,UAAU,EAAC,SAAS,YAAY,KAAI,GAAG,KAAK;AAC/D,gBAAI,oBAAoB,QAAQ,KAAK;UACvC;UACA,MAAM,IAAI,IAAI,OAAO,IAAI;;;AAE3B,YAAI,GAAG,KAAK;MACd;IACF;AAfA,YAAA,qBAAA;AAiBA,YAAA,UAAe;;;;;;;;;ACxGf,QAAA,YAAA;AACA,QAAA,SAAA;AAIA,QAAM,QAAgC;MACpC,SAAS;MACT,QAAQ,CAAC,EAAC,OAAM,OAAM,GAAA,UAAA,oBAAmB,OAAO,YAAY;;AAG9D,QAAM,MAA6B;MACjC,SAAS;MACT,MAAM;MACN,YAAY,CAAC,UAAU,SAAS;MAChC;MACA,KAAK,KAAe;AAClB,cAAM,EAAC,KAAK,QAAQ,MAAM,GAAE,IAAI;AAChC,aAAI,GAAA,OAAA,mBAAkB,IAAI,MAAM;AAAG;AACnC,cAAM,QAAQ,IAAI,KAAK,OAAO;AAE9B,YAAI,MAAM,OAAO,MAAM,CAAC,QAAO;AAC7B,cAAI,UAAU,EAAC,cAAc,IAAG,CAAC;AACjC,cAAI,UACF;YACE,SAAS;YACT,MAAM;YACN,WAAW,CAAC,QAAQ;YACpB,cAAc;YACd,eAAe;aAEjB,KAAK;AAEP,cAAI,IAAG,GAAA,UAAA,KAAI,KAAK,GAAG,MAAK;AACtB,gBAAI,MAAM,IAAI;AACd,gBAAI,CAAC,GAAG;AAAW,kBAAI,MAAK;UAC9B,CAAC;QACH,CAAC;AAED,YAAI,GAAG,KAAK;MACd;;AAGF,YAAA,UAAe;;;;;;;;;AC1Cf,QAAA,SAAA;AACA,QAAA,YAAA;AACA,QAAA,UAAA;AAEA,QAAA,SAAA;AAQA,QAAM,QAAgC;MACpC,SAAS;MACT,QAAQ,CAAC,EAAC,OAAM,OAAM,GAAA,UAAA,0BAAyB,OAAO,kBAAkB;;AAG1E,QAAM,MAAsD;MAC1D,SAAS;MACT,MAAM,CAAC,QAAQ;MACf,YAAY,CAAC,WAAW,QAAQ;MAChC,gBAAgB;MAChB,aAAa;MACb;MACA,KAAK,KAAG;AACN,cAAM,EAAC,KAAK,QAAQ,cAAc,MAAM,WAAW,GAAE,IAAI;AAEzD,YAAI,CAAC;AAAW,gBAAM,IAAI,MAAM,0BAA0B;AAC1D,cAAM,EAAC,WAAW,KAAI,IAAI;AAC1B,WAAG,QAAQ;AACX,YAAI,KAAK,qBAAqB,UAAS,GAAA,OAAA,mBAAkB,IAAI,MAAM;AAAG;AACtE,cAAM,SAAQ,GAAA,OAAA,qBAAoB,aAAa,UAAU;AACzD,cAAM,YAAW,GAAA,OAAA,qBAAoB,aAAa,iBAAiB;AACnE,kCAAyB;AACzB,YAAI,IAAG,GAAA,UAAA,KAAI,SAAS,QAAQ,QAAA,QAAE,MAAM,EAAE;AAEtC,iBAAS,4BAAyB;AAChC,cAAI,MAAM,OAAO,MAAM,CAAC,QAAa;AACnC,gBAAI,CAAC,MAAM,UAAU,CAAC,SAAS;AAAQ,qCAAuB,GAAG;;AAC5D,kBAAI,GAAG,aAAa,GAAG,GAAG,MAAM,uBAAuB,GAAG,CAAC;UAClE,CAAC;QACH;AAEA,iBAAS,aAAa,KAAS;AAC7B,cAAI;AACJ,cAAI,MAAM,SAAS,GAAG;AAEpB,kBAAM,eAAc,GAAA,OAAA,gBAAe,IAAI,aAAa,YAAY,YAAY;AAC5E,2BAAc,GAAA,OAAA,eAAc,KAAK,aAAqB,GAAG;UAC3D,WAAW,MAAM,QAAQ;AACvB,2BAAc,GAAA,UAAA,IAAG,GAAG,MAAM,IAAI,CAAC,OAAM,GAAA,UAAA,KAAI,GAAG,QAAQ,CAAC,EAAE,CAAC;UAC1D,OAAO;AACL,0BAAc,UAAA;UAChB;AACA,cAAI,SAAS,QAAQ;AACnB,2BAAc,GAAA,UAAA,IAAG,aAAa,GAAG,SAAS,IAAI,CAAC,OAAM,GAAA,UAAA,MAAI,GAAA,OAAA,YAAW,KAAK,CAAC,CAAC,SAAS,GAAG,GAAG,CAAC;UAC7F;AACA,kBAAO,GAAA,UAAA,KAAI,WAAW;QACxB;AAEA,iBAAS,iBAAiB,KAAS;AACjC,cAAI,MAAK,GAAA,UAAA,YAAW,IAAI,IAAI,GAAG,GAAG;QACpC;AAEA,iBAAS,uBAAuB,KAAS;AACvC,cAAI,KAAK,qBAAqB,SAAU,KAAK,oBAAoB,WAAW,OAAQ;AAClF,6BAAiB,GAAG;AACpB;UACF;AAEA,cAAI,WAAW,OAAO;AACpB,gBAAI,UAAU,EAAC,oBAAoB,IAAG,CAAC;AACvC,gBAAI,MAAK;AACT,gBAAI,CAAC;AAAW,kBAAI,MAAK;AACzB;UACF;AAEA,cAAI,OAAO,UAAU,YAAY,EAAC,GAAA,OAAA,mBAAkB,IAAI,MAAM,GAAG;AAC/D,kBAAM,QAAQ,IAAI,KAAK,OAAO;AAC9B,gBAAI,KAAK,qBAAqB,WAAW;AACvC,oCAAsB,KAAK,OAAO,KAAK;AACvC,kBAAI,IAAG,GAAA,UAAA,KAAI,KAAK,GAAG,MAAK;AACtB,oBAAI,MAAK;AACT,iCAAiB,GAAG;cACtB,CAAC;YACH,OAAO;AACL,oCAAsB,KAAK,KAAK;AAChC,kBAAI,CAAC;AAAW,oBAAI,IAAG,GAAA,UAAA,KAAI,KAAK,GAAG,MAAM,IAAI,MAAK,CAAE;YACtD;UACF;QACF;AAEA,iBAAS,sBAAsB,KAAW,OAAa,QAAc;AACnE,gBAAM,YAA2B;YAC/B,SAAS;YACT,UAAU;YACV,cAAc,OAAA,KAAK;;AAErB,cAAI,WAAW,OAAO;AACpB,mBAAO,OAAO,WAAW;cACvB,eAAe;cACf,cAAc;cACd,WAAW;aACZ;UACH;AACA,cAAI,UAAU,WAAW,KAAK;QAChC;MACF;;AAGF,YAAA,UAAe;;;;;;;;;ACpHf,QAAA,aAAA;AACA,QAAA,SAAA;AACA,QAAA,SAAA;AACA,QAAA,yBAAA;AAEA,QAAM,MAA6B;MACjC,SAAS;MACT,MAAM;MACN,YAAY;MACZ,KAAK,KAAe;AAClB,cAAM,EAAC,KAAK,QAAQ,cAAc,MAAM,GAAE,IAAI;AAC9C,YAAI,GAAG,KAAK,qBAAqB,SAAS,aAAa,yBAAyB,QAAW;AACzF,iCAAA,QAAM,KAAK,IAAI,WAAA,WAAW,IAAI,uBAAA,SAAO,sBAAsB,CAAC;QAC9D;AACA,cAAM,YAAW,GAAA,OAAA,qBAAoB,MAAM;AAC3C,mBAAW,QAAQ,UAAU;AAC3B,aAAG,kBAAkB,IAAI,IAAI;QAC/B;AACA,YAAI,GAAG,KAAK,eAAe,SAAS,UAAU,GAAG,UAAU,MAAM;AAC/D,aAAG,QAAQ,OAAA,eAAe,MAAM,MAAK,GAAA,OAAA,QAAO,QAAQ,GAAG,GAAG,KAAK;QACjE;AACA,cAAM,aAAa,SAAS,OAAO,CAAC,MAAM,EAAC,GAAA,OAAA,mBAAkB,IAAI,OAAO,CAAC,CAAC,CAAC;AAC3E,YAAI,WAAW,WAAW;AAAG;AAC7B,cAAM,QAAQ,IAAI,KAAK,OAAO;AAE9B,mBAAW,QAAQ,YAAY;AAC7B,cAAI,WAAW,IAAI,GAAG;AACpB,gCAAoB,IAAI;UAC1B,OAAO;AACL,gBAAI,IAAG,GAAA,OAAA,gBAAe,KAAK,MAAM,MAAM,GAAG,KAAK,aAAa,CAAC;AAC7D,gCAAoB,IAAI;AACxB,gBAAI,CAAC,GAAG;AAAW,kBAAI,KAAI,EAAG,IAAI,OAAO,IAAI;AAC7C,gBAAI,MAAK;UACX;AACA,cAAI,GAAG,kBAAkB,IAAI,IAAI;AACjC,cAAI,GAAG,KAAK;QACd;AAEA,iBAAS,WAAW,MAAY;AAC9B,iBAAO,GAAG,KAAK,eAAe,CAAC,GAAG,iBAAiB,OAAO,IAAI,EAAE,YAAY;QAC9E;AAEA,iBAAS,oBAAoB,MAAY;AACvC,cAAI,UACF;YACE,SAAS;YACT,YAAY;YACZ,UAAU;aAEZ,KAAK;QAET;MACF;;AAGF,YAAA,UAAe;;;;;;;;;ACtDf,QAAA,SAAA;AACA,QAAA,YAAA;AACA,QAAA,SAAA;AACA,QAAA,SAAA;AAGA,QAAM,MAA6B;MACjC,SAAS;MACT,MAAM;MACN,YAAY;MACZ,KAAK,KAAe;AAClB,cAAM,EAAC,KAAK,QAAQ,MAAM,cAAc,GAAE,IAAI;AAC9C,cAAM,EAAC,KAAI,IAAI;AACf,cAAM,YAAW,GAAA,OAAA,qBAAoB,MAAM;AAC3C,cAAM,sBAAsB,SAAS,OAAO,CAAC,OAC3C,GAAA,OAAA,mBAAkB,IAAI,OAAO,CAAC,CAAc,CAAC;AAG/C,YACE,SAAS,WAAW,KACnB,oBAAoB,WAAW,SAAS,WACtC,CAAC,GAAG,KAAK,eAAe,GAAG,UAAU,OACxC;AACA;QACF;AAEA,cAAM,kBACJ,KAAK,gBAAgB,CAAC,KAAK,2BAA2B,aAAa;AACrE,cAAM,QAAQ,IAAI,KAAK,OAAO;AAC9B,YAAI,GAAG,UAAU,QAAQ,EAAE,GAAG,iBAAiB,UAAA,OAAO;AACpD,aAAG,SAAQ,GAAA,OAAA,sBAAqB,KAAK,GAAG,KAAK;QAC/C;AACA,cAAM,EAAC,MAAK,IAAI;AAChB,kCAAyB;AAEzB,iBAAS,4BAAyB;AAChC,qBAAW,OAAO,UAAU;AAC1B,gBAAI;AAAiB,sCAAwB,GAAG;AAChD,gBAAI,GAAG,WAAW;AAChB,iCAAmB,GAAG;YACxB,OAAO;AACL,kBAAI,IAAI,OAAO,IAAI;AACnB,iCAAmB,GAAG;AACtB,kBAAI,GAAG,KAAK;YACd;UACF;QACF;AAEA,iBAAS,wBAAwB,KAAW;AAC1C,qBAAW,QAAQ,iBAAiB;AAClC,gBAAI,IAAI,OAAO,GAAG,EAAE,KAAK,IAAI,GAAG;AAC9B,eAAA,GAAA,OAAA,iBACE,IACA,YAAY,IAAI,oBAAoB,GAAG,gCAAgC;YAE3E;UACF;QACF;AAEA,iBAAS,mBAAmB,KAAW;AACrC,cAAI,MAAM,OAAO,MAAM,CAAC,QAAO;AAC7B,gBAAI,IAAG,GAAA,UAAA,MAAI,GAAA,OAAA,YAAW,KAAK,GAAG,CAAC,SAAS,GAAG,KAAK,MAAK;AACnD,oBAAM,cAAc,oBAAoB,SAAS,GAAG;AACpD,kBAAI,CAAC,aAAa;AAChB,oBAAI,UACF;kBACE,SAAS;kBACT,YAAY;kBACZ,UAAU;kBACV,cAAc,OAAA,KAAK;mBAErB,KAAK;cAET;AAEA,kBAAI,GAAG,KAAK,eAAe,UAAU,MAAM;AACzC,oBAAI,QAAO,GAAA,UAAA,KAAI,KAAK,IAAI,GAAG,KAAK,IAAI;cACtC,WAAW,CAAC,eAAe,CAAC,GAAG,WAAW;AAGxC,oBAAI,IAAG,GAAA,UAAA,KAAI,KAAK,GAAG,MAAM,IAAI,MAAK,CAAE;cACtC;YACF,CAAC;UACH,CAAC;QACH;MACF;;AAGF,YAAA,UAAe;;;;;;;;;ACxFf,QAAA,SAAA;AAIA,QAAM,MAA6B;MACjC,SAAS;MACT,YAAY,CAAC,UAAU,SAAS;MAChC,aAAa;MACb,KAAK,KAAe;AAClB,cAAM,EAAC,KAAK,QAAQ,GAAE,IAAI;AAC1B,aAAI,GAAA,OAAA,mBAAkB,IAAI,MAAM,GAAG;AACjC,cAAI,KAAI;AACR;QACF;AAEA,cAAM,QAAQ,IAAI,KAAK,OAAO;AAC9B,YAAI,UACF;UACE,SAAS;UACT,eAAe;UACf,cAAc;UACd,WAAW;WAEb,KAAK;AAGP,YAAI,WACF,OACA,MAAM,IAAI,MAAK,GACf,MAAM,IAAI,MAAK,CAAE;MAErB;MACA,OAAO,EAAC,SAAS,oBAAmB;;AAGtC,YAAA,UAAe;;;;;;;;;ACpCf,QAAA,SAAA;AAIA,QAAM,MAA6B;MACjC,SAAS;MACT,YAAY;MACZ,aAAa;MACb,MAAM,OAAA;MACN,OAAO,EAAC,SAAS,+BAA8B;;AAGjD,YAAA,UAAe;;;;;;;;;ACNf,QAAA,YAAA;AACA,QAAA,SAAA;AASA,QAAM,QAAgC;MACpC,SAAS;MACT,QAAQ,CAAC,EAAC,OAAM,OAAM,GAAA,UAAA,sBAAqB,OAAO,OAAO;;AAG3D,QAAM,MAA6B;MACjC,SAAS;MACT,YAAY;MACZ,aAAa;MACb;MACA,KAAK,KAAe;AAClB,cAAM,EAAC,KAAK,QAAQ,cAAc,GAAE,IAAI;AAExC,YAAI,CAAC,MAAM,QAAQ,MAAM;AAAG,gBAAM,IAAI,MAAM,0BAA0B;AACtE,YAAI,GAAG,KAAK,iBAAiB,aAAa;AAAe;AACzD,cAAM,SAAsB;AAC5B,cAAM,QAAQ,IAAI,IAAI,SAAS,KAAK;AACpC,cAAM,UAAU,IAAI,IAAI,WAAW,IAAI;AACvC,cAAM,WAAW,IAAI,KAAK,QAAQ;AAClC,YAAI,UAAU,EAAC,QAAO,CAAC;AAGvB,YAAI,MAAM,aAAa;AAEvB,YAAI,OACF,OACA,MAAM,IAAI,MAAK,GACf,MAAM,IAAI,MAAM,IAAI,CAAC;AAGvB,iBAAS,gBAAa;AACpB,iBAAO,QAAQ,CAAC,KAAgB,MAAa;AAC3C,gBAAI;AACJ,iBAAI,GAAA,OAAA,mBAAkB,IAAI,GAAG,GAAG;AAC9B,kBAAI,IAAI,UAAU,IAAI;YACxB,OAAO;AACL,uBAAS,IAAI,UACX;gBACE,SAAS;gBACT,YAAY;gBACZ,eAAe;iBAEjB,QAAQ;YAEZ;AAEA,gBAAI,IAAI,GAAG;AACT,kBACG,IAAG,GAAA,UAAA,KAAI,QAAQ,OAAO,KAAK,EAAE,EAC7B,OAAO,OAAO,KAAK,EACnB,OAAO,UAAS,GAAA,UAAA,MAAK,OAAO,KAAK,CAAC,GAAG,EACrC,KAAI;YACT;AAEA,gBAAI,GAAG,UAAU,MAAK;AACpB,kBAAI,OAAO,OAAO,IAAI;AACtB,kBAAI,OAAO,SAAS,CAAC;AACrB,kBAAI;AAAQ,oBAAI,eAAe,QAAQ,UAAA,IAAI;YAC7C,CAAC;UACH,CAAC;QACH;MACF;;AAGF,YAAA,UAAe;;;;;;;;;AC/Ef,QAAA,SAAA;AAEA,QAAM,MAA6B;MACjC,SAAS;MACT,YAAY;MACZ,KAAK,KAAe;AAClB,cAAM,EAAC,KAAK,QAAQ,GAAE,IAAI;AAE1B,YAAI,CAAC,MAAM,QAAQ,MAAM;AAAG,gBAAM,IAAI,MAAM,0BAA0B;AACtE,cAAM,QAAQ,IAAI,KAAK,OAAO;AAC9B,eAAO,QAAQ,CAAC,KAAgB,MAAa;AAC3C,eAAI,GAAA,OAAA,mBAAkB,IAAI,GAAG;AAAG;AAChC,gBAAM,SAAS,IAAI,UAAU,EAAC,SAAS,SAAS,YAAY,EAAC,GAAG,KAAK;AACrE,cAAI,GAAG,KAAK;AACZ,cAAI,eAAe,MAAM;QAC3B,CAAC;MACH;;AAGF,YAAA,UAAe;;;;;;;;;ACbf,QAAA,YAAA;AACA,QAAA,SAAA;AAIA,QAAM,QAAgC;MACpC,SAAS,CAAC,EAAC,OAAM,OAAM,GAAA,UAAA,mBAAkB,OAAO,QAAQ;MACxD,QAAQ,CAAC,EAAC,OAAM,OAAM,GAAA,UAAA,sBAAqB,OAAO,QAAQ;;AAG5D,QAAM,MAA6B;MACjC,SAAS;MACT,YAAY,CAAC,UAAU,SAAS;MAChC,aAAa;MACb;MACA,KAAK,KAAe;AAClB,cAAM,EAAC,KAAK,cAAc,GAAE,IAAI;AAChC,YAAI,aAAa,SAAS,UAAa,aAAa,SAAS,QAAW;AACtE,WAAA,GAAA,OAAA,iBAAgB,IAAI,2CAA2C;QACjE;AACA,cAAM,UAAU,UAAU,IAAI,MAAM;AACpC,cAAM,UAAU,UAAU,IAAI,MAAM;AACpC,YAAI,CAAC,WAAW,CAAC;AAAS;AAE1B,cAAM,QAAQ,IAAI,IAAI,SAAS,IAAI;AACnC,cAAM,WAAW,IAAI,KAAK,QAAQ;AAClC,mBAAU;AACV,YAAI,MAAK;AAET,YAAI,WAAW,SAAS;AACtB,gBAAM,WAAW,IAAI,IAAI,UAAU;AACnC,cAAI,UAAU,EAAC,SAAQ,CAAC;AACxB,cAAI,GAAG,UAAU,eAAe,QAAQ,QAAQ,GAAG,eAAe,QAAQ,QAAQ,CAAC;QACrF,WAAW,SAAS;AAClB,cAAI,GAAG,UAAU,eAAe,MAAM,CAAC;QACzC,OAAO;AACL,cAAI,IAAG,GAAA,UAAA,KAAI,QAAQ,GAAG,eAAe,MAAM,CAAC;QAC9C;AAEA,YAAI,KAAK,OAAO,MAAM,IAAI,MAAM,IAAI,CAAC;AAErC,iBAAS,aAAU;AACjB,gBAAM,SAAS,IAAI,UACjB;YACE,SAAS;YACT,eAAe;YACf,cAAc;YACd,WAAW;aAEb,QAAQ;AAEV,cAAI,eAAe,MAAM;QAC3B;AAEA,iBAAS,eAAe,SAAiB,UAAe;AACtD,iBAAO,MAAK;AACV,kBAAM,SAAS,IAAI,UAAU,EAAC,QAAO,GAAG,QAAQ;AAChD,gBAAI,OAAO,OAAO,QAAQ;AAC1B,gBAAI,oBAAoB,QAAQ,KAAK;AACrC,gBAAI;AAAU,kBAAI,OAAO,WAAU,GAAA,UAAA,KAAI,OAAO,EAAE;;AAC3C,kBAAI,UAAU,EAAC,UAAU,QAAO,CAAC;UACxC;QACF;MACF;;AAGF,aAAS,UAAU,IAAkB,SAAe;AAClD,YAAM,SAAS,GAAG,OAAO,OAAO;AAChC,aAAO,WAAW,UAAa,EAAC,GAAA,OAAA,mBAAkB,IAAI,MAAM;IAC9D;AAEA,YAAA,UAAe;;;;;;;;;AC7Ef,QAAA,SAAA;AAEA,QAAM,MAA6B;MACjC,SAAS,CAAC,QAAQ,MAAM;MACxB,YAAY,CAAC,UAAU,SAAS;MAChC,KAAK,EAAC,SAAS,cAAc,GAAE,GAAa;AAC1C,YAAI,aAAa,OAAO;AAAW,WAAA,GAAA,OAAA,iBAAgB,IAAI,IAAI,OAAO,2BAA2B;MAC/F;;AAGF,YAAA,UAAe;;;;;;;;;ACXf,QAAA,oBAAA;AACA,QAAA,gBAAA;AACA,QAAA,UAAA;AACA,QAAA,cAAA;AACA,QAAA,aAAA;AACA,QAAA,iBAAA;AACA,QAAA,kBAAA;AACA,QAAA,yBAAA;AACA,QAAA,eAAA;AACA,QAAA,sBAAA;AACA,QAAA,QAAA;AACA,QAAA,UAAA;AACA,QAAA,UAAA;AACA,QAAA,UAAA;AACA,QAAA,OAAA;AACA,QAAA,aAAA;AAEA,aAAwB,cAAc,YAAY,OAAK;AACrD,YAAM,aAAa;;QAEjB,MAAA;QACA,QAAA;QACA,QAAA;QACA,QAAA;QACA,KAAA;QACA,WAAA;;QAEA,gBAAA;QACA,uBAAA;QACA,eAAA;QACA,aAAA;QACA,oBAAA;;AAGF,UAAI;AAAW,mBAAW,KAAK,cAAA,SAAa,YAAA,OAAS;;AAChD,mBAAW,KAAK,kBAAA,SAAiB,QAAA,OAAK;AAC3C,iBAAW,KAAK,WAAA,OAAQ;AACxB,aAAO;IACT;AArBA,YAAA,UAAA;;;;;;;;;ACTA,QAAA,YAAA;AAaA,QAAM,QAAgC;MACpC,SAAS,CAAC,EAAC,WAAU,OAAM,GAAA,UAAA,0BAAyB,UAAU;MAC9D,QAAQ,CAAC,EAAC,WAAU,OAAM,GAAA,UAAA,cAAa,UAAU;;AAGnD,QAAM,MAA6B;MACjC,SAAS;MACT,MAAM,CAAC,UAAU,QAAQ;MACzB,YAAY;MACZ,OAAO;MACP;MACA,KAAK,KAAiB,UAAiB;AACrC,cAAM,EAAC,KAAK,MAAM,OAAO,QAAQ,YAAY,GAAE,IAAI;AACnD,cAAM,EAAC,MAAM,eAAe,WAAW,KAAI,IAAI;AAC/C,YAAI,CAAC,KAAK;AAAiB;AAE3B,YAAI;AAAO,8BAAmB;;AACzB,yBAAc;AAEnB,iBAAS,sBAAmB;AAC1B,gBAAM,OAAO,IAAI,WAAW,WAAW;YACrC,KAAK,KAAK;YACV,MAAM,KAAK,KAAK;WACjB;AACD,gBAAM,OAAO,IAAI,MAAM,SAAQ,GAAA,UAAA,KAAI,IAAI,IAAI,UAAU,GAAG;AACxD,gBAAM,QAAQ,IAAI,IAAI,OAAO;AAC7B,gBAAM,SAAS,IAAI,IAAI,QAAQ;AAE/B,cAAI,IACF,GAAA,UAAA,YAAW,IAAI,qBAAqB,IAAI,uBACxC,MAAM,IAAI,OAAO,QAAO,GAAA,UAAA,KAAI,IAAI,mBAAmB,EAAE,OAAO,SAAQ,GAAA,UAAA,KAAI,IAAI,WAAW,GACvF,MAAM,IAAI,OAAO,QAAO,GAAA,UAAA,YAAW,EAAE,OAAO,QAAQ,IAAI,CAAC;AAE3D,cAAI,WAAU,GAAA,UAAA,IAAG,WAAU,GAAI,WAAU,CAAE,CAAC;AAE5C,mBAAS,aAAU;AACjB,gBAAI,KAAK,iBAAiB;AAAO,qBAAO,UAAA;AACxC,oBAAO,GAAA,UAAA,KAAI,UAAU,QAAQ,MAAM;UACrC;AAEA,mBAAS,aAAU;AACjB,kBAAM,aAAa,UAAU,UACzB,GAAA,UAAA,MAAK,IAAI,kBAAkB,MAAM,IAAI,IAAI,OAAO,MAAM,IAAI,IAAI,QAC9D,GAAA,UAAA,KAAI,MAAM,IAAI,IAAI;AACtB,kBAAM,aAAY,GAAA,UAAA,aAAY,MAAM,oBAAoB,UAAU,MAAM,MAAM,SAAS,IAAI;AAC3F,oBAAO,GAAA,UAAA,KAAI,MAAM,OAAO,MAAM,gBAAgB,KAAK,QAAQ,QAAQ,QAAQ,SAAS;UACtF;QACF;AAEA,iBAAS,iBAAc;AACrB,gBAAM,YAAqC,KAAK,QAAQ,MAAM;AAC9D,cAAI,CAAC,WAAW;AACd,0BAAa;AACb;UACF;AACA,cAAI,cAAc;AAAM;AACxB,gBAAM,CAAC,SAAS,QAAQ,MAAM,IAAI,UAAU,SAAS;AACrD,cAAI,YAAY;AAAU,gBAAI,KAAK,eAAc,CAAE;AAEnD,mBAAS,gBAAa;AACpB,gBAAI,KAAK,iBAAiB,OAAO;AAC/B,mBAAK,OAAO,KAAK,WAAU,CAAE;AAC7B;YACF;AACA,kBAAM,IAAI,MAAM,WAAU,CAAE;AAE5B,qBAAS,aAAU;AACjB,qBAAO,mBAAmB,MAAgB,gCAAgC,aAAa;YACzF;UACF;AAEA,mBAAS,UAAU,QAAmB;AACpC,kBAAM,OACJ,kBAAkB,UACd,GAAA,UAAA,YAAW,MAAM,IACjB,KAAK,KAAK,WACV,GAAA,UAAA,KAAI,KAAK,KAAK,OAAO,IAAG,GAAA,UAAA,aAAY,MAAM,CAAC,KAC3C;AACN,kBAAM,MAAM,IAAI,WAAW,WAAW,EAAC,KAAK,QAAQ,KAAK,QAAQ,KAAI,CAAC;AACtE,gBAAI,OAAO,UAAU,YAAY,EAAE,kBAAkB,SAAS;AAC5D,qBAAO,CAAC,OAAO,QAAQ,UAAU,OAAO,WAAU,GAAA,UAAA,KAAI,GAAG,WAAW;YACtE;AAEA,mBAAO,CAAC,UAAU,QAAQ,GAAG;UAC/B;AAEA,mBAAS,iBAAc;AACrB,gBAAI,OAAO,aAAa,YAAY,EAAE,qBAAqB,WAAW,UAAU,OAAO;AACrF,kBAAI,CAAC,UAAU;AAAQ,sBAAM,IAAI,MAAM,6BAA6B;AACpE,sBAAO,GAAA,UAAA,WAAU,MAAM,IAAI,IAAI;YACjC;AACA,mBAAO,OAAO,UAAU,cAAa,GAAA,UAAA,KAAI,MAAM,IAAI,IAAI,OAAM,GAAA,UAAA,KAAI,MAAM,SAAS,IAAI;UACtF;QACF;MACF;;AAGF,YAAA,UAAe;;;;;;;;;ACtHf,QAAA,WAAA;AAEA,QAAM,SAAqB,CAAC,SAAA,OAAa;AAEzC,YAAA,UAAe;;;;;;;;;;ACHF,YAAA,qBAAiC;MAC5C;MACA;MACA;MACA;MACA;MACA;MACA;;AAGW,YAAA,oBAAgC;MAC3C;MACA;MACA;;;;;;;;;;ACdF,QAAA,SAAA;AACA,QAAA,eAAA;AACA,QAAA,eAAA;AACA,QAAA,WAAA;AACA,QAAA,aAAA;AAEA,QAAM,qBAAmC;MACvC,OAAA;MACA,aAAA;OACA,GAAA,aAAA,SAAuB;MACvB,SAAA;MACA,WAAA;MACA,WAAA;;AAGF,YAAA,UAAe;;;;;;;;;;ACdf,QAAY;AAAZ,KAAA,SAAYC,aAAU;AACpB,MAAAA,YAAA,KAAA,IAAA;AACA,MAAAA,YAAA,SAAA,IAAA;IACF,GAHY,eAAU,QAAA,aAAV,aAAU,CAAA,EAAA;;;;;;;;;ACAtB,QAAA,YAAA;AACA,QAAA,UAAA;AACA,QAAA,YAAA;AACA,QAAA,cAAA;AACA,QAAA,SAAA;AAIA,QAAM,QAAgC;MACpC,SAAS,CAAC,EAAC,QAAQ,EAAC,YAAY,QAAO,EAAC,MACtC,eAAe,QAAA,WAAW,MACtB,QAAQ,OAAO,qBACf,iBAAiB,OAAO;MAC9B,QAAQ,CAAC,EAAC,QAAQ,EAAC,YAAY,KAAK,QAAO,EAAC,OAC1C,GAAA,UAAA,aAAY,UAAU,UAAU,OAAO,eAAe,GAAG;;AAG7D,QAAM,MAA6B;MACjC,SAAS;MACT,MAAM;MACN,YAAY;MACZ;MACA,KAAK,KAAe;AAClB,cAAM,EAAC,KAAK,MAAM,QAAQ,cAAc,GAAE,IAAI;AAC9C,cAAM,EAAC,MAAK,IAAI;AAChB,YAAI,CAAC,GAAG,KAAK,eAAe;AAC1B,gBAAM,IAAI,MAAM,8CAA8C;QAChE;AACA,cAAM,UAAU,OAAO;AACvB,YAAI,OAAO,WAAW;AAAU,gBAAM,IAAI,MAAM,sCAAsC;AACtF,YAAI,OAAO;AAAS,gBAAM,IAAI,MAAM,yCAAyC;AAC7E,YAAI,CAAC;AAAO,gBAAM,IAAI,MAAM,uCAAuC;AACnE,cAAM,QAAQ,IAAI,IAAI,SAAS,KAAK;AACpC,cAAM,MAAM,IAAI,MAAM,QAAO,GAAA,UAAA,KAAI,IAAI,IAAG,GAAA,UAAA,aAAY,OAAO,CAAC,EAAE;AAC9D,YAAI,IACF,GAAA,UAAA,YAAW,GAAG,gBACd,MAAM,gBAAe,GACrB,MAAM,IAAI,MAAM,OAAO,EAAC,YAAY,QAAA,WAAW,KAAK,KAAK,QAAO,CAAC,CAAC;AAEpE,YAAI,GAAG,KAAK;AAEZ,iBAAS,kBAAe;AACtB,gBAAM,UAAU,WAAU;AAC1B,cAAI,GAAG,KAAK;AACZ,qBAAW,YAAY,SAAS;AAC9B,gBAAI,QAAO,GAAA,UAAA,KAAI,GAAG,QAAQ,QAAQ,EAAE;AACpC,gBAAI,OAAO,OAAO,eAAe,QAAQ,QAAQ,CAAC,CAAC;UACrD;AACA,cAAI,KAAI;AACR,cAAI,MAAM,OAAO,EAAC,YAAY,QAAA,WAAW,SAAS,KAAK,QAAO,CAAC;AAC/D,cAAI,MAAK;QACX;AAEA,iBAAS,eAAe,YAAmB;AACzC,gBAAM,SAAS,IAAI,KAAK,OAAO;AAC/B,gBAAM,SAAS,IAAI,UAAU,EAAC,SAAS,SAAS,WAAU,GAAG,MAAM;AACnE,cAAI,eAAe,QAAQ,UAAA,IAAI;AAC/B,iBAAO;QACT;AAEA,iBAAS,aAAU;;AACjB,gBAAM,eAAyC,CAAA;AAC/C,gBAAM,cAAc,YAAY,YAAY;AAC5C,cAAI,cAAc;AAClB,mBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,gBAAI,MAAM,MAAM,CAAC;AACjB,iBAAI,QAAG,QAAH,QAAG,SAAA,SAAH,IAAK,SAAQ,EAAC,GAAA,OAAA,sBAAqB,KAAK,GAAG,KAAK,KAAK,GAAG;AAC1D,oBAAM,MAAM,IAAI;AAChB,oBAAM,UAAA,WAAW,KAAK,GAAG,MAAM,GAAG,UAAU,MAAM,GAAG,QAAQ,GAAG;AAChE,kBAAI,eAAe,UAAA;AAAW,sBAAM,IAAI;AACxC,kBAAI,QAAQ;AAAW,sBAAM,IAAI,YAAA,QAAgB,GAAG,KAAK,aAAa,GAAG,QAAQ,GAAG;YACtF;AACA,kBAAM,WAAU,KAAA,QAAG,QAAH,QAAG,SAAA,SAAH,IAAK,gBAAU,QAAA,OAAA,SAAA,SAAA,GAAG,OAAO;AACzC,gBAAI,OAAO,WAAW,UAAU;AAC9B,oBAAM,IAAI,MACR,iFAAiF,OAAO,GAAG;YAE/F;AACA,0BAAc,gBAAgB,eAAe,YAAY,GAAG;AAC5D,wBAAY,SAAS,CAAC;UACxB;AACA,cAAI,CAAC;AAAa,kBAAM,IAAI,MAAM,mBAAmB,OAAO,oBAAoB;AAChF,iBAAO;AAEP,mBAAS,YAAY,EAAC,SAAQ,GAAkB;AAC9C,mBAAO,MAAM,QAAQ,QAAQ,KAAK,SAAS,SAAS,OAAO;UAC7D;AAEA,mBAAS,YAAY,KAAsB,GAAS;AAClD,gBAAI,IAAI,OAAO;AACb,yBAAW,IAAI,OAAO,CAAC;YACzB,WAAW,IAAI,MAAM;AACnB,yBAAW,YAAY,IAAI,MAAM;AAC/B,2BAAW,UAAU,CAAC;cACxB;YACF,OAAO;AACL,oBAAM,IAAI,MAAM,8BAA8B,OAAO,+BAA+B;YACtF;UACF;AAEA,mBAAS,WAAW,UAAmB,GAAS;AAC9C,gBAAI,OAAO,YAAY,YAAY,YAAY,cAAc;AAC3D,oBAAM,IAAI,MAAM,mBAAmB,OAAO,iCAAiC;YAC7E;AACA,yBAAa,QAAQ,IAAI;UAC3B;QACF;MACF;;AAGF,YAAA,UAAe;;;;;AChHf;AAAA;AAAA;AAAA,MACE,SAAW;AAAA,MACX,KAAO;AAAA,MACP,OAAS;AAAA,MACT,aAAe;AAAA,QACb,aAAe;AAAA,UACb,MAAQ;AAAA,UACR,UAAY;AAAA,UACZ,OAAS,EAAC,MAAQ,IAAG;AAAA,QACvB;AAAA,QACA,oBAAsB;AAAA,UACpB,MAAQ;AAAA,UACR,SAAW;AAAA,QACb;AAAA,QACA,4BAA8B;AAAA,UAC5B,OAAS,CAAC,EAAC,MAAQ,mCAAkC,GAAG,EAAC,SAAW,EAAC,CAAC;AAAA,QACxE;AAAA,QACA,aAAe;AAAA,UACb,MAAQ,CAAC,SAAS,WAAW,WAAW,QAAQ,UAAU,UAAU,QAAQ;AAAA,QAC9E;AAAA,QACA,aAAe;AAAA,UACb,MAAQ;AAAA,UACR,OAAS,EAAC,MAAQ,SAAQ;AAAA,UAC1B,aAAe;AAAA,UACf,SAAW,CAAC;AAAA,QACd;AAAA,MACF;AAAA,MACA,MAAQ,CAAC,UAAU,SAAS;AAAA,MAC5B,YAAc;AAAA,QACZ,KAAO;AAAA,UACL,MAAQ;AAAA,UACR,QAAU;AAAA,QACZ;AAAA,QACA,SAAW;AAAA,UACT,MAAQ;AAAA,UACR,QAAU;AAAA,QACZ;AAAA,QACA,MAAQ;AAAA,UACN,MAAQ;AAAA,UACR,QAAU;AAAA,QACZ;AAAA,QACA,UAAY;AAAA,UACV,MAAQ;AAAA,QACV;AAAA,QACA,OAAS;AAAA,UACP,MAAQ;AAAA,QACV;AAAA,QACA,aAAe;AAAA,UACb,MAAQ;AAAA,QACV;AAAA,QACA,SAAW;AAAA,QACX,UAAY;AAAA,UACV,MAAQ;AAAA,UACR,SAAW;AAAA,QACb;AAAA,QACA,UAAY;AAAA,UACV,MAAQ;AAAA,UACR,OAAS;AAAA,QACX;AAAA,QACA,YAAc;AAAA,UACZ,MAAQ;AAAA,UACR,kBAAoB;AAAA,QACtB;AAAA,QACA,SAAW;AAAA,UACT,MAAQ;AAAA,QACV;AAAA,QACA,kBAAoB;AAAA,UAClB,MAAQ;AAAA,QACV;AAAA,QACA,SAAW;AAAA,UACT,MAAQ;AAAA,QACV;AAAA,QACA,kBAAoB;AAAA,UAClB,MAAQ;AAAA,QACV;AAAA,QACA,WAAa,EAAC,MAAQ,mCAAkC;AAAA,QACxD,WAAa,EAAC,MAAQ,2CAA0C;AAAA,QAChE,SAAW;AAAA,UACT,MAAQ;AAAA,UACR,QAAU;AAAA,QACZ;AAAA,QACA,iBAAmB,EAAC,MAAQ,IAAG;AAAA,QAC/B,OAAS;AAAA,UACP,OAAS,CAAC,EAAC,MAAQ,IAAG,GAAG,EAAC,MAAQ,4BAA2B,CAAC;AAAA,UAC9D,SAAW;AAAA,QACb;AAAA,QACA,UAAY,EAAC,MAAQ,mCAAkC;AAAA,QACvD,UAAY,EAAC,MAAQ,2CAA0C;AAAA,QAC/D,aAAe;AAAA,UACb,MAAQ;AAAA,UACR,SAAW;AAAA,QACb;AAAA,QACA,UAAY,EAAC,MAAQ,IAAG;AAAA,QACxB,eAAiB,EAAC,MAAQ,mCAAkC;AAAA,QAC5D,eAAiB,EAAC,MAAQ,2CAA0C;AAAA,QACpE,UAAY,EAAC,MAAQ,4BAA2B;AAAA,QAChD,sBAAwB,EAAC,MAAQ,IAAG;AAAA,QACpC,aAAe;AAAA,UACb,MAAQ;AAAA,UACR,sBAAwB,EAAC,MAAQ,IAAG;AAAA,UACpC,SAAW,CAAC;AAAA,QACd;AAAA,QACA,YAAc;AAAA,UACZ,MAAQ;AAAA,UACR,sBAAwB,EAAC,MAAQ,IAAG;AAAA,UACpC,SAAW,CAAC;AAAA,QACd;AAAA,QACA,mBAAqB;AAAA,UACnB,MAAQ;AAAA,UACR,sBAAwB,EAAC,MAAQ,IAAG;AAAA,UACpC,eAAiB,EAAC,QAAU,QAAO;AAAA,UACnC,SAAW,CAAC;AAAA,QACd;AAAA,QACA,cAAgB;AAAA,UACd,MAAQ;AAAA,UACR,sBAAwB;AAAA,YACtB,OAAS,CAAC,EAAC,MAAQ,IAAG,GAAG,EAAC,MAAQ,4BAA2B,CAAC;AAAA,UAChE;AAAA,QACF;AAAA,QACA,eAAiB,EAAC,MAAQ,IAAG;AAAA,QAC7B,OAAS;AAAA,QACT,MAAQ;AAAA,UACN,MAAQ;AAAA,UACR,OAAS;AAAA,UACT,UAAY;AAAA,UACZ,aAAe;AAAA,QACjB;AAAA,QACA,MAAQ;AAAA,UACN,OAAS;AAAA,YACP,EAAC,MAAQ,4BAA2B;AAAA,YACpC;AAAA,cACE,MAAQ;AAAA,cACR,OAAS,EAAC,MAAQ,4BAA2B;AAAA,cAC7C,UAAY;AAAA,cACZ,aAAe;AAAA,YACjB;AAAA,UACF;AAAA,QACF;AAAA,QACA,QAAU,EAAC,MAAQ,SAAQ;AAAA,QAC3B,kBAAoB,EAAC,MAAQ,SAAQ;AAAA,QACrC,iBAAmB,EAAC,MAAQ,SAAQ;AAAA,QACpC,IAAM,EAAC,MAAQ,IAAG;AAAA,QAClB,MAAQ,EAAC,MAAQ,IAAG;AAAA,QACpB,MAAQ,EAAC,MAAQ,IAAG;AAAA,QACpB,OAAS,EAAC,MAAQ,4BAA2B;AAAA,QAC7C,OAAS,EAAC,MAAQ,4BAA2B;AAAA,QAC7C,OAAS,EAAC,MAAQ,4BAA2B;AAAA,QAC7C,KAAO,EAAC,MAAQ,IAAG;AAAA,MACrB;AAAA,MACA,SAAW;AAAA,IACb;AAAA;AAAA;;;;;;;ACrJA,QAAA,SAAA;AACA,QAAA,WAAA;AACA,QAAA,kBAAA;AACA,QAAA,mBAAA;AAEA,QAAM,oBAAoB,CAAC,aAAa;AAExC,QAAM,iBAAiB;AAEvB,QAAa,MAAb,cAAyB,OAAA,QAAO;MAC9B,mBAAgB;AACd,cAAM,iBAAgB;AACtB,iBAAA,QAAmB,QAAQ,CAAC,MAAM,KAAK,cAAc,CAAC,CAAC;AACvD,YAAI,KAAK,KAAK;AAAe,eAAK,WAAW,gBAAA,OAAa;MAC5D;MAEA,wBAAqB;AACnB,cAAM,sBAAqB;AAC3B,YAAI,CAAC,KAAK,KAAK;AAAM;AACrB,cAAM,aAAa,KAAK,KAAK,QACzB,KAAK,gBAAgB,kBAAkB,iBAAiB,IACxD;AACJ,aAAK,cAAc,YAAY,gBAAgB,KAAK;AACpD,aAAK,KAAK,+BAA+B,IAAI;MAC/C;MAEA,cAAW;AACT,eAAQ,KAAK,KAAK,cAChB,MAAM,YAAW,MAAO,KAAK,UAAU,cAAc,IAAI,iBAAiB;MAC9E;;AApBF,YAAA,MAAA;AAuBA,WAAO,UAAU,UAAU;AAC3B,WAAO,QAAQ,MAAM;AACrB,WAAO,eAAe,SAAS,cAAc,EAAC,OAAO,KAAI,CAAC;AAE1D,YAAA,UAAe;AA0Bf,QAAA,aAAA;AAAQ,WAAA,eAAA,SAAA,cAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAAA,WAAA;IAAU,EAAA,CAAA;AAIlB,QAAA,YAAA;AAAQ,WAAA,eAAA,SAAA,KAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAAA,UAAA;IAAC,EAAA,CAAA;AAAE,WAAA,eAAA,SAAA,OAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAAA,UAAA;IAAG,EAAA,CAAA;AAAE,WAAA,eAAA,SAAA,aAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAAA,UAAA;IAAS,EAAA,CAAA;AAAE,WAAA,eAAA,SAAA,OAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAAA,UAAA;IAAG,EAAA,CAAA;AAAE,WAAA,eAAA,SAAA,QAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAAA,UAAA;IAAI,EAAA,CAAA;AAAQ,WAAA,eAAA,SAAA,WAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAAA,UAAA;IAAO,EAAA,CAAA;AACnD,QAAA,qBAAA;AAAQ,WAAA,eAAA,SAAA,mBAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAAA,mBAAA;IAAO,EAAA,CAAA;AACf,QAAA,cAAA;AAAQ,WAAA,eAAA,SAAA,mBAAA,EAAA,YAAA,MAAA,KAAA,WAAA;AAAA,aAAA,YAAA;IAAO,EAAA,CAAA;;;", "names": ["UsedValueState", "Type", "DataType", "_a", "DiscrError"]}