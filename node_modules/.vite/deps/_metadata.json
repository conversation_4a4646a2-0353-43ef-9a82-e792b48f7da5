{"hash": "d9df09cd", "configHash": "ead11ac3", "lockfileHash": "2f093bc2", "browserHash": "7e036123", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "4995ca86", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "317157b0", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "f0952509", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "9d2b42f4", "needsInterop": true}, "@dnd-kit/core": {"src": "../../@dnd-kit/core/dist/core.esm.js", "file": "@dnd-kit_core.js", "fileHash": "04cc58c6", "needsInterop": false}, "@dnd-kit/sortable": {"src": "../../@dnd-kit/sortable/dist/sortable.esm.js", "file": "@dnd-kit_sortable.js", "fileHash": "02bdf036", "needsInterop": false}, "@dnd-kit/utilities": {"src": "../../@dnd-kit/utilities/dist/utilities.esm.js", "file": "@dnd-kit_utilities.js", "fileHash": "ecdcd379", "needsInterop": false}, "ajv": {"src": "../../ajv/dist/ajv.js", "file": "ajv.js", "fileHash": "e44eed3a", "needsInterop": true}, "ajv-formats": {"src": "../../ajv-formats/dist/index.js", "file": "ajv-formats.js", "fileHash": "cf763050", "needsInterop": true}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "b53038b8", "needsInterop": false}, "nanoid": {"src": "../../nanoid/index.browser.js", "file": "nanoid.js", "fileHash": "b6345bb9", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "55c5e8eb", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "7fe68789", "needsInterop": false}, "zustand": {"src": "../../zustand/esm/index.mjs", "file": "zustand.js", "fileHash": "6fb93142", "needsInterop": false}, "zustand/middleware": {"src": "../../zustand/esm/middleware.mjs", "file": "zustand_middleware.js", "fileHash": "33f86223", "needsInterop": false}}, "chunks": {"chunk-7QLZELLG": {"file": "chunk-7QLZELLG.js"}, "chunk-YEXTT7AB": {"file": "chunk-YEXTT7AB.js"}, "chunk-HE4GKDYE": {"file": "chunk-HE4GKDYE.js"}, "chunk-SWGRY3PF": {"file": "chunk-SWGRY3PF.js"}, "chunk-UGC3UZ7L": {"file": "chunk-UGC3UZ7L.js"}, "chunk-G3PMV62Z": {"file": "chunk-G3PMV62Z.js"}}}