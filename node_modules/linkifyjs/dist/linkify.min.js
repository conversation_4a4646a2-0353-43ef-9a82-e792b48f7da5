var linkify=function(t){"use strict";const e=(t,e)=>{for(const n in e)t[n]=e[n];return t},n="numeric",i="ascii",r="alpha",s="asciinumeric",o="alphanumeric",a="domain",l="emoji",u="scheme",c="slashscheme",g="whitespace";function h(t,e){return t in e||(e[t]=[]),e[t]}function d(t,e,u){e[n]&&(e[s]=!0,e[o]=!0),e[i]&&(e[s]=!0,e[r]=!0),e[s]&&(e[o]=!0),e[r]&&(e[o]=!0),e[o]&&(e[a]=!0),e[l]&&(e[a]=!0);for(const n in e){const e=h(n,u);e.indexOf(t)<0&&e.push(t)}}function f(t=null){this.j={},this.jr=[],this.jd=null,this.t=t}f.groups={},f.prototype={accepts(){return!!this.t},go(t){const e=this,n=e.j[t];if(n)return n;for(let n=0;n<e.jr.length;n++){const i=e.jr[n][0],r=e.jr[n][1];if(r&&i.test(t))return r}return e.jd},has(t,e=!1){return e?t in this.j:!!this.go(t)},ta(t,e,n,i){for(let r=0;r<t.length;r++)this.tt(t[r],e,n,i)},tr(t,e,n,i){let r;return i=i||f.groups,e&&e.j?r=e:(r=new f(e),n&&i&&d(e,n,i)),this.jr.push([t,r]),r},ts(t,e,n,i){let r=this;const s=t.length;if(!s)return r;for(let e=0;e<s-1;e++)r=r.tt(t[e]);return r.tt(t[s-1],e,n,i)},tt(t,n,i,r){r=r||f.groups;const s=this;if(n&&n.j)return s.j[t]=n,n;const o=n;let a,l=s.go(t);if(l?(a=new f,e(a.j,l.j),a.jr.push.apply(a.jr,l.jr),a.jd=l.jd,a.t=l.t):a=new f,o){if(r)if(a.t&&"string"==typeof a.t){const t=e(function(t,e){const n={};for(const i in e)e[i].indexOf(t)>=0&&(n[i]=!0);return n}(a.t,r),i);d(o,t,r)}else i&&d(o,i,r);a.t=o}return s.j[t]=a,a}};const p=(t,e,n,i,r)=>t.ta(e,n,i,r),m=(t,e,n,i,r)=>t.tr(e,n,i,r),E=(t,e,n,i,r)=>t.ts(e,n,i,r),k=(t,e,n,i,r)=>t.tt(e,n,i,r),y="WORD",b="UWORD",T="ASCIINUMERICAL",L="ALPHANUMERICAL",A="LOCALHOST",R="TLD",C="UTLD",O="SCHEME",v="SLASH_SCHEME",S="NUM",w="WS",I="NL",j="OPENBRACE",N="CLOSEBRACE",H="OPENBRACKET",x="CLOSEBRACKET",P="OPENPAREN",z="CLOSEPAREN",D="OPENANGLEBRACKET",U="CLOSEANGLEBRACKET",M="FULLWIDTHLEFTPAREN",B="FULLWIDTHRIGHTPAREN",K="LEFTCORNERBRACKET",F="RIGHTCORNERBRACKET",Q="LEFTWHITECORNERBRACKET",_="RIGHTWHITECORNERBRACKET",W="FULLWIDTHLESSTHAN",$="FULLWIDTHGREATERTHAN",G="AMPERSAND",q="APOSTROPHE",Y="ASTERISK",J="AT",X="BACKSLASH",V="BACKTICK",Z="CARET",tt="COLON",et="COMMA",nt="DOLLAR",it="DOT",rt="EQUALS",st="EXCLAMATION",ot="HYPHEN",at="PERCENT",lt="PIPE",ut="PLUS",ct="POUND",gt="QUERY",ht="QUOTE",dt="FULLWIDTHMIDDLEDOT",ft="SEMI",pt="SLASH",mt="TILDE",Et="UNDERSCORE",kt="EMOJI",yt="SYM";var bt=Object.freeze({__proto__:null,ALPHANUMERICAL:L,AMPERSAND:G,APOSTROPHE:q,ASCIINUMERICAL:T,ASTERISK:Y,AT:J,BACKSLASH:X,BACKTICK:V,CARET:Z,CLOSEANGLEBRACKET:U,CLOSEBRACE:N,CLOSEBRACKET:x,CLOSEPAREN:z,COLON:tt,COMMA:et,DOLLAR:nt,DOT:it,EMOJI:kt,EQUALS:rt,EXCLAMATION:st,FULLWIDTHGREATERTHAN:$,FULLWIDTHLEFTPAREN:M,FULLWIDTHLESSTHAN:W,FULLWIDTHMIDDLEDOT:dt,FULLWIDTHRIGHTPAREN:B,HYPHEN:ot,LEFTCORNERBRACKET:K,LEFTWHITECORNERBRACKET:Q,LOCALHOST:A,NL:I,NUM:S,OPENANGLEBRACKET:D,OPENBRACE:j,OPENBRACKET:H,OPENPAREN:P,PERCENT:at,PIPE:lt,PLUS:ut,POUND:ct,QUERY:gt,QUOTE:ht,RIGHTCORNERBRACKET:F,RIGHTWHITECORNERBRACKET:_,SCHEME:O,SEMI:ft,SLASH:pt,SLASH_SCHEME:v,SYM:yt,TILDE:mt,TLD:R,UNDERSCORE:Et,UTLD:C,UWORD:b,WORD:y,WS:w});const Tt=/[a-z]/,Lt=/\p{L}/u,At=/\p{Emoji}/u,Rt=/\d/,Ct=/\s/;var Ot=Object.freeze({__proto__:null,ASCII_LETTER:Tt,DIGIT:Rt,EMOJI:At,EMOJI_VARIATION:/\ufe0f/,LETTER:Lt,SPACE:Ct});let vt=null,St=null;function wt(t,e){const n=It(e.replace(/[A-Z]/g,(t=>t.toLowerCase()))),i=n.length,r=[];let s=0,o=0;for(;o<i;){let a=t,l=null,u=0,c=null,g=-1,h=-1;for(;o<i&&(l=a.go(n[o]));)a=l,a.accepts()?(g=0,h=0,c=a):g>=0&&(g+=n[o].length,h++),u+=n[o].length,s+=n[o].length,o++;s-=g,o-=h,u-=g,r.push({t:c.t,v:e.slice(s-u,s),s:s-u,e:s})}return r}function It(t){const e=[],n=t.length;let i=0;for(;i<n;){let r,s=t.charCodeAt(i),o=s<55296||s>56319||i+1===n||(r=t.charCodeAt(i+1))<56320||r>57343?t[i]:t.slice(i,i+2);e.push(o),i+=o.length}return e}function jt(t,e,n,i,r){let s;const o=e.length;for(let n=0;n<o-1;n++){const o=e[n];t.j[o]?s=t.j[o]:(s=new f(i),s.jr=r.slice(),t.j[o]=s),t=s}return s=new f(n),s.jr=r.slice(),t.j[e[o-1]]=s,s}function Nt(t){const e=[],n=[];let i=0;for(;i<t.length;){let r=0;for(;"0123456789".indexOf(t[i+r])>=0;)r++;if(r>0){e.push(n.join(""));for(let e=parseInt(t.substring(i,i+r),10);e>0;e--)n.pop();i+=r}else n.push(t[i]),i++}return e}const Ht={defaultProtocol:"http",events:null,format:Pt,formatHref:Pt,nl2br:!1,tagName:"a",target:null,rel:null,validate:!0,truncate:1/0,className:null,attributes:null,ignoreTags:[],render:null};function xt(t,n=null){let i=e({},Ht);t&&(i=e(i,t instanceof xt?t.o:t));const r=i.ignoreTags,s=[];for(let t=0;t<r.length;t++)s.push(r[t].toUpperCase());this.o=i,n&&(this.defaultRender=n),this.ignoreTags=s}function Pt(t){return t}xt.prototype={o:Ht,ignoreTags:[],defaultRender:t=>t,check(t){return this.get("validate",t.toString(),t)},get(t,e,n){const i=null!=e;let r=this.o[t];return r?("object"==typeof r?(r=n.t in r?r[n.t]:Ht[t],"function"==typeof r&&i&&(r=r(e,n))):"function"==typeof r&&i&&(r=r(e,n.t,n)),r):r},getObj(t,e,n){let i=this.o[t];return"function"==typeof i&&null!=e&&(i=i(e,n.t,n)),i},render(t){const e=t.render(this);return(this.get("render",null,t)||this.defaultRender)(e,t.t,t)}};var zt=Object.freeze({__proto__:null,Options:xt,assign:e,defaults:Ht});function Dt(t,e){this.t="token",this.v=t,this.tk=e}function Ut(t,e){class n extends Dt{constructor(e,n){super(e,n),this.t=t}}for(const t in e)n.prototype[t]=e[t];return n.t=t,n}Dt.prototype={isLink:!1,toString(){return this.v},toHref(t){return this.toString()},toFormattedString(t){const e=this.toString(),n=t.get("truncate",e,this),i=t.get("format",e,this);return n&&i.length>n?i.substring(0,n)+"…":i},toFormattedHref(t){return t.get("formatHref",this.toHref(t.get("defaultProtocol")),this)},startIndex(){return this.tk[0].s},endIndex(){return this.tk[this.tk.length-1].e},toObject(t=Ht.defaultProtocol){return{type:this.t,value:this.toString(),isLink:this.isLink,href:this.toHref(t),start:this.startIndex(),end:this.endIndex()}},toFormattedObject(t){return{type:this.t,value:this.toFormattedString(t),isLink:this.isLink,href:this.toFormattedHref(t),start:this.startIndex(),end:this.endIndex()}},validate(t){return t.get("validate",this.toString(),this)},render(t){const n=this,i=this.toHref(t.get("defaultProtocol")),r=t.get("formatHref",i,this),s=t.get("tagName",i,n),o=this.toFormattedString(t),a={},l=t.get("className",i,n),u=t.get("target",i,n),c=t.get("rel",i,n),g=t.getObj("attributes",i,n),h=t.getObj("events",i,n);return a.href=r,l&&(a.class=l),u&&(a.target=u),c&&(a.rel=c),g&&e(a,g),{tagName:s,attributes:a,content:o,eventListeners:h}}};const Mt=Ut("email",{isLink:!0,toHref(){return"mailto:"+this.toString()}}),Bt=Ut("text"),Kt=Ut("nl"),Ft=Ut("url",{isLink:!0,toHref(t=Ht.defaultProtocol){return this.hasProtocol()?this.v:`${t}://${this.v}`},hasProtocol(){const t=this.tk;return t.length>=2&&t[0].t!==A&&t[1].t===tt}});var Qt=Object.freeze({__proto__:null,Base:Dt,Email:Mt,MultiToken:Dt,Nl:Kt,Text:Bt,Url:Ft,createTokenClass:Ut});const _t=t=>new f(t);function Wt(t,e,n){const i=n[0].s,r=n[n.length-1].e;return new t(e.slice(i,r),n)}const $t="undefined"!=typeof console&&console&&console.warn||(()=>{}),Gt="until manual call of linkify.init(). Register all schemes and plugins before invoking linkify the first time.",qt={scanner:null,parser:null,tokenQueue:[],pluginQueue:[],customSchemes:[],initialized:!1};function Yt(){qt.scanner=function(t=[]){const h={};f.groups=h;const p=new f;null==vt&&(vt=Nt("aaa1rp3bb0ott3vie4c1le2ogado5udhabi7c0ademy5centure6ountant0s9o1tor4d0s1ult4e0g1ro2tna4f0l1rica5g0akhan5ency5i0g1rbus3force5tel5kdn3l0ibaba4pay4lfinanz6state5y2sace3tom5m0azon4ericanexpress7family11x2fam3ica3sterdam8nalytics7droid5quan4z2o0l2partments8p0le4q0uarelle8r0ab1mco4chi3my2pa2t0e3s0da2ia2sociates9t0hleta5torney7u0ction5di0ble3o3spost5thor3o0s4w0s2x0a2z0ure5ba0by2idu3namex4d1k2r0celona5laycard4s5efoot5gains6seball5ketball8uhaus5yern5b0c1t1va3cg1n2d1e0ats2uty4er2rlin4st0buy5t2f1g1h0arti5i0ble3d1ke2ng0o3o1z2j1lack0friday9ockbuster8g1omberg7ue3m0s1w2n0pparibas9o0ats3ehringer8fa2m1nd2o0k0ing5sch2tik2on4t1utique6x2r0adesco6idgestone9oadway5ker3ther5ussels7s1t1uild0ers6siness6y1zz3v1w1y1z0h3ca0b1fe2l0l1vinklein9m0era3p2non3petown5ital0one8r0avan4ds2e0er0s4s2sa1e1h1ino4t0ering5holic7ba1n1re3c1d1enter4o1rn3f0a1d2g1h0anel2nel4rity4se2t2eap3intai5ristmas6ome4urch5i0priani6rcle4sco3tadel4i0c2y3k1l0aims4eaning6ick2nic1que6othing5ud3ub0med6m1n1o0ach3des3ffee4llege4ogne5m0mbank4unity6pany2re3uter5sec4ndos3struction8ulting7tact3ractors9oking4l1p2rsica5untry4pon0s4rses6pa2r0edit0card4union9icket5own3s1uise0s6u0isinella9v1w1x1y0mru3ou3z2dad1nce3ta1e1ing3sun4y2clk3ds2e0al0er2s3gree4livery5l1oitte5ta3mocrat6ntal2ist5si0gn4v2hl2iamonds6et2gital5rect0ory7scount3ver5h2y2j1k1m1np2o0cs1tor4g1mains5t1wnload7rive4tv2ubai3nlop4pont4rban5vag2r2z2earth3t2c0o2deka3u0cation8e1g1mail3erck5nergy4gineer0ing9terprises10pson4quipment8r0icsson6ni3s0q1tate5t1u0rovision8s2vents5xchange6pert3osed4ress5traspace10fage2il1rwinds6th3mily4n0s2rm0ers5shion4t3edex3edback6rrari3ero6i0delity5o2lm2nal1nce1ial7re0stone6mdale6sh0ing5t0ness6j1k1lickr3ghts4r2orist4wers5y2m1o0o0d1tball6rd1ex2sale4um3undation8x2r0ee1senius7l1ogans4ntier7tr2ujitsu5n0d2rniture7tbol5yi3ga0l0lery3o1up4me0s3p1rden4y2b0iz3d0n2e0a1nt0ing5orge5f1g0ee3h1i0ft0s3ves2ing5l0ass3e1obal2o4m0ail3bh2o1x2n1odaddy5ld0point6f2o0dyear5g0le4p1t1v2p1q1r0ainger5phics5tis4een3ipe3ocery4up4s1t1u0cci3ge2ide2tars5ru3w1y2hair2mburg5ngout5us3bo2dfc0bank7ealth0care8lp1sinki6re1mes5iphop4samitsu7tachi5v2k0t2m1n1ockey4ldings5iday5medepot5goods5s0ense7nda3rse3spital5t0ing5t0els3mail5use3w2r1sbc3t1u0ghes5yatt3undai7ibm2cbc2e1u2d1e0ee3fm2kano4l1m0amat4db2mo0bilien9n0c1dustries8finiti5o2g1k1stitute6urance4e4t0ernational10uit4vestments10o1piranga7q1r0ish4s0maili5t0anbul7t0au2v3jaguar4va3cb2e0ep2tzt3welry6io2ll2m0p2nj2o0bs1urg4t1y2p0morgan6rs3uegos4niper7kaufen5ddi3e0rryhotels6properties14fh2g1h1i0a1ds2m1ndle4tchen5wi3m1n1oeln3matsu5sher5p0mg2n2r0d1ed3uokgroup8w1y0oto4z2la0caixa5mborghini8er3nd0rover6xess5salle5t0ino3robe5w0yer5b1c1ds2ease3clerc5frak4gal2o2xus4gbt3i0dl2fe0insurance9style7ghting6ke2lly3mited4o2ncoln4k2ve1ing5k1lc1p2oan0s3cker3us3l1ndon4tte1o3ve3pl0financial11r1s1t0d0a3u0ndbeck6xe1ury5v1y2ma0drid4if1son4keup4n0agement7go3p1rket0ing3s4riott5shalls7ttel5ba2c0kinsey7d1e0d0ia3et2lbourne7me1orial6n0u2rckmsd7g1h1iami3crosoft7l1ni1t2t0subishi9k1l0b1s2m0a2n1o0bi0le4da2e1i1m1nash3ey2ster5rmon3tgage6scow4to0rcycles9v0ie4p1q1r1s0d2t0n1r2u0seum3ic4v1w1x1y1z2na0b1goya4me2vy3ba2c1e0c1t0bank4flix4work5ustar5w0s2xt0direct7us4f0l2g0o2hk2i0co2ke1on3nja3ssan1y5l1o0kia3rton4w0ruz3tv4p1r0a1w2tt2u1yc2z2obi1server7ffice5kinawa6layan0group9lo3m0ega4ne1g1l0ine5oo2pen3racle3nge4g0anic5igins6saka4tsuka4t2vh3pa0ge2nasonic7ris2s1tners4s1y3y2ccw3e0t2f0izer5g1h0armacy6d1ilips5one2to0graphy6s4ysio5ics1tet2ures6d1n0g1k2oneer5zza4k1l0ace2y0station9umbing5s3m1n0c2ohl2ker3litie5rn2st3r0america6xi3ess3ime3o0d0uctions8f1gressive8mo2perties3y5tection8u0dential9s1t1ub2w0c2y2qa1pon3uebec3st5racing4dio4e0ad1lestate6tor2y4cipes5d0stone5umbrella9hab3ise0n3t2liance6n0t0als5pair3ort3ublican8st0aurant8view0s5xroth6ich0ardli6oh3l1o1p2o0cks3deo3gers4om3s0vp3u0gby3hr2n2w0e2yukyu6sa0arland6fe0ty4kura4le1on3msclub4ung5ndvik0coromant12ofi4p1rl2s1ve2xo3b0i1s2c0b1haeffler7midt4olarships8ol3ule3warz5ience5ot3d1e0arch3t2cure1ity6ek2lect4ner3rvices6ven3w1x0y3fr2g1h0angrila6rp3ell3ia1ksha5oes2p0ping5uji3w3i0lk2na1gles5te3j1k0i0n2y0pe4l0ing4m0art3ile4n0cf3o0ccer3ial4ftbank4ware6hu2lar2utions7ng1y2y2pa0ce3ort2t3r0l2s1t0ada2ples4r1tebank4farm7c0group6ockholm6rage3e3ream4udio2y3yle4u0cks3pplies3y2ort5rf1gery5zuki5v1watch4iss4x1y0dney4stems6z2tab1ipei4lk2obao4rget4tamotors6r2too4x0i3c0i2d0k2eam2ch0nology8l1masek5nnis4va3f1g1h0d1eater2re6iaa2ckets5enda4ps2res2ol4j0maxx4x2k0maxx5l1m0all4n1o0day3kyo3ols3p1ray3shiba5tal3urs3wn2yota3s3r0ade1ing4ining5vel0ers0insurance16ust3v2t1ube2i1nes3shu4v0s2w1z2ua1bank3s2g1k1nicom3versity8o2ol2ps2s1y1z2va0cations7na1guard7c1e0gas3ntures6risign5mögensberater2ung14sicherung10t2g1i0ajes4deo3g1king4llas4n1p1rgin4sa1ion4va1o3laanderen9n1odka3lvo3te1ing3o2yage5u2wales2mart4ter4ng0gou5tch0es6eather0channel12bcam3er2site5d0ding5ibo2r3f1hoswho6ien2ki2lliamhill9n0dows4e1ners6me2olterskluwer11odside6rk0s2ld3w2s1tc1f3xbox3erox4ihuan4n2xx2yz3yachts4hoo3maxun5ndex5e1odobashi7ga2kohama6u0tube6t1un3za0ppos4ra3ero3ip2m1one3uerich6w2")),null==St&&(St=Nt("ελ1υ2бг1ел3дети4ею2католик6ом3мкд2он1сква6онлайн5рг3рус2ф2сайт3рб3укр3қаз3հայ3ישראל5קום3ابوظبي5رامكو5لاردن4بحرين5جزائر5سعودية6عليان5مغرب5مارات5یران5بارت2زار4يتك3ھارت5تونس4سودان3رية5شبكة4عراق2ب2مان4فلسطين6قطر3كاثوليك6وم3مصر2ليسيا5وريتانيا7قع4همراه5پاکستان7ڀارت4कॉम3नेट3भारत0म्3ोत5संगठन5বাংলা5ভারত2ৰত4ਭਾਰਤ4ભારત4ଭାରତ4இந்தியா6லங்கை6சிங்கப்பூர்11భారత్5ಭಾರತ4ഭാരതം5ලංකා4คอม3ไทย3ລາວ3გე2みんな3アマゾン4クラウド4グーグル4コム2ストア3セール3ファッション6ポイント4世界2中信1国1國1文网3亚马逊3企业2佛山2信息2健康2八卦2公司1益2台湾1灣2商城1店1标2嘉里0大酒店5在线2大拿2天主教3娱乐2家電2广东2微博2慈善2我爱你3手机2招聘2政务1府2新加坡2闻2时尚2書籍2机构2淡马锡3游戏2澳門2点看2移动2组织机构4网址1店1站1络2联通2谷歌2购物2通販2集团2電訊盈科4飞利浦3食品2餐厅2香格里拉3港2닷넷1컴2삼성2한국2")),k(p,"'",q),k(p,"{",j),k(p,"}",N),k(p,"[",H),k(p,"]",x),k(p,"(",P),k(p,")",z),k(p,"<",D),k(p,">",U),k(p,"（",M),k(p,"）",B),k(p,"「",K),k(p,"」",F),k(p,"『",Q),k(p,"』",_),k(p,"＜",W),k(p,"＞",$),k(p,"&",G),k(p,"*",Y),k(p,"@",J),k(p,"`",V),k(p,"^",Z),k(p,":",tt),k(p,",",et),k(p,"$",nt),k(p,".",it),k(p,"=",rt),k(p,"!",st),k(p,"-",ot),k(p,"%",at),k(p,"|",lt),k(p,"+",ut),k(p,"#",ct),k(p,"?",gt),k(p,'"',ht),k(p,"/",pt),k(p,";",ft),k(p,"~",mt),k(p,"_",Et),k(p,"\\",X),k(p,"・",dt);const Ot=m(p,Rt,S,{[n]:!0});m(Ot,Rt,Ot);const wt=m(Ot,Tt,T,{[s]:!0}),It=m(Ot,Lt,L,{[o]:!0}),Ht=m(p,Tt,y,{[i]:!0});m(Ht,Rt,wt),m(Ht,Tt,Ht),m(wt,Rt,wt),m(wt,Tt,wt);const xt=m(p,Lt,b,{[r]:!0});m(xt,Tt),m(xt,Rt,It),m(xt,Lt,xt),m(It,Rt,It),m(It,Tt),m(It,Lt,It);const Pt=k(p,"\n",I,{[g]:!0}),zt=k(p,"\r",w,{[g]:!0}),Dt=m(p,Ct,w,{[g]:!0});k(p,"￼",Dt),k(zt,"\n",Pt),k(zt,"￼",Dt),m(zt,Ct,Dt),k(Dt,"\r"),k(Dt,"\n"),m(Dt,Ct,Dt),k(Dt,"￼",Dt);const Ut=m(p,At,kt,{[l]:!0});k(Ut,"#"),m(Ut,At,Ut),k(Ut,"️",Ut);const Mt=k(Ut,"‍");k(Mt,"#"),m(Mt,At,Ut);const Bt=[[Tt,Ht],[Rt,wt]],Kt=[[Tt,null],[Lt,xt],[Rt,It]];for(let t=0;t<vt.length;t++)jt(p,vt[t],R,y,Bt);for(let t=0;t<St.length;t++)jt(p,St[t],C,b,Kt);d(R,{tld:!0,ascii:!0},h),d(C,{utld:!0,alpha:!0},h),jt(p,"file",O,y,Bt),jt(p,"mailto",O,y,Bt),jt(p,"http",v,y,Bt),jt(p,"https",v,y,Bt),jt(p,"ftp",v,y,Bt),jt(p,"ftps",v,y,Bt),d(O,{scheme:!0,ascii:!0},h),d(v,{slashscheme:!0,ascii:!0},h),t=t.sort(((t,e)=>t[0]>e[0]?1:-1));for(let e=0;e<t.length;e++){const r=t[e][0],o=t[e][1]?{[u]:!0}:{[c]:!0};r.indexOf("-")>=0?o[a]=!0:Tt.test(r)?Rt.test(r)?o[s]=!0:o[i]=!0:o[n]=!0,E(p,r,r,o)}return E(p,"localhost",A,{ascii:!0}),p.jd=new f(yt),{start:p,tokens:e({groups:h},bt)}}(qt.customSchemes);for(let t=0;t<qt.tokenQueue.length;t++)qt.tokenQueue[t][1]({scanner:qt.scanner});qt.parser=function({groups:t}){const e=t.domain.concat([G,Y,J,X,V,Z,nt,rt,ot,S,at,lt,ut,ct,pt,yt,mt,Et]),n=[q,tt,et,it,st,at,gt,ht,ft,D,U,j,N,x,H,P,z,M,B,K,F,Q,_,W,$],i=[G,q,Y,X,V,Z,nt,rt,ot,j,N,at,lt,ut,ct,gt,pt,yt,mt,Et],r=_t(),s=k(r,mt);p(s,i,s),p(s,t.domain,s);const o=_t(),a=_t(),l=_t();p(r,t.domain,o),p(r,t.scheme,a),p(r,t.slashscheme,l),p(o,i,s),p(o,t.domain,o);const u=k(o,J);k(s,J,u),k(a,J,u),k(l,J,u);const c=k(s,it);p(c,i,s),p(c,t.domain,s);const g=_t();p(u,t.domain,g),p(g,t.domain,g);const h=k(g,it);p(h,t.domain,g);const d=_t(Mt);p(h,t.tld,d),p(h,t.utld,d),k(u,A,d);const f=k(g,ot);k(f,ot,f),p(f,t.domain,g),p(d,t.domain,g),k(d,it,h),k(d,ot,f);const m=k(d,tt);p(m,t.numeric,Mt);const E=k(o,ot),y=k(o,it);k(E,ot,E),p(E,t.domain,o),p(y,i,s),p(y,t.domain,o);const b=_t(Ft);p(y,t.tld,b),p(y,t.utld,b),p(b,t.domain,o),p(b,i,s),k(b,it,y),k(b,ot,E),k(b,J,u);const T=k(b,tt),L=_t(Ft);p(T,t.numeric,L);const R=_t(Ft),C=_t();p(R,e,R),p(R,n,C),p(C,e,R),p(C,n,C),k(b,pt,R),k(L,pt,R);const O=k(a,tt),v=k(l,tt),w=k(v,pt),dt=k(w,pt);p(a,t.domain,o),k(a,it,y),k(a,ot,E),p(l,t.domain,o),k(l,it,y),k(l,ot,E),p(O,t.domain,R),k(O,pt,R),k(O,gt,R),p(dt,t.domain,R),p(dt,e,R),k(dt,pt,R);const kt=[[j,N],[H,x],[P,z],[D,U],[M,B],[K,F],[Q,_],[W,$]];for(let t=0;t<kt.length;t++){const[i,r]=kt[t],s=k(R,i);k(C,i,s),k(s,r,R);const o=_t(Ft);p(s,e,o);const a=_t();p(s,n),p(o,e,o),p(o,n,a),p(a,e,o),p(a,n,a),k(o,r,R),k(a,r,R)}return k(r,A,b),k(r,I,Kt),{start:r,tokens:bt}}(qt.scanner.tokens);for(let t=0;t<qt.pluginQueue.length;t++)qt.pluginQueue[t][1]({scanner:qt.scanner,parser:qt.parser});return qt.initialized=!0,qt}function Jt(t){return qt.initialized||Yt(),function(t,e,n){let i=n.length,r=0,s=[],o=[];for(;r<i;){let a=t,l=null,u=null,c=0,g=null,h=-1;for(;r<i&&!(l=a.go(n[r].t));)o.push(n[r++]);for(;r<i&&(u=l||a.go(n[r].t));)l=null,a=u,a.accepts()?(h=0,g=a):h>=0&&h++,r++,c++;if(h<0)r-=c,r<i&&(o.push(n[r]),r++);else{o.length>0&&(s.push(Wt(Bt,e,o)),o=[]),r-=h,c-=h;const t=g.t,i=n.slice(r-c,r);s.push(Wt(t,e,i))}}return o.length>0&&s.push(Wt(Bt,e,o)),s}(qt.parser.start,t,wt(qt.scanner.start,t))}return Jt.scan=wt,t.MultiToken=Dt,t.Options=xt,t.State=f,t.createTokenClass=Ut,t.find=function(t,e=null,n=null){if(e&&"object"==typeof e){if(n)throw Error(`linkifyjs: Invalid link type ${e}; must be a string`);n=e,e=null}const i=new xt(n),r=Jt(t),s=[];for(let t=0;t<r.length;t++){const n=r[t];!n.isLink||e&&n.t!==e||!i.check(n)||s.push(n.toFormattedObject(i))}return s},t.init=Yt,t.multi=Qt,t.options=zt,t.regexp=Ot,t.registerCustomProtocol=function(t,e=!1){if(qt.initialized&&$t(`linkifyjs: already initialized - will not register custom scheme "${t}" ${Gt}`),!/^[0-9a-z]+(-[0-9a-z]+)*$/.test(t))throw new Error('linkifyjs: incorrect scheme format.\n1. Must only contain digits, lowercase ASCII letters or "-"\n2. Cannot start or end with "-"\n3. "-" cannot repeat');qt.customSchemes.push([t,e])},t.registerPlugin=function(t,e){if("function"!=typeof e)throw new Error(`linkifyjs: Invalid plugin ${e} (expects function)`);for(let n=0;n<qt.pluginQueue.length;n++)if(t===qt.pluginQueue[n][0])return $t(`linkifyjs: plugin "${t}" already registered - will be overwritten`),void(qt.pluginQueue[n]=[t,e]);qt.pluginQueue.push([t,e]),qt.initialized&&$t(`linkifyjs: already initialized - will not register plugin "${t}" ${Gt}`)},t.registerTokenPlugin=function(t,e){if("function"!=typeof e)throw new Error(`linkifyjs: Invalid token plugin ${e} (expects function)`);for(let n=0;n<qt.tokenQueue.length;n++)if(t===qt.tokenQueue[n][0])return $t(`linkifyjs: token plugin "${t}" already registered - will be overwritten`),void(qt.tokenQueue[n]=[t,e]);qt.tokenQueue.push([t,e]),qt.initialized&&$t(`linkifyjs: already initialized - will not register token plugin "${t}" ${Gt}`)},t.reset=function(){return f.groups={},qt.scanner=null,qt.parser=null,qt.tokenQueue=[],qt.pluginQueue=[],qt.customSchemes=[],qt.initialized=!1,qt},t.stringToArray=It,t.test=function(t,e=null){const n=Jt(t);return 1===n.length&&n[0].isLink&&(!e||n[0].t===e)},t.text=Qt,t.tokenize=Jt,t}({});
