{"version": 3, "file": "index.cjs", "sources": ["../src/bold.ts"], "sourcesContent": ["import {\n  Mark,\n  markInputRule,\n  markPasteRule,\n  mergeAttributes,\n} from '@tiptap/core'\n\nexport interface BoldOptions {\n  /**\n   * HTML attributes to add to the bold element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    bold: {\n      /**\n       * Set a bold mark\n       */\n      setBold: () => ReturnType,\n      /**\n       * Toggle a bold mark\n       */\n      toggleBold: () => ReturnType,\n      /**\n       * Unset a bold mark\n       */\n      unsetBold: () => ReturnType,\n    }\n  }\n}\n\n/**\n * Matches bold text via `**` as input.\n */\nexport const starInputRegex = /(?:^|\\s)(\\*\\*(?!\\s+\\*\\*)((?:[^*]+))\\*\\*(?!\\s+\\*\\*))$/\n\n/**\n * Matches bold text via `**` while pasting.\n */\nexport const starPasteRegex = /(?:^|\\s)(\\*\\*(?!\\s+\\*\\*)((?:[^*]+))\\*\\*(?!\\s+\\*\\*))/g\n\n/**\n * Matches bold text via `__` as input.\n */\nexport const underscoreInputRegex = /(?:^|\\s)(__(?!\\s+__)((?:[^_]+))__(?!\\s+__))$/\n\n/**\n * Matches bold text via `__` while pasting.\n */\nexport const underscorePasteRegex = /(?:^|\\s)(__(?!\\s+__)((?:[^_]+))__(?!\\s+__))/g\n\n/**\n * This extension allows you to mark text as bold.\n * @see https://tiptap.dev/api/marks/bold\n */\nexport const Bold = Mark.create<BoldOptions>({\n  name: 'bold',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 'strong',\n      },\n      {\n        tag: 'b',\n        getAttrs: node => (node as HTMLElement).style.fontWeight !== 'normal' && null,\n      },\n      {\n        style: 'font-weight=400',\n        clearMark: mark => mark.type.name === this.name,\n      },\n      {\n        style: 'font-weight',\n        getAttrs: value => /^(bold(er)?|[5-9]\\d{2,})$/.test(value as string) && null,\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['strong', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setBold: () => ({ commands }) => {\n        return commands.setMark(this.name)\n      },\n      toggleBold: () => ({ commands }) => {\n        return commands.toggleMark(this.name)\n      },\n      unsetBold: () => ({ commands }) => {\n        return commands.unsetMark(this.name)\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-b': () => this.editor.commands.toggleBold(),\n      'Mod-B': () => this.editor.commands.toggleBold(),\n    }\n  },\n\n  addInputRules() {\n    return [\n      markInputRule({\n        find: starInputRegex,\n        type: this.type,\n      }),\n      markInputRule({\n        find: underscoreInputRegex,\n        type: this.type,\n      }),\n    ]\n  },\n\n  addPasteRules() {\n    return [\n      markPasteRule({\n        find: starPasteRegex,\n        type: this.type,\n      }),\n      markPasteRule({\n        find: underscorePasteRegex,\n        type: this.type,\n      }),\n    ]\n  },\n})\n"], "names": ["<PERSON>", "mergeAttributes", "markInputRule", "markPasteRule"], "mappings": ";;;;;;AAmCA;;AAEG;AACI,MAAM,cAAc,GAAG;AAE9B;;AAEG;AACI,MAAM,cAAc,GAAG;AAE9B;;AAEG;AACI,MAAM,oBAAoB,GAAG;AAEpC;;AAEG;AACI,MAAM,oBAAoB,GAAG;AAEpC;;;AAGG;AACU,MAAA,IAAI,GAAGA,SAAI,CAAC,MAAM,CAAc;AAC3C,IAAA,IAAI,EAAE,MAAM;IAEZ,UAAU,GAAA;QACR,OAAO;AACL,YAAA,cAAc,EAAE,EAAE;SACnB;KACF;IAED,SAAS,GAAA;QACP,OAAO;AACL,YAAA;AACE,gBAAA,GAAG,EAAE,QAAQ;AACd,aAAA;AACD,YAAA;AACE,gBAAA,GAAG,EAAE,GAAG;AACR,gBAAA,QAAQ,EAAE,IAAI,IAAK,IAAoB,CAAC,KAAK,CAAC,UAAU,KAAK,QAAQ,IAAI,IAAI;AAC9E,aAAA;AACD,YAAA;AACE,gBAAA,KAAK,EAAE,iBAAiB;AACxB,gBAAA,SAAS,EAAE,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI;AAChD,aAAA;AACD,YAAA;AACE,gBAAA,KAAK,EAAE,aAAa;AACpB,gBAAA,QAAQ,EAAE,KAAK,IAAI,2BAA2B,CAAC,IAAI,CAAC,KAAe,CAAC,IAAI,IAAI;AAC7E,aAAA;SACF;KACF;IAED,UAAU,CAAC,EAAE,cAAc,EAAE,EAAA;AAC3B,QAAA,OAAO,CAAC,QAAQ,EAAEC,oBAAe,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC,EAAE,CAAC,CAAC;KACnF;IAED,WAAW,GAAA;QACT,OAAO;YACL,OAAO,EAAE,MAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;gBAC9B,OAAO,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;aACnC;YACD,UAAU,EAAE,MAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;gBACjC,OAAO,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;aACtC;YACD,SAAS,EAAE,MAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;gBAChC,OAAO,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;aACrC;SACF;KACF;IAED,oBAAoB,GAAA;QAClB,OAAO;YACL,OAAO,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,EAAE;YAChD,OAAO,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,EAAE;SACjD;KACF;IAED,aAAa,GAAA;QACX,OAAO;AACL,YAAAC,kBAAa,CAAC;AACZ,gBAAA,IAAI,EAAE,cAAc;gBACpB,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB,CAAC;AACF,YAAAA,kBAAa,CAAC;AACZ,gBAAA,IAAI,EAAE,oBAAoB;gBAC1B,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB,CAAC;SACH;KACF;IAED,aAAa,GAAA;QACX,OAAO;AACL,YAAAC,kBAAa,CAAC;AACZ,gBAAA,IAAI,EAAE,cAAc;gBACpB,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB,CAAC;AACF,YAAAA,kBAAa,CAAC;AACZ,gBAAA,IAAI,EAAE,oBAAoB;gBAC1B,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB,CAAC;SACH;KACF;AACF,CAAA;;;;;;;;;"}