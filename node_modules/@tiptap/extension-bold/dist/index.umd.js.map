{"version": 3, "file": "index.umd.js", "sources": ["../src/bold.ts"], "sourcesContent": ["import {\n  Mark,\n  markInputRule,\n  markPasteRule,\n  mergeAttributes,\n} from '@tiptap/core'\n\nexport interface BoldOptions {\n  /**\n   * HTML attributes to add to the bold element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    bold: {\n      /**\n       * Set a bold mark\n       */\n      setBold: () => ReturnType,\n      /**\n       * Toggle a bold mark\n       */\n      toggleBold: () => ReturnType,\n      /**\n       * Unset a bold mark\n       */\n      unsetBold: () => ReturnType,\n    }\n  }\n}\n\n/**\n * Matches bold text via `**` as input.\n */\nexport const starInputRegex = /(?:^|\\s)(\\*\\*(?!\\s+\\*\\*)((?:[^*]+))\\*\\*(?!\\s+\\*\\*))$/\n\n/**\n * Matches bold text via `**` while pasting.\n */\nexport const starPasteRegex = /(?:^|\\s)(\\*\\*(?!\\s+\\*\\*)((?:[^*]+))\\*\\*(?!\\s+\\*\\*))/g\n\n/**\n * Matches bold text via `__` as input.\n */\nexport const underscoreInputRegex = /(?:^|\\s)(__(?!\\s+__)((?:[^_]+))__(?!\\s+__))$/\n\n/**\n * Matches bold text via `__` while pasting.\n */\nexport const underscorePasteRegex = /(?:^|\\s)(__(?!\\s+__)((?:[^_]+))__(?!\\s+__))/g\n\n/**\n * This extension allows you to mark text as bold.\n * @see https://tiptap.dev/api/marks/bold\n */\nexport const Bold = Mark.create<BoldOptions>({\n  name: 'bold',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 'strong',\n      },\n      {\n        tag: 'b',\n        getAttrs: node => (node as HTMLElement).style.fontWeight !== 'normal' && null,\n      },\n      {\n        style: 'font-weight=400',\n        clearMark: mark => mark.type.name === this.name,\n      },\n      {\n        style: 'font-weight',\n        getAttrs: value => /^(bold(er)?|[5-9]\\d{2,})$/.test(value as string) && null,\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['strong', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setBold: () => ({ commands }) => {\n        return commands.setMark(this.name)\n      },\n      toggleBold: () => ({ commands }) => {\n        return commands.toggleMark(this.name)\n      },\n      unsetBold: () => ({ commands }) => {\n        return commands.unsetMark(this.name)\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-b': () => this.editor.commands.toggleBold(),\n      'Mod-B': () => this.editor.commands.toggleBold(),\n    }\n  },\n\n  addInputRules() {\n    return [\n      markInputRule({\n        find: starInputRegex,\n        type: this.type,\n      }),\n      markInputRule({\n        find: underscoreInputRegex,\n        type: this.type,\n      }),\n    ]\n  },\n\n  addPasteRules() {\n    return [\n      markPasteRule({\n        find: starPasteRegex,\n        type: this.type,\n      }),\n      markPasteRule({\n        find: underscorePasteRegex,\n        type: this.type,\n      }),\n    ]\n  },\n})\n"], "names": ["<PERSON>", "mergeAttributes", "markInputRule", "markPasteRule"], "mappings": ";;;;;;EAmCA;;EAEG;AACI,QAAM,cAAc,GAAG;EAE9B;;EAEG;AACI,QAAM,cAAc,GAAG;EAE9B;;EAEG;AACI,QAAM,oBAAoB,GAAG;EAEpC;;EAEG;AACI,QAAM,oBAAoB,GAAG;EAEpC;;;EAGG;AACU,QAAA,IAAI,GAAGA,SAAI,CAAC,MAAM,CAAc;EAC3C,IAAA,IAAI,EAAE,MAAM;MAEZ,UAAU,GAAA;UACR,OAAO;EACL,YAAA,cAAc,EAAE,EAAE;WACnB;OACF;MAED,SAAS,GAAA;UACP,OAAO;EACL,YAAA;EACE,gBAAA,GAAG,EAAE,QAAQ;EACd,aAAA;EACD,YAAA;EACE,gBAAA,GAAG,EAAE,GAAG;EACR,gBAAA,QAAQ,EAAE,IAAI,IAAK,IAAoB,CAAC,KAAK,CAAC,UAAU,KAAK,QAAQ,IAAI,IAAI;EAC9E,aAAA;EACD,YAAA;EACE,gBAAA,KAAK,EAAE,iBAAiB;EACxB,gBAAA,SAAS,EAAE,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI;EAChD,aAAA;EACD,YAAA;EACE,gBAAA,KAAK,EAAE,aAAa;EACpB,gBAAA,QAAQ,EAAE,KAAK,IAAI,2BAA2B,CAAC,IAAI,CAAC,KAAe,CAAC,IAAI,IAAI;EAC7E,aAAA;WACF;OACF;MAED,UAAU,CAAC,EAAE,cAAc,EAAE,EAAA;EAC3B,QAAA,OAAO,CAAC,QAAQ,EAAEC,oBAAe,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC,EAAE,CAAC,CAAC;OACnF;MAED,WAAW,GAAA;UACT,OAAO;cACL,OAAO,EAAE,MAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;kBAC9B,OAAO,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;eACnC;cACD,UAAU,EAAE,MAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;kBACjC,OAAO,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;eACtC;cACD,SAAS,EAAE,MAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;kBAChC,OAAO,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;eACrC;WACF;OACF;MAED,oBAAoB,GAAA;UAClB,OAAO;cACL,OAAO,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,EAAE;cAChD,OAAO,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,EAAE;WACjD;OACF;MAED,aAAa,GAAA;UACX,OAAO;EACL,YAAAC,kBAAa,CAAC;EACZ,gBAAA,IAAI,EAAE,cAAc;kBACpB,IAAI,EAAE,IAAI,CAAC,IAAI;eAChB,CAAC;EACF,YAAAA,kBAAa,CAAC;EACZ,gBAAA,IAAI,EAAE,oBAAoB;kBAC1B,IAAI,EAAE,IAAI,CAAC,IAAI;eAChB,CAAC;WACH;OACF;MAED,aAAa,GAAA;UACX,OAAO;EACL,YAAAC,kBAAa,CAAC;EACZ,gBAAA,IAAI,EAAE,cAAc;kBACpB,IAAI,EAAE,IAAI,CAAC,IAAI;eAChB,CAAC;EACF,YAAAA,kBAAa,CAAC;EACZ,gBAAA,IAAI,EAAE,oBAAoB;kBAC1B,IAAI,EAAE,IAAI,CAAC,IAAI;eAChB,CAAC;WACH;OACF;EACF,CAAA;;;;;;;;;;;;;;;"}