{"version": 3, "file": "index.cjs", "sources": ["../src/text-style.ts"], "sourcesContent": ["import {\n  Mark,\n  mergeAttributes,\n} from '@tiptap/core'\n\nexport interface TextStyleOptions {\n  /**\n   * HTML attributes to add to the span element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n  /**\n   * When enabled, merges the styles of nested spans into the child span during HTML parsing.\n   * This prioritizes the style of the child span.\n   * Used when parsing content created in other editors.\n   * (Fix for ProseMirror's default behavior.)\n   * @default false\n   */\n  mergeNestedSpanStyles: boolean,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    textStyle: {\n      /**\n       * Remove spans without inline style attributes.\n       * @example editor.commands.removeEmptyTextStyle()\n       */\n      removeEmptyTextStyle: () => ReturnType,\n    }\n  }\n}\n\nconst mergeNestedSpanStyles = (element: HTMLElement) => {\n  if (!element.children.length) { return }\n  const childSpans = element.querySelectorAll('span')\n\n  if (!childSpans) { return }\n\n  childSpans.forEach(childSpan => {\n    const childStyle = childSpan.getAttribute('style')\n    const closestParentSpanStyleOfChild = childSpan.parentElement?.closest('span')?.getAttribute('style')\n\n    childSpan.setAttribute('style', `${closestParentSpanStyleOfChild};${childStyle}`)\n\n  })\n}\n\n/**\n * This extension allows you to create text styles. It is required by default\n * for the `textColor` and `backgroundColor` extensions.\n * @see https://www.tiptap.dev/api/marks/text-style\n */\nexport const TextStyle = Mark.create<TextStyleOptions>({\n  name: 'textStyle',\n\n  priority: 101,\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n      mergeNestedSpanStyles: false,\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 'span',\n        getAttrs: element => {\n          const hasStyles = (element as HTMLElement).hasAttribute('style')\n\n          if (!hasStyles) {\n            return false\n          }\n          if (this.options.mergeNestedSpanStyles) { mergeNestedSpanStyles(element) }\n\n          return {}\n        },\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['span', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      removeEmptyTextStyle: () => ({ tr }) => {\n\n        const { selection } = tr\n\n        // Gather all of the nodes within the selection range.\n        // We would need to go through each node individually\n        // to check if it has any inline style attributes.\n        // Otherwise, calling commands.unsetMark(this.name)\n        // removes everything from all the nodes\n        // within the selection range.\n        tr.doc.nodesBetween(selection.from, selection.to, (node, pos) => {\n\n          // Check if it's a paragraph element, if so, skip this node as we apply\n          // the text style to inline text nodes only (span).\n          if (node.isTextblock) {\n            return true\n          }\n\n          // Check if the node has no inline style attributes.\n          // Filter out non-`textStyle` marks.\n          if (\n            !node.marks.filter(mark => mark.type === this.type).some(mark => Object.values(mark.attrs).some(value => !!value))) {\n            // Proceed with the removal of the `textStyle` mark for this node only\n            tr.removeMark(pos, pos + node.nodeSize, this.type)\n          }\n        })\n\n        return true\n      },\n    }\n  },\n\n})\n"], "names": ["<PERSON>", "mergeAttributes"], "mappings": ";;;;;;AAkCA,MAAM,qBAAqB,GAAG,CAAC,OAAoB,KAAI;AACrD,IAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,EAAE;QAAE;;IAChC,MAAM,UAAU,GAAG,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC;IAEnD,IAAI,CAAC,UAAU,EAAE;QAAE;;AAEnB,IAAA,UAAU,CAAC,OAAO,CAAC,SAAS,IAAG;;QAC7B,MAAM,UAAU,GAAG,SAAS,CAAC,YAAY,CAAC,OAAO,CAAC;AAClD,QAAA,MAAM,6BAA6B,GAAG,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,SAAS,CAAC,aAAa,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,OAAO,CAAC,MAAM,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,YAAY,CAAC,OAAO,CAAC;QAErG,SAAS,CAAC,YAAY,CAAC,OAAO,EAAE,CAAG,EAAA,6BAA6B,CAAI,CAAA,EAAA,UAAU,CAAE,CAAA,CAAC;AAEnF,KAAC,CAAC;AACJ,CAAC;AAED;;;;AAIG;AACU,MAAA,SAAS,GAAGA,SAAI,CAAC,MAAM,CAAmB;AACrD,IAAA,IAAI,EAAE,WAAW;AAEjB,IAAA,QAAQ,EAAE,GAAG;IAEb,UAAU,GAAA;QACR,OAAO;AACL,YAAA,cAAc,EAAE,EAAE;AAClB,YAAA,qBAAqB,EAAE,KAAK;SAC7B;KACF;IAED,SAAS,GAAA;QACP,OAAO;AACL,YAAA;AACE,gBAAA,GAAG,EAAE,MAAM;gBACX,QAAQ,EAAE,OAAO,IAAG;oBAClB,MAAM,SAAS,GAAI,OAAuB,CAAC,YAAY,CAAC,OAAO,CAAC;oBAEhE,IAAI,CAAC,SAAS,EAAE;AACd,wBAAA,OAAO,KAAK;;AAEd,oBAAA,IAAI,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE;wBAAE,qBAAqB,CAAC,OAAO,CAAC;;AAExE,oBAAA,OAAO,EAAE;iBACV;AACF,aAAA;SACF;KACF;IAED,UAAU,CAAC,EAAE,cAAc,EAAE,EAAA;AAC3B,QAAA,OAAO,CAAC,MAAM,EAAEC,oBAAe,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC,EAAE,CAAC,CAAC;KACjF;IAED,WAAW,GAAA;QACT,OAAO;YACL,oBAAoB,EAAE,MAAM,CAAC,EAAE,EAAE,EAAE,KAAI;AAErC,gBAAA,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE;;;;;;;AAQxB,gBAAA,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,EAAE,CAAC,IAAI,EAAE,GAAG,KAAI;;;AAI9D,oBAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,wBAAA,OAAO,IAAI;;;;oBAKb,IACE,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;;AAEpH,wBAAA,EAAE,CAAC,UAAU,CAAC,GAAG,EAAE,GAAG,GAAG,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC;;AAEtD,iBAAC,CAAC;AAEF,gBAAA,OAAO,IAAI;aACZ;SACF;KACF;AAEF,CAAA;;;;;"}