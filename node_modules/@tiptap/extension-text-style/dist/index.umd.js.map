{"version": 3, "file": "index.umd.js", "sources": ["../src/text-style.ts"], "sourcesContent": ["import {\n  Mark,\n  mergeAttributes,\n} from '@tiptap/core'\n\nexport interface TextStyleOptions {\n  /**\n   * HTML attributes to add to the span element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n  /**\n   * When enabled, merges the styles of nested spans into the child span during HTML parsing.\n   * This prioritizes the style of the child span.\n   * Used when parsing content created in other editors.\n   * (Fix for ProseMirror's default behavior.)\n   * @default false\n   */\n  mergeNestedSpanStyles: boolean,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    textStyle: {\n      /**\n       * Remove spans without inline style attributes.\n       * @example editor.commands.removeEmptyTextStyle()\n       */\n      removeEmptyTextStyle: () => ReturnType,\n    }\n  }\n}\n\nconst mergeNestedSpanStyles = (element: HTMLElement) => {\n  if (!element.children.length) { return }\n  const childSpans = element.querySelectorAll('span')\n\n  if (!childSpans) { return }\n\n  childSpans.forEach(childSpan => {\n    const childStyle = childSpan.getAttribute('style')\n    const closestParentSpanStyleOfChild = childSpan.parentElement?.closest('span')?.getAttribute('style')\n\n    childSpan.setAttribute('style', `${closestParentSpanStyleOfChild};${childStyle}`)\n\n  })\n}\n\n/**\n * This extension allows you to create text styles. It is required by default\n * for the `textColor` and `backgroundColor` extensions.\n * @see https://www.tiptap.dev/api/marks/text-style\n */\nexport const TextStyle = Mark.create<TextStyleOptions>({\n  name: 'textStyle',\n\n  priority: 101,\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n      mergeNestedSpanStyles: false,\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 'span',\n        getAttrs: element => {\n          const hasStyles = (element as HTMLElement).hasAttribute('style')\n\n          if (!hasStyles) {\n            return false\n          }\n          if (this.options.mergeNestedSpanStyles) { mergeNestedSpanStyles(element) }\n\n          return {}\n        },\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['span', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      removeEmptyTextStyle: () => ({ tr }) => {\n\n        const { selection } = tr\n\n        // Gather all of the nodes within the selection range.\n        // We would need to go through each node individually\n        // to check if it has any inline style attributes.\n        // Otherwise, calling commands.unsetMark(this.name)\n        // removes everything from all the nodes\n        // within the selection range.\n        tr.doc.nodesBetween(selection.from, selection.to, (node, pos) => {\n\n          // Check if it's a paragraph element, if so, skip this node as we apply\n          // the text style to inline text nodes only (span).\n          if (node.isTextblock) {\n            return true\n          }\n\n          // Check if the node has no inline style attributes.\n          // Filter out non-`textStyle` marks.\n          if (\n            !node.marks.filter(mark => mark.type === this.type).some(mark => Object.values(mark.attrs).some(value => !!value))) {\n            // Proceed with the removal of the `textStyle` mark for this node only\n            tr.removeMark(pos, pos + node.nodeSize, this.type)\n          }\n        })\n\n        return true\n      },\n    }\n  },\n\n})\n"], "names": ["<PERSON>", "mergeAttributes"], "mappings": ";;;;;;EAkCA,MAAM,qBAAqB,GAAG,CAAC,OAAoB,KAAI;EACrD,IAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,EAAE;UAAE;;MAChC,MAAM,UAAU,GAAG,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC;MAEnD,IAAI,CAAC,UAAU,EAAE;UAAE;;EAEnB,IAAA,UAAU,CAAC,OAAO,CAAC,SAAS,IAAG;;UAC7B,MAAM,UAAU,GAAG,SAAS,CAAC,YAAY,CAAC,OAAO,CAAC;EAClD,QAAA,MAAM,6BAA6B,GAAG,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,SAAS,CAAC,aAAa,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,OAAO,CAAC,MAAM,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,YAAY,CAAC,OAAO,CAAC;UAErG,SAAS,CAAC,YAAY,CAAC,OAAO,EAAE,CAAG,EAAA,6BAA6B,CAAI,CAAA,EAAA,UAAU,CAAE,CAAA,CAAC;EAEnF,KAAC,CAAC;EACJ,CAAC;EAED;;;;EAIG;AACU,QAAA,SAAS,GAAGA,SAAI,CAAC,MAAM,CAAmB;EACrD,IAAA,IAAI,EAAE,WAAW;EAEjB,IAAA,QAAQ,EAAE,GAAG;MAEb,UAAU,GAAA;UACR,OAAO;EACL,YAAA,cAAc,EAAE,EAAE;EAClB,YAAA,qBAAqB,EAAE,KAAK;WAC7B;OACF;MAED,SAAS,GAAA;UACP,OAAO;EACL,YAAA;EACE,gBAAA,GAAG,EAAE,MAAM;kBACX,QAAQ,EAAE,OAAO,IAAG;sBAClB,MAAM,SAAS,GAAI,OAAuB,CAAC,YAAY,CAAC,OAAO,CAAC;sBAEhE,IAAI,CAAC,SAAS,EAAE;EACd,wBAAA,OAAO,KAAK;;EAEd,oBAAA,IAAI,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE;0BAAE,qBAAqB,CAAC,OAAO,CAAC;;EAExE,oBAAA,OAAO,EAAE;mBACV;EACF,aAAA;WACF;OACF;MAED,UAAU,CAAC,EAAE,cAAc,EAAE,EAAA;EAC3B,QAAA,OAAO,CAAC,MAAM,EAAEC,oBAAe,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC,EAAE,CAAC,CAAC;OACjF;MAED,WAAW,GAAA;UACT,OAAO;cACL,oBAAoB,EAAE,MAAM,CAAC,EAAE,EAAE,EAAE,KAAI;EAErC,gBAAA,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE;;;;;;;EAQxB,gBAAA,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,EAAE,CAAC,IAAI,EAAE,GAAG,KAAI;;;EAI9D,oBAAA,IAAI,IAAI,CAAC,WAAW,EAAE;EACpB,wBAAA,OAAO,IAAI;;;;sBAKb,IACE,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;;EAEpH,wBAAA,EAAE,CAAC,UAAU,CAAC,GAAG,EAAE,GAAG,GAAG,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC;;EAEtD,iBAAC,CAAC;EAEF,gBAAA,OAAO,IAAI;eACZ;WACF;OACF;EAEF,CAAA;;;;;;;;;;;"}