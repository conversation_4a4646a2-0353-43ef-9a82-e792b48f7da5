{"version": 3, "file": "index.umd.js", "sources": ["../src/strike.ts"], "sourcesContent": ["import {\n  Mark,\n  markInputRule,\n  markPasteRule,\n  mergeAttributes,\n} from '@tiptap/core'\n\nexport interface StrikeOptions {\n  /**\n   * HTML attributes to add to the strike element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    strike: {\n      /**\n       * Set a strike mark\n       * @example editor.commands.setStrike()\n       */\n      setStrike: () => ReturnType,\n      /**\n       * Toggle a strike mark\n       * @example editor.commands.toggleStrike()\n       */\n      toggleStrike: () => ReturnType,\n      /**\n       * Unset a strike mark\n       * @example editor.commands.unsetStrike()\n       */\n      unsetStrike: () => ReturnType,\n    }\n  }\n}\n\n/**\n * Matches a strike to a ~~strike~~ on input.\n */\nexport const inputRegex = /(?:^|\\s)(~~(?!\\s+~~)((?:[^~]+))~~(?!\\s+~~))$/\n\n/**\n * Matches a strike to a ~~strike~~ on paste.\n */\nexport const pasteRegex = /(?:^|\\s)(~~(?!\\s+~~)((?:[^~]+))~~(?!\\s+~~))/g\n\n/**\n * This extension allows you to create strike text.\n * @see https://www.tiptap.dev/api/marks/strike\n */\nexport const Strike = Mark.create<StrikeOptions>({\n  name: 'strike',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 's',\n      },\n      {\n        tag: 'del',\n      },\n      {\n        tag: 'strike',\n      },\n      {\n        style: 'text-decoration',\n        consuming: false,\n        getAttrs: style => ((style as string).includes('line-through') ? {} : false),\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['s', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setStrike: () => ({ commands }) => {\n        return commands.setMark(this.name)\n      },\n      toggleStrike: () => ({ commands }) => {\n        return commands.toggleMark(this.name)\n      },\n      unsetStrike: () => ({ commands }) => {\n        return commands.unsetMark(this.name)\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-Shift-s': () => this.editor.commands.toggleStrike(),\n    }\n  },\n\n  addInputRules() {\n    return [\n      markInputRule({\n        find: inputRegex,\n        type: this.type,\n      }),\n    ]\n  },\n\n  addPasteRules() {\n    return [\n      markPasteRule({\n        find: pasteRegex,\n        type: this.type,\n      }),\n    ]\n  },\n})\n"], "names": ["<PERSON>", "mergeAttributes", "markInputRule", "markPasteRule"], "mappings": ";;;;;;EAsCA;;EAEG;AACI,QAAM,UAAU,GAAG;EAE1B;;EAEG;AACI,QAAM,UAAU,GAAG;EAE1B;;;EAGG;AACU,QAAA,MAAM,GAAGA,SAAI,CAAC,MAAM,CAAgB;EAC/C,IAAA,IAAI,EAAE,QAAQ;MAEd,UAAU,GAAA;UACR,OAAO;EACL,YAAA,cAAc,EAAE,EAAE;WACnB;OACF;MAED,SAAS,GAAA;UACP,OAAO;EACL,YAAA;EACE,gBAAA,GAAG,EAAE,GAAG;EACT,aAAA;EACD,YAAA;EACE,gBAAA,GAAG,EAAE,KAAK;EACX,aAAA;EACD,YAAA;EACE,gBAAA,GAAG,EAAE,QAAQ;EACd,aAAA;EACD,YAAA;EACE,gBAAA,KAAK,EAAE,iBAAiB;EACxB,gBAAA,SAAS,EAAE,KAAK;kBAChB,QAAQ,EAAE,KAAK,KAAM,KAAgB,CAAC,QAAQ,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC;EAC7E,aAAA;WACF;OACF;MAED,UAAU,CAAC,EAAE,cAAc,EAAE,EAAA;EAC3B,QAAA,OAAO,CAAC,GAAG,EAAEC,oBAAe,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC,EAAE,CAAC,CAAC;OAC9E;MAED,WAAW,GAAA;UACT,OAAO;cACL,SAAS,EAAE,MAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;kBAChC,OAAO,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;eACnC;cACD,YAAY,EAAE,MAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;kBACnC,OAAO,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;eACtC;cACD,WAAW,EAAE,MAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;kBAClC,OAAO,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;eACrC;WACF;OACF;MAED,oBAAoB,GAAA;UAClB,OAAO;cACL,aAAa,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,EAAE;WACzD;OACF;MAED,aAAa,GAAA;UACX,OAAO;EACL,YAAAC,kBAAa,CAAC;EACZ,gBAAA,IAAI,EAAE,UAAU;kBAChB,IAAI,EAAE,IAAI,CAAC,IAAI;eAChB,CAAC;WACH;OACF;MAED,aAAa,GAAA;UACX,OAAO;EACL,YAAAC,kBAAa,CAAC;EACZ,gBAAA,IAAI,EAAE,UAAU;kBAChB,IAAI,EAAE,IAAI,CAAC,IAAI;eAChB,CAAC;WACH;OACF;EACF,CAAA;;;;;;;;;;;;;"}