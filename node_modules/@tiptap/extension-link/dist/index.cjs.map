{"version": 3, "file": "index.cjs", "sources": ["../src/helpers/autolink.ts", "../src/helpers/clickHandler.ts", "../src/helpers/pasteHandler.ts", "../src/link.ts"], "sourcesContent": ["import {\n  combineTransactionSteps,\n  findChildrenInRange,\n  getChangedRanges,\n  getMarksBetween,\n  NodeWithPos,\n} from '@tiptap/core'\nimport { MarkType } from '@tiptap/pm/model'\nimport { Plugin, PluginKey } from '@tiptap/pm/state'\nimport { MultiToken, tokenize } from 'linkifyjs'\n\n/**\n * Check if the provided tokens form a valid link structure, which can either be a single link token\n * or a link token surrounded by parentheses or square brackets.\n *\n * This ensures that only complete and valid text is hyperlinked, preventing cases where a valid\n * top-level domain (TLD) is immediately followed by an invalid character, like a number. For\n * example, with the `find` method from Linkify, entering `example.com1` would result in\n * `example.com` being linked and the trailing `1` left as plain text. By using the `tokenize`\n * method, we can perform more comprehensive validation on the input text.\n */\nfunction isValidLinkStructure(tokens: Array<ReturnType<MultiToken['toObject']>>) {\n  if (tokens.length === 1) {\n    return tokens[0].isLink\n  }\n\n  if (tokens.length === 3 && tokens[1].isLink) {\n    return ['()', '[]'].includes(tokens[0].value + tokens[2].value)\n  }\n\n  return false\n}\n\ntype AutolinkOptions = {\n  type: MarkType\n  defaultProtocol: string\n  validate: (url: string) => boolean\n  shouldAutoLink: (url: string) => boolean\n}\n\n/**\n * This plugin allows you to automatically add links to your editor.\n * @param options The plugin options\n * @returns The plugin instance\n */\nexport function autolink(options: AutolinkOptions): Plugin {\n  return new Plugin({\n    key: new PluginKey('autolink'),\n    appendTransaction: (transactions, oldState, newState) => {\n      /**\n       * Does the transaction change the document?\n       */\n      const docChanges = transactions.some(transaction => transaction.docChanged) && !oldState.doc.eq(newState.doc)\n\n      /**\n       * Prevent autolink if the transaction is not a document change or if the transaction has the meta `preventAutolink`.\n       */\n      const preventAutolink = transactions.some(transaction => transaction.getMeta('preventAutolink'))\n\n      /**\n       * Prevent autolink if the transaction is not a document change\n       * or if the transaction has the meta `preventAutolink`.\n       */\n      if (!docChanges || preventAutolink) {\n        return\n      }\n\n      const { tr } = newState\n      const transform = combineTransactionSteps(oldState.doc, [...transactions])\n      const changes = getChangedRanges(transform)\n\n      changes.forEach(({ newRange }) => {\n        // Now let’s see if we can add new links.\n        const nodesInChangedRanges = findChildrenInRange(\n          newState.doc,\n          newRange,\n          node => node.isTextblock,\n        )\n\n        let textBlock: NodeWithPos | undefined\n        let textBeforeWhitespace: string | undefined\n\n        if (nodesInChangedRanges.length > 1) {\n          // Grab the first node within the changed ranges (ex. the first of two paragraphs when hitting enter).\n          textBlock = nodesInChangedRanges[0]\n          textBeforeWhitespace = newState.doc.textBetween(\n            textBlock.pos,\n            textBlock.pos + textBlock.node.nodeSize,\n            undefined,\n            ' ',\n          )\n        } else if (\n          nodesInChangedRanges.length\n          // We want to make sure to include the block seperator argument to treat hard breaks like spaces.\n          && newState.doc.textBetween(newRange.from, newRange.to, ' ', ' ').endsWith(' ')\n        ) {\n          textBlock = nodesInChangedRanges[0]\n          textBeforeWhitespace = newState.doc.textBetween(\n            textBlock.pos,\n            newRange.to,\n            undefined,\n            ' ',\n          )\n        }\n\n        if (textBlock && textBeforeWhitespace) {\n          const wordsBeforeWhitespace = textBeforeWhitespace.split(' ').filter(s => s !== '')\n\n          if (wordsBeforeWhitespace.length <= 0) {\n            return false\n          }\n\n          const lastWordBeforeSpace = wordsBeforeWhitespace[wordsBeforeWhitespace.length - 1]\n          const lastWordAndBlockOffset = textBlock.pos + textBeforeWhitespace.lastIndexOf(lastWordBeforeSpace)\n\n          if (!lastWordBeforeSpace) {\n            return false\n          }\n\n          const linksBeforeSpace = tokenize(lastWordBeforeSpace).map(t => t.toObject(options.defaultProtocol))\n\n          if (!isValidLinkStructure(linksBeforeSpace)) {\n            return false\n          }\n\n          linksBeforeSpace\n            .filter(link => link.isLink)\n            // Calculate link position.\n            .map(link => ({\n              ...link,\n              from: lastWordAndBlockOffset + link.start + 1,\n              to: lastWordAndBlockOffset + link.end + 1,\n            }))\n            // ignore link inside code mark\n            .filter(link => {\n              if (!newState.schema.marks.code) {\n                return true\n              }\n\n              return !newState.doc.rangeHasMark(\n                link.from,\n                link.to,\n                newState.schema.marks.code,\n              )\n            })\n            // validate link\n            .filter(link => options.validate(link.value))\n            // check whether should autolink\n            .filter(link => options.shouldAutoLink(link.value))\n            // Add link mark.\n            .forEach(link => {\n              if (getMarksBetween(link.from, link.to, newState.doc).some(item => item.mark.type === options.type)) {\n                return\n              }\n\n              tr.addMark(\n                link.from,\n                link.to,\n                options.type.create({\n                  href: link.href,\n                }),\n              )\n            })\n        }\n      })\n\n      if (!tr.steps.length) {\n        return\n      }\n\n      return tr\n    },\n  })\n}\n", "import { getAttributes } from '@tiptap/core'\nimport { MarkType } from '@tiptap/pm/model'\nimport { Plugin, PluginKey } from '@tiptap/pm/state'\n\ntype ClickHandlerOptions = {\n  type: MarkType;\n}\n\nexport function clickHandler(options: ClickHandlerOptions): Plugin {\n  return new Plugin({\n    key: new PluginKey('handleClickLink'),\n    props: {\n      handleClick: (view, pos, event) => {\n        if (event.button !== 0) {\n          return false\n        }\n\n        if (!view.editable) {\n          return false\n        }\n\n        let a = event.target as HTMLElement\n        const els = []\n\n        while (a.nodeName !== 'DIV') {\n          els.push(a)\n          a = a.parentNode as HTMLElement\n        }\n\n        if (!els.find(value => value.nodeName === 'A')) {\n          return false\n        }\n\n        const attrs = getAttributes(view.state, options.type.name)\n        const link = (event.target as HTMLAnchorElement)\n\n        const href = link?.href ?? attrs.href\n        const target = link?.target ?? attrs.target\n\n        if (link && href) {\n          window.open(href, target)\n\n          return true\n        }\n\n        return false\n      },\n    },\n  })\n}\n", "import { Editor } from '@tiptap/core'\nimport { MarkType } from '@tiptap/pm/model'\nimport { Plugin, Plugin<PERSON>ey } from '@tiptap/pm/state'\nimport { find } from 'linkifyjs'\n\ntype PasteHandlerOptions = {\n  editor: Editor\n  defaultProtocol: string\n  type: MarkType\n}\n\nexport function pasteHandler(options: PasteHandlerOptions): Plugin {\n  return new Plugin({\n    key: new Plugin<PERSON>ey('handlePasteLink'),\n    props: {\n      handlePaste: (view, event, slice) => {\n        const { state } = view\n        const { selection } = state\n        const { empty } = selection\n\n        if (empty) {\n          return false\n        }\n\n        let textContent = ''\n\n        slice.content.forEach(node => {\n          textContent += node.textContent\n        })\n\n        const link = find(textContent, { defaultProtocol: options.defaultProtocol }).find(item => item.isLink && item.value === textContent)\n\n        if (!textContent || !link) {\n          return false\n        }\n\n        return options.editor.commands.setMark(options.type, {\n          href: link.href,\n        })\n      },\n    },\n  })\n}\n", "import {\n  <PERSON>, mark<PERSON>aste<PERSON><PERSON>, merge<PERSON>ttribut<PERSON>, PasteRuleMatch,\n} from '@tiptap/core'\nimport { Plugin } from '@tiptap/pm/state'\nimport { find, registerCustomProtocol, reset } from 'linkifyjs'\n\nimport { autolink } from './helpers/autolink.js'\nimport { clickHandler } from './helpers/clickHandler.js'\nimport { pasteHandler } from './helpers/pasteHandler.js'\n\nexport interface LinkProtocolOptions {\n  /**\n   * The protocol scheme to be registered.\n   * @default '''\n   * @example 'ftp'\n   * @example 'git'\n   */\n  scheme: string;\n\n  /**\n   * If enabled, it allows optional slashes after the protocol.\n   * @default false\n   * @example true\n   */\n  optionalSlashes?: boolean;\n}\n\nexport const pasteRegex = /https?:\\/\\/(?:www\\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\\.[a-zA-Z]{2,}\\b(?:[-a-zA-Z0-9@:%._+~#=?!&/]*)(?:[-a-zA-Z0-9@:%._+~#=?!&/]*)/gi\n\n/**\n * @deprecated The default behavior is now to open links when the editor is not editable.\n */\ntype DeprecatedOpenWhenNotEditable = 'whenNotEditable';\n\nexport interface LinkOptions {\n  /**\n   * If enabled, the extension will automatically add links as you type.\n   * @default true\n   * @example false\n   */\n  autolink: boolean;\n\n  /**\n   * An array of custom protocols to be registered with linkifyjs.\n   * @default []\n   * @example ['ftp', 'git']\n   */\n  protocols: Array<LinkProtocolOptions | string>;\n\n  /**\n   * Default protocol to use when no protocol is specified.\n   * @default 'http'\n   */\n  defaultProtocol: string;\n  /**\n   * If enabled, links will be opened on click.\n   * @default true\n   * @example false\n   */\n  openOnClick: boolean | DeprecatedOpenWhenNotEditable;\n  /**\n   * Adds a link to the current selection if the pasted content only contains an url.\n   * @default true\n   * @example false\n   */\n  linkOnPaste: boolean;\n\n  /**\n   * HTML attributes to add to the link element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>;\n\n  /**\n   * @deprecated Use the `shouldAutoLink` option instead.\n   * A validation function that modifies link verification for the auto linker.\n   * @param url - The url to be validated.\n   * @returns - True if the url is valid, false otherwise.\n   */\n  validate: (url: string) => boolean;\n\n  /**\n   * A validation function which is used for configuring link verification for preventing XSS attacks.\n   * Only modify this if you know what you're doing.\n   *\n   * @returns {boolean} `true` if the URL is valid, `false` otherwise.\n   *\n   * @example\n   * isAllowedUri: (url, { defaultValidate, protocols, defaultProtocol }) => {\n   * return url.startsWith('./') || defaultValidate(url)\n   * }\n   */\n  isAllowedUri: (\n    /**\n     * The URL to be validated.\n     */\n    url: string,\n    ctx: {\n      /**\n       * The default validation function.\n       */\n      defaultValidate: (url: string) => boolean;\n      /**\n       * An array of allowed protocols for the URL (e.g., \"http\", \"https\"). As defined in the `protocols` option.\n       */\n      protocols: Array<LinkProtocolOptions | string>;\n      /**\n       * A string that represents the default protocol (e.g., 'http'). As defined in the `defaultProtocol` option.\n       */\n      defaultProtocol: string;\n    }\n  ) => boolean;\n\n  /**\n   * Determines whether a valid link should be automatically linked in the content.\n   *\n   * @param {string} url - The URL that has already been validated.\n   * @returns {boolean} - True if the link should be auto-linked; false if it should not be auto-linked.\n   */\n  shouldAutoLink: (url: string) => boolean;\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    link: {\n      /**\n       * Set a link mark\n       * @param attributes The link attributes\n       * @example editor.commands.setLink({ href: 'https://tiptap.dev' })\n       */\n      setLink: (attributes: {\n        href: string;\n        target?: string | null;\n        rel?: string | null;\n        class?: string | null;\n      }) => ReturnType;\n      /**\n       * Toggle a link mark\n       * @param attributes The link attributes\n       * @example editor.commands.toggleLink({ href: 'https://tiptap.dev' })\n       */\n      toggleLink: (attributes: {\n        href: string;\n        target?: string | null;\n        rel?: string | null;\n        class?: string | null;\n      }) => ReturnType;\n      /**\n       * Unset a link mark\n       * @example editor.commands.unsetLink()\n       */\n      unsetLink: () => ReturnType;\n    };\n  }\n}\n\n// From DOMPurify\n// https://github.com/cure53/DOMPurify/blob/main/src/regexp.js\n// eslint-disable-next-line no-control-regex\nconst ATTR_WHITESPACE = /[\\u0000-\\u0020\\u00A0\\u1680\\u180E\\u2000-\\u2029\\u205F\\u3000]/g\n\nexport function isAllowedUri(uri: string | undefined, protocols?: LinkOptions['protocols']) {\n  const allowedProtocols: string[] = [\n    'http',\n    'https',\n    'ftp',\n    'ftps',\n    'mailto',\n    'tel',\n    'callto',\n    'sms',\n    'cid',\n    'xmpp',\n  ]\n\n  if (protocols) {\n    protocols.forEach(protocol => {\n      const nextProtocol = typeof protocol === 'string' ? protocol : protocol.scheme\n\n      if (nextProtocol) {\n        allowedProtocols.push(nextProtocol)\n      }\n    })\n  }\n\n  return (\n    !uri\n    || uri\n      .replace(ATTR_WHITESPACE, '')\n      .match(\n        new RegExp(\n          // eslint-disable-next-line no-useless-escape\n          `^(?:(?:${allowedProtocols.join('|')}):|[^a-z]|[a-z0-9+.\\-]+(?:[^a-z+.\\-:]|$))`,\n          'i',\n        ),\n      )\n  )\n}\n\n/**\n * This extension allows you to create links.\n * @see https://www.tiptap.dev/api/marks/link\n */\nexport const Link = Mark.create<LinkOptions>({\n  name: 'link',\n\n  priority: 1000,\n\n  keepOnSplit: false,\n\n  exitable: true,\n\n  onCreate() {\n    if (this.options.validate && !this.options.shouldAutoLink) {\n      // Copy the validate function to the shouldAutoLink option\n      this.options.shouldAutoLink = this.options.validate\n      console.warn(\n        'The `validate` option is deprecated. Rename to the `shouldAutoLink` option instead.',\n      )\n    }\n    this.options.protocols.forEach(protocol => {\n      if (typeof protocol === 'string') {\n        registerCustomProtocol(protocol)\n        return\n      }\n      registerCustomProtocol(protocol.scheme, protocol.optionalSlashes)\n    })\n  },\n\n  onDestroy() {\n    reset()\n  },\n\n  inclusive() {\n    return this.options.autolink\n  },\n\n  addOptions() {\n    return {\n      openOnClick: true,\n      linkOnPaste: true,\n      autolink: true,\n      protocols: [],\n      defaultProtocol: 'http',\n      HTMLAttributes: {\n        target: '_blank',\n        rel: 'noopener noreferrer nofollow',\n        class: null,\n      },\n      isAllowedUri: (url, ctx) => !!isAllowedUri(url, ctx.protocols),\n      validate: url => !!url,\n      shouldAutoLink: url => !!url,\n    }\n  },\n\n  addAttributes() {\n    return {\n      href: {\n        default: null,\n        parseHTML(element) {\n          return element.getAttribute('href')\n        },\n      },\n      target: {\n        default: this.options.HTMLAttributes.target,\n      },\n      rel: {\n        default: this.options.HTMLAttributes.rel,\n      },\n      class: {\n        default: this.options.HTMLAttributes.class,\n      },\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 'a[href]',\n        getAttrs: dom => {\n          const href = (dom as HTMLElement).getAttribute('href')\n\n          // prevent XSS attacks\n          if (\n            !href\n            || !this.options.isAllowedUri(href, {\n              defaultValidate: url => !!isAllowedUri(url, this.options.protocols),\n              protocols: this.options.protocols,\n              defaultProtocol: this.options.defaultProtocol,\n            })\n          ) {\n            return false\n          }\n          return null\n        },\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    // prevent XSS attacks\n    if (\n      !this.options.isAllowedUri(HTMLAttributes.href, {\n        defaultValidate: href => !!isAllowedUri(href, this.options.protocols),\n        protocols: this.options.protocols,\n        defaultProtocol: this.options.defaultProtocol,\n      })\n    ) {\n      // strip out the href\n      return [\n        'a',\n        mergeAttributes(this.options.HTMLAttributes, { ...HTMLAttributes, href: '' }),\n        0,\n      ]\n    }\n\n    return ['a', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setLink:\n        attributes => ({ chain }) => {\n          const { href } = attributes\n\n          if (!this.options.isAllowedUri(href, {\n            defaultValidate: url => !!isAllowedUri(url, this.options.protocols),\n            protocols: this.options.protocols,\n            defaultProtocol: this.options.defaultProtocol,\n          })) {\n            return false\n          }\n\n          return chain().setMark(this.name, attributes).setMeta('preventAutolink', true).run()\n        },\n\n      toggleLink:\n        attributes => ({ chain }) => {\n          const { href } = attributes\n\n          if (!this.options.isAllowedUri(href, {\n            defaultValidate: url => !!isAllowedUri(url, this.options.protocols),\n            protocols: this.options.protocols,\n            defaultProtocol: this.options.defaultProtocol,\n          })) {\n            return false\n          }\n\n          return chain()\n            .toggleMark(this.name, attributes, { extendEmptyMarkRange: true })\n            .setMeta('preventAutolink', true)\n            .run()\n        },\n\n      unsetLink:\n        () => ({ chain }) => {\n          return chain()\n            .unsetMark(this.name, { extendEmptyMarkRange: true })\n            .setMeta('preventAutolink', true)\n            .run()\n        },\n    }\n  },\n\n  addPasteRules() {\n    return [\n      markPasteRule({\n        find: text => {\n          const foundLinks: PasteRuleMatch[] = []\n\n          if (text) {\n            const { protocols, defaultProtocol } = this.options\n            const links = find(text).filter(\n              item => item.isLink\n                && this.options.isAllowedUri(item.value, {\n                  defaultValidate: href => !!isAllowedUri(href, protocols),\n                  protocols,\n                  defaultProtocol,\n                }),\n            )\n\n            if (links.length) {\n              links.forEach(link => foundLinks.push({\n                text: link.value,\n                data: {\n                  href: link.href,\n                },\n                index: link.start,\n              }))\n            }\n          }\n\n          return foundLinks\n        },\n        type: this.type,\n        getAttributes: match => {\n          return {\n            href: match.data?.href,\n          }\n        },\n      }),\n    ]\n  },\n\n  addProseMirrorPlugins() {\n    const plugins: Plugin[] = []\n    const { protocols, defaultProtocol } = this.options\n\n    if (this.options.autolink) {\n      plugins.push(\n        autolink({\n          type: this.type,\n          defaultProtocol: this.options.defaultProtocol,\n          validate: url => this.options.isAllowedUri(url, {\n            defaultValidate: href => !!isAllowedUri(href, protocols),\n            protocols,\n            defaultProtocol,\n          }),\n          shouldAutoLink: this.options.shouldAutoLink,\n        }),\n      )\n    }\n\n    if (this.options.openOnClick === true) {\n      plugins.push(\n        clickHandler({\n          type: this.type,\n        }),\n      )\n    }\n\n    if (this.options.linkOnPaste) {\n      plugins.push(\n        pasteHandler({\n          editor: this.editor,\n          defaultProtocol: this.options.defaultProtocol,\n          type: this.type,\n        }),\n      )\n    }\n\n    return plugins\n  },\n})\n"], "names": ["Plugin", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "combineTransactionSteps", "getChangedRanges", "findChildrenInRange", "tokenize", "getMarksBetween", "getAttributes", "find", "<PERSON>", "registerCustomProtocol", "reset", "mergeAttributes", "markPasteRule"], "mappings": ";;;;;;;;AAWA;;;;;;;;;AASG;AACH,SAAS,oBAAoB,CAAC,MAAiD,EAAA;AAC7E,IAAA,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;AACvB,QAAA,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM;;AAGzB,IAAA,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;QAC3C,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;;AAGjE,IAAA,OAAO,KAAK;AACd;AASA;;;;AAIG;AACG,SAAU,QAAQ,CAAC,OAAwB,EAAA;IAC/C,OAAO,IAAIA,YAAM,CAAC;AAChB,QAAA,GAAG,EAAE,IAAIC,eAAS,CAAC,UAAU,CAAC;QAC9B,iBAAiB,EAAE,CAAC,YAAY,EAAE,QAAQ,EAAE,QAAQ,KAAI;AACtD;;AAEG;YACH,MAAM,UAAU,GAAG,YAAY,CAAC,IAAI,CAAC,WAAW,IAAI,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC;AAE7G;;AAEG;AACH,YAAA,MAAM,eAAe,GAAG,YAAY,CAAC,IAAI,CAAC,WAAW,IAAI,WAAW,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;AAEhG;;;AAGG;AACH,YAAA,IAAI,CAAC,UAAU,IAAI,eAAe,EAAE;gBAClC;;AAGF,YAAA,MAAM,EAAE,EAAE,EAAE,GAAG,QAAQ;AACvB,YAAA,MAAM,SAAS,GAAGC,4BAAuB,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,GAAG,YAAY,CAAC,CAAC;AAC1E,YAAA,MAAM,OAAO,GAAGC,qBAAgB,CAAC,SAAS,CAAC;YAE3C,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE,QAAQ,EAAE,KAAI;;AAE/B,gBAAA,MAAM,oBAAoB,GAAGC,wBAAmB,CAC9C,QAAQ,CAAC,GAAG,EACZ,QAAQ,EACR,IAAI,IAAI,IAAI,CAAC,WAAW,CACzB;AAED,gBAAA,IAAI,SAAkC;AACtC,gBAAA,IAAI,oBAAwC;AAE5C,gBAAA,IAAI,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE;;AAEnC,oBAAA,SAAS,GAAG,oBAAoB,CAAC,CAAC,CAAC;oBACnC,oBAAoB,GAAG,QAAQ,CAAC,GAAG,CAAC,WAAW,CAC7C,SAAS,CAAC,GAAG,EACb,SAAS,CAAC,GAAG,GAAG,SAAS,CAAC,IAAI,CAAC,QAAQ,EACvC,SAAS,EACT,GAAG,CACJ;;qBACI,IACL,oBAAoB,CAAC;;uBAElB,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,EAC/E;AACA,oBAAA,SAAS,GAAG,oBAAoB,CAAC,CAAC,CAAC;AACnC,oBAAA,oBAAoB,GAAG,QAAQ,CAAC,GAAG,CAAC,WAAW,CAC7C,SAAS,CAAC,GAAG,EACb,QAAQ,CAAC,EAAE,EACX,SAAS,EACT,GAAG,CACJ;;AAGH,gBAAA,IAAI,SAAS,IAAI,oBAAoB,EAAE;AACrC,oBAAA,MAAM,qBAAqB,GAAG,oBAAoB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;AAEnF,oBAAA,IAAI,qBAAqB,CAAC,MAAM,IAAI,CAAC,EAAE;AACrC,wBAAA,OAAO,KAAK;;oBAGd,MAAM,mBAAmB,GAAG,qBAAqB,CAAC,qBAAqB,CAAC,MAAM,GAAG,CAAC,CAAC;AACnF,oBAAA,MAAM,sBAAsB,GAAG,SAAS,CAAC,GAAG,GAAG,oBAAoB,CAAC,WAAW,CAAC,mBAAmB,CAAC;oBAEpG,IAAI,CAAC,mBAAmB,EAAE;AACxB,wBAAA,OAAO,KAAK;;oBAGd,MAAM,gBAAgB,GAAGC,kBAAQ,CAAC,mBAAmB,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;AAEpG,oBAAA,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,EAAE;AAC3C,wBAAA,OAAO,KAAK;;oBAGd;yBACG,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM;;AAE1B,yBAAA,GAAG,CAAC,IAAI,KAAK;AACZ,wBAAA,GAAG,IAAI;AACP,wBAAA,IAAI,EAAE,sBAAsB,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC;AAC7C,wBAAA,EAAE,EAAE,sBAAsB,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;AAC1C,qBAAA,CAAC;;yBAED,MAAM,CAAC,IAAI,IAAG;wBACb,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE;AAC/B,4BAAA,OAAO,IAAI;;wBAGb,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,YAAY,CAC/B,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,EAAE,EACP,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAC3B;AACH,qBAAC;;AAEA,yBAAA,MAAM,CAAC,IAAI,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC;;AAE3C,yBAAA,MAAM,CAAC,IAAI,IAAI,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC;;yBAEjD,OAAO,CAAC,IAAI,IAAG;AACd,wBAAA,IAAIC,oBAAe,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI,CAAC,EAAE;4BACnG;;AAGF,wBAAA,EAAE,CAAC,OAAO,CACR,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,EAAE,EACP,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC;4BAClB,IAAI,EAAE,IAAI,CAAC,IAAI;AAChB,yBAAA,CAAC,CACH;AACH,qBAAC,CAAC;;AAER,aAAC,CAAC;AAEF,YAAA,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE;gBACpB;;AAGF,YAAA,OAAO,EAAE;SACV;AACF,KAAA,CAAC;AACJ;;ACrKM,SAAU,YAAY,CAAC,OAA4B,EAAA;IACvD,OAAO,IAAIN,YAAM,CAAC;AAChB,QAAA,GAAG,EAAE,IAAIC,eAAS,CAAC,iBAAiB,CAAC;AACrC,QAAA,KAAK,EAAE;YACL,WAAW,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,KAAI;;AAChC,gBAAA,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;AACtB,oBAAA,OAAO,KAAK;;AAGd,gBAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,oBAAA,OAAO,KAAK;;AAGd,gBAAA,IAAI,CAAC,GAAG,KAAK,CAAC,MAAqB;gBACnC,MAAM,GAAG,GAAG,EAAE;AAEd,gBAAA,OAAO,CAAC,CAAC,QAAQ,KAAK,KAAK,EAAE;AAC3B,oBAAA,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;AACX,oBAAA,CAAC,GAAG,CAAC,CAAC,UAAyB;;AAGjC,gBAAA,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,QAAQ,KAAK,GAAG,CAAC,EAAE;AAC9C,oBAAA,OAAO,KAAK;;AAGd,gBAAA,MAAM,KAAK,GAAGM,kBAAa,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;AAC1D,gBAAA,MAAM,IAAI,GAAI,KAAK,CAAC,MAA4B;AAEhD,gBAAA,MAAM,IAAI,GAAG,CAAA,EAAA,GAAA,IAAI,aAAJ,IAAI,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAJ,IAAI,CAAE,IAAI,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,KAAK,CAAC,IAAI;AACrC,gBAAA,MAAM,MAAM,GAAG,CAAA,EAAA,GAAA,IAAI,aAAJ,IAAI,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAJ,IAAI,CAAE,MAAM,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,KAAK,CAAC,MAAM;AAE3C,gBAAA,IAAI,IAAI,IAAI,IAAI,EAAE;AAChB,oBAAA,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC;AAEzB,oBAAA,OAAO,IAAI;;AAGb,gBAAA,OAAO,KAAK;aACb;AACF,SAAA;AACF,KAAA,CAAC;AACJ;;ACtCM,SAAU,YAAY,CAAC,OAA4B,EAAA;IACvD,OAAO,IAAIP,YAAM,CAAC;AAChB,QAAA,GAAG,EAAE,IAAIC,eAAS,CAAC,iBAAiB,CAAC;AACrC,QAAA,KAAK,EAAE;YACL,WAAW,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,KAAI;AAClC,gBAAA,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI;AACtB,gBAAA,MAAM,EAAE,SAAS,EAAE,GAAG,KAAK;AAC3B,gBAAA,MAAM,EAAE,KAAK,EAAE,GAAG,SAAS;gBAE3B,IAAI,KAAK,EAAE;AACT,oBAAA,OAAO,KAAK;;gBAGd,IAAI,WAAW,GAAG,EAAE;AAEpB,gBAAA,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,IAAG;AAC3B,oBAAA,WAAW,IAAI,IAAI,CAAC,WAAW;AACjC,iBAAC,CAAC;AAEF,gBAAA,MAAM,IAAI,GAAGO,cAAI,CAAC,WAAW,EAAE,EAAE,eAAe,EAAE,OAAO,CAAC,eAAe,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,KAAK,WAAW,CAAC;AAEpI,gBAAA,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,EAAE;AACzB,oBAAA,OAAO,KAAK;;gBAGd,OAAO,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE;oBACnD,IAAI,EAAE,IAAI,CAAC,IAAI;AAChB,iBAAA,CAAC;aACH;AACF,SAAA;AACF,KAAA,CAAC;AACJ;;ACfO,MAAM,UAAU,GAAG;AAkI1B;AACA;AACA;AACA,MAAM,eAAe,GAAG,6DAA6D;AAErE,SAAA,YAAY,CAAC,GAAuB,EAAE,SAAoC,EAAA;AACxF,IAAA,MAAM,gBAAgB,GAAa;QACjC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;QACR,KAAK;QACL,QAAQ;QACR,KAAK;QACL,KAAK;QACL,MAAM;KACP;IAED,IAAI,SAAS,EAAE;AACb,QAAA,SAAS,CAAC,OAAO,CAAC,QAAQ,IAAG;AAC3B,YAAA,MAAM,YAAY,GAAG,OAAO,QAAQ,KAAK,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC,MAAM;YAE9E,IAAI,YAAY,EAAE;AAChB,gBAAA,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC;;AAEvC,SAAC,CAAC;;IAGJ,QACE,CAAC;WACE;AACA,aAAA,OAAO,CAAC,eAAe,EAAE,EAAE;aAC3B,KAAK,CACJ,IAAI,MAAM;;AAER,QAAA,CAAA,OAAA,EAAU,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,CAA2C,yCAAA,CAAA,EAC/E,GAAG,CACJ,CACF;AAEP;AAEA;;;AAGG;AACU,MAAA,IAAI,GAAGC,SAAI,CAAC,MAAM,CAAc;AAC3C,IAAA,IAAI,EAAE,MAAM;AAEZ,IAAA,QAAQ,EAAE,IAAI;AAEd,IAAA,WAAW,EAAE,KAAK;AAElB,IAAA,QAAQ,EAAE,IAAI;IAEd,QAAQ,GAAA;AACN,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE;;YAEzD,IAAI,CAAC,OAAO,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ;AACnD,YAAA,OAAO,CAAC,IAAI,CACV,qFAAqF,CACtF;;QAEH,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,IAAG;AACxC,YAAA,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;gBAChCC,gCAAsB,CAAC,QAAQ,CAAC;gBAChC;;YAEFA,gCAAsB,CAAC,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,eAAe,CAAC;AACnE,SAAC,CAAC;KACH;IAED,SAAS,GAAA;AACP,QAAAC,eAAK,EAAE;KACR;IAED,SAAS,GAAA;AACP,QAAA,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ;KAC7B;IAED,UAAU,GAAA;QACR,OAAO;AACL,YAAA,WAAW,EAAE,IAAI;AACjB,YAAA,WAAW,EAAE,IAAI;AACjB,YAAA,QAAQ,EAAE,IAAI;AACd,YAAA,SAAS,EAAE,EAAE;AACb,YAAA,eAAe,EAAE,MAAM;AACvB,YAAA,cAAc,EAAE;AACd,gBAAA,MAAM,EAAE,QAAQ;AAChB,gBAAA,GAAG,EAAE,8BAA8B;AACnC,gBAAA,KAAK,EAAE,IAAI;AACZ,aAAA;AACD,YAAA,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,CAAC,YAAY,CAAC,GAAG,EAAE,GAAG,CAAC,SAAS,CAAC;AAC9D,YAAA,QAAQ,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG;AACtB,YAAA,cAAc,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG;SAC7B;KACF;IAED,aAAa,GAAA;QACX,OAAO;AACL,YAAA,IAAI,EAAE;AACJ,gBAAA,OAAO,EAAE,IAAI;AACb,gBAAA,SAAS,CAAC,OAAO,EAAA;AACf,oBAAA,OAAO,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC;iBACpC;AACF,aAAA;AACD,YAAA,MAAM,EAAE;AACN,gBAAA,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,MAAM;AAC5C,aAAA;AACD,YAAA,GAAG,EAAE;AACH,gBAAA,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG;AACzC,aAAA;AACD,YAAA,KAAK,EAAE;AACL,gBAAA,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK;AAC3C,aAAA;SACF;KACF;IAED,SAAS,GAAA;QACP,OAAO;AACL,YAAA;AACE,gBAAA,GAAG,EAAE,SAAS;gBACd,QAAQ,EAAE,GAAG,IAAG;oBACd,MAAM,IAAI,GAAI,GAAmB,CAAC,YAAY,CAAC,MAAM,CAAC;;AAGtD,oBAAA,IACE,CAAC;AACE,2BAAA,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE;AAClC,4BAAA,eAAe,EAAE,GAAG,IAAI,CAAC,CAAC,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;AACnE,4BAAA,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS;AACjC,4BAAA,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;AAC9C,yBAAA,CAAC,EACF;AACA,wBAAA,OAAO,KAAK;;AAEd,oBAAA,OAAO,IAAI;iBACZ;AACF,aAAA;SACF;KACF;IAED,UAAU,CAAC,EAAE,cAAc,EAAE,EAAA;;QAE3B,IACE,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,cAAc,CAAC,IAAI,EAAE;AAC9C,YAAA,eAAe,EAAE,IAAI,IAAI,CAAC,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;AACrE,YAAA,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS;AACjC,YAAA,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;AAC9C,SAAA,CAAC,EACF;;YAEA,OAAO;gBACL,GAAG;AACH,gBAAAC,oBAAe,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,EAAE,GAAG,cAAc,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;gBAC7E,CAAC;aACF;;AAGH,QAAA,OAAO,CAAC,GAAG,EAAEA,oBAAe,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC,EAAE,CAAC,CAAC;KAC9E;IAED,WAAW,GAAA;QACT,OAAO;YACL,OAAO,EACL,UAAU,IAAI,CAAC,EAAE,KAAK,EAAE,KAAI;AAC1B,gBAAA,MAAM,EAAE,IAAI,EAAE,GAAG,UAAU;gBAE3B,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE;AACnC,oBAAA,eAAe,EAAE,GAAG,IAAI,CAAC,CAAC,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;AACnE,oBAAA,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS;AACjC,oBAAA,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;AAC9C,iBAAA,CAAC,EAAE;AACF,oBAAA,OAAO,KAAK;;gBAGd,OAAO,KAAK,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,OAAO,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC,GAAG,EAAE;aACrF;YAEH,UAAU,EACR,UAAU,IAAI,CAAC,EAAE,KAAK,EAAE,KAAI;AAC1B,gBAAA,MAAM,EAAE,IAAI,EAAE,GAAG,UAAU;gBAE3B,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE;AACnC,oBAAA,eAAe,EAAE,GAAG,IAAI,CAAC,CAAC,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;AACnE,oBAAA,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS;AACjC,oBAAA,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;AAC9C,iBAAA,CAAC,EAAE;AACF,oBAAA,OAAO,KAAK;;AAGd,gBAAA,OAAO,KAAK;AACT,qBAAA,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,EAAE,EAAE,oBAAoB,EAAE,IAAI,EAAE;AAChE,qBAAA,OAAO,CAAC,iBAAiB,EAAE,IAAI;AAC/B,qBAAA,GAAG,EAAE;aACT;YAEH,SAAS,EACP,MAAM,CAAC,EAAE,KAAK,EAAE,KAAI;AAClB,gBAAA,OAAO,KAAK;qBACT,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,oBAAoB,EAAE,IAAI,EAAE;AACnD,qBAAA,OAAO,CAAC,iBAAiB,EAAE,IAAI;AAC/B,qBAAA,GAAG,EAAE;aACT;SACJ;KACF;IAED,aAAa,GAAA;QACX,OAAO;AACL,YAAAC,kBAAa,CAAC;gBACZ,IAAI,EAAE,IAAI,IAAG;oBACX,MAAM,UAAU,GAAqB,EAAE;oBAEvC,IAAI,IAAI,EAAE;wBACR,MAAM,EAAE,SAAS,EAAE,eAAe,EAAE,GAAG,IAAI,CAAC,OAAO;AACnD,wBAAA,MAAM,KAAK,GAAGL,cAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAC7B,IAAI,IAAI,IAAI,CAAC;+BACR,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE;AACvC,gCAAA,eAAe,EAAE,IAAI,IAAI,CAAC,CAAC,YAAY,CAAC,IAAI,EAAE,SAAS,CAAC;gCACxD,SAAS;gCACT,eAAe;AAChB,6BAAA,CAAC,CACL;AAED,wBAAA,IAAI,KAAK,CAAC,MAAM,EAAE;4BAChB,KAAK,CAAC,OAAO,CAAC,IAAI,IAAI,UAAU,CAAC,IAAI,CAAC;gCACpC,IAAI,EAAE,IAAI,CAAC,KAAK;AAChB,gCAAA,IAAI,EAAE;oCACJ,IAAI,EAAE,IAAI,CAAC,IAAI;AAChB,iCAAA;gCACD,KAAK,EAAE,IAAI,CAAC,KAAK;AAClB,6BAAA,CAAC,CAAC;;;AAIP,oBAAA,OAAO,UAAU;iBAClB;gBACD,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,aAAa,EAAE,KAAK,IAAG;;oBACrB,OAAO;AACL,wBAAA,IAAI,EAAE,CAAA,EAAA,GAAA,KAAK,CAAC,IAAI,0CAAE,IAAI;qBACvB;iBACF;aACF,CAAC;SACH;KACF;IAED,qBAAqB,GAAA;QACnB,MAAM,OAAO,GAAa,EAAE;QAC5B,MAAM,EAAE,SAAS,EAAE,eAAe,EAAE,GAAG,IAAI,CAAC,OAAO;AAEnD,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;AACzB,YAAA,OAAO,CAAC,IAAI,CACV,QAAQ,CAAC;gBACP,IAAI,EAAE,IAAI,CAAC,IAAI;AACf,gBAAA,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;AAC7C,gBAAA,QAAQ,EAAE,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,EAAE;AAC9C,oBAAA,eAAe,EAAE,IAAI,IAAI,CAAC,CAAC,YAAY,CAAC,IAAI,EAAE,SAAS,CAAC;oBACxD,SAAS;oBACT,eAAe;iBAChB,CAAC;AACF,gBAAA,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc;AAC5C,aAAA,CAAC,CACH;;QAGH,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,KAAK,IAAI,EAAE;AACrC,YAAA,OAAO,CAAC,IAAI,CACV,YAAY,CAAC;gBACX,IAAI,EAAE,IAAI,CAAC,IAAI;AAChB,aAAA,CAAC,CACH;;AAGH,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;AAC5B,YAAA,OAAO,CAAC,IAAI,CACV,YAAY,CAAC;gBACX,MAAM,EAAE,IAAI,CAAC,MAAM;AACnB,gBAAA,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;gBAC7C,IAAI,EAAE,IAAI,CAAC,IAAI;AAChB,aAAA,CAAC,CACH;;AAGH,QAAA,OAAO,OAAO;KACf;AACF,CAAA;;;;;;;"}