{"version": 3, "file": "index.umd.js", "sources": ["../src/helpers/autolink.ts", "../src/helpers/clickHandler.ts", "../src/helpers/pasteHandler.ts", "../src/link.ts"], "sourcesContent": ["import {\n  combineTransactionSteps,\n  findChildrenInRange,\n  getChangedRanges,\n  getMarksBetween,\n  NodeWithPos,\n} from '@tiptap/core'\nimport { MarkType } from '@tiptap/pm/model'\nimport { Plugin, PluginKey } from '@tiptap/pm/state'\nimport { MultiToken, tokenize } from 'linkifyjs'\n\n/**\n * Check if the provided tokens form a valid link structure, which can either be a single link token\n * or a link token surrounded by parentheses or square brackets.\n *\n * This ensures that only complete and valid text is hyperlinked, preventing cases where a valid\n * top-level domain (TLD) is immediately followed by an invalid character, like a number. For\n * example, with the `find` method from Linkify, entering `example.com1` would result in\n * `example.com` being linked and the trailing `1` left as plain text. By using the `tokenize`\n * method, we can perform more comprehensive validation on the input text.\n */\nfunction isValidLinkStructure(tokens: Array<ReturnType<MultiToken['toObject']>>) {\n  if (tokens.length === 1) {\n    return tokens[0].isLink\n  }\n\n  if (tokens.length === 3 && tokens[1].isLink) {\n    return ['()', '[]'].includes(tokens[0].value + tokens[2].value)\n  }\n\n  return false\n}\n\ntype AutolinkOptions = {\n  type: MarkType\n  defaultProtocol: string\n  validate: (url: string) => boolean\n  shouldAutoLink: (url: string) => boolean\n}\n\n/**\n * This plugin allows you to automatically add links to your editor.\n * @param options The plugin options\n * @returns The plugin instance\n */\nexport function autolink(options: AutolinkOptions): Plugin {\n  return new Plugin({\n    key: new PluginKey('autolink'),\n    appendTransaction: (transactions, oldState, newState) => {\n      /**\n       * Does the transaction change the document?\n       */\n      const docChanges = transactions.some(transaction => transaction.docChanged) && !oldState.doc.eq(newState.doc)\n\n      /**\n       * Prevent autolink if the transaction is not a document change or if the transaction has the meta `preventAutolink`.\n       */\n      const preventAutolink = transactions.some(transaction => transaction.getMeta('preventAutolink'))\n\n      /**\n       * Prevent autolink if the transaction is not a document change\n       * or if the transaction has the meta `preventAutolink`.\n       */\n      if (!docChanges || preventAutolink) {\n        return\n      }\n\n      const { tr } = newState\n      const transform = combineTransactionSteps(oldState.doc, [...transactions])\n      const changes = getChangedRanges(transform)\n\n      changes.forEach(({ newRange }) => {\n        // Now let’s see if we can add new links.\n        const nodesInChangedRanges = findChildrenInRange(\n          newState.doc,\n          newRange,\n          node => node.isTextblock,\n        )\n\n        let textBlock: NodeWithPos | undefined\n        let textBeforeWhitespace: string | undefined\n\n        if (nodesInChangedRanges.length > 1) {\n          // Grab the first node within the changed ranges (ex. the first of two paragraphs when hitting enter).\n          textBlock = nodesInChangedRanges[0]\n          textBeforeWhitespace = newState.doc.textBetween(\n            textBlock.pos,\n            textBlock.pos + textBlock.node.nodeSize,\n            undefined,\n            ' ',\n          )\n        } else if (\n          nodesInChangedRanges.length\n          // We want to make sure to include the block seperator argument to treat hard breaks like spaces.\n          && newState.doc.textBetween(newRange.from, newRange.to, ' ', ' ').endsWith(' ')\n        ) {\n          textBlock = nodesInChangedRanges[0]\n          textBeforeWhitespace = newState.doc.textBetween(\n            textBlock.pos,\n            newRange.to,\n            undefined,\n            ' ',\n          )\n        }\n\n        if (textBlock && textBeforeWhitespace) {\n          const wordsBeforeWhitespace = textBeforeWhitespace.split(' ').filter(s => s !== '')\n\n          if (wordsBeforeWhitespace.length <= 0) {\n            return false\n          }\n\n          const lastWordBeforeSpace = wordsBeforeWhitespace[wordsBeforeWhitespace.length - 1]\n          const lastWordAndBlockOffset = textBlock.pos + textBeforeWhitespace.lastIndexOf(lastWordBeforeSpace)\n\n          if (!lastWordBeforeSpace) {\n            return false\n          }\n\n          const linksBeforeSpace = tokenize(lastWordBeforeSpace).map(t => t.toObject(options.defaultProtocol))\n\n          if (!isValidLinkStructure(linksBeforeSpace)) {\n            return false\n          }\n\n          linksBeforeSpace\n            .filter(link => link.isLink)\n            // Calculate link position.\n            .map(link => ({\n              ...link,\n              from: lastWordAndBlockOffset + link.start + 1,\n              to: lastWordAndBlockOffset + link.end + 1,\n            }))\n            // ignore link inside code mark\n            .filter(link => {\n              if (!newState.schema.marks.code) {\n                return true\n              }\n\n              return !newState.doc.rangeHasMark(\n                link.from,\n                link.to,\n                newState.schema.marks.code,\n              )\n            })\n            // validate link\n            .filter(link => options.validate(link.value))\n            // check whether should autolink\n            .filter(link => options.shouldAutoLink(link.value))\n            // Add link mark.\n            .forEach(link => {\n              if (getMarksBetween(link.from, link.to, newState.doc).some(item => item.mark.type === options.type)) {\n                return\n              }\n\n              tr.addMark(\n                link.from,\n                link.to,\n                options.type.create({\n                  href: link.href,\n                }),\n              )\n            })\n        }\n      })\n\n      if (!tr.steps.length) {\n        return\n      }\n\n      return tr\n    },\n  })\n}\n", "import { getAttributes } from '@tiptap/core'\nimport { MarkType } from '@tiptap/pm/model'\nimport { Plugin, PluginKey } from '@tiptap/pm/state'\n\ntype ClickHandlerOptions = {\n  type: MarkType;\n}\n\nexport function clickHandler(options: ClickHandlerOptions): Plugin {\n  return new Plugin({\n    key: new PluginKey('handleClickLink'),\n    props: {\n      handleClick: (view, pos, event) => {\n        if (event.button !== 0) {\n          return false\n        }\n\n        if (!view.editable) {\n          return false\n        }\n\n        let a = event.target as HTMLElement\n        const els = []\n\n        while (a.nodeName !== 'DIV') {\n          els.push(a)\n          a = a.parentNode as HTMLElement\n        }\n\n        if (!els.find(value => value.nodeName === 'A')) {\n          return false\n        }\n\n        const attrs = getAttributes(view.state, options.type.name)\n        const link = (event.target as HTMLAnchorElement)\n\n        const href = link?.href ?? attrs.href\n        const target = link?.target ?? attrs.target\n\n        if (link && href) {\n          window.open(href, target)\n\n          return true\n        }\n\n        return false\n      },\n    },\n  })\n}\n", "import { Editor } from '@tiptap/core'\nimport { MarkType } from '@tiptap/pm/model'\nimport { Plugin, Plugin<PERSON>ey } from '@tiptap/pm/state'\nimport { find } from 'linkifyjs'\n\ntype PasteHandlerOptions = {\n  editor: Editor\n  defaultProtocol: string\n  type: MarkType\n}\n\nexport function pasteHandler(options: PasteHandlerOptions): Plugin {\n  return new Plugin({\n    key: new Plugin<PERSON>ey('handlePasteLink'),\n    props: {\n      handlePaste: (view, event, slice) => {\n        const { state } = view\n        const { selection } = state\n        const { empty } = selection\n\n        if (empty) {\n          return false\n        }\n\n        let textContent = ''\n\n        slice.content.forEach(node => {\n          textContent += node.textContent\n        })\n\n        const link = find(textContent, { defaultProtocol: options.defaultProtocol }).find(item => item.isLink && item.value === textContent)\n\n        if (!textContent || !link) {\n          return false\n        }\n\n        return options.editor.commands.setMark(options.type, {\n          href: link.href,\n        })\n      },\n    },\n  })\n}\n", "import {\n  <PERSON>, mark<PERSON>aste<PERSON><PERSON>, merge<PERSON>ttribut<PERSON>, PasteRuleMatch,\n} from '@tiptap/core'\nimport { Plugin } from '@tiptap/pm/state'\nimport { find, registerCustomProtocol, reset } from 'linkifyjs'\n\nimport { autolink } from './helpers/autolink.js'\nimport { clickHandler } from './helpers/clickHandler.js'\nimport { pasteHandler } from './helpers/pasteHandler.js'\n\nexport interface LinkProtocolOptions {\n  /**\n   * The protocol scheme to be registered.\n   * @default '''\n   * @example 'ftp'\n   * @example 'git'\n   */\n  scheme: string;\n\n  /**\n   * If enabled, it allows optional slashes after the protocol.\n   * @default false\n   * @example true\n   */\n  optionalSlashes?: boolean;\n}\n\nexport const pasteRegex = /https?:\\/\\/(?:www\\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\\.[a-zA-Z]{2,}\\b(?:[-a-zA-Z0-9@:%._+~#=?!&/]*)(?:[-a-zA-Z0-9@:%._+~#=?!&/]*)/gi\n\n/**\n * @deprecated The default behavior is now to open links when the editor is not editable.\n */\ntype DeprecatedOpenWhenNotEditable = 'whenNotEditable';\n\nexport interface LinkOptions {\n  /**\n   * If enabled, the extension will automatically add links as you type.\n   * @default true\n   * @example false\n   */\n  autolink: boolean;\n\n  /**\n   * An array of custom protocols to be registered with linkifyjs.\n   * @default []\n   * @example ['ftp', 'git']\n   */\n  protocols: Array<LinkProtocolOptions | string>;\n\n  /**\n   * Default protocol to use when no protocol is specified.\n   * @default 'http'\n   */\n  defaultProtocol: string;\n  /**\n   * If enabled, links will be opened on click.\n   * @default true\n   * @example false\n   */\n  openOnClick: boolean | DeprecatedOpenWhenNotEditable;\n  /**\n   * Adds a link to the current selection if the pasted content only contains an url.\n   * @default true\n   * @example false\n   */\n  linkOnPaste: boolean;\n\n  /**\n   * HTML attributes to add to the link element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>;\n\n  /**\n   * @deprecated Use the `shouldAutoLink` option instead.\n   * A validation function that modifies link verification for the auto linker.\n   * @param url - The url to be validated.\n   * @returns - True if the url is valid, false otherwise.\n   */\n  validate: (url: string) => boolean;\n\n  /**\n   * A validation function which is used for configuring link verification for preventing XSS attacks.\n   * Only modify this if you know what you're doing.\n   *\n   * @returns {boolean} `true` if the URL is valid, `false` otherwise.\n   *\n   * @example\n   * isAllowedUri: (url, { defaultValidate, protocols, defaultProtocol }) => {\n   * return url.startsWith('./') || defaultValidate(url)\n   * }\n   */\n  isAllowedUri: (\n    /**\n     * The URL to be validated.\n     */\n    url: string,\n    ctx: {\n      /**\n       * The default validation function.\n       */\n      defaultValidate: (url: string) => boolean;\n      /**\n       * An array of allowed protocols for the URL (e.g., \"http\", \"https\"). As defined in the `protocols` option.\n       */\n      protocols: Array<LinkProtocolOptions | string>;\n      /**\n       * A string that represents the default protocol (e.g., 'http'). As defined in the `defaultProtocol` option.\n       */\n      defaultProtocol: string;\n    }\n  ) => boolean;\n\n  /**\n   * Determines whether a valid link should be automatically linked in the content.\n   *\n   * @param {string} url - The URL that has already been validated.\n   * @returns {boolean} - True if the link should be auto-linked; false if it should not be auto-linked.\n   */\n  shouldAutoLink: (url: string) => boolean;\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    link: {\n      /**\n       * Set a link mark\n       * @param attributes The link attributes\n       * @example editor.commands.setLink({ href: 'https://tiptap.dev' })\n       */\n      setLink: (attributes: {\n        href: string;\n        target?: string | null;\n        rel?: string | null;\n        class?: string | null;\n      }) => ReturnType;\n      /**\n       * Toggle a link mark\n       * @param attributes The link attributes\n       * @example editor.commands.toggleLink({ href: 'https://tiptap.dev' })\n       */\n      toggleLink: (attributes: {\n        href: string;\n        target?: string | null;\n        rel?: string | null;\n        class?: string | null;\n      }) => ReturnType;\n      /**\n       * Unset a link mark\n       * @example editor.commands.unsetLink()\n       */\n      unsetLink: () => ReturnType;\n    };\n  }\n}\n\n// From DOMPurify\n// https://github.com/cure53/DOMPurify/blob/main/src/regexp.js\n// eslint-disable-next-line no-control-regex\nconst ATTR_WHITESPACE = /[\\u0000-\\u0020\\u00A0\\u1680\\u180E\\u2000-\\u2029\\u205F\\u3000]/g\n\nexport function isAllowedUri(uri: string | undefined, protocols?: LinkOptions['protocols']) {\n  const allowedProtocols: string[] = [\n    'http',\n    'https',\n    'ftp',\n    'ftps',\n    'mailto',\n    'tel',\n    'callto',\n    'sms',\n    'cid',\n    'xmpp',\n  ]\n\n  if (protocols) {\n    protocols.forEach(protocol => {\n      const nextProtocol = typeof protocol === 'string' ? protocol : protocol.scheme\n\n      if (nextProtocol) {\n        allowedProtocols.push(nextProtocol)\n      }\n    })\n  }\n\n  return (\n    !uri\n    || uri\n      .replace(ATTR_WHITESPACE, '')\n      .match(\n        new RegExp(\n          // eslint-disable-next-line no-useless-escape\n          `^(?:(?:${allowedProtocols.join('|')}):|[^a-z]|[a-z0-9+.\\-]+(?:[^a-z+.\\-:]|$))`,\n          'i',\n        ),\n      )\n  )\n}\n\n/**\n * This extension allows you to create links.\n * @see https://www.tiptap.dev/api/marks/link\n */\nexport const Link = Mark.create<LinkOptions>({\n  name: 'link',\n\n  priority: 1000,\n\n  keepOnSplit: false,\n\n  exitable: true,\n\n  onCreate() {\n    if (this.options.validate && !this.options.shouldAutoLink) {\n      // Copy the validate function to the shouldAutoLink option\n      this.options.shouldAutoLink = this.options.validate\n      console.warn(\n        'The `validate` option is deprecated. Rename to the `shouldAutoLink` option instead.',\n      )\n    }\n    this.options.protocols.forEach(protocol => {\n      if (typeof protocol === 'string') {\n        registerCustomProtocol(protocol)\n        return\n      }\n      registerCustomProtocol(protocol.scheme, protocol.optionalSlashes)\n    })\n  },\n\n  onDestroy() {\n    reset()\n  },\n\n  inclusive() {\n    return this.options.autolink\n  },\n\n  addOptions() {\n    return {\n      openOnClick: true,\n      linkOnPaste: true,\n      autolink: true,\n      protocols: [],\n      defaultProtocol: 'http',\n      HTMLAttributes: {\n        target: '_blank',\n        rel: 'noopener noreferrer nofollow',\n        class: null,\n      },\n      isAllowedUri: (url, ctx) => !!isAllowedUri(url, ctx.protocols),\n      validate: url => !!url,\n      shouldAutoLink: url => !!url,\n    }\n  },\n\n  addAttributes() {\n    return {\n      href: {\n        default: null,\n        parseHTML(element) {\n          return element.getAttribute('href')\n        },\n      },\n      target: {\n        default: this.options.HTMLAttributes.target,\n      },\n      rel: {\n        default: this.options.HTMLAttributes.rel,\n      },\n      class: {\n        default: this.options.HTMLAttributes.class,\n      },\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 'a[href]',\n        getAttrs: dom => {\n          const href = (dom as HTMLElement).getAttribute('href')\n\n          // prevent XSS attacks\n          if (\n            !href\n            || !this.options.isAllowedUri(href, {\n              defaultValidate: url => !!isAllowedUri(url, this.options.protocols),\n              protocols: this.options.protocols,\n              defaultProtocol: this.options.defaultProtocol,\n            })\n          ) {\n            return false\n          }\n          return null\n        },\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    // prevent XSS attacks\n    if (\n      !this.options.isAllowedUri(HTMLAttributes.href, {\n        defaultValidate: href => !!isAllowedUri(href, this.options.protocols),\n        protocols: this.options.protocols,\n        defaultProtocol: this.options.defaultProtocol,\n      })\n    ) {\n      // strip out the href\n      return [\n        'a',\n        mergeAttributes(this.options.HTMLAttributes, { ...HTMLAttributes, href: '' }),\n        0,\n      ]\n    }\n\n    return ['a', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setLink:\n        attributes => ({ chain }) => {\n          const { href } = attributes\n\n          if (!this.options.isAllowedUri(href, {\n            defaultValidate: url => !!isAllowedUri(url, this.options.protocols),\n            protocols: this.options.protocols,\n            defaultProtocol: this.options.defaultProtocol,\n          })) {\n            return false\n          }\n\n          return chain().setMark(this.name, attributes).setMeta('preventAutolink', true).run()\n        },\n\n      toggleLink:\n        attributes => ({ chain }) => {\n          const { href } = attributes\n\n          if (!this.options.isAllowedUri(href, {\n            defaultValidate: url => !!isAllowedUri(url, this.options.protocols),\n            protocols: this.options.protocols,\n            defaultProtocol: this.options.defaultProtocol,\n          })) {\n            return false\n          }\n\n          return chain()\n            .toggleMark(this.name, attributes, { extendEmptyMarkRange: true })\n            .setMeta('preventAutolink', true)\n            .run()\n        },\n\n      unsetLink:\n        () => ({ chain }) => {\n          return chain()\n            .unsetMark(this.name, { extendEmptyMarkRange: true })\n            .setMeta('preventAutolink', true)\n            .run()\n        },\n    }\n  },\n\n  addPasteRules() {\n    return [\n      markPasteRule({\n        find: text => {\n          const foundLinks: PasteRuleMatch[] = []\n\n          if (text) {\n            const { protocols, defaultProtocol } = this.options\n            const links = find(text).filter(\n              item => item.isLink\n                && this.options.isAllowedUri(item.value, {\n                  defaultValidate: href => !!isAllowedUri(href, protocols),\n                  protocols,\n                  defaultProtocol,\n                }),\n            )\n\n            if (links.length) {\n              links.forEach(link => foundLinks.push({\n                text: link.value,\n                data: {\n                  href: link.href,\n                },\n                index: link.start,\n              }))\n            }\n          }\n\n          return foundLinks\n        },\n        type: this.type,\n        getAttributes: match => {\n          return {\n            href: match.data?.href,\n          }\n        },\n      }),\n    ]\n  },\n\n  addProseMirrorPlugins() {\n    const plugins: Plugin[] = []\n    const { protocols, defaultProtocol } = this.options\n\n    if (this.options.autolink) {\n      plugins.push(\n        autolink({\n          type: this.type,\n          defaultProtocol: this.options.defaultProtocol,\n          validate: url => this.options.isAllowedUri(url, {\n            defaultValidate: href => !!isAllowedUri(href, protocols),\n            protocols,\n            defaultProtocol,\n          }),\n          shouldAutoLink: this.options.shouldAutoLink,\n        }),\n      )\n    }\n\n    if (this.options.openOnClick === true) {\n      plugins.push(\n        clickHandler({\n          type: this.type,\n        }),\n      )\n    }\n\n    if (this.options.linkOnPaste) {\n      plugins.push(\n        pasteHandler({\n          editor: this.editor,\n          defaultProtocol: this.options.defaultProtocol,\n          type: this.type,\n        }),\n      )\n    }\n\n    return plugins\n  },\n})\n"], "names": ["Plugin", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "combineTransactionSteps", "getChangedRanges", "findChildrenInRange", "tokenize", "getMarksBetween", "getAttributes", "find", "<PERSON>", "registerCustomProtocol", "reset", "mergeAttributes", "markPasteRule"], "mappings": ";;;;;;EAWA;;;;;;;;;EASG;EACH,SAAS,oBAAoB,CAAC,MAAiD,EAAA;EAC7E,IAAA,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;EACvB,QAAA,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM;;EAGzB,IAAA,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;UAC3C,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;;EAGjE,IAAA,OAAO,KAAK;EACd;EASA;;;;EAIG;EACG,SAAU,QAAQ,CAAC,OAAwB,EAAA;MAC/C,OAAO,IAAIA,YAAM,CAAC;EAChB,QAAA,GAAG,EAAE,IAAIC,eAAS,CAAC,UAAU,CAAC;UAC9B,iBAAiB,EAAE,CAAC,YAAY,EAAE,QAAQ,EAAE,QAAQ,KAAI;EACtD;;EAEG;cACH,MAAM,UAAU,GAAG,YAAY,CAAC,IAAI,CAAC,WAAW,IAAI,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC;EAE7G;;EAEG;EACH,YAAA,MAAM,eAAe,GAAG,YAAY,CAAC,IAAI,CAAC,WAAW,IAAI,WAAW,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;EAEhG;;;EAGG;EACH,YAAA,IAAI,CAAC,UAAU,IAAI,eAAe,EAAE;kBAClC;;EAGF,YAAA,MAAM,EAAE,EAAE,EAAE,GAAG,QAAQ;EACvB,YAAA,MAAM,SAAS,GAAGC,4BAAuB,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,GAAG,YAAY,CAAC,CAAC;EAC1E,YAAA,MAAM,OAAO,GAAGC,qBAAgB,CAAC,SAAS,CAAC;cAE3C,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE,QAAQ,EAAE,KAAI;;EAE/B,gBAAA,MAAM,oBAAoB,GAAGC,wBAAmB,CAC9C,QAAQ,CAAC,GAAG,EACZ,QAAQ,EACR,IAAI,IAAI,IAAI,CAAC,WAAW,CACzB;EAED,gBAAA,IAAI,SAAkC;EACtC,gBAAA,IAAI,oBAAwC;EAE5C,gBAAA,IAAI,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE;;EAEnC,oBAAA,SAAS,GAAG,oBAAoB,CAAC,CAAC,CAAC;sBACnC,oBAAoB,GAAG,QAAQ,CAAC,GAAG,CAAC,WAAW,CAC7C,SAAS,CAAC,GAAG,EACb,SAAS,CAAC,GAAG,GAAG,SAAS,CAAC,IAAI,CAAC,QAAQ,EACvC,SAAS,EACT,GAAG,CACJ;;uBACI,IACL,oBAAoB,CAAC;;yBAElB,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,EAC/E;EACA,oBAAA,SAAS,GAAG,oBAAoB,CAAC,CAAC,CAAC;EACnC,oBAAA,oBAAoB,GAAG,QAAQ,CAAC,GAAG,CAAC,WAAW,CAC7C,SAAS,CAAC,GAAG,EACb,QAAQ,CAAC,EAAE,EACX,SAAS,EACT,GAAG,CACJ;;EAGH,gBAAA,IAAI,SAAS,IAAI,oBAAoB,EAAE;EACrC,oBAAA,MAAM,qBAAqB,GAAG,oBAAoB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;EAEnF,oBAAA,IAAI,qBAAqB,CAAC,MAAM,IAAI,CAAC,EAAE;EACrC,wBAAA,OAAO,KAAK;;sBAGd,MAAM,mBAAmB,GAAG,qBAAqB,CAAC,qBAAqB,CAAC,MAAM,GAAG,CAAC,CAAC;EACnF,oBAAA,MAAM,sBAAsB,GAAG,SAAS,CAAC,GAAG,GAAG,oBAAoB,CAAC,WAAW,CAAC,mBAAmB,CAAC;sBAEpG,IAAI,CAAC,mBAAmB,EAAE;EACxB,wBAAA,OAAO,KAAK;;sBAGd,MAAM,gBAAgB,GAAGC,kBAAQ,CAAC,mBAAmB,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;EAEpG,oBAAA,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,EAAE;EAC3C,wBAAA,OAAO,KAAK;;sBAGd;2BACG,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM;;EAE1B,yBAAA,GAAG,CAAC,IAAI,KAAK;EACZ,wBAAA,GAAG,IAAI;EACP,wBAAA,IAAI,EAAE,sBAAsB,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC;EAC7C,wBAAA,EAAE,EAAE,sBAAsB,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;EAC1C,qBAAA,CAAC;;2BAED,MAAM,CAAC,IAAI,IAAG;0BACb,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE;EAC/B,4BAAA,OAAO,IAAI;;0BAGb,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,YAAY,CAC/B,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,EAAE,EACP,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAC3B;EACH,qBAAC;;EAEA,yBAAA,MAAM,CAAC,IAAI,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC;;EAE3C,yBAAA,MAAM,CAAC,IAAI,IAAI,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC;;2BAEjD,OAAO,CAAC,IAAI,IAAG;EACd,wBAAA,IAAIC,oBAAe,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI,CAAC,EAAE;8BACnG;;EAGF,wBAAA,EAAE,CAAC,OAAO,CACR,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,EAAE,EACP,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC;8BAClB,IAAI,EAAE,IAAI,CAAC,IAAI;EAChB,yBAAA,CAAC,CACH;EACH,qBAAC,CAAC;;EAER,aAAC,CAAC;EAEF,YAAA,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE;kBACpB;;EAGF,YAAA,OAAO,EAAE;WACV;EACF,KAAA,CAAC;EACJ;;ECrKM,SAAU,YAAY,CAAC,OAA4B,EAAA;MACvD,OAAO,IAAIN,YAAM,CAAC;EAChB,QAAA,GAAG,EAAE,IAAIC,eAAS,CAAC,iBAAiB,CAAC;EACrC,QAAA,KAAK,EAAE;cACL,WAAW,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,KAAI;;EAChC,gBAAA,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;EACtB,oBAAA,OAAO,KAAK;;EAGd,gBAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;EAClB,oBAAA,OAAO,KAAK;;EAGd,gBAAA,IAAI,CAAC,GAAG,KAAK,CAAC,MAAqB;kBACnC,MAAM,GAAG,GAAG,EAAE;EAEd,gBAAA,OAAO,CAAC,CAAC,QAAQ,KAAK,KAAK,EAAE;EAC3B,oBAAA,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;EACX,oBAAA,CAAC,GAAG,CAAC,CAAC,UAAyB;;EAGjC,gBAAA,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,QAAQ,KAAK,GAAG,CAAC,EAAE;EAC9C,oBAAA,OAAO,KAAK;;EAGd,gBAAA,MAAM,KAAK,GAAGM,kBAAa,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;EAC1D,gBAAA,MAAM,IAAI,GAAI,KAAK,CAAC,MAA4B;EAEhD,gBAAA,MAAM,IAAI,GAAG,CAAA,EAAA,GAAA,IAAI,aAAJ,IAAI,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAJ,IAAI,CAAE,IAAI,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,KAAK,CAAC,IAAI;EACrC,gBAAA,MAAM,MAAM,GAAG,CAAA,EAAA,GAAA,IAAI,aAAJ,IAAI,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAJ,IAAI,CAAE,MAAM,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,KAAK,CAAC,MAAM;EAE3C,gBAAA,IAAI,IAAI,IAAI,IAAI,EAAE;EAChB,oBAAA,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC;EAEzB,oBAAA,OAAO,IAAI;;EAGb,gBAAA,OAAO,KAAK;eACb;EACF,SAAA;EACF,KAAA,CAAC;EACJ;;ECtCM,SAAU,YAAY,CAAC,OAA4B,EAAA;MACvD,OAAO,IAAIP,YAAM,CAAC;EAChB,QAAA,GAAG,EAAE,IAAIC,eAAS,CAAC,iBAAiB,CAAC;EACrC,QAAA,KAAK,EAAE;cACL,WAAW,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,KAAI;EAClC,gBAAA,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI;EACtB,gBAAA,MAAM,EAAE,SAAS,EAAE,GAAG,KAAK;EAC3B,gBAAA,MAAM,EAAE,KAAK,EAAE,GAAG,SAAS;kBAE3B,IAAI,KAAK,EAAE;EACT,oBAAA,OAAO,KAAK;;kBAGd,IAAI,WAAW,GAAG,EAAE;EAEpB,gBAAA,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,IAAG;EAC3B,oBAAA,WAAW,IAAI,IAAI,CAAC,WAAW;EACjC,iBAAC,CAAC;EAEF,gBAAA,MAAM,IAAI,GAAGO,cAAI,CAAC,WAAW,EAAE,EAAE,eAAe,EAAE,OAAO,CAAC,eAAe,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,KAAK,WAAW,CAAC;EAEpI,gBAAA,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,EAAE;EACzB,oBAAA,OAAO,KAAK;;kBAGd,OAAO,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE;sBACnD,IAAI,EAAE,IAAI,CAAC,IAAI;EAChB,iBAAA,CAAC;eACH;EACF,SAAA;EACF,KAAA,CAAC;EACJ;;ACfO,QAAM,UAAU,GAAG;EAkI1B;EACA;EACA;EACA,MAAM,eAAe,GAAG,6DAA6D;EAErE,SAAA,YAAY,CAAC,GAAuB,EAAE,SAAoC,EAAA;EACxF,IAAA,MAAM,gBAAgB,GAAa;UACjC,MAAM;UACN,OAAO;UACP,KAAK;UACL,MAAM;UACN,QAAQ;UACR,KAAK;UACL,QAAQ;UACR,KAAK;UACL,KAAK;UACL,MAAM;OACP;MAED,IAAI,SAAS,EAAE;EACb,QAAA,SAAS,CAAC,OAAO,CAAC,QAAQ,IAAG;EAC3B,YAAA,MAAM,YAAY,GAAG,OAAO,QAAQ,KAAK,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC,MAAM;cAE9E,IAAI,YAAY,EAAE;EAChB,gBAAA,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC;;EAEvC,SAAC,CAAC;;MAGJ,QACE,CAAC;aACE;EACA,aAAA,OAAO,CAAC,eAAe,EAAE,EAAE;eAC3B,KAAK,CACJ,IAAI,MAAM;;EAER,QAAA,CAAA,OAAA,EAAU,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,CAA2C,yCAAA,CAAA,EAC/E,GAAG,CACJ,CACF;EAEP;EAEA;;;EAGG;AACU,QAAA,IAAI,GAAGC,SAAI,CAAC,MAAM,CAAc;EAC3C,IAAA,IAAI,EAAE,MAAM;EAEZ,IAAA,QAAQ,EAAE,IAAI;EAEd,IAAA,WAAW,EAAE,KAAK;EAElB,IAAA,QAAQ,EAAE,IAAI;MAEd,QAAQ,GAAA;EACN,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE;;cAEzD,IAAI,CAAC,OAAO,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ;EACnD,YAAA,OAAO,CAAC,IAAI,CACV,qFAAqF,CACtF;;UAEH,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,IAAG;EACxC,YAAA,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;kBAChCC,gCAAsB,CAAC,QAAQ,CAAC;kBAChC;;cAEFA,gCAAsB,CAAC,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,eAAe,CAAC;EACnE,SAAC,CAAC;OACH;MAED,SAAS,GAAA;EACP,QAAAC,eAAK,EAAE;OACR;MAED,SAAS,GAAA;EACP,QAAA,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ;OAC7B;MAED,UAAU,GAAA;UACR,OAAO;EACL,YAAA,WAAW,EAAE,IAAI;EACjB,YAAA,WAAW,EAAE,IAAI;EACjB,YAAA,QAAQ,EAAE,IAAI;EACd,YAAA,SAAS,EAAE,EAAE;EACb,YAAA,eAAe,EAAE,MAAM;EACvB,YAAA,cAAc,EAAE;EACd,gBAAA,MAAM,EAAE,QAAQ;EAChB,gBAAA,GAAG,EAAE,8BAA8B;EACnC,gBAAA,KAAK,EAAE,IAAI;EACZ,aAAA;EACD,YAAA,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,CAAC,YAAY,CAAC,GAAG,EAAE,GAAG,CAAC,SAAS,CAAC;EAC9D,YAAA,QAAQ,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG;EACtB,YAAA,cAAc,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG;WAC7B;OACF;MAED,aAAa,GAAA;UACX,OAAO;EACL,YAAA,IAAI,EAAE;EACJ,gBAAA,OAAO,EAAE,IAAI;EACb,gBAAA,SAAS,CAAC,OAAO,EAAA;EACf,oBAAA,OAAO,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC;mBACpC;EACF,aAAA;EACD,YAAA,MAAM,EAAE;EACN,gBAAA,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,MAAM;EAC5C,aAAA;EACD,YAAA,GAAG,EAAE;EACH,gBAAA,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG;EACzC,aAAA;EACD,YAAA,KAAK,EAAE;EACL,gBAAA,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK;EAC3C,aAAA;WACF;OACF;MAED,SAAS,GAAA;UACP,OAAO;EACL,YAAA;EACE,gBAAA,GAAG,EAAE,SAAS;kBACd,QAAQ,EAAE,GAAG,IAAG;sBACd,MAAM,IAAI,GAAI,GAAmB,CAAC,YAAY,CAAC,MAAM,CAAC;;EAGtD,oBAAA,IACE,CAAC;EACE,2BAAA,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE;EAClC,4BAAA,eAAe,EAAE,GAAG,IAAI,CAAC,CAAC,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;EACnE,4BAAA,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS;EACjC,4BAAA,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;EAC9C,yBAAA,CAAC,EACF;EACA,wBAAA,OAAO,KAAK;;EAEd,oBAAA,OAAO,IAAI;mBACZ;EACF,aAAA;WACF;OACF;MAED,UAAU,CAAC,EAAE,cAAc,EAAE,EAAA;;UAE3B,IACE,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,cAAc,CAAC,IAAI,EAAE;EAC9C,YAAA,eAAe,EAAE,IAAI,IAAI,CAAC,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;EACrE,YAAA,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS;EACjC,YAAA,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;EAC9C,SAAA,CAAC,EACF;;cAEA,OAAO;kBACL,GAAG;EACH,gBAAAC,oBAAe,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,EAAE,GAAG,cAAc,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;kBAC7E,CAAC;eACF;;EAGH,QAAA,OAAO,CAAC,GAAG,EAAEA,oBAAe,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC,EAAE,CAAC,CAAC;OAC9E;MAED,WAAW,GAAA;UACT,OAAO;cACL,OAAO,EACL,UAAU,IAAI,CAAC,EAAE,KAAK,EAAE,KAAI;EAC1B,gBAAA,MAAM,EAAE,IAAI,EAAE,GAAG,UAAU;kBAE3B,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE;EACnC,oBAAA,eAAe,EAAE,GAAG,IAAI,CAAC,CAAC,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;EACnE,oBAAA,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS;EACjC,oBAAA,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;EAC9C,iBAAA,CAAC,EAAE;EACF,oBAAA,OAAO,KAAK;;kBAGd,OAAO,KAAK,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,OAAO,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC,GAAG,EAAE;eACrF;cAEH,UAAU,EACR,UAAU,IAAI,CAAC,EAAE,KAAK,EAAE,KAAI;EAC1B,gBAAA,MAAM,EAAE,IAAI,EAAE,GAAG,UAAU;kBAE3B,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE;EACnC,oBAAA,eAAe,EAAE,GAAG,IAAI,CAAC,CAAC,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;EACnE,oBAAA,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS;EACjC,oBAAA,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;EAC9C,iBAAA,CAAC,EAAE;EACF,oBAAA,OAAO,KAAK;;EAGd,gBAAA,OAAO,KAAK;EACT,qBAAA,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,EAAE,EAAE,oBAAoB,EAAE,IAAI,EAAE;EAChE,qBAAA,OAAO,CAAC,iBAAiB,EAAE,IAAI;EAC/B,qBAAA,GAAG,EAAE;eACT;cAEH,SAAS,EACP,MAAM,CAAC,EAAE,KAAK,EAAE,KAAI;EAClB,gBAAA,OAAO,KAAK;uBACT,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,oBAAoB,EAAE,IAAI,EAAE;EACnD,qBAAA,OAAO,CAAC,iBAAiB,EAAE,IAAI;EAC/B,qBAAA,GAAG,EAAE;eACT;WACJ;OACF;MAED,aAAa,GAAA;UACX,OAAO;EACL,YAAAC,kBAAa,CAAC;kBACZ,IAAI,EAAE,IAAI,IAAG;sBACX,MAAM,UAAU,GAAqB,EAAE;sBAEvC,IAAI,IAAI,EAAE;0BACR,MAAM,EAAE,SAAS,EAAE,eAAe,EAAE,GAAG,IAAI,CAAC,OAAO;EACnD,wBAAA,MAAM,KAAK,GAAGL,cAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAC7B,IAAI,IAAI,IAAI,CAAC;iCACR,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE;EACvC,gCAAA,eAAe,EAAE,IAAI,IAAI,CAAC,CAAC,YAAY,CAAC,IAAI,EAAE,SAAS,CAAC;kCACxD,SAAS;kCACT,eAAe;EAChB,6BAAA,CAAC,CACL;EAED,wBAAA,IAAI,KAAK,CAAC,MAAM,EAAE;8BAChB,KAAK,CAAC,OAAO,CAAC,IAAI,IAAI,UAAU,CAAC,IAAI,CAAC;kCACpC,IAAI,EAAE,IAAI,CAAC,KAAK;EAChB,gCAAA,IAAI,EAAE;sCACJ,IAAI,EAAE,IAAI,CAAC,IAAI;EAChB,iCAAA;kCACD,KAAK,EAAE,IAAI,CAAC,KAAK;EAClB,6BAAA,CAAC,CAAC;;;EAIP,oBAAA,OAAO,UAAU;mBAClB;kBACD,IAAI,EAAE,IAAI,CAAC,IAAI;kBACf,aAAa,EAAE,KAAK,IAAG;;sBACrB,OAAO;EACL,wBAAA,IAAI,EAAE,CAAA,EAAA,GAAA,KAAK,CAAC,IAAI,0CAAE,IAAI;uBACvB;mBACF;eACF,CAAC;WACH;OACF;MAED,qBAAqB,GAAA;UACnB,MAAM,OAAO,GAAa,EAAE;UAC5B,MAAM,EAAE,SAAS,EAAE,eAAe,EAAE,GAAG,IAAI,CAAC,OAAO;EAEnD,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;EACzB,YAAA,OAAO,CAAC,IAAI,CACV,QAAQ,CAAC;kBACP,IAAI,EAAE,IAAI,CAAC,IAAI;EACf,gBAAA,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;EAC7C,gBAAA,QAAQ,EAAE,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,EAAE;EAC9C,oBAAA,eAAe,EAAE,IAAI,IAAI,CAAC,CAAC,YAAY,CAAC,IAAI,EAAE,SAAS,CAAC;sBACxD,SAAS;sBACT,eAAe;mBAChB,CAAC;EACF,gBAAA,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc;EAC5C,aAAA,CAAC,CACH;;UAGH,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,KAAK,IAAI,EAAE;EACrC,YAAA,OAAO,CAAC,IAAI,CACV,YAAY,CAAC;kBACX,IAAI,EAAE,IAAI,CAAC,IAAI;EAChB,aAAA,CAAC,CACH;;EAGH,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;EAC5B,YAAA,OAAO,CAAC,IAAI,CACV,YAAY,CAAC;kBACX,MAAM,EAAE,IAAI,CAAC,MAAM;EACnB,gBAAA,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;kBAC7C,IAAI,EAAE,IAAI,CAAC,IAAI;EAChB,aAAA,CAAC,CACH;;EAGH,QAAA,OAAO,OAAO;OACf;EACF,CAAA;;;;;;;;;;;;;"}