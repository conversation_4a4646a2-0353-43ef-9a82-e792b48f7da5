{"version": 3, "file": "index.umd.js", "sources": ["../src/starter-kit.ts"], "sourcesContent": ["import { Extension } from '@tiptap/core'\nimport { Blockquote, BlockquoteOptions } from '@tiptap/extension-blockquote'\nimport { Bold, BoldOptions } from '@tiptap/extension-bold'\nimport { BulletList, BulletListOptions } from '@tiptap/extension-bullet-list'\nimport { Code, CodeOptions } from '@tiptap/extension-code'\nimport { CodeBlock, CodeBlockOptions } from '@tiptap/extension-code-block'\nimport { Document } from '@tiptap/extension-document'\nimport { Dropcursor, DropcursorOptions } from '@tiptap/extension-dropcursor'\nimport { Gapcursor } from '@tiptap/extension-gapcursor'\nimport { HardBreak, HardBreakOptions } from '@tiptap/extension-hard-break'\nimport { Heading, HeadingOptions } from '@tiptap/extension-heading'\nimport { History, HistoryOptions } from '@tiptap/extension-history'\nimport { HorizontalRule, HorizontalRuleOptions } from '@tiptap/extension-horizontal-rule'\nimport { Italic, ItalicOptions } from '@tiptap/extension-italic'\nimport { ListItem, ListItemOptions } from '@tiptap/extension-list-item'\nimport { OrderedList, OrderedListOptions } from '@tiptap/extension-ordered-list'\nimport { Paragraph, ParagraphOptions } from '@tiptap/extension-paragraph'\nimport { Strike, StrikeOptions } from '@tiptap/extension-strike'\nimport { Text } from '@tiptap/extension-text'\n\nexport interface StarterKitOptions {\n  /**\n   * If set to false, the blockquote extension will not be registered\n   * @example blockquote: false\n   */\n  blockquote: Partial<BlockquoteOptions> | false,\n\n  /**\n   * If set to false, the bold extension will not be registered\n   * @example bold: false\n   */\n  bold: Partial<BoldOptions> | false,\n\n  /**\n   * If set to false, the bulletList extension will not be registered\n   * @example bulletList: false\n   */\n  bulletList: Partial<BulletListOptions> | false,\n\n  /**\n   * If set to false, the code extension will not be registered\n   * @example code: false\n   */\n  code: Partial<CodeOptions> | false,\n\n  /**\n   * If set to false, the codeBlock extension will not be registered\n   * @example codeBlock: false\n   */\n  codeBlock: Partial<CodeBlockOptions> | false,\n\n  /**\n   * If set to false, the document extension will not be registered\n   * @example document: false\n   */\n  document: false,\n\n  /**\n   * If set to false, the dropcursor extension will not be registered\n   * @example dropcursor: false\n   */\n  dropcursor: Partial<DropcursorOptions> | false,\n\n  /**\n   * If set to false, the gapcursor extension will not be registered\n   * @example gapcursor: false\n   */\n  gapcursor: false,\n\n  /**\n   * If set to false, the hardBreak extension will not be registered\n   * @example hardBreak: false\n   */\n  hardBreak: Partial<HardBreakOptions> | false,\n\n  /**\n   * If set to false, the heading extension will not be registered\n   * @example heading: false\n   */\n  heading: Partial<HeadingOptions> | false,\n\n  /**\n   * If set to false, the history extension will not be registered\n   * @example history: false\n   */\n  history: Partial<HistoryOptions> | false,\n\n  /**\n   * If set to false, the horizontalRule extension will not be registered\n   * @example horizontalRule: false\n   */\n  horizontalRule: Partial<HorizontalRuleOptions> | false,\n\n  /**\n   * If set to false, the italic extension will not be registered\n   * @example italic: false\n   */\n  italic: Partial<ItalicOptions> | false,\n\n  /**\n   * If set to false, the listItem extension will not be registered\n   * @example listItem: false\n   */\n  listItem: Partial<ListItemOptions> | false,\n\n  /**\n   * If set to false, the orderedList extension will not be registered\n   * @example orderedList: false\n   */\n  orderedList: Partial<OrderedListOptions> | false,\n\n  /**\n   * If set to false, the paragraph extension will not be registered\n   * @example paragraph: false\n   */\n  paragraph: Partial<ParagraphOptions> | false,\n\n  /**\n   * If set to false, the strike extension will not be registered\n   * @example strike: false\n   */\n  strike: Partial<StrikeOptions> | false,\n\n  /**\n   * If set to false, the text extension will not be registered\n   * @example text: false\n   */\n  text: false,\n}\n\n/**\n * The starter kit is a collection of essential editor extensions.\n *\n * It’s a good starting point for building your own editor.\n */\nexport const StarterKit = Extension.create<StarterKitOptions>({\n  name: 'starterKit',\n\n  addExtensions() {\n    const extensions = []\n\n    if (this.options.bold !== false) {\n      extensions.push(Bold.configure(this.options.bold))\n    }\n\n    if (this.options.blockquote !== false) {\n      extensions.push(Blockquote.configure(this.options.blockquote))\n    }\n\n    if (this.options.bulletList !== false) {\n      extensions.push(BulletList.configure(this.options.bulletList))\n    }\n\n    if (this.options.code !== false) {\n      extensions.push(Code.configure(this.options.code))\n    }\n\n    if (this.options.codeBlock !== false) {\n      extensions.push(CodeBlock.configure(this.options.codeBlock))\n    }\n\n    if (this.options.document !== false) {\n      extensions.push(Document.configure(this.options.document))\n    }\n\n    if (this.options.dropcursor !== false) {\n      extensions.push(Dropcursor.configure(this.options.dropcursor))\n    }\n\n    if (this.options.gapcursor !== false) {\n      extensions.push(Gapcursor.configure(this.options.gapcursor))\n    }\n\n    if (this.options.hardBreak !== false) {\n      extensions.push(HardBreak.configure(this.options.hardBreak))\n    }\n\n    if (this.options.heading !== false) {\n      extensions.push(Heading.configure(this.options.heading))\n    }\n\n    if (this.options.history !== false) {\n      extensions.push(History.configure(this.options.history))\n    }\n\n    if (this.options.horizontalRule !== false) {\n      extensions.push(HorizontalRule.configure(this.options.horizontalRule))\n    }\n\n    if (this.options.italic !== false) {\n      extensions.push(Italic.configure(this.options.italic))\n    }\n\n    if (this.options.listItem !== false) {\n      extensions.push(ListItem.configure(this.options.listItem))\n    }\n\n    if (this.options.orderedList !== false) {\n      extensions.push(OrderedList.configure(this.options.orderedList))\n    }\n\n    if (this.options.paragraph !== false) {\n      extensions.push(Paragraph.configure(this.options.paragraph))\n    }\n\n    if (this.options.strike !== false) {\n      extensions.push(Strike.configure(this.options.strike))\n    }\n\n    if (this.options.text !== false) {\n      extensions.push(Text.configure(this.options.text))\n    }\n\n    return extensions\n  },\n})\n"], "names": ["Extension", "Bold", "Blockquote", "BulletList", "Code", "CodeBlock", "Document", "Dropcursor", "Gapcursor", "HardBreak", "Heading", "History", "HorizontalRule", "Italic", "ListItem", "OrderedList", "Paragraph", "Strike", "Text"], "mappings": ";;;;;;EAkIA;;;;EAIG;AACU,QAAA,UAAU,GAAGA,cAAS,CAAC,MAAM,CAAoB;EAC5D,IAAA,IAAI,EAAE,YAAY;MAElB,aAAa,GAAA;UACX,MAAM,UAAU,GAAG,EAAE;UAErB,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,KAAK,EAAE;EAC/B,YAAA,UAAU,CAAC,IAAI,CAACC,kBAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;;UAGpD,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,KAAK,KAAK,EAAE;EACrC,YAAA,UAAU,CAAC,IAAI,CAACC,8BAAU,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;;UAGhE,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,KAAK,KAAK,EAAE;EACrC,YAAA,UAAU,CAAC,IAAI,CAACC,8BAAU,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;;UAGhE,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,KAAK,EAAE;EAC/B,YAAA,UAAU,CAAC,IAAI,CAACC,kBAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;;UAGpD,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,KAAK,KAAK,EAAE;EACpC,YAAA,UAAU,CAAC,IAAI,CAACC,4BAAS,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;;UAG9D,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,KAAK,EAAE;EACnC,YAAA,UAAU,CAAC,IAAI,CAACC,0BAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;;UAG5D,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,KAAK,KAAK,EAAE;EACrC,YAAA,UAAU,CAAC,IAAI,CAACC,8BAAU,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;;UAGhE,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,KAAK,KAAK,EAAE;EACpC,YAAA,UAAU,CAAC,IAAI,CAACC,4BAAS,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;;UAG9D,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,KAAK,KAAK,EAAE;EACpC,YAAA,UAAU,CAAC,IAAI,CAACC,4BAAS,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;;UAG9D,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,KAAK,EAAE;EAClC,YAAA,UAAU,CAAC,IAAI,CAACC,wBAAO,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;;UAG1D,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,KAAK,EAAE;EAClC,YAAA,UAAU,CAAC,IAAI,CAACC,wBAAO,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;;UAG1D,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,KAAK,KAAK,EAAE;EACzC,YAAA,UAAU,CAAC,IAAI,CAACC,sCAAc,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;;UAGxE,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,KAAK,EAAE;EACjC,YAAA,UAAU,CAAC,IAAI,CAACC,sBAAM,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;;UAGxD,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,KAAK,EAAE;EACnC,YAAA,UAAU,CAAC,IAAI,CAACC,0BAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;;UAG5D,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,KAAK,KAAK,EAAE;EACtC,YAAA,UAAU,CAAC,IAAI,CAACC,gCAAW,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;;UAGlE,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,KAAK,KAAK,EAAE;EACpC,YAAA,UAAU,CAAC,IAAI,CAACC,4BAAS,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;;UAG9D,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,KAAK,EAAE;EACjC,YAAA,UAAU,CAAC,IAAI,CAACC,sBAAM,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;;UAGxD,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,KAAK,EAAE;EAC/B,YAAA,UAAU,CAAC,IAAI,CAACC,kBAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;;EAGpD,QAAA,OAAO,UAAU;OAClB;EACF,CAAA;;;;;;;;;;;"}