{"version": 3, "file": "index.cjs", "sources": ["../src/history.ts"], "sourcesContent": ["import { Extension } from '@tiptap/core'\nimport { history, redo, undo } from '@tiptap/pm/history'\n\nexport interface HistoryOptions {\n  /**\n   * The amount of history events that are collected before the oldest events are discarded.\n   * @default 100\n   * @example 50\n   */\n  depth: number,\n\n  /**\n   * The delay (in milliseconds) between changes after which a new group should be started.\n   * @default 500\n   * @example 1000\n   */\n  newGroupDelay: number,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    history: {\n      /**\n       * Undo recent changes\n       * @example editor.commands.undo()\n       */\n      undo: () => ReturnType,\n      /**\n       * Reapply reverted changes\n       * @example editor.commands.redo()\n       */\n      redo: () => ReturnType,\n    }\n  }\n}\n\n/**\n * This extension allows you to undo and redo recent changes.\n * @see https://www.tiptap.dev/api/extensions/history\n *\n * **Important**: If the `@tiptap/extension-collaboration` package is used, make sure to remove\n * the `history` extension, as it is not compatible with the `collaboration` extension.\n *\n * `@tiptap/extension-collaboration` uses its own history implementation.\n */\nexport const History = Extension.create<HistoryOptions>({\n  name: 'history',\n\n  addOptions() {\n    return {\n      depth: 100,\n      newGroupDelay: 500,\n    }\n  },\n\n  addCommands() {\n    return {\n      undo: () => ({ state, dispatch }) => {\n        return undo(state, dispatch)\n      },\n      redo: () => ({ state, dispatch }) => {\n        return redo(state, dispatch)\n      },\n    }\n  },\n\n  addProseMirrorPlugins() {\n    return [\n      history(this.options),\n    ]\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-z': () => this.editor.commands.undo(),\n      'Shift-Mod-z': () => this.editor.commands.redo(),\n      'Mod-y': () => this.editor.commands.redo(),\n\n      // Russian keyboard layouts\n      'Mod-я': () => this.editor.commands.undo(),\n      'Shift-Mod-я': () => this.editor.commands.redo(),\n    }\n  },\n})\n"], "names": ["Extension", "undo", "redo", "history"], "mappings": ";;;;;;;AAoCA;;;;;;;;AAQG;AACU,MAAA,OAAO,GAAGA,cAAS,CAAC,MAAM,CAAiB;AACtD,IAAA,IAAI,EAAE,SAAS;IAEf,UAAU,GAAA;QACR,OAAO;AACL,YAAA,KAAK,EAAE,GAAG;AACV,YAAA,aAAa,EAAE,GAAG;SACnB;KACF;IAED,WAAW,GAAA;QACT,OAAO;YACL,IAAI,EAAE,MAAM,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;AAClC,gBAAA,OAAOC,YAAI,CAAC,KAAK,EAAE,QAAQ,CAAC;aAC7B;YACD,IAAI,EAAE,MAAM,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAI;AAClC,gBAAA,OAAOC,YAAI,CAAC,KAAK,EAAE,QAAQ,CAAC;aAC7B;SACF;KACF;IAED,qBAAqB,GAAA;QACnB,OAAO;AACL,YAAAC,eAAO,CAAC,IAAI,CAAC,OAAO,CAAC;SACtB;KACF;IAED,oBAAoB,GAAA;QAClB,OAAO;YACL,OAAO,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE;YAC1C,aAAa,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE;YAChD,OAAO,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE;;YAG1C,OAAO,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE;YAC1C,aAAa,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE;SACjD;KACF;AACF,CAAA;;;;;"}