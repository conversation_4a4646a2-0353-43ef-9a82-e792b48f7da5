{"version": 3, "file": "index.umd.js", "sources": ["../src/blockquote.ts"], "sourcesContent": ["import { mergeAttributes, Node, wrappingInputRule } from '@tiptap/core'\n\nexport interface BlockquoteOptions {\n  /**\n   * HTML attributes to add to the blockquote element\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    blockQuote: {\n      /**\n       * Set a blockquote node\n       */\n      setBlockquote: () => ReturnType,\n      /**\n       * Toggle a blockquote node\n       */\n      toggleBlockquote: () => ReturnType,\n      /**\n       * Unset a blockquote node\n       */\n      unsetBlockquote: () => ReturnType,\n    }\n  }\n}\n\n/**\n * Matches a blockquote to a `>` as input.\n */\nexport const inputRegex = /^\\s*>\\s$/\n\n/**\n * This extension allows you to create blockquotes.\n * @see https://tiptap.dev/api/nodes/blockquote\n */\nexport const Blockquote = Node.create<BlockquoteOptions>({\n\n  name: 'blockquote',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  content: 'block+',\n\n  group: 'block',\n\n  defining: true,\n\n  parseHTML() {\n    return [\n      { tag: 'blockquote' },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['blockquote', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setBlockquote: () => ({ commands }) => {\n        return commands.wrapIn(this.name)\n      },\n      toggleBlockquote: () => ({ commands }) => {\n        return commands.toggleWrap(this.name)\n      },\n      unsetBlockquote: () => ({ commands }) => {\n        return commands.lift(this.name)\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-Shift-b': () => this.editor.commands.toggleBlockquote(),\n    }\n  },\n\n  addInputRules() {\n    return [\n      wrappingInputRule({\n        find: inputRegex,\n        type: this.type,\n      }),\n    ]\n  },\n})\n"], "names": ["Node", "mergeAttributes", "wrappingInputRule"], "mappings": ";;;;;;EA8BA;;EAEG;AACI,QAAM,UAAU,GAAG;EAE1B;;;EAGG;AACU,QAAA,UAAU,GAAGA,SAAI,CAAC,MAAM,CAAoB;EAEvD,IAAA,IAAI,EAAE,YAAY;MAElB,UAAU,GAAA;UACR,OAAO;EACL,YAAA,cAAc,EAAE,EAAE;WACnB;OACF;EAED,IAAA,OAAO,EAAE,QAAQ;EAEjB,IAAA,KAAK,EAAE,OAAO;EAEd,IAAA,QAAQ,EAAE,IAAI;MAEd,SAAS,GAAA;UACP,OAAO;cACL,EAAE,GAAG,EAAE,YAAY,EAAE;WACtB;OACF;MAED,UAAU,CAAC,EAAE,cAAc,EAAE,EAAA;EAC3B,QAAA,OAAO,CAAC,YAAY,EAAEC,oBAAe,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC,EAAE,CAAC,CAAC;OACvF;MAED,WAAW,GAAA;UACT,OAAO;cACL,aAAa,EAAE,MAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;kBACpC,OAAO,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;eAClC;cACD,gBAAgB,EAAE,MAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;kBACvC,OAAO,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;eACtC;cACD,eAAe,EAAE,MAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;kBACtC,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;eAChC;WACF;OACF;MAED,oBAAoB,GAAA;UAClB,OAAO;cACL,aAAa,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,gBAAgB,EAAE;WAC7D;OACF;MAED,aAAa,GAAA;UACX,OAAO;EACL,YAAAC,sBAAiB,CAAC;EAChB,gBAAA,IAAI,EAAE,UAAU;kBAChB,IAAI,EAAE,IAAI,CAAC,IAAI;eAChB,CAAC;WACH;OACF;EACF,CAAA;;;;;;;;;;;;"}