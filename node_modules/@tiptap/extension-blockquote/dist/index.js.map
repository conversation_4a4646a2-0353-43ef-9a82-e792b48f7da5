{"version": 3, "file": "index.js", "sources": ["../src/blockquote.ts"], "sourcesContent": ["import { mergeAttributes, Node, wrappingInputRule } from '@tiptap/core'\n\nexport interface BlockquoteOptions {\n  /**\n   * HTML attributes to add to the blockquote element\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    blockQuote: {\n      /**\n       * Set a blockquote node\n       */\n      setBlockquote: () => ReturnType,\n      /**\n       * Toggle a blockquote node\n       */\n      toggleBlockquote: () => ReturnType,\n      /**\n       * Unset a blockquote node\n       */\n      unsetBlockquote: () => ReturnType,\n    }\n  }\n}\n\n/**\n * Matches a blockquote to a `>` as input.\n */\nexport const inputRegex = /^\\s*>\\s$/\n\n/**\n * This extension allows you to create blockquotes.\n * @see https://tiptap.dev/api/nodes/blockquote\n */\nexport const Blockquote = Node.create<BlockquoteOptions>({\n\n  name: 'blockquote',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  content: 'block+',\n\n  group: 'block',\n\n  defining: true,\n\n  parseHTML() {\n    return [\n      { tag: 'blockquote' },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['blockquote', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setBlockquote: () => ({ commands }) => {\n        return commands.wrapIn(this.name)\n      },\n      toggleBlockquote: () => ({ commands }) => {\n        return commands.toggleWrap(this.name)\n      },\n      unsetBlockquote: () => ({ commands }) => {\n        return commands.lift(this.name)\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-Shift-b': () => this.editor.commands.toggleBlockquote(),\n    }\n  },\n\n  addInputRules() {\n    return [\n      wrappingInputRule({\n        find: inputRegex,\n        type: this.type,\n      }),\n    ]\n  },\n})\n"], "names": [], "mappings": ";;AA8BA;;AAEG;AACI,MAAM,UAAU,GAAG;AAE1B;;;AAGG;AACU,MAAA,UAAU,GAAG,IAAI,CAAC,MAAM,CAAoB;AAEvD,IAAA,IAAI,EAAE,YAAY;IAElB,UAAU,GAAA;QACR,OAAO;AACL,YAAA,cAAc,EAAE,EAAE;SACnB;KACF;AAED,IAAA,OAAO,EAAE,QAAQ;AAEjB,IAAA,KAAK,EAAE,OAAO;AAEd,IAAA,QAAQ,EAAE,IAAI;IAEd,SAAS,GAAA;QACP,OAAO;YACL,EAAE,GAAG,EAAE,YAAY,EAAE;SACtB;KACF;IAED,UAAU,CAAC,EAAE,cAAc,EAAE,EAAA;AAC3B,QAAA,OAAO,CAAC,YAAY,EAAE,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC,EAAE,CAAC,CAAC;KACvF;IAED,WAAW,GAAA;QACT,OAAO;YACL,aAAa,EAAE,MAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;gBACpC,OAAO,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;aAClC;YACD,gBAAgB,EAAE,MAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;gBACvC,OAAO,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;aACtC;YACD,eAAe,EAAE,MAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;gBACtC,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;aAChC;SACF;KACF;IAED,oBAAoB,GAAA;QAClB,OAAO;YACL,aAAa,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,gBAAgB,EAAE;SAC7D;KACF;IAED,aAAa,GAAA;QACX,OAAO;AACL,YAAA,iBAAiB,CAAC;AAChB,gBAAA,IAAI,EAAE,UAAU;gBAChB,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB,CAAC;SACH;KACF;AACF,CAAA;;;;"}