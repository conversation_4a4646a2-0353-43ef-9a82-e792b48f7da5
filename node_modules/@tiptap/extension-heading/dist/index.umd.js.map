{"version": 3, "file": "index.umd.js", "sources": ["../src/heading.ts"], "sourcesContent": ["import { mergeAttributes, Node, textblockTypeInputRule } from '@tiptap/core'\n\n/**\n * The heading level options.\n */\nexport type Level = 1 | 2 | 3 | 4 | 5 | 6\n\nexport interface HeadingOptions {\n  /**\n   * The available heading levels.\n   * @default [1, 2, 3, 4, 5, 6]\n   * @example [1, 2, 3]\n   */\n  levels: Level[],\n\n  /**\n   * The HTML attributes for a heading node.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    heading: {\n      /**\n       * Set a heading node\n       * @param attributes The heading attributes\n       * @example editor.commands.setHeading({ level: 1 })\n       */\n      setHeading: (attributes: { level: Level }) => ReturnType,\n      /**\n       * Toggle a heading node\n       * @param attributes The heading attributes\n       * @example editor.commands.toggleHeading({ level: 1 })\n       */\n      toggleHeading: (attributes: { level: Level }) => ReturnType,\n    }\n  }\n}\n\n/**\n * This extension allows you to create headings.\n * @see https://www.tiptap.dev/api/nodes/heading\n */\nexport const Heading = Node.create<HeadingOptions>({\n  name: 'heading',\n\n  addOptions() {\n    return {\n      levels: [1, 2, 3, 4, 5, 6],\n      HTMLAttributes: {},\n    }\n  },\n\n  content: 'inline*',\n\n  group: 'block',\n\n  defining: true,\n\n  addAttributes() {\n    return {\n      level: {\n        default: 1,\n        rendered: false,\n      },\n    }\n  },\n\n  parseHTML() {\n    return this.options.levels\n      .map((level: Level) => ({\n        tag: `h${level}`,\n        attrs: { level },\n      }))\n  },\n\n  renderHTML({ node, HTMLAttributes }) {\n    const hasLevel = this.options.levels.includes(node.attrs.level)\n    const level = hasLevel\n      ? node.attrs.level\n      : this.options.levels[0]\n\n    return [`h${level}`, mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setHeading: attributes => ({ commands }) => {\n        if (!this.options.levels.includes(attributes.level)) {\n          return false\n        }\n\n        return commands.setNode(this.name, attributes)\n      },\n      toggleHeading: attributes => ({ commands }) => {\n        if (!this.options.levels.includes(attributes.level)) {\n          return false\n        }\n\n        return commands.toggleNode(this.name, 'paragraph', attributes)\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return this.options.levels.reduce((items, level) => ({\n      ...items,\n      ...{\n        [`Mod-Alt-${level}`]: () => this.editor.commands.toggleHeading({ level }),\n      },\n    }), {})\n  },\n\n  addInputRules() {\n    return this.options.levels.map(level => {\n      return textblockTypeInputRule({\n        find: new RegExp(`^(#{${Math.min(...this.options.levels)},${level}})\\\\s$`),\n        type: this.type,\n        getAttributes: {\n          level,\n        },\n      })\n    })\n  },\n})\n"], "names": ["Node", "mergeAttributes", "textblockTypeInputRule"], "mappings": ";;;;;;EA0CA;;;EAGG;AACU,QAAA,OAAO,GAAGA,SAAI,CAAC,MAAM,CAAiB;EACjD,IAAA,IAAI,EAAE,SAAS;MAEf,UAAU,GAAA;UACR,OAAO;EACL,YAAA,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC1B,YAAA,cAAc,EAAE,EAAE;WACnB;OACF;EAED,IAAA,OAAO,EAAE,SAAS;EAElB,IAAA,KAAK,EAAE,OAAO;EAEd,IAAA,QAAQ,EAAE,IAAI;MAEd,aAAa,GAAA;UACX,OAAO;EACL,YAAA,KAAK,EAAE;EACL,gBAAA,OAAO,EAAE,CAAC;EACV,gBAAA,QAAQ,EAAE,KAAK;EAChB,aAAA;WACF;OACF;MAED,SAAS,GAAA;EACP,QAAA,OAAO,IAAI,CAAC,OAAO,CAAC;EACjB,aAAA,GAAG,CAAC,CAAC,KAAY,MAAM;cACtB,GAAG,EAAE,CAAI,CAAA,EAAA,KAAK,CAAE,CAAA;cAChB,KAAK,EAAE,EAAE,KAAK,EAAE;EACjB,SAAA,CAAC,CAAC;OACN;EAED,IAAA,UAAU,CAAC,EAAE,IAAI,EAAE,cAAc,EAAE,EAAA;EACjC,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;UAC/D,MAAM,KAAK,GAAG;EACZ,cAAE,IAAI,CAAC,KAAK,CAAC;gBACX,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;EAE1B,QAAA,OAAO,CAAC,CAAI,CAAA,EAAA,KAAK,EAAE,EAAEC,oBAAe,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC,EAAE,CAAC,CAAC;OACtF;MAED,WAAW,GAAA;UACT,OAAO;cACL,UAAU,EAAE,UAAU,IAAI,CAAC,EAAE,QAAQ,EAAE,KAAI;EACzC,gBAAA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;EACnD,oBAAA,OAAO,KAAK;;kBAGd,OAAO,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC;eAC/C;cACD,aAAa,EAAE,UAAU,IAAI,CAAC,EAAE,QAAQ,EAAE,KAAI;EAC5C,gBAAA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;EACnD,oBAAA,OAAO,KAAK;;EAGd,gBAAA,OAAO,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW,EAAE,UAAU,CAAC;eAC/D;WACF;OACF;MAED,oBAAoB,GAAA;EAClB,QAAA,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,KAAK,MAAM;EACnD,YAAA,GAAG,KAAK;cACR,GAAG;EACD,gBAAA,CAAC,WAAW,KAAK,CAAA,CAAE,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,KAAK,EAAE,CAAC;EAC1E,aAAA;WACF,CAAC,EAAE,EAAE,CAAC;OACR;MAED,aAAa,GAAA;UACX,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,IAAG;EACrC,YAAA,OAAOC,2BAAsB,CAAC;EAC5B,gBAAA,IAAI,EAAE,IAAI,MAAM,CAAC,CAAO,IAAA,EAAA,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAI,CAAA,EAAA,KAAK,QAAQ,CAAC;kBAC1E,IAAI,EAAE,IAAI,CAAC,IAAI;EACf,gBAAA,aAAa,EAAE;sBACb,KAAK;EACN,iBAAA;EACF,aAAA,CAAC;EACJ,SAAC,CAAC;OACH;EACF,CAAA;;;;;;;;;;;"}