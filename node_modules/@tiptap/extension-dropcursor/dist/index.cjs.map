{"version": 3, "file": "index.cjs", "sources": ["../src/dropcursor.ts"], "sourcesContent": ["import { Extension } from '@tiptap/core'\nimport { dropCursor } from '@tiptap/pm/dropcursor'\n\nexport interface DropcursorOptions {\n  /**\n   * The color of the drop cursor\n   * @default 'currentColor'\n   * @example 'red'\n   */\n  color: string | undefined,\n\n  /**\n   * The width of the drop cursor\n   * @default 1\n   * @example 2\n  */\n  width: number | undefined,\n\n  /**\n   * The class of the drop cursor\n   * @default undefined\n   * @example 'drop-cursor'\n  */\n  class: string | undefined,\n}\n\n/**\n * This extension allows you to add a drop cursor to your editor.\n * A drop cursor is a line that appears when you drag and drop content\n * inbetween nodes.\n * @see https://tiptap.dev/api/extensions/dropcursor\n */\nexport const Dropcursor = Extension.create<DropcursorOptions>({\n  name: 'dropCursor',\n\n  addOptions() {\n    return {\n      color: 'currentColor',\n      width: 1,\n      class: undefined,\n    }\n  },\n\n  addProseMirrorPlugins() {\n    return [\n      dropCursor(this.options),\n    ]\n  },\n})\n"], "names": ["Extension", "dropCursor"], "mappings": ";;;;;;;AA0BA;;;;;AAKG;AACU,MAAA,UAAU,GAAGA,cAAS,CAAC,MAAM,CAAoB;AAC5D,IAAA,IAAI,EAAE,YAAY;IAElB,UAAU,GAAA;QACR,OAAO;AACL,YAAA,KAAK,EAAE,cAAc;AACrB,YAAA,KAAK,EAAE,CAAC;AACR,YAAA,KAAK,EAAE,SAAS;SACjB;KACF;IAED,qBAAqB,GAAA;QACnB,OAAO;AACL,YAAAC,qBAAU,CAAC,IAAI,CAAC,OAAO,CAAC;SACzB;KACF;AACF,CAAA;;;;;"}