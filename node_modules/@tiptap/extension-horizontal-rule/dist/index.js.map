{"version": 3, "file": "index.js", "sources": ["../src/horizontal-rule.ts"], "sourcesContent": ["import {\n  isNodeSelection, mergeAttributes, Node, nodeInputRule,\n} from '@tiptap/core'\nimport { NodeSelection, TextSelection } from '@tiptap/pm/state'\n\nexport interface HorizontalRuleOptions {\n  /**\n   * The HTML attributes for a horizontal rule node.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    horizontalRule: {\n      /**\n       * Add a horizontal rule\n       * @example editor.commands.setHorizontalRule()\n       */\n      setHorizontalRule: () => ReturnType\n    }\n  }\n}\n\n/**\n * This extension allows you to insert horizontal rules.\n * @see https://www.tiptap.dev/api/nodes/horizontal-rule\n */\nexport const HorizontalRule = Node.create<HorizontalRuleOptions>({\n  name: 'horizontalRule',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  group: 'block',\n\n  parseHTML() {\n    return [{ tag: 'hr' }]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['hr', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes)]\n  },\n\n  addCommands() {\n    return {\n      setHorizontalRule:\n        () => ({ chain, state }) => {\n          const { selection } = state\n          const { $from: $originFrom, $to: $originTo } = selection\n\n          const currentChain = chain()\n\n          if ($originFrom.parentOffset === 0) {\n            currentChain.insertContentAt(\n              {\n                from: Math.max($originFrom.pos - 1, 0),\n                to: $originTo.pos,\n              },\n              {\n                type: this.name,\n              },\n            )\n          } else if (isNodeSelection(selection)) {\n            currentChain.insertContentAt($originTo.pos, {\n              type: this.name,\n            })\n          } else {\n            currentChain.insertContent({ type: this.name })\n          }\n\n          return (\n            currentChain\n              // set cursor after horizontal rule\n              .command(({ tr, dispatch }) => {\n                if (dispatch) {\n                  const { $to } = tr.selection\n                  const posAfter = $to.end()\n\n                  if ($to.nodeAfter) {\n                    if ($to.nodeAfter.isTextblock) {\n                      tr.setSelection(TextSelection.create(tr.doc, $to.pos + 1))\n                    } else if ($to.nodeAfter.isBlock) {\n                      tr.setSelection(NodeSelection.create(tr.doc, $to.pos))\n                    } else {\n                      tr.setSelection(TextSelection.create(tr.doc, $to.pos))\n                    }\n                  } else {\n                    // add node after horizontal rule if it’s the end of the document\n                    const node = $to.parent.type.contentMatch.defaultType?.create()\n\n                    if (node) {\n                      tr.insert(posAfter, node)\n                      tr.setSelection(TextSelection.create(tr.doc, posAfter + 1))\n                    }\n                  }\n\n                  tr.scrollIntoView()\n                }\n\n                return true\n              })\n              .run()\n          )\n        },\n    }\n  },\n\n  addInputRules() {\n    return [\n      nodeInputRule({\n        find: /^(?:---|—-|___\\s|\\*\\*\\*\\s)$/,\n        type: this.type,\n      }),\n    ]\n  },\n})\n"], "names": [], "mappings": ";;;AA0BA;;;AAGG;AACU,MAAA,cAAc,GAAG,IAAI,CAAC,MAAM,CAAwB;AAC/D,IAAA,IAAI,EAAE,gBAAgB;IAEtB,UAAU,GAAA;QACR,OAAO;AACL,YAAA,cAAc,EAAE,EAAE;SACnB;KACF;AAED,IAAA,KAAK,EAAE,OAAO;IAEd,SAAS,GAAA;AACP,QAAA,OAAO,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;KACvB;IAED,UAAU,CAAC,EAAE,cAAc,EAAE,EAAA;AAC3B,QAAA,OAAO,CAAC,IAAI,EAAE,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;KAC5E;IAED,WAAW,GAAA;QACT,OAAO;YACL,iBAAiB,EACf,MAAM,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,KAAI;AACzB,gBAAA,MAAM,EAAE,SAAS,EAAE,GAAG,KAAK;gBAC3B,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,SAAS;AAExD,gBAAA,MAAM,YAAY,GAAG,KAAK,EAAE;AAE5B,gBAAA,IAAI,WAAW,CAAC,YAAY,KAAK,CAAC,EAAE;oBAClC,YAAY,CAAC,eAAe,CAC1B;AACE,wBAAA,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;wBACtC,EAAE,EAAE,SAAS,CAAC,GAAG;qBAClB,EACD;wBACE,IAAI,EAAE,IAAI,CAAC,IAAI;AAChB,qBAAA,CACF;;AACI,qBAAA,IAAI,eAAe,CAAC,SAAS,CAAC,EAAE;AACrC,oBAAA,YAAY,CAAC,eAAe,CAAC,SAAS,CAAC,GAAG,EAAE;wBAC1C,IAAI,EAAE,IAAI,CAAC,IAAI;AAChB,qBAAA,CAAC;;qBACG;oBACL,YAAY,CAAC,aAAa,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC;;AAGjD,gBAAA,QACE;;qBAEG,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,KAAI;;oBAC5B,IAAI,QAAQ,EAAE;AACZ,wBAAA,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,SAAS;AAC5B,wBAAA,MAAM,QAAQ,GAAG,GAAG,CAAC,GAAG,EAAE;AAE1B,wBAAA,IAAI,GAAG,CAAC,SAAS,EAAE;AACjB,4BAAA,IAAI,GAAG,CAAC,SAAS,CAAC,WAAW,EAAE;AAC7B,gCAAA,EAAE,CAAC,YAAY,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;;AACrD,iCAAA,IAAI,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE;AAChC,gCAAA,EAAE,CAAC,YAAY,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;;iCACjD;AACL,gCAAA,EAAE,CAAC,YAAY,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;;;6BAEnD;;AAEL,4BAAA,MAAM,IAAI,GAAG,CAAA,EAAA,GAAA,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,MAAM,EAAE;4BAE/D,IAAI,IAAI,EAAE;AACR,gCAAA,EAAE,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC;AACzB,gCAAA,EAAE,CAAC,YAAY,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,QAAQ,GAAG,CAAC,CAAC,CAAC;;;wBAI/D,EAAE,CAAC,cAAc,EAAE;;AAGrB,oBAAA,OAAO,IAAI;AACb,iBAAC;qBACA,GAAG,EAAE;aAEX;SACJ;KACF;IAED,aAAa,GAAA;QACX,OAAO;AACL,YAAA,aAAa,CAAC;AACZ,gBAAA,IAAI,EAAE,6BAA6B;gBACnC,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB,CAAC;SACH;KACF;AACF,CAAA;;;;"}