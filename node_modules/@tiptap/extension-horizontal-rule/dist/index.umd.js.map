{"version": 3, "file": "index.umd.js", "sources": ["../src/horizontal-rule.ts"], "sourcesContent": ["import {\n  isNodeSelection, mergeAttributes, Node, nodeInputRule,\n} from '@tiptap/core'\nimport { NodeSelection, TextSelection } from '@tiptap/pm/state'\n\nexport interface HorizontalRuleOptions {\n  /**\n   * The HTML attributes for a horizontal rule node.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    horizontalRule: {\n      /**\n       * Add a horizontal rule\n       * @example editor.commands.setHorizontalRule()\n       */\n      setHorizontalRule: () => ReturnType\n    }\n  }\n}\n\n/**\n * This extension allows you to insert horizontal rules.\n * @see https://www.tiptap.dev/api/nodes/horizontal-rule\n */\nexport const HorizontalRule = Node.create<HorizontalRuleOptions>({\n  name: 'horizontalRule',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  group: 'block',\n\n  parseHTML() {\n    return [{ tag: 'hr' }]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['hr', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes)]\n  },\n\n  addCommands() {\n    return {\n      setHorizontalRule:\n        () => ({ chain, state }) => {\n          const { selection } = state\n          const { $from: $originFrom, $to: $originTo } = selection\n\n          const currentChain = chain()\n\n          if ($originFrom.parentOffset === 0) {\n            currentChain.insertContentAt(\n              {\n                from: Math.max($originFrom.pos - 1, 0),\n                to: $originTo.pos,\n              },\n              {\n                type: this.name,\n              },\n            )\n          } else if (isNodeSelection(selection)) {\n            currentChain.insertContentAt($originTo.pos, {\n              type: this.name,\n            })\n          } else {\n            currentChain.insertContent({ type: this.name })\n          }\n\n          return (\n            currentChain\n              // set cursor after horizontal rule\n              .command(({ tr, dispatch }) => {\n                if (dispatch) {\n                  const { $to } = tr.selection\n                  const posAfter = $to.end()\n\n                  if ($to.nodeAfter) {\n                    if ($to.nodeAfter.isTextblock) {\n                      tr.setSelection(TextSelection.create(tr.doc, $to.pos + 1))\n                    } else if ($to.nodeAfter.isBlock) {\n                      tr.setSelection(NodeSelection.create(tr.doc, $to.pos))\n                    } else {\n                      tr.setSelection(TextSelection.create(tr.doc, $to.pos))\n                    }\n                  } else {\n                    // add node after horizontal rule if it’s the end of the document\n                    const node = $to.parent.type.contentMatch.defaultType?.create()\n\n                    if (node) {\n                      tr.insert(posAfter, node)\n                      tr.setSelection(TextSelection.create(tr.doc, posAfter + 1))\n                    }\n                  }\n\n                  tr.scrollIntoView()\n                }\n\n                return true\n              })\n              .run()\n          )\n        },\n    }\n  },\n\n  addInputRules() {\n    return [\n      nodeInputRule({\n        find: /^(?:---|—-|___\\s|\\*\\*\\*\\s)$/,\n        type: this.type,\n      }),\n    ]\n  },\n})\n"], "names": ["Node", "mergeAttributes", "state", "isNodeSelection", "TextSelection", "NodeSelection", "nodeInputRule"], "mappings": ";;;;;;EA0BA;;;EAGG;AACU,QAAA,cAAc,GAAGA,SAAI,CAAC,MAAM,CAAwB;EAC/D,IAAA,IAAI,EAAE,gBAAgB;MAEtB,UAAU,GAAA;UACR,OAAO;EACL,YAAA,cAAc,EAAE,EAAE;WACnB;OACF;EAED,IAAA,KAAK,EAAE,OAAO;MAEd,SAAS,GAAA;EACP,QAAA,OAAO,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;OACvB;MAED,UAAU,CAAC,EAAE,cAAc,EAAE,EAAA;EAC3B,QAAA,OAAO,CAAC,IAAI,EAAEC,oBAAe,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;OAC5E;MAED,WAAW,GAAA;UACT,OAAO;cACL,iBAAiB,EACf,MAAM,CAAC,EAAE,KAAK,SAAEC,OAAK,EAAE,KAAI;EACzB,gBAAA,MAAM,EAAE,SAAS,EAAE,GAAGA,OAAK;kBAC3B,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,SAAS;EAExD,gBAAA,MAAM,YAAY,GAAG,KAAK,EAAE;EAE5B,gBAAA,IAAI,WAAW,CAAC,YAAY,KAAK,CAAC,EAAE;sBAClC,YAAY,CAAC,eAAe,CAC1B;EACE,wBAAA,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;0BACtC,EAAE,EAAE,SAAS,CAAC,GAAG;uBAClB,EACD;0BACE,IAAI,EAAE,IAAI,CAAC,IAAI;EAChB,qBAAA,CACF;;EACI,qBAAA,IAAIC,oBAAe,CAAC,SAAS,CAAC,EAAE;EACrC,oBAAA,YAAY,CAAC,eAAe,CAAC,SAAS,CAAC,GAAG,EAAE;0BAC1C,IAAI,EAAE,IAAI,CAAC,IAAI;EAChB,qBAAA,CAAC;;uBACG;sBACL,YAAY,CAAC,aAAa,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC;;EAGjD,gBAAA,QACE;;uBAEG,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,KAAI;;sBAC5B,IAAI,QAAQ,EAAE;EACZ,wBAAA,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,SAAS;EAC5B,wBAAA,MAAM,QAAQ,GAAG,GAAG,CAAC,GAAG,EAAE;EAE1B,wBAAA,IAAI,GAAG,CAAC,SAAS,EAAE;EACjB,4BAAA,IAAI,GAAG,CAAC,SAAS,CAAC,WAAW,EAAE;EAC7B,gCAAA,EAAE,CAAC,YAAY,CAACC,mBAAa,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;;EACrD,iCAAA,IAAI,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE;EAChC,gCAAA,EAAE,CAAC,YAAY,CAACC,mBAAa,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;;mCACjD;EACL,gCAAA,EAAE,CAAC,YAAY,CAACD,mBAAa,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;;;+BAEnD;;EAEL,4BAAA,MAAM,IAAI,GAAG,CAAA,EAAA,GAAA,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,MAAM,EAAE;8BAE/D,IAAI,IAAI,EAAE;EACR,gCAAA,EAAE,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC;EACzB,gCAAA,EAAE,CAAC,YAAY,CAACA,mBAAa,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,QAAQ,GAAG,CAAC,CAAC,CAAC;;;0BAI/D,EAAE,CAAC,cAAc,EAAE;;EAGrB,oBAAA,OAAO,IAAI;EACb,iBAAC;uBACA,GAAG,EAAE;eAEX;WACJ;OACF;MAED,aAAa,GAAA;UACX,OAAO;EACL,YAAAE,kBAAa,CAAC;EACZ,gBAAA,IAAI,EAAE,6BAA6B;kBACnC,IAAI,EAAE,IAAI,CAAC,IAAI;eAChB,CAAC;WACH;OACF;EACF,CAAA;;;;;;;;;;;"}