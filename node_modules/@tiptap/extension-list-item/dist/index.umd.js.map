{"version": 3, "file": "index.umd.js", "sources": ["../src/list-item.ts"], "sourcesContent": ["import { mergeAttributes, Node } from '@tiptap/core'\n\nexport interface ListItemOptions {\n  /**\n   * The HTML attributes for a list item node.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n\n  /**\n   * The node type for bulletList nodes\n   * @default 'bulletList'\n   * @example 'myCustomBulletList'\n   */\n  bulletListTypeName: string\n\n  /**\n   * The node type for orderedList nodes\n   * @default 'orderedList'\n   * @example 'myCustomOrderedList'\n   */\n  orderedListTypeName: string\n}\n\n/**\n * This extension allows you to create list items.\n * @see https://www.tiptap.dev/api/nodes/list-item\n */\nexport const ListItem = Node.create<ListItemOptions>({\n  name: 'listItem',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n      bulletListTypeName: 'bulletList',\n      orderedListTypeName: 'orderedList',\n    }\n  },\n\n  content: 'paragraph block*',\n\n  defining: true,\n\n  parseHTML() {\n    return [\n      {\n        tag: 'li',\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['li', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      Enter: () => this.editor.commands.splitListItem(this.name),\n      Tab: () => this.editor.commands.sinkListItem(this.name),\n      'Shift-Tab': () => this.editor.commands.liftListItem(this.name),\n    }\n  },\n})\n"], "names": ["Node", "mergeAttributes"], "mappings": ";;;;;;EAyBA;;;EAGG;AACU,QAAA,QAAQ,GAAGA,SAAI,CAAC,MAAM,CAAkB;EACnD,IAAA,IAAI,EAAE,UAAU;MAEhB,UAAU,GAAA;UACR,OAAO;EACL,YAAA,cAAc,EAAE,EAAE;EAClB,YAAA,kBAAkB,EAAE,YAAY;EAChC,YAAA,mBAAmB,EAAE,aAAa;WACnC;OACF;EAED,IAAA,OAAO,EAAE,kBAAkB;EAE3B,IAAA,QAAQ,EAAE,IAAI;MAEd,SAAS,GAAA;UACP,OAAO;EACL,YAAA;EACE,gBAAA,GAAG,EAAE,IAAI;EACV,aAAA;WACF;OACF;MAED,UAAU,CAAC,EAAE,cAAc,EAAE,EAAA;EAC3B,QAAA,OAAO,CAAC,IAAI,EAAEC,oBAAe,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC,EAAE,CAAC,CAAC;OAC/E;MAED,oBAAoB,GAAA;UAClB,OAAO;EACL,YAAA,KAAK,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC;EAC1D,YAAA,GAAG,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;EACvD,YAAA,WAAW,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;WAChE;OACF;EACF,CAAA;;;;;;;;;;;"}