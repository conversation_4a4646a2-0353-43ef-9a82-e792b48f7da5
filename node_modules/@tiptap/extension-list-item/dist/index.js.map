{"version": 3, "file": "index.js", "sources": ["../src/list-item.ts"], "sourcesContent": ["import { mergeAttributes, Node } from '@tiptap/core'\n\nexport interface ListItemOptions {\n  /**\n   * The HTML attributes for a list item node.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n\n  /**\n   * The node type for bulletList nodes\n   * @default 'bulletList'\n   * @example 'myCustomBulletList'\n   */\n  bulletListTypeName: string\n\n  /**\n   * The node type for orderedList nodes\n   * @default 'orderedList'\n   * @example 'myCustomOrderedList'\n   */\n  orderedListTypeName: string\n}\n\n/**\n * This extension allows you to create list items.\n * @see https://www.tiptap.dev/api/nodes/list-item\n */\nexport const ListItem = Node.create<ListItemOptions>({\n  name: 'listItem',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n      bulletListTypeName: 'bulletList',\n      orderedListTypeName: 'orderedList',\n    }\n  },\n\n  content: 'paragraph block*',\n\n  defining: true,\n\n  parseHTML() {\n    return [\n      {\n        tag: 'li',\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['li', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      Enter: () => this.editor.commands.splitListItem(this.name),\n      Tab: () => this.editor.commands.sinkListItem(this.name),\n      'Shift-Tab': () => this.editor.commands.liftListItem(this.name),\n    }\n  },\n})\n"], "names": [], "mappings": ";;AAyBA;;;AAGG;AACU,MAAA,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAkB;AACnD,IAAA,IAAI,EAAE,UAAU;IAEhB,UAAU,GAAA;QACR,OAAO;AACL,YAAA,cAAc,EAAE,EAAE;AAClB,YAAA,kBAAkB,EAAE,YAAY;AAChC,YAAA,mBAAmB,EAAE,aAAa;SACnC;KACF;AAED,IAAA,OAAO,EAAE,kBAAkB;AAE3B,IAAA,QAAQ,EAAE,IAAI;IAEd,SAAS,GAAA;QACP,OAAO;AACL,YAAA;AACE,gBAAA,GAAG,EAAE,IAAI;AACV,aAAA;SACF;KACF;IAED,UAAU,CAAC,EAAE,cAAc,EAAE,EAAA;AAC3B,QAAA,OAAO,CAAC,IAAI,EAAE,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC,EAAE,CAAC,CAAC;KAC/E;IAED,oBAAoB,GAAA;QAClB,OAAO;AACL,YAAA,KAAK,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC;AAC1D,YAAA,GAAG,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;AACvD,YAAA,WAAW,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;SAChE;KACF;AACF,CAAA;;;;"}