{"version": 3, "file": "index.umd.js", "sources": ["../src/paragraph.ts"], "sourcesContent": ["import { mergeAttributes, Node } from '@tiptap/core'\n\nexport interface ParagraphOptions {\n  /**\n   * The HTML attributes for a paragraph node.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    paragraph: {\n      /**\n       * Toggle a paragraph\n       * @example editor.commands.toggleParagraph()\n       */\n      setParagraph: () => ReturnType,\n    }\n  }\n}\n\n/**\n * This extension allows you to create paragraphs.\n * @see https://www.tiptap.dev/api/nodes/paragraph\n */\nexport const Paragraph = Node.create<ParagraphOptions>({\n  name: 'paragraph',\n\n  priority: 1000,\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  group: 'block',\n\n  content: 'inline*',\n\n  parseHTML() {\n    return [\n      { tag: 'p' },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['p', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setParagraph: () => ({ commands }) => {\n        return commands.setNode(this.name)\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-Alt-0': () => this.editor.commands.setParagraph(),\n    }\n  },\n})\n"], "names": ["Node", "mergeAttributes"], "mappings": ";;;;;;EAuBA;;;EAGG;AACU,QAAA,SAAS,GAAGA,SAAI,CAAC,MAAM,CAAmB;EACrD,IAAA,IAAI,EAAE,WAAW;EAEjB,IAAA,QAAQ,EAAE,IAAI;MAEd,UAAU,GAAA;UACR,OAAO;EACL,YAAA,cAAc,EAAE,EAAE;WACnB;OACF;EAED,IAAA,KAAK,EAAE,OAAO;EAEd,IAAA,OAAO,EAAE,SAAS;MAElB,SAAS,GAAA;UACP,OAAO;cACL,EAAE,GAAG,EAAE,GAAG,EAAE;WACb;OACF;MAED,UAAU,CAAC,EAAE,cAAc,EAAE,EAAA;EAC3B,QAAA,OAAO,CAAC,GAAG,EAAEC,oBAAe,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC,EAAE,CAAC,CAAC;OAC9E;MAED,WAAW,GAAA;UACT,OAAO;cACL,YAAY,EAAE,MAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;kBACnC,OAAO,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;eACnC;WACF;OACF;MAED,oBAAoB,GAAA;UAClB,OAAO;cACL,WAAW,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,EAAE;WACvD;OACF;EACF,CAAA;;;;;;;;;;;"}