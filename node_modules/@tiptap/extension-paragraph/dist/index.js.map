{"version": 3, "file": "index.js", "sources": ["../src/paragraph.ts"], "sourcesContent": ["import { mergeAttributes, Node } from '@tiptap/core'\n\nexport interface ParagraphOptions {\n  /**\n   * The HTML attributes for a paragraph node.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    paragraph: {\n      /**\n       * Toggle a paragraph\n       * @example editor.commands.toggleParagraph()\n       */\n      setParagraph: () => ReturnType,\n    }\n  }\n}\n\n/**\n * This extension allows you to create paragraphs.\n * @see https://www.tiptap.dev/api/nodes/paragraph\n */\nexport const Paragraph = Node.create<ParagraphOptions>({\n  name: 'paragraph',\n\n  priority: 1000,\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  group: 'block',\n\n  content: 'inline*',\n\n  parseHTML() {\n    return [\n      { tag: 'p' },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['p', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setParagraph: () => ({ commands }) => {\n        return commands.setNode(this.name)\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-Alt-0': () => this.editor.commands.setParagraph(),\n    }\n  },\n})\n"], "names": [], "mappings": ";;AAuBA;;;AAGG;AACU,MAAA,SAAS,GAAG,IAAI,CAAC,MAAM,CAAmB;AACrD,IAAA,IAAI,EAAE,WAAW;AAEjB,IAAA,QAAQ,EAAE,IAAI;IAEd,UAAU,GAAA;QACR,OAAO;AACL,YAAA,cAAc,EAAE,EAAE;SACnB;KACF;AAED,IAAA,KAAK,EAAE,OAAO;AAEd,IAAA,OAAO,EAAE,SAAS;IAElB,SAAS,GAAA;QACP,OAAO;YACL,EAAE,GAAG,EAAE,GAAG,EAAE;SACb;KACF;IAED,UAAU,CAAC,EAAE,cAAc,EAAE,EAAA;AAC3B,QAAA,OAAO,CAAC,GAAG,EAAE,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC,EAAE,CAAC,CAAC;KAC9E;IAED,WAAW,GAAA;QACT,OAAO;YACL,YAAY,EAAE,MAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;gBACnC,OAAO,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;aACnC;SACF;KACF;IAED,oBAAoB,GAAA;QAClB,OAAO;YACL,WAAW,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,EAAE;SACvD;KACF;AACF,CAAA;;;;"}