{"version": 3, "file": "index.cjs", "sources": ["../src/hard-break.ts"], "sourcesContent": ["import { mergeAttributes, Node } from '@tiptap/core'\n\nexport interface HardBreakOptions {\n  /**\n   * Controls if marks should be kept after being split by a hard break.\n   * @default true\n   * @example false\n   */\n  keepMarks: boolean,\n\n  /**\n   * HTML attributes to add to the hard break element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    hardBreak: {\n      /**\n       * Add a hard break\n       * @example editor.commands.setHardBreak()\n       */\n      setHardBreak: () => ReturnType,\n    }\n  }\n}\n\n/**\n * This extension allows you to insert hard breaks.\n * @see https://www.tiptap.dev/api/nodes/hard-break\n */\nexport const HardBreak = Node.create<HardBreakOptions>({\n  name: 'hardBreak',\n\n  addOptions() {\n    return {\n      keepMarks: true,\n      HTMLAttributes: {},\n    }\n  },\n\n  inline: true,\n\n  group: 'inline',\n\n  selectable: false,\n\n  linebreakReplacement: true,\n\n  parseHTML() {\n    return [\n      { tag: 'br' },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['br', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes)]\n  },\n\n  renderText() {\n    return '\\n'\n  },\n\n  addCommands() {\n    return {\n      setHardBreak: () => ({\n        commands,\n        chain,\n        state,\n        editor,\n      }) => {\n        return commands.first([\n          () => commands.exitCode(),\n          () => commands.command(() => {\n            const { selection, storedMarks } = state\n\n            if (selection.$from.parent.type.spec.isolating) {\n              return false\n            }\n\n            const { keepMarks } = this.options\n            const { splittableMarks } = editor.extensionManager\n            const marks = storedMarks\n              || (selection.$to.parentOffset && selection.$from.marks())\n\n            return chain()\n              .insertContent({ type: this.name })\n              .command(({ tr, dispatch }) => {\n                if (dispatch && marks && keepMarks) {\n                  const filteredMarks = marks\n                    .filter(mark => splittableMarks.includes(mark.type.name))\n\n                  tr.ensureMarks(filteredMarks)\n                }\n\n                return true\n              })\n              .run()\n          }),\n        ])\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-Enter': () => this.editor.commands.setHardBreak(),\n      'Shift-Enter': () => this.editor.commands.setHardBreak(),\n    }\n  },\n})\n"], "names": ["Node", "mergeAttributes"], "mappings": ";;;;;;AA8BA;;;AAGG;AACU,MAAA,SAAS,GAAGA,SAAI,CAAC,MAAM,CAAmB;AACrD,IAAA,IAAI,EAAE,WAAW;IAEjB,UAAU,GAAA;QACR,OAAO;AACL,YAAA,SAAS,EAAE,IAAI;AACf,YAAA,cAAc,EAAE,EAAE;SACnB;KACF;AAED,IAAA,MAAM,EAAE,IAAI;AAEZ,IAAA,KAAK,EAAE,QAAQ;AAEf,IAAA,UAAU,EAAE,KAAK;AAEjB,IAAA,oBAAoB,EAAE,IAAI;IAE1B,SAAS,GAAA;QACP,OAAO;YACL,EAAE,GAAG,EAAE,IAAI,EAAE;SACd;KACF;IAED,UAAU,CAAC,EAAE,cAAc,EAAE,EAAA;AAC3B,QAAA,OAAO,CAAC,IAAI,EAAEC,oBAAe,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;KAC5E;IAED,UAAU,GAAA;AACR,QAAA,OAAO,IAAI;KACZ;IAED,WAAW,GAAA;QACT,OAAO;AACL,YAAA,YAAY,EAAE,MAAM,CAAC,EACnB,QAAQ,EACR,KAAK,EACL,KAAK,EACL,MAAM,GACP,KAAI;gBACH,OAAO,QAAQ,CAAC,KAAK,CAAC;AACpB,oBAAA,MAAM,QAAQ,CAAC,QAAQ,EAAE;AACzB,oBAAA,MAAM,QAAQ,CAAC,OAAO,CAAC,MAAK;AAC1B,wBAAA,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,KAAK;AAExC,wBAAA,IAAI,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;AAC9C,4BAAA,OAAO,KAAK;;AAGd,wBAAA,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,OAAO;AAClC,wBAAA,MAAM,EAAE,eAAe,EAAE,GAAG,MAAM,CAAC,gBAAgB;wBACnD,MAAM,KAAK,GAAG;AACT,gCAAC,SAAS,CAAC,GAAG,CAAC,YAAY,IAAI,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;AAE5D,wBAAA,OAAO,KAAK;6BACT,aAAa,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE;6BACjC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,KAAI;AAC5B,4BAAA,IAAI,QAAQ,IAAI,KAAK,IAAI,SAAS,EAAE;gCAClC,MAAM,aAAa,GAAG;AACnB,qCAAA,MAAM,CAAC,IAAI,IAAI,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAE3D,gCAAA,EAAE,CAAC,WAAW,CAAC,aAAa,CAAC;;AAG/B,4BAAA,OAAO,IAAI;AACb,yBAAC;AACA,6BAAA,GAAG,EAAE;AACV,qBAAC,CAAC;AACH,iBAAA,CAAC;aACH;SACF;KACF;IAED,oBAAoB,GAAA;QAClB,OAAO;YACL,WAAW,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,EAAE;YACtD,aAAa,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,EAAE;SACzD;KACF;AACF,CAAA;;;;;"}