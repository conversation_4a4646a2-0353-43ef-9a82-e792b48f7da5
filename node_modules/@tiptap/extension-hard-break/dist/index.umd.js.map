{"version": 3, "file": "index.umd.js", "sources": ["../src/hard-break.ts"], "sourcesContent": ["import { mergeAttributes, Node } from '@tiptap/core'\n\nexport interface HardBreakOptions {\n  /**\n   * Controls if marks should be kept after being split by a hard break.\n   * @default true\n   * @example false\n   */\n  keepMarks: boolean,\n\n  /**\n   * HTML attributes to add to the hard break element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    hardBreak: {\n      /**\n       * Add a hard break\n       * @example editor.commands.setHardBreak()\n       */\n      setHardBreak: () => ReturnType,\n    }\n  }\n}\n\n/**\n * This extension allows you to insert hard breaks.\n * @see https://www.tiptap.dev/api/nodes/hard-break\n */\nexport const HardBreak = Node.create<HardBreakOptions>({\n  name: 'hardBreak',\n\n  addOptions() {\n    return {\n      keepMarks: true,\n      HTMLAttributes: {},\n    }\n  },\n\n  inline: true,\n\n  group: 'inline',\n\n  selectable: false,\n\n  linebreakReplacement: true,\n\n  parseHTML() {\n    return [\n      { tag: 'br' },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['br', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes)]\n  },\n\n  renderText() {\n    return '\\n'\n  },\n\n  addCommands() {\n    return {\n      setHardBreak: () => ({\n        commands,\n        chain,\n        state,\n        editor,\n      }) => {\n        return commands.first([\n          () => commands.exitCode(),\n          () => commands.command(() => {\n            const { selection, storedMarks } = state\n\n            if (selection.$from.parent.type.spec.isolating) {\n              return false\n            }\n\n            const { keepMarks } = this.options\n            const { splittableMarks } = editor.extensionManager\n            const marks = storedMarks\n              || (selection.$to.parentOffset && selection.$from.marks())\n\n            return chain()\n              .insertContent({ type: this.name })\n              .command(({ tr, dispatch }) => {\n                if (dispatch && marks && keepMarks) {\n                  const filteredMarks = marks\n                    .filter(mark => splittableMarks.includes(mark.type.name))\n\n                  tr.ensureMarks(filteredMarks)\n                }\n\n                return true\n              })\n              .run()\n          }),\n        ])\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-Enter': () => this.editor.commands.setHardBreak(),\n      'Shift-Enter': () => this.editor.commands.setHardBreak(),\n    }\n  },\n})\n"], "names": ["Node", "mergeAttributes"], "mappings": ";;;;;;EA8BA;;;EAGG;AACU,QAAA,SAAS,GAAGA,SAAI,CAAC,MAAM,CAAmB;EACrD,IAAA,IAAI,EAAE,WAAW;MAEjB,UAAU,GAAA;UACR,OAAO;EACL,YAAA,SAAS,EAAE,IAAI;EACf,YAAA,cAAc,EAAE,EAAE;WACnB;OACF;EAED,IAAA,MAAM,EAAE,IAAI;EAEZ,IAAA,KAAK,EAAE,QAAQ;EAEf,IAAA,UAAU,EAAE,KAAK;EAEjB,IAAA,oBAAoB,EAAE,IAAI;MAE1B,SAAS,GAAA;UACP,OAAO;cACL,EAAE,GAAG,EAAE,IAAI,EAAE;WACd;OACF;MAED,UAAU,CAAC,EAAE,cAAc,EAAE,EAAA;EAC3B,QAAA,OAAO,CAAC,IAAI,EAAEC,oBAAe,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;OAC5E;MAED,UAAU,GAAA;EACR,QAAA,OAAO,IAAI;OACZ;MAED,WAAW,GAAA;UACT,OAAO;EACL,YAAA,YAAY,EAAE,MAAM,CAAC,EACnB,QAAQ,EACR,KAAK,EACL,KAAK,EACL,MAAM,GACP,KAAI;kBACH,OAAO,QAAQ,CAAC,KAAK,CAAC;EACpB,oBAAA,MAAM,QAAQ,CAAC,QAAQ,EAAE;EACzB,oBAAA,MAAM,QAAQ,CAAC,OAAO,CAAC,MAAK;EAC1B,wBAAA,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,KAAK;EAExC,wBAAA,IAAI,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;EAC9C,4BAAA,OAAO,KAAK;;EAGd,wBAAA,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,OAAO;EAClC,wBAAA,MAAM,EAAE,eAAe,EAAE,GAAG,MAAM,CAAC,gBAAgB;0BACnD,MAAM,KAAK,GAAG;EACT,gCAAC,SAAS,CAAC,GAAG,CAAC,YAAY,IAAI,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;EAE5D,wBAAA,OAAO,KAAK;+BACT,aAAa,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE;+BACjC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,KAAI;EAC5B,4BAAA,IAAI,QAAQ,IAAI,KAAK,IAAI,SAAS,EAAE;kCAClC,MAAM,aAAa,GAAG;EACnB,qCAAA,MAAM,CAAC,IAAI,IAAI,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;EAE3D,gCAAA,EAAE,CAAC,WAAW,CAAC,aAAa,CAAC;;EAG/B,4BAAA,OAAO,IAAI;EACb,yBAAC;EACA,6BAAA,GAAG,EAAE;EACV,qBAAC,CAAC;EACH,iBAAA,CAAC;eACH;WACF;OACF;MAED,oBAAoB,GAAA;UAClB,OAAO;cACL,WAAW,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,EAAE;cACtD,aAAa,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,EAAE;WACzD;OACF;EACF,CAAA;;;;;;;;;;;"}