{"version": 3, "file": "index.umd.js", "sources": ["../src/text-align.ts"], "sourcesContent": ["import { Extension } from '@tiptap/core'\n\nexport interface TextAlignOptions {\n  /**\n   * The types where the text align attribute can be applied.\n   * @default []\n   * @example ['heading', 'paragraph']\n   */\n  types: string[],\n\n  /**\n   * The alignments which are allowed.\n   * @default ['left', 'center', 'right', 'justify']\n   * @example ['left', 'right']\n   */\n  alignments: string[],\n\n  /**\n   * The default alignment.\n   * @default null\n   * @example 'center'\n   */\n  defaultAlignment: string | null,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    textAlign: {\n      /**\n       * Set the text align attribute\n       * @param alignment The alignment\n       * @example editor.commands.setTextAlign('left')\n       */\n      setTextAlign: (alignment: string) => ReturnType,\n      /**\n       * Unset the text align attribute\n       * @example editor.commands.unsetTextAlign()\n       */\n      unsetTextAlign: () => ReturnType,\n      /**\n       * Toggle the text align attribute\n       * @param alignment The alignment\n       * @example editor.commands.toggleTextAlign('right')\n       */\n      toggleTextAlign: (alignment: string) => ReturnType,\n    }\n  }\n}\n\n/**\n * This extension allows you to align text.\n * @see https://www.tiptap.dev/api/extensions/text-align\n */\nexport const TextAlign = Extension.create<TextAlignOptions>({\n  name: 'textAlign',\n\n  addOptions() {\n    return {\n      types: [],\n      alignments: ['left', 'center', 'right', 'justify'],\n      defaultAlignment: null,\n    }\n  },\n\n  addGlobalAttributes() {\n    return [\n      {\n        types: this.options.types,\n        attributes: {\n          textAlign: {\n            default: this.options.defaultAlignment,\n            parseHTML: element => {\n              const alignment = element.style.textAlign\n\n              return this.options.alignments.includes(alignment) ? alignment : this.options.defaultAlignment\n            },\n            renderHTML: attributes => {\n              if (!attributes.textAlign) {\n                return {}\n              }\n\n              return { style: `text-align: ${attributes.textAlign}` }\n            },\n          },\n        },\n      },\n    ]\n  },\n\n  addCommands() {\n    return {\n      setTextAlign: (alignment: string) => ({ commands }) => {\n        if (!this.options.alignments.includes(alignment)) {\n          return false\n        }\n\n        return this.options.types\n          .map(type => commands.updateAttributes(type, { textAlign: alignment }))\n          .every(response => response)\n      },\n\n      unsetTextAlign: () => ({ commands }) => {\n        return this.options.types\n          .map(type => commands.resetAttributes(type, 'textAlign'))\n          .every(response => response)\n      },\n\n      toggleTextAlign: alignment => ({ editor, commands }) => {\n        if (!this.options.alignments.includes(alignment)) {\n          return false\n        }\n\n        if (editor.isActive({ textAlign: alignment })) {\n          return commands.unsetTextAlign()\n        }\n        return commands.setTextAlign(alignment)\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-Shift-l': () => this.editor.commands.setTextAlign('left'),\n      'Mod-Shift-e': () => this.editor.commands.setTextAlign('center'),\n      'Mod-Shift-r': () => this.editor.commands.setTextAlign('right'),\n      'Mod-Shift-j': () => this.editor.commands.setTextAlign('justify'),\n    }\n  },\n})\n"], "names": ["Extension"], "mappings": ";;;;;;EAiDA;;;EAGG;AACU,QAAA,SAAS,GAAGA,cAAS,CAAC,MAAM,CAAmB;EAC1D,IAAA,IAAI,EAAE,WAAW;MAEjB,UAAU,GAAA;UACR,OAAO;EACL,YAAA,KAAK,EAAE,EAAE;cACT,UAAU,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC;EAClD,YAAA,gBAAgB,EAAE,IAAI;WACvB;OACF;MAED,mBAAmB,GAAA;UACjB,OAAO;EACL,YAAA;EACE,gBAAA,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK;EACzB,gBAAA,UAAU,EAAE;EACV,oBAAA,SAAS,EAAE;EACT,wBAAA,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB;0BACtC,SAAS,EAAE,OAAO,IAAG;EACnB,4BAAA,MAAM,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC,SAAS;8BAEzC,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB;2BAC/F;0BACD,UAAU,EAAE,UAAU,IAAG;EACvB,4BAAA,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE;EACzB,gCAAA,OAAO,EAAE;;8BAGX,OAAO,EAAE,KAAK,EAAE,CAAA,YAAA,EAAe,UAAU,CAAC,SAAS,CAAE,CAAA,EAAE;2BACxD;EACF,qBAAA;EACF,iBAAA;EACF,aAAA;WACF;OACF;MAED,WAAW,GAAA;UACT,OAAO;cACL,YAAY,EAAE,CAAC,SAAiB,KAAK,CAAC,EAAE,QAAQ,EAAE,KAAI;EACpD,gBAAA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;EAChD,oBAAA,OAAO,KAAK;;EAGd,gBAAA,OAAO,IAAI,CAAC,OAAO,CAAC;EACjB,qBAAA,GAAG,CAAC,IAAI,IAAI,QAAQ,CAAC,gBAAgB,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC;EACrE,qBAAA,KAAK,CAAC,QAAQ,IAAI,QAAQ,CAAC;eAC/B;cAED,cAAc,EAAE,MAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;EACrC,gBAAA,OAAO,IAAI,CAAC,OAAO,CAAC;EACjB,qBAAA,GAAG,CAAC,IAAI,IAAI,QAAQ,CAAC,eAAe,CAAC,IAAI,EAAE,WAAW,CAAC;EACvD,qBAAA,KAAK,CAAC,QAAQ,IAAI,QAAQ,CAAC;eAC/B;EAED,YAAA,eAAe,EAAE,SAAS,IAAI,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAI;EACrD,gBAAA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;EAChD,oBAAA,OAAO,KAAK;;kBAGd,IAAI,MAAM,CAAC,QAAQ,CAAC,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC,EAAE;EAC7C,oBAAA,OAAO,QAAQ,CAAC,cAAc,EAAE;;EAElC,gBAAA,OAAO,QAAQ,CAAC,YAAY,CAAC,SAAS,CAAC;eACxC;WACF;OACF;MAED,oBAAoB,GAAA;UAClB,OAAO;EACL,YAAA,aAAa,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,MAAM,CAAC;EAC9D,YAAA,aAAa,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,QAAQ,CAAC;EAChE,YAAA,aAAa,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,OAAO,CAAC;EAC/D,YAAA,aAAa,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,SAAS,CAAC;WAClE;OACF;EACF,CAAA;;;;;;;;;;;"}