{"version": 3, "file": "index.cjs", "sources": ["../src/text-align.ts"], "sourcesContent": ["import { Extension } from '@tiptap/core'\n\nexport interface TextAlignOptions {\n  /**\n   * The types where the text align attribute can be applied.\n   * @default []\n   * @example ['heading', 'paragraph']\n   */\n  types: string[],\n\n  /**\n   * The alignments which are allowed.\n   * @default ['left', 'center', 'right', 'justify']\n   * @example ['left', 'right']\n   */\n  alignments: string[],\n\n  /**\n   * The default alignment.\n   * @default null\n   * @example 'center'\n   */\n  defaultAlignment: string | null,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    textAlign: {\n      /**\n       * Set the text align attribute\n       * @param alignment The alignment\n       * @example editor.commands.setTextAlign('left')\n       */\n      setTextAlign: (alignment: string) => ReturnType,\n      /**\n       * Unset the text align attribute\n       * @example editor.commands.unsetTextAlign()\n       */\n      unsetTextAlign: () => ReturnType,\n      /**\n       * Toggle the text align attribute\n       * @param alignment The alignment\n       * @example editor.commands.toggleTextAlign('right')\n       */\n      toggleTextAlign: (alignment: string) => ReturnType,\n    }\n  }\n}\n\n/**\n * This extension allows you to align text.\n * @see https://www.tiptap.dev/api/extensions/text-align\n */\nexport const TextAlign = Extension.create<TextAlignOptions>({\n  name: 'textAlign',\n\n  addOptions() {\n    return {\n      types: [],\n      alignments: ['left', 'center', 'right', 'justify'],\n      defaultAlignment: null,\n    }\n  },\n\n  addGlobalAttributes() {\n    return [\n      {\n        types: this.options.types,\n        attributes: {\n          textAlign: {\n            default: this.options.defaultAlignment,\n            parseHTML: element => {\n              const alignment = element.style.textAlign\n\n              return this.options.alignments.includes(alignment) ? alignment : this.options.defaultAlignment\n            },\n            renderHTML: attributes => {\n              if (!attributes.textAlign) {\n                return {}\n              }\n\n              return { style: `text-align: ${attributes.textAlign}` }\n            },\n          },\n        },\n      },\n    ]\n  },\n\n  addCommands() {\n    return {\n      setTextAlign: (alignment: string) => ({ commands }) => {\n        if (!this.options.alignments.includes(alignment)) {\n          return false\n        }\n\n        return this.options.types\n          .map(type => commands.updateAttributes(type, { textAlign: alignment }))\n          .every(response => response)\n      },\n\n      unsetTextAlign: () => ({ commands }) => {\n        return this.options.types\n          .map(type => commands.resetAttributes(type, 'textAlign'))\n          .every(response => response)\n      },\n\n      toggleTextAlign: alignment => ({ editor, commands }) => {\n        if (!this.options.alignments.includes(alignment)) {\n          return false\n        }\n\n        if (editor.isActive({ textAlign: alignment })) {\n          return commands.unsetTextAlign()\n        }\n        return commands.setTextAlign(alignment)\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-Shift-l': () => this.editor.commands.setTextAlign('left'),\n      'Mod-Shift-e': () => this.editor.commands.setTextAlign('center'),\n      'Mod-Shift-r': () => this.editor.commands.setTextAlign('right'),\n      'Mod-Shift-j': () => this.editor.commands.setTextAlign('justify'),\n    }\n  },\n})\n"], "names": ["Extension"], "mappings": ";;;;;;AAiDA;;;AAGG;AACU,MAAA,SAAS,GAAGA,cAAS,CAAC,MAAM,CAAmB;AAC1D,IAAA,IAAI,EAAE,WAAW;IAEjB,UAAU,GAAA;QACR,OAAO;AACL,YAAA,KAAK,EAAE,EAAE;YACT,UAAU,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC;AAClD,YAAA,gBAAgB,EAAE,IAAI;SACvB;KACF;IAED,mBAAmB,GAAA;QACjB,OAAO;AACL,YAAA;AACE,gBAAA,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK;AACzB,gBAAA,UAAU,EAAE;AACV,oBAAA,SAAS,EAAE;AACT,wBAAA,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB;wBACtC,SAAS,EAAE,OAAO,IAAG;AACnB,4BAAA,MAAM,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC,SAAS;4BAEzC,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB;yBAC/F;wBACD,UAAU,EAAE,UAAU,IAAG;AACvB,4BAAA,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE;AACzB,gCAAA,OAAO,EAAE;;4BAGX,OAAO,EAAE,KAAK,EAAE,CAAA,YAAA,EAAe,UAAU,CAAC,SAAS,CAAE,CAAA,EAAE;yBACxD;AACF,qBAAA;AACF,iBAAA;AACF,aAAA;SACF;KACF;IAED,WAAW,GAAA;QACT,OAAO;YACL,YAAY,EAAE,CAAC,SAAiB,KAAK,CAAC,EAAE,QAAQ,EAAE,KAAI;AACpD,gBAAA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;AAChD,oBAAA,OAAO,KAAK;;AAGd,gBAAA,OAAO,IAAI,CAAC,OAAO,CAAC;AACjB,qBAAA,GAAG,CAAC,IAAI,IAAI,QAAQ,CAAC,gBAAgB,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC;AACrE,qBAAA,KAAK,CAAC,QAAQ,IAAI,QAAQ,CAAC;aAC/B;YAED,cAAc,EAAE,MAAM,CAAC,EAAE,QAAQ,EAAE,KAAI;AACrC,gBAAA,OAAO,IAAI,CAAC,OAAO,CAAC;AACjB,qBAAA,GAAG,CAAC,IAAI,IAAI,QAAQ,CAAC,eAAe,CAAC,IAAI,EAAE,WAAW,CAAC;AACvD,qBAAA,KAAK,CAAC,QAAQ,IAAI,QAAQ,CAAC;aAC/B;AAED,YAAA,eAAe,EAAE,SAAS,IAAI,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAI;AACrD,gBAAA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;AAChD,oBAAA,OAAO,KAAK;;gBAGd,IAAI,MAAM,CAAC,QAAQ,CAAC,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC,EAAE;AAC7C,oBAAA,OAAO,QAAQ,CAAC,cAAc,EAAE;;AAElC,gBAAA,OAAO,QAAQ,CAAC,YAAY,CAAC,SAAS,CAAC;aACxC;SACF;KACF;IAED,oBAAoB,GAAA;QAClB,OAAO;AACL,YAAA,aAAa,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,MAAM,CAAC;AAC9D,YAAA,aAAa,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,QAAQ,CAAC;AAChE,YAAA,aAAa,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,OAAO,CAAC;AAC/D,YAAA,aAAa,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,SAAS,CAAC;SAClE;KACF;AACF,CAAA;;;;;"}