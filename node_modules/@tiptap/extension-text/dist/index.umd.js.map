{"version": 3, "file": "index.umd.js", "sources": ["../src/text.ts"], "sourcesContent": ["import { Node } from '@tiptap/core'\n\n/**\n * This extension allows you to create text nodes.\n * @see https://www.tiptap.dev/api/nodes/text\n */\nexport const Text = Node.create({\n  name: 'text',\n  group: 'inline',\n})\n"], "names": ["Node"], "mappings": ";;;;;;EAEA;;;EAGG;AACU,QAAA,IAAI,GAAGA,SAAI,CAAC,MAAM,CAAC;EAC9B,IAAA,IAAI,EAAE,MAAM;EACZ,IAAA,KAAK,EAAE,QAAQ;EAChB,CAAA;;;;;;;;;;;"}