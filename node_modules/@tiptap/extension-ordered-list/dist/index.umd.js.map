{"version": 3, "file": "index.umd.js", "sources": ["../src/ordered-list.ts"], "sourcesContent": ["import { mergeAttributes, Node, wrappingInputRule } from '@tiptap/core'\n\nconst ListItemName = 'listItem'\nconst TextStyleName = 'textStyle'\n\nexport interface OrderedListOptions {\n  /**\n   * The node type name for list items.\n   * @default 'listItem'\n   * @example 'myListItem'\n   */\n  itemTypeName: string,\n\n  /**\n   * The HTML attributes for an ordered list node.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n\n  /**\n   * Keep the marks when splitting a list item.\n   * @default false\n   * @example true\n   */\n  keepMarks: boolean,\n\n  /**\n   * Keep the attributes when splitting a list item.\n   * @default false\n   * @example true\n   */\n  keepAttributes: boolean,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    orderedList: {\n      /**\n       * Toggle an ordered list\n       * @example editor.commands.toggleOrderedList()\n       */\n      toggleOrderedList: () => ReturnType,\n    }\n  }\n}\n\n/**\n * Matches an ordered list to a 1. on input (or any number followed by a dot).\n */\nexport const inputRegex = /^(\\d+)\\.\\s$/\n\n/**\n * This extension allows you to create ordered lists.\n * This requires the ListItem extension\n * @see https://www.tiptap.dev/api/nodes/ordered-list\n * @see https://www.tiptap.dev/api/nodes/list-item\n */\nexport const OrderedList = Node.create<OrderedListOptions>({\n  name: 'orderedList',\n\n  addOptions() {\n    return {\n      itemTypeName: 'listItem',\n      HTMLAttributes: {},\n      keepMarks: false,\n      keepAttributes: false,\n    }\n  },\n\n  group: 'block list',\n\n  content() {\n    return `${this.options.itemTypeName}+`\n  },\n\n  addAttributes() {\n    return {\n      start: {\n        default: 1,\n        parseHTML: element => {\n          return element.hasAttribute('start')\n            ? parseInt(element.getAttribute('start') || '', 10)\n            : 1\n        },\n      },\n      type: {\n        default: null,\n        parseHTML: element => element.getAttribute('type'),\n      },\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 'ol',\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    const { start, ...attributesWithoutStart } = HTMLAttributes\n\n    return start === 1\n      ? ['ol', mergeAttributes(this.options.HTMLAttributes, attributesWithoutStart), 0]\n      : ['ol', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      toggleOrderedList: () => ({ commands, chain }) => {\n        if (this.options.keepAttributes) {\n          return chain().toggleList(this.name, this.options.itemTypeName, this.options.keepMarks).updateAttributes(ListItemName, this.editor.getAttributes(TextStyleName)).run()\n        }\n        return commands.toggleList(this.name, this.options.itemTypeName, this.options.keepMarks)\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-Shift-7': () => this.editor.commands.toggleOrderedList(),\n    }\n  },\n\n  addInputRules() {\n    let inputRule = wrappingInputRule({\n      find: inputRegex,\n      type: this.type,\n      getAttributes: match => ({ start: +match[1] }),\n      joinPredicate: (match, node) => node.childCount + node.attrs.start === +match[1],\n    })\n\n    if (this.options.keepMarks || this.options.keepAttributes) {\n      inputRule = wrappingInputRule({\n        find: inputRegex,\n        type: this.type,\n        keepMarks: this.options.keepMarks,\n        keepAttributes: this.options.keepAttributes,\n        getAttributes: match => ({ start: +match[1], ...this.editor.getAttributes(TextStyleName) }),\n        joinPredicate: (match, node) => node.childCount + node.attrs.start === +match[1],\n        editor: this.editor,\n      })\n    }\n    return [\n      inputRule,\n    ]\n  },\n})\n"], "names": ["Node", "mergeAttributes", "wrappingInputRule"], "mappings": ";;;;;;EAEA,MAAM,YAAY,GAAG,UAAU;EAC/B,MAAM,aAAa,GAAG,WAAW;EA4CjC;;EAEG;AACI,QAAM,UAAU,GAAG;EAE1B;;;;;EAKG;AACU,QAAA,WAAW,GAAGA,SAAI,CAAC,MAAM,CAAqB;EACzD,IAAA,IAAI,EAAE,aAAa;MAEnB,UAAU,GAAA;UACR,OAAO;EACL,YAAA,YAAY,EAAE,UAAU;EACxB,YAAA,cAAc,EAAE,EAAE;EAClB,YAAA,SAAS,EAAE,KAAK;EAChB,YAAA,cAAc,EAAE,KAAK;WACtB;OACF;EAED,IAAA,KAAK,EAAE,YAAY;MAEnB,OAAO,GAAA;EACL,QAAA,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG;OACvC;MAED,aAAa,GAAA;UACX,OAAO;EACL,YAAA,KAAK,EAAE;EACL,gBAAA,OAAO,EAAE,CAAC;kBACV,SAAS,EAAE,OAAO,IAAG;EACnB,oBAAA,OAAO,OAAO,CAAC,YAAY,CAAC,OAAO;EACjC,0BAAE,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,EAAE;4BAChD,CAAC;mBACN;EACF,aAAA;EACD,YAAA,IAAI,EAAE;EACJ,gBAAA,OAAO,EAAE,IAAI;kBACb,SAAS,EAAE,OAAO,IAAI,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC;EACnD,aAAA;WACF;OACF;MAED,SAAS,GAAA;UACP,OAAO;EACL,YAAA;EACE,gBAAA,GAAG,EAAE,IAAI;EACV,aAAA;WACF;OACF;MAED,UAAU,CAAC,EAAE,cAAc,EAAE,EAAA;UAC3B,MAAM,EAAE,KAAK,EAAE,GAAG,sBAAsB,EAAE,GAAG,cAAc;UAE3D,OAAO,KAAK,KAAK;EACf,cAAE,CAAC,IAAI,EAAEC,oBAAe,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,sBAAsB,CAAC,EAAE,CAAC;EAChF,cAAE,CAAC,IAAI,EAAEA,oBAAe,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC,EAAE,CAAC,CAAC;OAC5E;MAED,WAAW,GAAA;UACT,OAAO;cACL,iBAAiB,EAAE,MAAM,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAI;EAC/C,gBAAA,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE;EAC/B,oBAAA,OAAO,KAAK,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,EAAE;;kBAExK,OAAO,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;eACzF;WACF;OACF;MAED,oBAAoB,GAAA;UAClB,OAAO;cACL,aAAa,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,iBAAiB,EAAE;WAC9D;OACF;MAED,aAAa,GAAA;UACX,IAAI,SAAS,GAAGC,sBAAiB,CAAC;EAChC,YAAA,IAAI,EAAE,UAAU;cAChB,IAAI,EAAE,IAAI,CAAC,IAAI;EACf,YAAA,aAAa,EAAE,KAAK,KAAK,EAAE,KAAK,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;cAC9C,aAAa,EAAE,CAAC,KAAK,EAAE,IAAI,KAAK,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;EACjF,SAAA,CAAC;EAEF,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE;cACzD,SAAS,GAAGA,sBAAiB,CAAC;EAC5B,gBAAA,IAAI,EAAE,UAAU;kBAChB,IAAI,EAAE,IAAI,CAAC,IAAI;EACf,gBAAA,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS;EACjC,gBAAA,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc;kBAC3C,aAAa,EAAE,KAAK,KAAK,EAAE,KAAK,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,aAAa,CAAC,EAAE,CAAC;kBAC3F,aAAa,EAAE,CAAC,KAAK,EAAE,IAAI,KAAK,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;kBAChF,MAAM,EAAE,IAAI,CAAC,MAAM;EACpB,aAAA,CAAC;;UAEJ,OAAO;cACL,SAAS;WACV;OACF;EACF,CAAA;;;;;;;;;;;;"}