{"version": 3, "file": "index.js", "sources": ["../src/ordered-list.ts"], "sourcesContent": ["import { mergeAttributes, Node, wrappingInputRule } from '@tiptap/core'\n\nconst ListItemName = 'listItem'\nconst TextStyleName = 'textStyle'\n\nexport interface OrderedListOptions {\n  /**\n   * The node type name for list items.\n   * @default 'listItem'\n   * @example 'myListItem'\n   */\n  itemTypeName: string,\n\n  /**\n   * The HTML attributes for an ordered list node.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n\n  /**\n   * Keep the marks when splitting a list item.\n   * @default false\n   * @example true\n   */\n  keepMarks: boolean,\n\n  /**\n   * Keep the attributes when splitting a list item.\n   * @default false\n   * @example true\n   */\n  keepAttributes: boolean,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    orderedList: {\n      /**\n       * Toggle an ordered list\n       * @example editor.commands.toggleOrderedList()\n       */\n      toggleOrderedList: () => ReturnType,\n    }\n  }\n}\n\n/**\n * Matches an ordered list to a 1. on input (or any number followed by a dot).\n */\nexport const inputRegex = /^(\\d+)\\.\\s$/\n\n/**\n * This extension allows you to create ordered lists.\n * This requires the ListItem extension\n * @see https://www.tiptap.dev/api/nodes/ordered-list\n * @see https://www.tiptap.dev/api/nodes/list-item\n */\nexport const OrderedList = Node.create<OrderedListOptions>({\n  name: 'orderedList',\n\n  addOptions() {\n    return {\n      itemTypeName: 'listItem',\n      HTMLAttributes: {},\n      keepMarks: false,\n      keepAttributes: false,\n    }\n  },\n\n  group: 'block list',\n\n  content() {\n    return `${this.options.itemTypeName}+`\n  },\n\n  addAttributes() {\n    return {\n      start: {\n        default: 1,\n        parseHTML: element => {\n          return element.hasAttribute('start')\n            ? parseInt(element.getAttribute('start') || '', 10)\n            : 1\n        },\n      },\n      type: {\n        default: null,\n        parseHTML: element => element.getAttribute('type'),\n      },\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 'ol',\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    const { start, ...attributesWithoutStart } = HTMLAttributes\n\n    return start === 1\n      ? ['ol', mergeAttributes(this.options.HTMLAttributes, attributesWithoutStart), 0]\n      : ['ol', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      toggleOrderedList: () => ({ commands, chain }) => {\n        if (this.options.keepAttributes) {\n          return chain().toggleList(this.name, this.options.itemTypeName, this.options.keepMarks).updateAttributes(ListItemName, this.editor.getAttributes(TextStyleName)).run()\n        }\n        return commands.toggleList(this.name, this.options.itemTypeName, this.options.keepMarks)\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-Shift-7': () => this.editor.commands.toggleOrderedList(),\n    }\n  },\n\n  addInputRules() {\n    let inputRule = wrappingInputRule({\n      find: inputRegex,\n      type: this.type,\n      getAttributes: match => ({ start: +match[1] }),\n      joinPredicate: (match, node) => node.childCount + node.attrs.start === +match[1],\n    })\n\n    if (this.options.keepMarks || this.options.keepAttributes) {\n      inputRule = wrappingInputRule({\n        find: inputRegex,\n        type: this.type,\n        keepMarks: this.options.keepMarks,\n        keepAttributes: this.options.keepAttributes,\n        getAttributes: match => ({ start: +match[1], ...this.editor.getAttributes(TextStyleName) }),\n        joinPredicate: (match, node) => node.childCount + node.attrs.start === +match[1],\n        editor: this.editor,\n      })\n    }\n    return [\n      inputRule,\n    ]\n  },\n})\n"], "names": [], "mappings": ";;AAEA,MAAM,YAAY,GAAG,UAAU;AAC/B,MAAM,aAAa,GAAG,WAAW;AA4CjC;;AAEG;AACI,MAAM,UAAU,GAAG;AAE1B;;;;;AAKG;AACU,MAAA,WAAW,GAAG,IAAI,CAAC,MAAM,CAAqB;AACzD,IAAA,IAAI,EAAE,aAAa;IAEnB,UAAU,GAAA;QACR,OAAO;AACL,YAAA,YAAY,EAAE,UAAU;AACxB,YAAA,cAAc,EAAE,EAAE;AAClB,YAAA,SAAS,EAAE,KAAK;AAChB,YAAA,cAAc,EAAE,KAAK;SACtB;KACF;AAED,IAAA,KAAK,EAAE,YAAY;IAEnB,OAAO,GAAA;AACL,QAAA,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG;KACvC;IAED,aAAa,GAAA;QACX,OAAO;AACL,YAAA,KAAK,EAAE;AACL,gBAAA,OAAO,EAAE,CAAC;gBACV,SAAS,EAAE,OAAO,IAAG;AACnB,oBAAA,OAAO,OAAO,CAAC,YAAY,CAAC,OAAO;AACjC,0BAAE,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,EAAE;0BAChD,CAAC;iBACN;AACF,aAAA;AACD,YAAA,IAAI,EAAE;AACJ,gBAAA,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,OAAO,IAAI,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC;AACnD,aAAA;SACF;KACF;IAED,SAAS,GAAA;QACP,OAAO;AACL,YAAA;AACE,gBAAA,GAAG,EAAE,IAAI;AACV,aAAA;SACF;KACF;IAED,UAAU,CAAC,EAAE,cAAc,EAAE,EAAA;QAC3B,MAAM,EAAE,KAAK,EAAE,GAAG,sBAAsB,EAAE,GAAG,cAAc;QAE3D,OAAO,KAAK,KAAK;AACf,cAAE,CAAC,IAAI,EAAE,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,sBAAsB,CAAC,EAAE,CAAC;AAChF,cAAE,CAAC,IAAI,EAAE,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC,EAAE,CAAC,CAAC;KAC5E;IAED,WAAW,GAAA;QACT,OAAO;YACL,iBAAiB,EAAE,MAAM,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAI;AAC/C,gBAAA,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE;AAC/B,oBAAA,OAAO,KAAK,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,EAAE;;gBAExK,OAAO,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;aACzF;SACF;KACF;IAED,oBAAoB,GAAA;QAClB,OAAO;YACL,aAAa,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,iBAAiB,EAAE;SAC9D;KACF;IAED,aAAa,GAAA;QACX,IAAI,SAAS,GAAG,iBAAiB,CAAC;AAChC,YAAA,IAAI,EAAE,UAAU;YAChB,IAAI,EAAE,IAAI,CAAC,IAAI;AACf,YAAA,aAAa,EAAE,KAAK,KAAK,EAAE,KAAK,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;YAC9C,aAAa,EAAE,CAAC,KAAK,EAAE,IAAI,KAAK,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;AACjF,SAAA,CAAC;AAEF,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE;YACzD,SAAS,GAAG,iBAAiB,CAAC;AAC5B,gBAAA,IAAI,EAAE,UAAU;gBAChB,IAAI,EAAE,IAAI,CAAC,IAAI;AACf,gBAAA,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS;AACjC,gBAAA,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc;gBAC3C,aAAa,EAAE,KAAK,KAAK,EAAE,KAAK,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,aAAa,CAAC,EAAE,CAAC;gBAC3F,aAAa,EAAE,CAAC,KAAK,EAAE,IAAI,KAAK,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;gBAChF,MAAM,EAAE,IAAI,CAAC,MAAM;AACpB,aAAA,CAAC;;QAEJ,OAAO;YACL,SAAS;SACV;KACF;AACF,CAAA;;;;"}