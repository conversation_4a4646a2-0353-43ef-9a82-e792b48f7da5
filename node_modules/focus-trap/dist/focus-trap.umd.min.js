/*!
* focus-trap 7.6.5
* @license MIT, https://github.com/focus-trap/focus-trap/blob/master/LICENSE
*/
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("tabbable")):"function"==typeof define&&define.amd?define(["exports","tabbable"],t):(e="undefined"!=typeof globalThis?globalThis:e||self,function(){var n=e.focusTrap,a=e.focusTrap={};t(a,e.tabbable),a.noConflict=function(){return e.focusTrap=n,a}}())}(this,(function(e,t){"use strict";function n(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=Array(t);n<t;n++)a[n]=e[n];return a}function a(e,t,n){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,t);if("object"!=typeof a)return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function o(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function r(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?o(Object(n),!0).forEach((function(t){a(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):o(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function i(e){return function(e){if(Array.isArray(e))return n(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return n(e,t);var a={}.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?n(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var u=function(e,t){if(e.length>0){var n=e[e.length-1];n!==t&&n._setPausedState(!0)}var a=e.indexOf(t);-1===a||e.splice(a,1),e.push(t)},s=function(e,t){var n=e.indexOf(t);-1!==n&&e.splice(n,1),e.length>0&&!e[e.length-1]._isManuallyPaused()&&e[e.length-1]._setPausedState(!1)},c=function(e){return"Tab"===(null==e?void 0:e.key)||9===(null==e?void 0:e.keyCode)},l=function(e){return c(e)&&!e.shiftKey},d=function(e){return c(e)&&e.shiftKey},b=function(e){return setTimeout(e,0)},f=function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),a=1;a<t;a++)n[a-1]=arguments[a];return"function"==typeof e?e.apply(void 0,n):e},v=function(e){return e.target.shadowRoot&&"function"==typeof e.composedPath?e.composedPath()[0]:e.target},p=[];e.createFocusTrap=function(e,n){var a,o=(null==n?void 0:n.document)||document,m=(null==n?void 0:n.trapStack)||p,y=r({returnFocusOnDeactivate:!0,escapeDeactivates:!0,delayInitialFocus:!0,isKeyForward:l,isKeyBackward:d},n),h={containers:[],containerGroups:[],tabbableGroups:[],nodeFocusedBeforeActivation:null,mostRecentlyFocusedNode:null,active:!1,paused:!1,manuallyPaused:!1,delayInitialFocusTimer:void 0,recentNavEvent:void 0},g=function(e,t,n){return e&&void 0!==e[t]?e[t]:y[n||t]},w=function(e,t){var n="function"==typeof(null==t?void 0:t.composedPath)?t.composedPath():void 0;return h.containerGroups.findIndex((function(t){var a=t.container,o=t.tabbableNodes;return a.contains(e)||(null==n?void 0:n.includes(a))||o.find((function(t){return t===e}))}))},T=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.hasFallback,a=void 0!==n&&n,r=t.params,u=void 0===r?[]:r,s=y[e];if("function"==typeof s&&(s=s.apply(void 0,i(u))),!0===s&&(s=void 0),!s){if(void 0===s||!1===s)return s;throw new Error("`".concat(e,"` was specified but was not a node, or did not return a node"))}var c=s;if("string"==typeof s){try{c=o.querySelector(s)}catch(t){throw new Error("`".concat(e,'` appears to be an invalid selector; error="').concat(t.message,'"'))}if(!c&&!a)throw new Error("`".concat(e,"` as selector refers to no known node"))}return c},F=function(){var e=T("initialFocus",{hasFallback:!0});if(!1===e)return!1;if(void 0===e||e&&!t.isFocusable(e,y.tabbableOptions))if(w(o.activeElement)>=0)e=o.activeElement;else{var n=h.tabbableGroups[0];e=n&&n.firstTabbableNode||T("fallbackFocus")}else null===e&&(e=T("fallbackFocus"));if(!e)throw new Error("Your focus-trap needs to have at least one focusable element");return e},N=function(){if(h.containerGroups=h.containers.map((function(e){var n=t.tabbable(e,y.tabbableOptions),a=t.focusable(e,y.tabbableOptions),o=n.length>0?n[0]:void 0,r=n.length>0?n[n.length-1]:void 0,i=a.find((function(e){return t.isTabbable(e)})),u=a.slice().reverse().find((function(e){return t.isTabbable(e)})),s=!!n.find((function(e){return t.getTabIndex(e)>0}));return{container:e,tabbableNodes:n,focusableNodes:a,posTabIndexesFound:s,firstTabbableNode:o,lastTabbableNode:r,firstDomTabbableNode:i,lastDomTabbableNode:u,nextTabbableNode:function(e){var o=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],r=n.indexOf(e);return r<0?o?a.slice(a.indexOf(e)+1).find((function(e){return t.isTabbable(e)})):a.slice(0,a.indexOf(e)).reverse().find((function(e){return t.isTabbable(e)})):n[r+(o?1:-1)]}}})),h.tabbableGroups=h.containerGroups.filter((function(e){return e.tabbableNodes.length>0})),h.tabbableGroups.length<=0&&!T("fallbackFocus"))throw new Error("Your focus-trap must have at least one container with at least one tabbable node in it at all times");if(h.containerGroups.find((function(e){return e.posTabIndexesFound}))&&h.containerGroups.length>1)throw new Error("At least one node with a positive tabindex was found in one of your focus-trap's multiple containers. Positive tabindexes are only supported in single-container focus-traps.")},O=function(e){var t=e.activeElement;if(t)return t.shadowRoot&&null!==t.shadowRoot.activeElement?O(t.shadowRoot):t},P=function(e){!1!==e&&e!==O(document)&&(e&&e.focus?(e.focus({preventScroll:!!y.preventScroll}),h.mostRecentlyFocusedNode=e,function(e){return e.tagName&&"input"===e.tagName.toLowerCase()&&"function"==typeof e.select}(e)&&e.select()):P(F()))},k=function(e){var t=T("setReturnFocus",{params:[e]});return t||!1!==t&&e},E=function(e){var n=e.target,a=e.event,o=e.isBackward,r=void 0!==o&&o;n=n||v(a),N();var i=null;if(h.tabbableGroups.length>0){var u=w(n,a),s=u>=0?h.containerGroups[u]:void 0;if(u<0)i=r?h.tabbableGroups[h.tabbableGroups.length-1].lastTabbableNode:h.tabbableGroups[0].firstTabbableNode;else if(r){var l=h.tabbableGroups.findIndex((function(e){var t=e.firstTabbableNode;return n===t}));if(l<0&&(s.container===n||t.isFocusable(n,y.tabbableOptions)&&!t.isTabbable(n,y.tabbableOptions)&&!s.nextTabbableNode(n,!1))&&(l=u),l>=0){var d=0===l?h.tabbableGroups.length-1:l-1,b=h.tabbableGroups[d];i=t.getTabIndex(n)>=0?b.lastTabbableNode:b.lastDomTabbableNode}else c(a)||(i=s.nextTabbableNode(n,!1))}else{var f=h.tabbableGroups.findIndex((function(e){var t=e.lastTabbableNode;return n===t}));if(f<0&&(s.container===n||t.isFocusable(n,y.tabbableOptions)&&!t.isTabbable(n,y.tabbableOptions)&&!s.nextTabbableNode(n))&&(f=u),f>=0){var p=f===h.tabbableGroups.length-1?0:f+1,m=h.tabbableGroups[p];i=t.getTabIndex(n)>=0?m.firstTabbableNode:m.firstDomTabbableNode}else c(a)||(i=s.nextTabbableNode(n))}}else i=T("fallbackFocus");return i},x=function(e){var t=v(e);w(t,e)>=0||(f(y.clickOutsideDeactivates,e)?a.deactivate({returnFocus:y.returnFocusOnDeactivate}):f(y.allowOutsideClick,e)||e.preventDefault())},D=function(e){var n=v(e),a=w(n,e)>=0;if(a||n instanceof Document)a&&(h.mostRecentlyFocusedNode=n);else{var o;e.stopImmediatePropagation();var r=!0;if(h.mostRecentlyFocusedNode)if(t.getTabIndex(h.mostRecentlyFocusedNode)>0){var i=w(h.mostRecentlyFocusedNode),u=h.containerGroups[i].tabbableNodes;if(u.length>0){var s=u.findIndex((function(e){return e===h.mostRecentlyFocusedNode}));s>=0&&(y.isKeyForward(h.recentNavEvent)?s+1<u.length&&(o=u[s+1],r=!1):s-1>=0&&(o=u[s-1],r=!1))}}else h.containerGroups.some((function(e){return e.tabbableNodes.some((function(e){return t.getTabIndex(e)>0}))}))||(r=!1);else r=!1;r&&(o=E({target:h.mostRecentlyFocusedNode,isBackward:y.isKeyBackward(h.recentNavEvent)})),P(o||(h.mostRecentlyFocusedNode||F()))}h.recentNavEvent=void 0},G=function(e){(y.isKeyForward(e)||y.isKeyBackward(e))&&function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];h.recentNavEvent=e;var n=E({event:e,isBackward:t});n&&(c(e)&&e.preventDefault(),P(n))}(e,y.isKeyBackward(e))},I=function(e){var t;"Escape"!==(null==(t=e)?void 0:t.key)&&"Esc"!==(null==t?void 0:t.key)&&27!==(null==t?void 0:t.keyCode)||!1===f(y.escapeDeactivates,e)||(e.preventDefault(),a.deactivate())},S=function(e){var t=v(e);w(t,e)>=0||f(y.clickOutsideDeactivates,e)||f(y.allowOutsideClick,e)||(e.preventDefault(),e.stopImmediatePropagation())},j=function(){if(h.active)return u(m,a),h.delayInitialFocusTimer=y.delayInitialFocus?b((function(){P(F())})):P(F()),o.addEventListener("focusin",D,!0),o.addEventListener("mousedown",x,{capture:!0,passive:!1}),o.addEventListener("touchstart",x,{capture:!0,passive:!1}),o.addEventListener("click",S,{capture:!0,passive:!1}),o.addEventListener("keydown",G,{capture:!0,passive:!1}),o.addEventListener("keydown",I),a},R=function(){if(h.active)return o.removeEventListener("focusin",D,!0),o.removeEventListener("mousedown",x,!0),o.removeEventListener("touchstart",x,!0),o.removeEventListener("click",S,!0),o.removeEventListener("keydown",G,!0),o.removeEventListener("keydown",I),a},A="undefined"!=typeof window&&"MutationObserver"in window?new MutationObserver((function(e){e.some((function(e){return Array.from(e.removedNodes).some((function(e){return e===h.mostRecentlyFocusedNode}))}))&&P(F())})):void 0,L=function(){A&&(A.disconnect(),h.active&&!h.paused&&h.containers.map((function(e){A.observe(e,{subtree:!0,childList:!0})})))};return a={get active(){return h.active},get paused(){return h.paused},activate:function(e){if(h.active)return this;var t=g(e,"onActivate"),n=g(e,"onPostActivate"),a=g(e,"checkCanFocusTrap");a||N(),h.active=!0,h.paused=!1,h.nodeFocusedBeforeActivation=O(o),null==t||t();var r=function(){a&&N(),j(),L(),null==n||n()};return a?(a(h.containers.concat()).then(r,r),this):(r(),this)},deactivate:function(e){if(!h.active)return this;var t=r({onDeactivate:y.onDeactivate,onPostDeactivate:y.onPostDeactivate,checkCanReturnFocus:y.checkCanReturnFocus},e);clearTimeout(h.delayInitialFocusTimer),h.delayInitialFocusTimer=void 0,R(),h.active=!1,h.paused=!1,L(),s(m,a);var n=g(t,"onDeactivate"),o=g(t,"onPostDeactivate"),i=g(t,"checkCanReturnFocus"),u=g(t,"returnFocus","returnFocusOnDeactivate");null==n||n();var c=function(){b((function(){u&&P(k(h.nodeFocusedBeforeActivation)),null==o||o()}))};return u&&i?(i(k(h.nodeFocusedBeforeActivation)).then(c,c),this):(c(),this)},pause:function(e){return h.active?(h.manuallyPaused=!0,this._setPausedState(!0,e)):this},unpause:function(e){return h.active?(h.manuallyPaused=!1,m[m.length-1]!==this?this:this._setPausedState(!1,e)):this},updateContainerElements:function(e){var t=[].concat(e).filter(Boolean);return h.containers=t.map((function(e){return"string"==typeof e?o.querySelector(e):e})),h.active&&N(),L(),this}},Object.defineProperties(a,{_isManuallyPaused:{value:function(){return h.manuallyPaused}},_setPausedState:{value:function(e,t){if(h.paused===e)return this;if(h.paused=e,e){var n=g(t,"onPause"),a=g(t,"onPostPause");null==n||n(),R(),L(),null==a||a()}else{var o=g(t,"onUnpause"),r=g(t,"onPostUnpause");null==o||o(),N(),j(),L(),null==r||r()}return this}}}),a.updateContainerElements(e),a}}));
//# sourceMappingURL=focus-trap.umd.min.js.map
