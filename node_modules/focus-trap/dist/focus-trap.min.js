/*!
* focus-trap 7.6.5
* @license MIT, https://github.com/focus-trap/focus-trap/blob/master/LICENSE
*/
"use strict";var e=require("tabbable");function t(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=Array(t);n<t;n++)a[n]=e[n];return a}function n(e,t,n){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,t);if("object"!=typeof a)return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function a(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function o(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?a(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):a(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}function r(e){return function(e){if(Array.isArray(e))return t(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,n){if(e){if("string"==typeof e)return t(e,n);var a={}.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?t(e,n):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var i=function(e,t){if(e.length>0){var n=e[e.length-1];n!==t&&n._setPausedState(!0)}var a=e.indexOf(t);-1===a||e.splice(a,1),e.push(t)},u=function(e,t){var n=e.indexOf(t);-1!==n&&e.splice(n,1),e.length>0&&!e[e.length-1]._isManuallyPaused()&&e[e.length-1]._setPausedState(!1)},s=function(e){return"Tab"===(null==e?void 0:e.key)||9===(null==e?void 0:e.keyCode)},c=function(e){return s(e)&&!e.shiftKey},l=function(e){return s(e)&&e.shiftKey},d=function(e){return setTimeout(e,0)},b=function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),a=1;a<t;a++)n[a-1]=arguments[a];return"function"==typeof e?e.apply(void 0,n):e},f=function(e){return e.target.shadowRoot&&"function"==typeof e.composedPath?e.composedPath()[0]:e.target},v=[];exports.createFocusTrap=function(t,n){var a,p=(null==n?void 0:n.document)||document,m=(null==n?void 0:n.trapStack)||v,y=o({returnFocusOnDeactivate:!0,escapeDeactivates:!0,delayInitialFocus:!0,isKeyForward:c,isKeyBackward:l},n),h={containers:[],containerGroups:[],tabbableGroups:[],nodeFocusedBeforeActivation:null,mostRecentlyFocusedNode:null,active:!1,paused:!1,manuallyPaused:!1,delayInitialFocusTimer:void 0,recentNavEvent:void 0},w=function(e,t,n){return e&&void 0!==e[t]?e[t]:y[n||t]},g=function(e,t){var n="function"==typeof(null==t?void 0:t.composedPath)?t.composedPath():void 0;return h.containerGroups.findIndex((function(t){var a=t.container,o=t.tabbableNodes;return a.contains(e)||(null==n?void 0:n.includes(a))||o.find((function(t){return t===e}))}))},F=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.hasFallback,a=void 0!==n&&n,o=t.params,i=void 0===o?[]:o,u=y[e];if("function"==typeof u&&(u=u.apply(void 0,r(i))),!0===u&&(u=void 0),!u){if(void 0===u||!1===u)return u;throw new Error("`".concat(e,"` was specified but was not a node, or did not return a node"))}var s=u;if("string"==typeof u){try{s=p.querySelector(u)}catch(t){throw new Error("`".concat(e,'` appears to be an invalid selector; error="').concat(t.message,'"'))}if(!s&&!a)throw new Error("`".concat(e,"` as selector refers to no known node"))}return s},N=function(){var t=F("initialFocus",{hasFallback:!0});if(!1===t)return!1;if(void 0===t||t&&!e.isFocusable(t,y.tabbableOptions))if(g(p.activeElement)>=0)t=p.activeElement;else{var n=h.tabbableGroups[0];t=n&&n.firstTabbableNode||F("fallbackFocus")}else null===t&&(t=F("fallbackFocus"));if(!t)throw new Error("Your focus-trap needs to have at least one focusable element");return t},T=function(){if(h.containerGroups=h.containers.map((function(t){var n=e.tabbable(t,y.tabbableOptions),a=e.focusable(t,y.tabbableOptions),o=n.length>0?n[0]:void 0,r=n.length>0?n[n.length-1]:void 0,i=a.find((function(t){return e.isTabbable(t)})),u=a.slice().reverse().find((function(t){return e.isTabbable(t)})),s=!!n.find((function(t){return e.getTabIndex(t)>0}));return{container:t,tabbableNodes:n,focusableNodes:a,posTabIndexesFound:s,firstTabbableNode:o,lastTabbableNode:r,firstDomTabbableNode:i,lastDomTabbableNode:u,nextTabbableNode:function(t){var o=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],r=n.indexOf(t);return r<0?o?a.slice(a.indexOf(t)+1).find((function(t){return e.isTabbable(t)})):a.slice(0,a.indexOf(t)).reverse().find((function(t){return e.isTabbable(t)})):n[r+(o?1:-1)]}}})),h.tabbableGroups=h.containerGroups.filter((function(e){return e.tabbableNodes.length>0})),h.tabbableGroups.length<=0&&!F("fallbackFocus"))throw new Error("Your focus-trap must have at least one container with at least one tabbable node in it at all times");if(h.containerGroups.find((function(e){return e.posTabIndexesFound}))&&h.containerGroups.length>1)throw new Error("At least one node with a positive tabindex was found in one of your focus-trap's multiple containers. Positive tabindexes are only supported in single-container focus-traps.")},O=function(e){var t=e.activeElement;if(t)return t.shadowRoot&&null!==t.shadowRoot.activeElement?O(t.shadowRoot):t},P=function(e){!1!==e&&e!==O(document)&&(e&&e.focus?(e.focus({preventScroll:!!y.preventScroll}),h.mostRecentlyFocusedNode=e,function(e){return e.tagName&&"input"===e.tagName.toLowerCase()&&"function"==typeof e.select}(e)&&e.select()):P(N()))},k=function(e){var t=F("setReturnFocus",{params:[e]});return t||!1!==t&&e},E=function(t){var n=t.target,a=t.event,o=t.isBackward,r=void 0!==o&&o;n=n||f(a),T();var i=null;if(h.tabbableGroups.length>0){var u=g(n,a),c=u>=0?h.containerGroups[u]:void 0;if(u<0)i=r?h.tabbableGroups[h.tabbableGroups.length-1].lastTabbableNode:h.tabbableGroups[0].firstTabbableNode;else if(r){var l=h.tabbableGroups.findIndex((function(e){var t=e.firstTabbableNode;return n===t}));if(l<0&&(c.container===n||e.isFocusable(n,y.tabbableOptions)&&!e.isTabbable(n,y.tabbableOptions)&&!c.nextTabbableNode(n,!1))&&(l=u),l>=0){var d=0===l?h.tabbableGroups.length-1:l-1,b=h.tabbableGroups[d];i=e.getTabIndex(n)>=0?b.lastTabbableNode:b.lastDomTabbableNode}else s(a)||(i=c.nextTabbableNode(n,!1))}else{var v=h.tabbableGroups.findIndex((function(e){var t=e.lastTabbableNode;return n===t}));if(v<0&&(c.container===n||e.isFocusable(n,y.tabbableOptions)&&!e.isTabbable(n,y.tabbableOptions)&&!c.nextTabbableNode(n))&&(v=u),v>=0){var p=v===h.tabbableGroups.length-1?0:v+1,m=h.tabbableGroups[p];i=e.getTabIndex(n)>=0?m.firstTabbableNode:m.firstDomTabbableNode}else s(a)||(i=c.nextTabbableNode(n))}}else i=F("fallbackFocus");return i},D=function(e){var t=f(e);g(t,e)>=0||(b(y.clickOutsideDeactivates,e)?a.deactivate({returnFocus:y.returnFocusOnDeactivate}):b(y.allowOutsideClick,e)||e.preventDefault())},x=function(t){var n=f(t),a=g(n,t)>=0;if(a||n instanceof Document)a&&(h.mostRecentlyFocusedNode=n);else{var o;t.stopImmediatePropagation();var r=!0;if(h.mostRecentlyFocusedNode)if(e.getTabIndex(h.mostRecentlyFocusedNode)>0){var i=g(h.mostRecentlyFocusedNode),u=h.containerGroups[i].tabbableNodes;if(u.length>0){var s=u.findIndex((function(e){return e===h.mostRecentlyFocusedNode}));s>=0&&(y.isKeyForward(h.recentNavEvent)?s+1<u.length&&(o=u[s+1],r=!1):s-1>=0&&(o=u[s-1],r=!1))}}else h.containerGroups.some((function(t){return t.tabbableNodes.some((function(t){return e.getTabIndex(t)>0}))}))||(r=!1);else r=!1;r&&(o=E({target:h.mostRecentlyFocusedNode,isBackward:y.isKeyBackward(h.recentNavEvent)})),P(o||(h.mostRecentlyFocusedNode||N()))}h.recentNavEvent=void 0},G=function(e){(y.isKeyForward(e)||y.isKeyBackward(e))&&function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];h.recentNavEvent=e;var n=E({event:e,isBackward:t});n&&(s(e)&&e.preventDefault(),P(n))}(e,y.isKeyBackward(e))},I=function(e){var t;"Escape"!==(null==(t=e)?void 0:t.key)&&"Esc"!==(null==t?void 0:t.key)&&27!==(null==t?void 0:t.keyCode)||!1===b(y.escapeDeactivates,e)||(e.preventDefault(),a.deactivate())},S=function(e){var t=f(e);g(t,e)>=0||b(y.clickOutsideDeactivates,e)||b(y.allowOutsideClick,e)||(e.preventDefault(),e.stopImmediatePropagation())},R=function(){if(h.active)return i(m,a),h.delayInitialFocusTimer=y.delayInitialFocus?d((function(){P(N())})):P(N()),p.addEventListener("focusin",x,!0),p.addEventListener("mousedown",D,{capture:!0,passive:!1}),p.addEventListener("touchstart",D,{capture:!0,passive:!1}),p.addEventListener("click",S,{capture:!0,passive:!1}),p.addEventListener("keydown",G,{capture:!0,passive:!1}),p.addEventListener("keydown",I),a},j=function(){if(h.active)return p.removeEventListener("focusin",x,!0),p.removeEventListener("mousedown",D,!0),p.removeEventListener("touchstart",D,!0),p.removeEventListener("click",S,!0),p.removeEventListener("keydown",G,!0),p.removeEventListener("keydown",I),a},A="undefined"!=typeof window&&"MutationObserver"in window?new MutationObserver((function(e){e.some((function(e){return Array.from(e.removedNodes).some((function(e){return e===h.mostRecentlyFocusedNode}))}))&&P(N())})):void 0,L=function(){A&&(A.disconnect(),h.active&&!h.paused&&h.containers.map((function(e){A.observe(e,{subtree:!0,childList:!0})})))};return a={get active(){return h.active},get paused(){return h.paused},activate:function(e){if(h.active)return this;var t=w(e,"onActivate"),n=w(e,"onPostActivate"),a=w(e,"checkCanFocusTrap");a||T(),h.active=!0,h.paused=!1,h.nodeFocusedBeforeActivation=O(p),null==t||t();var o=function(){a&&T(),R(),L(),null==n||n()};return a?(a(h.containers.concat()).then(o,o),this):(o(),this)},deactivate:function(e){if(!h.active)return this;var t=o({onDeactivate:y.onDeactivate,onPostDeactivate:y.onPostDeactivate,checkCanReturnFocus:y.checkCanReturnFocus},e);clearTimeout(h.delayInitialFocusTimer),h.delayInitialFocusTimer=void 0,j(),h.active=!1,h.paused=!1,L(),u(m,a);var n=w(t,"onDeactivate"),r=w(t,"onPostDeactivate"),i=w(t,"checkCanReturnFocus"),s=w(t,"returnFocus","returnFocusOnDeactivate");null==n||n();var c=function(){d((function(){s&&P(k(h.nodeFocusedBeforeActivation)),null==r||r()}))};return s&&i?(i(k(h.nodeFocusedBeforeActivation)).then(c,c),this):(c(),this)},pause:function(e){return h.active?(h.manuallyPaused=!0,this._setPausedState(!0,e)):this},unpause:function(e){return h.active?(h.manuallyPaused=!1,m[m.length-1]!==this?this:this._setPausedState(!1,e)):this},updateContainerElements:function(e){var t=[].concat(e).filter(Boolean);return h.containers=t.map((function(e){return"string"==typeof e?p.querySelector(e):e})),h.active&&T(),L(),this}},Object.defineProperties(a,{_isManuallyPaused:{value:function(){return h.manuallyPaused}},_setPausedState:{value:function(e,t){if(h.paused===e)return this;if(h.paused=e,e){var n=w(t,"onPause"),a=w(t,"onPostPause");null==n||n(),j(),L(),null==a||a()}else{var o=w(t,"onUnpause"),r=w(t,"onPostUnpause");null==o||o(),T(),R(),L(),null==r||r()}return this}}}),a.updateContainerElements(t),a};
//# sourceMappingURL=focus-trap.min.js.map
