{"version": 3, "file": "focus-trap.esm.min.js", "sources": ["../index.js"], "sourcesContent": ["import {\n  tabbable,\n  focusable,\n  isFocusable,\n  isTabbable,\n  getTabIndex,\n} from 'tabbable';\n\nconst activeFocusTraps = {\n  activateTrap(trapStack, trap) {\n    if (trapStack.length > 0) {\n      const activeTrap = trapStack[trapStack.length - 1];\n      if (activeTrap !== trap) {\n        activeTrap._setPausedState(true);\n      }\n    }\n\n    const trapIndex = trapStack.indexOf(trap);\n    if (trapIndex === -1) {\n      trapStack.push(trap);\n    } else {\n      // move this existing trap to the front of the queue\n      trapStack.splice(trapIndex, 1);\n      trapStack.push(trap);\n    }\n  },\n\n  deactivateTrap(trapStack, trap) {\n    const trapIndex = trapStack.indexOf(trap);\n    if (trapIndex !== -1) {\n      trapStack.splice(trapIndex, 1);\n    }\n\n    if (\n      trapStack.length > 0 &&\n      !trapStack[trapStack.length - 1]._isManuallyPaused()\n    ) {\n      trapStack[trapStack.length - 1]._setPausedState(false);\n    }\n  },\n};\n\nconst isSelectableInput = function (node) {\n  return (\n    node.tagName &&\n    node.tagName.toLowerCase() === 'input' &&\n    typeof node.select === 'function'\n  );\n};\n\nconst isEscapeEvent = function (e) {\n  return e?.key === 'Escape' || e?.key === 'Esc' || e?.keyCode === 27;\n};\n\nconst isTabEvent = function (e) {\n  return e?.key === 'Tab' || e?.keyCode === 9;\n};\n\n// checks for TAB by default\nconst isKeyForward = function (e) {\n  return isTabEvent(e) && !e.shiftKey;\n};\n\n// checks for SHIFT+TAB by default\nconst isKeyBackward = function (e) {\n  return isTabEvent(e) && e.shiftKey;\n};\n\nconst delay = function (fn) {\n  return setTimeout(fn, 0);\n};\n\n/**\n * Get an option's value when it could be a plain value, or a handler that provides\n *  the value.\n * @param {*} value Option's value to check.\n * @param {...*} [params] Any parameters to pass to the handler, if `value` is a function.\n * @returns {*} The `value`, or the handler's returned value.\n */\nconst valueOrHandler = function (value, ...params) {\n  return typeof value === 'function' ? value(...params) : value;\n};\n\nconst getActualTarget = function (event) {\n  // NOTE: If the trap is _inside_ a shadow DOM, event.target will always be the\n  //  shadow host. However, event.target.composedPath() will be an array of\n  //  nodes \"clicked\" from inner-most (the actual element inside the shadow) to\n  //  outer-most (the host HTML document). If we have access to composedPath(),\n  //  then use its first element; otherwise, fall back to event.target (and\n  //  this only works for an _open_ shadow DOM; otherwise,\n  //  composedPath()[0] === event.target always).\n  return event.target.shadowRoot && typeof event.composedPath === 'function'\n    ? event.composedPath()[0]\n    : event.target;\n};\n\n// NOTE: this must be _outside_ `createFocusTrap()` to make sure all traps in this\n//  current instance use the same stack if `userOptions.trapStack` isn't specified\nconst internalTrapStack = [];\n\nconst createFocusTrap = function (elements, userOptions) {\n  // SSR: a live trap shouldn't be created in this type of environment so this\n  //  should be safe code to execute if the `document` option isn't specified\n  const doc = userOptions?.document || document;\n\n  const trapStack = userOptions?.trapStack || internalTrapStack;\n\n  const config = {\n    returnFocusOnDeactivate: true,\n    escapeDeactivates: true,\n    delayInitialFocus: true,\n    isKeyForward,\n    isKeyBackward,\n    ...userOptions,\n  };\n\n  const state = {\n    // containers given to createFocusTrap()\n    // @type {Array<HTMLElement>}\n    containers: [],\n\n    // list of objects identifying tabbable nodes in `containers` in the trap\n    // NOTE: it's possible that a group has no tabbable nodes if nodes get removed while the trap\n    //  is active, but the trap should never get to a state where there isn't at least one group\n    //  with at least one tabbable node in it (that would lead to an error condition that would\n    //  result in an error being thrown)\n    // @type {Array<{\n    //   container: HTMLElement,\n    //   tabbableNodes: Array<HTMLElement>, // empty if none\n    //   focusableNodes: Array<HTMLElement>, // empty if none\n    //   posTabIndexesFound: boolean,\n    //   firstTabbableNode: HTMLElement|undefined,\n    //   lastTabbableNode: HTMLElement|undefined,\n    //   firstDomTabbableNode: HTMLElement|undefined,\n    //   lastDomTabbableNode: HTMLElement|undefined,\n    //   nextTabbableNode: (node: HTMLElement, forward: boolean) => HTMLElement|undefined\n    // }>}\n    containerGroups: [], // same order/length as `containers` list\n\n    // references to objects in `containerGroups`, but only those that actually have\n    //  tabbable nodes in them\n    // NOTE: same order as `containers` and `containerGroups`, but __not necessarily__\n    //  the same length\n    tabbableGroups: [],\n\n    nodeFocusedBeforeActivation: null,\n    mostRecentlyFocusedNode: null,\n    active: false,\n    paused: false,\n    manuallyPaused: false,\n\n    // timer ID for when delayInitialFocus is true and initial focus in this trap\n    //  has been delayed during activation\n    delayInitialFocusTimer: undefined,\n\n    // the most recent KeyboardEvent for the configured nav key (typically [SHIFT+]TAB), if any\n    recentNavEvent: undefined,\n  };\n\n  let trap; // eslint-disable-line prefer-const -- some private functions reference it, and its methods reference private functions, so we must declare here and define later\n\n  /**\n   * Gets a configuration option value.\n   * @param {Object|undefined} configOverrideOptions If true, and option is defined in this set,\n   *  value will be taken from this object. Otherwise, value will be taken from base configuration.\n   * @param {string} optionName Name of the option whose value is sought.\n   * @param {string|undefined} [configOptionName] Name of option to use __instead of__ `optionName`\n   *  IIF `configOverrideOptions` is not defined. Otherwise, `optionName` is used.\n   */\n  const getOption = (configOverrideOptions, optionName, configOptionName) => {\n    return configOverrideOptions &&\n      configOverrideOptions[optionName] !== undefined\n      ? configOverrideOptions[optionName]\n      : config[configOptionName || optionName];\n  };\n\n  /**\n   * Finds the index of the container that contains the element.\n   * @param {HTMLElement} element\n   * @param {Event} [event] If available, and `element` isn't directly found in any container,\n   *  the event's composed path is used to see if includes any known trap containers in the\n   *  case where the element is inside a Shadow DOM.\n   * @returns {number} Index of the container in either `state.containers` or\n   *  `state.containerGroups` (the order/length of these lists are the same); -1\n   *  if the element isn't found.\n   */\n  const findContainerIndex = function (element, event) {\n    const composedPath =\n      typeof event?.composedPath === 'function'\n        ? event.composedPath()\n        : undefined;\n    // NOTE: search `containerGroups` because it's possible a group contains no tabbable\n    //  nodes, but still contains focusable nodes (e.g. if they all have `tabindex=-1`)\n    //  and we still need to find the element in there\n    return state.containerGroups.findIndex(\n      ({ container, tabbableNodes }) =>\n        container.contains(element) ||\n        // fall back to explicit tabbable search which will take into consideration any\n        //  web components if the `tabbableOptions.getShadowRoot` option was used for\n        //  the trap, enabling shadow DOM support in tabbable (`Node.contains()` doesn't\n        //  look inside web components even if open)\n        composedPath?.includes(container) ||\n        tabbableNodes.find((node) => node === element)\n    );\n  };\n\n  /**\n   * Gets the node for the given option, which is expected to be an option that\n   *  can be either a DOM node, a string that is a selector to get a node, `false`\n   *  (if a node is explicitly NOT given), or a function that returns any of these\n   *  values.\n   * @param {string} optionName\n   * @param {Object} options\n   * @param {boolean} [options.hasFallback] True if the option could be a selector string\n   *  and the option allows for a fallback scenario in the case where the selector is\n   *  valid but does not match a node (i.e. the queried node doesn't exist in the DOM).\n   * @param {Array} [options.params] Params to pass to the option if it's a function.\n   * @returns {undefined | null | false | HTMLElement | SVGElement} Returns\n   *  `undefined` if the option is not specified; `null` if the option didn't resolve\n   *  to a node but `options.hasFallback=true`, `false` if the option resolved to `false`\n   *  (node explicitly not given); otherwise, the resolved DOM node.\n   * @throws {Error} If the option is set, not `false`, and is not, or does not\n   *  resolve to a node, unless the option is a selector string and `options.hasFallback=true`.\n   */\n  const getNodeForOption = function (\n    optionName,\n    { hasFallback = false, params = [] } = {}\n  ) {\n    let optionValue = config[optionName];\n\n    if (typeof optionValue === 'function') {\n      optionValue = optionValue(...params);\n    }\n\n    if (optionValue === true) {\n      optionValue = undefined; // use default value\n    }\n\n    if (!optionValue) {\n      if (optionValue === undefined || optionValue === false) {\n        return optionValue;\n      }\n      // else, empty string (invalid), null (invalid), 0 (invalid)\n\n      throw new Error(\n        `\\`${optionName}\\` was specified but was not a node, or did not return a node`\n      );\n    }\n\n    let node = optionValue; // could be HTMLElement, SVGElement, or non-empty string at this point\n\n    if (typeof optionValue === 'string') {\n      try {\n        node = doc.querySelector(optionValue); // resolve to node, or null if fails\n      } catch (err) {\n        throw new Error(\n          `\\`${optionName}\\` appears to be an invalid selector; error=\"${err.message}\"`\n        );\n      }\n\n      if (!node) {\n        if (!hasFallback) {\n          throw new Error(\n            `\\`${optionName}\\` as selector refers to no known node`\n          );\n        }\n        // else, `node` MUST be `null` because that's what `Document.querySelector()` returns\n        //  if the selector is valid but doesn't match anything\n      }\n    }\n\n    return node;\n  };\n\n  const getInitialFocusNode = function () {\n    let node = getNodeForOption('initialFocus', { hasFallback: true });\n\n    // false explicitly indicates we want no initialFocus at all\n    if (node === false) {\n      return false;\n    }\n\n    if (\n      node === undefined ||\n      (node && !isFocusable(node, config.tabbableOptions))\n    ) {\n      // option not specified nor focusable: use fallback options\n      if (findContainerIndex(doc.activeElement) >= 0) {\n        node = doc.activeElement;\n      } else {\n        const firstTabbableGroup = state.tabbableGroups[0];\n        const firstTabbableNode =\n          firstTabbableGroup && firstTabbableGroup.firstTabbableNode;\n\n        // NOTE: `fallbackFocus` option function cannot return `false` (not supported)\n        node = firstTabbableNode || getNodeForOption('fallbackFocus');\n      }\n    } else if (node === null) {\n      // option is a VALID selector string that doesn't yield a node: use the `fallbackFocus`\n      //  option instead of the default behavior when the option isn't specified at all\n      node = getNodeForOption('fallbackFocus');\n    }\n\n    if (!node) {\n      throw new Error(\n        'Your focus-trap needs to have at least one focusable element'\n      );\n    }\n\n    return node;\n  };\n\n  const updateTabbableNodes = function () {\n    state.containerGroups = state.containers.map((container) => {\n      const tabbableNodes = tabbable(container, config.tabbableOptions);\n\n      // NOTE: if we have tabbable nodes, we must have focusable nodes; focusable nodes\n      //  are a superset of tabbable nodes since nodes with negative `tabindex` attributes\n      //  are focusable but not tabbable\n      const focusableNodes = focusable(container, config.tabbableOptions);\n\n      const firstTabbableNode =\n        tabbableNodes.length > 0 ? tabbableNodes[0] : undefined;\n      const lastTabbableNode =\n        tabbableNodes.length > 0\n          ? tabbableNodes[tabbableNodes.length - 1]\n          : undefined;\n\n      const firstDomTabbableNode = focusableNodes.find((node) =>\n        isTabbable(node)\n      );\n      const lastDomTabbableNode = focusableNodes\n        .slice()\n        .reverse()\n        .find((node) => isTabbable(node));\n\n      const posTabIndexesFound = !!tabbableNodes.find(\n        (node) => getTabIndex(node) > 0\n      );\n\n      return {\n        container,\n        tabbableNodes,\n        focusableNodes,\n\n        /** True if at least one node with positive `tabindex` was found in this container. */\n        posTabIndexesFound,\n\n        /** First tabbable node in container, __tabindex__ order; `undefined` if none. */\n        firstTabbableNode,\n        /** Last tabbable node in container, __tabindex__ order; `undefined` if none. */\n        lastTabbableNode,\n\n        // NOTE: DOM order is NOT NECESSARILY \"document position\" order, but figuring that out\n        //  would require more than just https://developer.mozilla.org/en-US/docs/Web/API/Node/compareDocumentPosition\n        //  because that API doesn't work with Shadow DOM as well as it should (@see\n        //  https://github.com/whatwg/dom/issues/320) and since this first/last is only needed, so far,\n        //  to address an edge case related to positive tabindex support, this seems like a much easier,\n        //  \"close enough most of the time\" alternative for positive tabindexes which should generally\n        //  be avoided anyway...\n        /** First tabbable node in container, __DOM__ order; `undefined` if none. */\n        firstDomTabbableNode,\n        /** Last tabbable node in container, __DOM__ order; `undefined` if none. */\n        lastDomTabbableNode,\n\n        /**\n         * Finds the __tabbable__ node that follows the given node in the specified direction,\n         *  in this container, if any.\n         * @param {HTMLElement} node\n         * @param {boolean} [forward] True if going in forward tab order; false if going\n         *  in reverse.\n         * @returns {HTMLElement|undefined} The next tabbable node, if any.\n         */\n        nextTabbableNode(node, forward = true) {\n          const nodeIdx = tabbableNodes.indexOf(node);\n          if (nodeIdx < 0) {\n            // either not tabbable nor focusable, or was focused but not tabbable (negative tabindex):\n            //  since `node` should at least have been focusable, we assume that's the case and mimic\n            //  what browsers do, which is set focus to the next node in __document position order__,\n            //  regardless of positive tabindexes, if any -- and for reasons explained in the NOTE\n            //  above related to `firstDomTabbable` and `lastDomTabbable` properties, we fall back to\n            //  basic DOM order\n            if (forward) {\n              return focusableNodes\n                .slice(focusableNodes.indexOf(node) + 1)\n                .find((el) => isTabbable(el));\n            }\n\n            return focusableNodes\n              .slice(0, focusableNodes.indexOf(node))\n              .reverse()\n              .find((el) => isTabbable(el));\n          }\n\n          return tabbableNodes[nodeIdx + (forward ? 1 : -1)];\n        },\n      };\n    });\n\n    state.tabbableGroups = state.containerGroups.filter(\n      (group) => group.tabbableNodes.length > 0\n    );\n\n    // throw if no groups have tabbable nodes and we don't have a fallback focus node either\n    if (\n      state.tabbableGroups.length <= 0 &&\n      !getNodeForOption('fallbackFocus') // returning false not supported for this option\n    ) {\n      throw new Error(\n        'Your focus-trap must have at least one container with at least one tabbable node in it at all times'\n      );\n    }\n\n    // NOTE: Positive tabindexes are only properly supported in single-container traps because\n    //  doing it across multiple containers where tabindexes could be all over the place\n    //  would require Tabbable to support multiple containers, would require additional\n    //  specialized Shadow DOM support, and would require Tabbable's multi-container support\n    //  to look at those containers in document position order rather than user-provided\n    //  order (as they are treated in Focus-trap, for legacy reasons). See discussion on\n    //  https://github.com/focus-trap/focus-trap/issues/375 for more details.\n    if (\n      state.containerGroups.find((g) => g.posTabIndexesFound) &&\n      state.containerGroups.length > 1\n    ) {\n      throw new Error(\n        \"At least one node with a positive tabindex was found in one of your focus-trap's multiple containers. Positive tabindexes are only supported in single-container focus-traps.\"\n      );\n    }\n  };\n\n  /**\n   * Gets the current activeElement. If it's a web-component and has open shadow-root\n   * it will recursively search inside shadow roots for the \"true\" activeElement.\n   *\n   * @param {Document | ShadowRoot} el\n   *\n   * @returns {HTMLElement} The element that currently has the focus\n   **/\n  const getActiveElement = function (el) {\n    const activeElement = el.activeElement;\n\n    if (!activeElement) {\n      return;\n    }\n\n    if (\n      activeElement.shadowRoot &&\n      activeElement.shadowRoot.activeElement !== null\n    ) {\n      return getActiveElement(activeElement.shadowRoot);\n    }\n\n    return activeElement;\n  };\n\n  const tryFocus = function (node) {\n    if (node === false) {\n      return;\n    }\n\n    if (node === getActiveElement(document)) {\n      return;\n    }\n\n    if (!node || !node.focus) {\n      tryFocus(getInitialFocusNode());\n      return;\n    }\n\n    node.focus({ preventScroll: !!config.preventScroll });\n    // NOTE: focus() API does not trigger focusIn event so set MRU node manually\n    state.mostRecentlyFocusedNode = node;\n\n    if (isSelectableInput(node)) {\n      node.select();\n    }\n  };\n\n  const getReturnFocusNode = function (previousActiveElement) {\n    const node = getNodeForOption('setReturnFocus', {\n      params: [previousActiveElement],\n    });\n    return node ? node : node === false ? false : previousActiveElement;\n  };\n\n  /**\n   * Finds the next node (in either direction) where focus should move according to a\n   *  keyboard focus-in event.\n   * @param {Object} params\n   * @param {Node} [params.target] Known target __from which__ to navigate, if any.\n   * @param {KeyboardEvent|FocusEvent} [params.event] Event to use if `target` isn't known (event\n   *  will be used to determine the `target`). Ignored if `target` is specified.\n   * @param {boolean} [params.isBackward] True if focus should move backward.\n   * @returns {Node|undefined} The next node, or `undefined` if a next node couldn't be\n   *  determined given the current state of the trap.\n   */\n  const findNextNavNode = function ({ target, event, isBackward = false }) {\n    target = target || getActualTarget(event);\n    updateTabbableNodes();\n\n    let destinationNode = null;\n\n    if (state.tabbableGroups.length > 0) {\n      // make sure the target is actually contained in a group\n      // NOTE: the target may also be the container itself if it's focusable\n      //  with tabIndex='-1' and was given initial focus\n      const containerIndex = findContainerIndex(target, event);\n      const containerGroup =\n        containerIndex >= 0 ? state.containerGroups[containerIndex] : undefined;\n\n      if (containerIndex < 0) {\n        // target not found in any group: quite possible focus has escaped the trap,\n        //  so bring it back into...\n        if (isBackward) {\n          // ...the last node in the last group\n          destinationNode =\n            state.tabbableGroups[state.tabbableGroups.length - 1]\n              .lastTabbableNode;\n        } else {\n          // ...the first node in the first group\n          destinationNode = state.tabbableGroups[0].firstTabbableNode;\n        }\n      } else if (isBackward) {\n        // REVERSE\n\n        // is the target the first tabbable node in a group?\n        let startOfGroupIndex = state.tabbableGroups.findIndex(\n          ({ firstTabbableNode }) => target === firstTabbableNode\n        );\n\n        if (\n          startOfGroupIndex < 0 &&\n          (containerGroup.container === target ||\n            (isFocusable(target, config.tabbableOptions) &&\n              !isTabbable(target, config.tabbableOptions) &&\n              !containerGroup.nextTabbableNode(target, false)))\n        ) {\n          // an exception case where the target is either the container itself, or\n          //  a non-tabbable node that was given focus (i.e. tabindex is negative\n          //  and user clicked on it or node was programmatically given focus)\n          //  and is not followed by any other tabbable node, in which\n          //  case, we should handle shift+tab as if focus were on the container's\n          //  first tabbable node, and go to the last tabbable node of the LAST group\n          startOfGroupIndex = containerIndex;\n        }\n\n        if (startOfGroupIndex >= 0) {\n          // YES: then shift+tab should go to the last tabbable node in the\n          //  previous group (and wrap around to the last tabbable node of\n          //  the LAST group if it's the first tabbable node of the FIRST group)\n          const destinationGroupIndex =\n            startOfGroupIndex === 0\n              ? state.tabbableGroups.length - 1\n              : startOfGroupIndex - 1;\n\n          const destinationGroup = state.tabbableGroups[destinationGroupIndex];\n\n          destinationNode =\n            getTabIndex(target) >= 0\n              ? destinationGroup.lastTabbableNode\n              : destinationGroup.lastDomTabbableNode;\n        } else if (!isTabEvent(event)) {\n          // user must have customized the nav keys so we have to move focus manually _within_\n          //  the active group: do this based on the order determined by tabbable()\n          destinationNode = containerGroup.nextTabbableNode(target, false);\n        }\n      } else {\n        // FORWARD\n\n        // is the target the last tabbable node in a group?\n        let lastOfGroupIndex = state.tabbableGroups.findIndex(\n          ({ lastTabbableNode }) => target === lastTabbableNode\n        );\n\n        if (\n          lastOfGroupIndex < 0 &&\n          (containerGroup.container === target ||\n            (isFocusable(target, config.tabbableOptions) &&\n              !isTabbable(target, config.tabbableOptions) &&\n              !containerGroup.nextTabbableNode(target)))\n        ) {\n          // an exception case where the target is the container itself, or\n          //  a non-tabbable node that was given focus (i.e. tabindex is negative\n          //  and user clicked on it or node was programmatically given focus)\n          //  and is not followed by any other tabbable node, in which\n          //  case, we should handle tab as if focus were on the container's\n          //  last tabbable node, and go to the first tabbable node of the FIRST group\n          lastOfGroupIndex = containerIndex;\n        }\n\n        if (lastOfGroupIndex >= 0) {\n          // YES: then tab should go to the first tabbable node in the next\n          //  group (and wrap around to the first tabbable node of the FIRST\n          //  group if it's the last tabbable node of the LAST group)\n          const destinationGroupIndex =\n            lastOfGroupIndex === state.tabbableGroups.length - 1\n              ? 0\n              : lastOfGroupIndex + 1;\n\n          const destinationGroup = state.tabbableGroups[destinationGroupIndex];\n\n          destinationNode =\n            getTabIndex(target) >= 0\n              ? destinationGroup.firstTabbableNode\n              : destinationGroup.firstDomTabbableNode;\n        } else if (!isTabEvent(event)) {\n          // user must have customized the nav keys so we have to move focus manually _within_\n          //  the active group: do this based on the order determined by tabbable()\n          destinationNode = containerGroup.nextTabbableNode(target);\n        }\n      }\n    } else {\n      // no groups available\n      // NOTE: the fallbackFocus option does not support returning false to opt-out\n      destinationNode = getNodeForOption('fallbackFocus');\n    }\n\n    return destinationNode;\n  };\n\n  // This needs to be done on mousedown and touchstart instead of click\n  // so that it precedes the focus event.\n  const checkPointerDown = function (e) {\n    const target = getActualTarget(e);\n\n    if (findContainerIndex(target, e) >= 0) {\n      // allow the click since it ocurred inside the trap\n      return;\n    }\n\n    if (valueOrHandler(config.clickOutsideDeactivates, e)) {\n      // immediately deactivate the trap\n      trap.deactivate({\n        // NOTE: by setting `returnFocus: false`, deactivate() will do nothing,\n        //  which will result in the outside click setting focus to the node\n        //  that was clicked (and if not focusable, to \"nothing\"); by setting\n        //  `returnFocus: true`, we'll attempt to re-focus the node originally-focused\n        //  on activation (or the configured `setReturnFocus` node), whether the\n        //  outside click was on a focusable node or not\n        returnFocus: config.returnFocusOnDeactivate,\n      });\n      return;\n    }\n\n    // This is needed for mobile devices.\n    // (If we'll only let `click` events through,\n    // then on mobile they will be blocked anyways if `touchstart` is blocked.)\n    if (valueOrHandler(config.allowOutsideClick, e)) {\n      // allow the click outside the trap to take place\n      return;\n    }\n\n    // otherwise, prevent the click\n    e.preventDefault();\n  };\n\n  // In case focus escapes the trap for some strange reason, pull it back in.\n  // NOTE: the focusIn event is NOT cancelable, so if focus escapes, it may cause unexpected\n  //  scrolling if the node that got focused was out of view; there's nothing we can do to\n  //  prevent that from happening by the time we discover that focus escaped\n  const checkFocusIn = function (event) {\n    const target = getActualTarget(event);\n    const targetContained = findContainerIndex(target, event) >= 0;\n\n    // In Firefox when you Tab out of an iframe the Document is briefly focused.\n    if (targetContained || target instanceof Document) {\n      if (targetContained) {\n        state.mostRecentlyFocusedNode = target;\n      }\n    } else {\n      // escaped! pull it back in to where it just left\n      event.stopImmediatePropagation();\n\n      // focus will escape if the MRU node had a positive tab index and user tried to nav forward;\n      //  it will also escape if the MRU node had a 0 tab index and user tried to nav backward\n      //  toward a node with a positive tab index\n      let nextNode; // next node to focus, if we find one\n      let navAcrossContainers = true;\n      if (state.mostRecentlyFocusedNode) {\n        if (getTabIndex(state.mostRecentlyFocusedNode) > 0) {\n          // MRU container index must be >=0 otherwise we wouldn't have it as an MRU node...\n          const mruContainerIdx = findContainerIndex(\n            state.mostRecentlyFocusedNode\n          );\n          // there MAY not be any tabbable nodes in the container if there are at least 2 containers\n          //  and the MRU node is focusable but not tabbable (focus-trap requires at least 1 container\n          //  with at least one tabbable node in order to function, so this could be the other container\n          //  with nothing tabbable in it)\n          const { tabbableNodes } = state.containerGroups[mruContainerIdx];\n          if (tabbableNodes.length > 0) {\n            // MRU tab index MAY not be found if the MRU node is focusable but not tabbable\n            const mruTabIdx = tabbableNodes.findIndex(\n              (node) => node === state.mostRecentlyFocusedNode\n            );\n            if (mruTabIdx >= 0) {\n              if (config.isKeyForward(state.recentNavEvent)) {\n                if (mruTabIdx + 1 < tabbableNodes.length) {\n                  nextNode = tabbableNodes[mruTabIdx + 1];\n                  navAcrossContainers = false;\n                }\n                // else, don't wrap within the container as focus should move to next/previous\n                //  container\n              } else {\n                if (mruTabIdx - 1 >= 0) {\n                  nextNode = tabbableNodes[mruTabIdx - 1];\n                  navAcrossContainers = false;\n                }\n                // else, don't wrap within the container as focus should move to next/previous\n                //  container\n              }\n              // else, don't find in container order without considering direction too\n            }\n          }\n          // else, no tabbable nodes in that container (which means we must have at least one other\n          //  container with at least one tabbable node in it, otherwise focus-trap would've thrown\n          //  an error the last time updateTabbableNodes() was run): find next node among all known\n          //  containers\n        } else {\n          // check to see if there's at least one tabbable node with a positive tab index inside\n          //  the trap because focus seems to escape when navigating backward from a tabbable node\n          //  with tabindex=0 when this is the case (instead of wrapping to the tabbable node with\n          //  the greatest positive tab index like it should)\n          if (\n            !state.containerGroups.some((g) =>\n              g.tabbableNodes.some((n) => getTabIndex(n) > 0)\n            )\n          ) {\n            // no containers with tabbable nodes with positive tab indexes which means the focus\n            //  escaped for some other reason and we should just execute the fallback to the\n            //  MRU node or initial focus node, if any\n            navAcrossContainers = false;\n          }\n        }\n      } else {\n        // no MRU node means we're likely in some initial condition when the trap has just\n        //  been activated and initial focus hasn't been given yet, in which case we should\n        //  fall through to trying to focus the initial focus node, which is what should\n        //  happen below at this point in the logic\n        navAcrossContainers = false;\n      }\n\n      if (navAcrossContainers) {\n        nextNode = findNextNavNode({\n          // move FROM the MRU node, not event-related node (which will be the node that is\n          //  outside the trap causing the focus escape we're trying to fix)\n          target: state.mostRecentlyFocusedNode,\n          isBackward: config.isKeyBackward(state.recentNavEvent),\n        });\n      }\n\n      if (nextNode) {\n        tryFocus(nextNode);\n      } else {\n        tryFocus(state.mostRecentlyFocusedNode || getInitialFocusNode());\n      }\n    }\n\n    state.recentNavEvent = undefined; // clear\n  };\n\n  // Hijack key nav events on the first and last focusable nodes of the trap,\n  // in order to prevent focus from escaping. If it escapes for even a\n  // moment it can end up scrolling the page and causing confusion so we\n  // kind of need to capture the action at the keydown phase.\n  const checkKeyNav = function (event, isBackward = false) {\n    state.recentNavEvent = event;\n\n    const destinationNode = findNextNavNode({ event, isBackward });\n    if (destinationNode) {\n      if (isTabEvent(event)) {\n        // since tab natively moves focus, we wouldn't have a destination node unless we\n        //  were on the edge of a container and had to move to the next/previous edge, in\n        //  which case we want to prevent default to keep the browser from moving focus\n        //  to where it normally would\n        event.preventDefault();\n      }\n      tryFocus(destinationNode);\n    }\n    // else, let the browser take care of [shift+]tab and move the focus\n  };\n\n  const checkTabKey = function (event) {\n    if (config.isKeyForward(event) || config.isKeyBackward(event)) {\n      checkKeyNav(event, config.isKeyBackward(event));\n    }\n  };\n\n  // we use a different event phase for the Escape key to allow canceling the event and checking for this in escapeDeactivates\n  const checkEscapeKey = function (event) {\n    if (\n      isEscapeEvent(event) &&\n      valueOrHandler(config.escapeDeactivates, event) !== false\n    ) {\n      event.preventDefault();\n      trap.deactivate();\n    }\n  };\n\n  const checkClick = function (e) {\n    const target = getActualTarget(e);\n\n    if (findContainerIndex(target, e) >= 0) {\n      return;\n    }\n\n    if (valueOrHandler(config.clickOutsideDeactivates, e)) {\n      return;\n    }\n\n    if (valueOrHandler(config.allowOutsideClick, e)) {\n      return;\n    }\n\n    e.preventDefault();\n    e.stopImmediatePropagation();\n  };\n\n  //\n  // EVENT LISTENERS\n  //\n\n  const addListeners = function () {\n    if (!state.active) {\n      return;\n    }\n\n    // There can be only one listening focus trap at a time\n    activeFocusTraps.activateTrap(trapStack, trap);\n\n    // Delay ensures that the focused element doesn't capture the event\n    // that caused the focus trap activation.\n    state.delayInitialFocusTimer = config.delayInitialFocus\n      ? delay(function () {\n          tryFocus(getInitialFocusNode());\n        })\n      : tryFocus(getInitialFocusNode());\n\n    doc.addEventListener('focusin', checkFocusIn, true);\n    doc.addEventListener('mousedown', checkPointerDown, {\n      capture: true,\n      passive: false,\n    });\n    doc.addEventListener('touchstart', checkPointerDown, {\n      capture: true,\n      passive: false,\n    });\n    doc.addEventListener('click', checkClick, {\n      capture: true,\n      passive: false,\n    });\n    doc.addEventListener('keydown', checkTabKey, {\n      capture: true,\n      passive: false,\n    });\n    doc.addEventListener('keydown', checkEscapeKey);\n\n    return trap;\n  };\n\n  const removeListeners = function () {\n    if (!state.active) {\n      return;\n    }\n\n    doc.removeEventListener('focusin', checkFocusIn, true);\n    doc.removeEventListener('mousedown', checkPointerDown, true);\n    doc.removeEventListener('touchstart', checkPointerDown, true);\n    doc.removeEventListener('click', checkClick, true);\n    doc.removeEventListener('keydown', checkTabKey, true);\n    doc.removeEventListener('keydown', checkEscapeKey);\n\n    return trap;\n  };\n\n  //\n  // MUTATION OBSERVER\n  //\n\n  const checkDomRemoval = function (mutations) {\n    const isFocusedNodeRemoved = mutations.some(function (mutation) {\n      const removedNodes = Array.from(mutation.removedNodes);\n      return removedNodes.some(function (node) {\n        return node === state.mostRecentlyFocusedNode;\n      });\n    });\n\n    // If the currently focused is removed then browsers will move focus to the\n    // <body> element. If this happens, try to move focus back into the trap.\n    if (isFocusedNodeRemoved) {\n      tryFocus(getInitialFocusNode());\n    }\n  };\n\n  // Use MutationObserver - if supported - to detect if focused node is removed\n  // from the DOM.\n  const mutationObserver =\n    typeof window !== 'undefined' && 'MutationObserver' in window\n      ? new MutationObserver(checkDomRemoval)\n      : undefined;\n\n  const updateObservedNodes = function () {\n    if (!mutationObserver) {\n      return;\n    }\n\n    mutationObserver.disconnect();\n    if (state.active && !state.paused) {\n      state.containers.map(function (container) {\n        mutationObserver.observe(container, {\n          subtree: true,\n          childList: true,\n        });\n      });\n    }\n  };\n\n  //\n  // TRAP DEFINITION\n  //\n\n  trap = {\n    get active() {\n      return state.active;\n    },\n\n    get paused() {\n      return state.paused;\n    },\n\n    activate(activateOptions) {\n      if (state.active) {\n        return this;\n      }\n\n      const onActivate = getOption(activateOptions, 'onActivate');\n      const onPostActivate = getOption(activateOptions, 'onPostActivate');\n      const checkCanFocusTrap = getOption(activateOptions, 'checkCanFocusTrap');\n\n      if (!checkCanFocusTrap) {\n        updateTabbableNodes();\n      }\n\n      state.active = true;\n      state.paused = false;\n      state.nodeFocusedBeforeActivation = getActiveElement(doc);\n\n      onActivate?.();\n\n      const finishActivation = () => {\n        if (checkCanFocusTrap) {\n          updateTabbableNodes();\n        }\n        addListeners();\n        updateObservedNodes();\n        onPostActivate?.();\n      };\n\n      if (checkCanFocusTrap) {\n        checkCanFocusTrap(state.containers.concat()).then(\n          finishActivation,\n          finishActivation\n        );\n        return this;\n      }\n\n      finishActivation();\n      return this;\n    },\n\n    deactivate(deactivateOptions) {\n      if (!state.active) {\n        return this;\n      }\n\n      const options = {\n        onDeactivate: config.onDeactivate,\n        onPostDeactivate: config.onPostDeactivate,\n        checkCanReturnFocus: config.checkCanReturnFocus,\n        ...deactivateOptions,\n      };\n\n      clearTimeout(state.delayInitialFocusTimer); // noop if undefined\n      state.delayInitialFocusTimer = undefined;\n\n      removeListeners();\n      state.active = false;\n      state.paused = false;\n      updateObservedNodes();\n\n      activeFocusTraps.deactivateTrap(trapStack, trap);\n\n      const onDeactivate = getOption(options, 'onDeactivate');\n      const onPostDeactivate = getOption(options, 'onPostDeactivate');\n      const checkCanReturnFocus = getOption(options, 'checkCanReturnFocus');\n      const returnFocus = getOption(\n        options,\n        'returnFocus',\n        'returnFocusOnDeactivate'\n      );\n\n      onDeactivate?.();\n\n      const finishDeactivation = () => {\n        delay(() => {\n          if (returnFocus) {\n            tryFocus(getReturnFocusNode(state.nodeFocusedBeforeActivation));\n          }\n          onPostDeactivate?.();\n        });\n      };\n\n      if (returnFocus && checkCanReturnFocus) {\n        checkCanReturnFocus(\n          getReturnFocusNode(state.nodeFocusedBeforeActivation)\n        ).then(finishDeactivation, finishDeactivation);\n        return this;\n      }\n\n      finishDeactivation();\n      return this;\n    },\n\n    pause(pauseOptions) {\n      if (!state.active) {\n        return this;\n      }\n\n      state.manuallyPaused = true;\n\n      return this._setPausedState(true, pauseOptions);\n    },\n\n    unpause(unpauseOptions) {\n      if (!state.active) {\n        return this;\n      }\n\n      state.manuallyPaused = false;\n\n      if (trapStack[trapStack.length - 1] !== this) {\n        return this;\n      }\n\n      return this._setPausedState(false, unpauseOptions);\n    },\n\n    updateContainerElements(containerElements) {\n      const elementsAsArray = [].concat(containerElements).filter(Boolean);\n\n      state.containers = elementsAsArray.map((element) =>\n        typeof element === 'string' ? doc.querySelector(element) : element\n      );\n\n      if (state.active) {\n        updateTabbableNodes();\n      }\n\n      updateObservedNodes();\n\n      return this;\n    },\n  };\n\n  Object.defineProperties(trap, {\n    _isManuallyPaused: {\n      value() {\n        return state.manuallyPaused;\n      },\n    },\n    _setPausedState: {\n      value(paused, options) {\n        if (state.paused === paused) {\n          return this;\n        }\n\n        state.paused = paused;\n        if (paused) {\n          const onPause = getOption(options, 'onPause');\n          const onPostPause = getOption(options, 'onPostPause');\n          onPause?.();\n\n          removeListeners();\n          updateObservedNodes();\n\n          onPostPause?.();\n        } else {\n          const onUnpause = getOption(options, 'onUnpause');\n          const onPostUnpause = getOption(options, 'onPostUnpause');\n\n          onUnpause?.();\n\n          updateTabbableNodes();\n          addListeners();\n          updateObservedNodes();\n\n          onPostUnpause?.();\n        }\n\n        return this;\n      },\n    },\n  });\n\n  // initialize container elements\n  trap.updateContainerElements(elements);\n\n  return trap;\n};\n\nexport { createFocusTrap };\n"], "names": ["activeFocusTraps", "trapStack", "trap", "length", "activeTrap", "_setPausedState", "trapIndex", "indexOf", "splice", "push", "_isManuallyPaused", "isTabEvent", "e", "key", "keyCode", "isKeyForward", "shift<PERSON>ey", "isKeyBackward", "delay", "fn", "setTimeout", "valueOrHandler", "value", "_len", "arguments", "params", "Array", "_key", "apply", "getActualTarget", "event", "target", "shadowRoot", "<PERSON><PERSON><PERSON>", "internalTrapStack", "createFocusTrap", "elements", "userOptions", "doc", "document", "config", "_objectSpread", "returnFocusOnDeactivate", "escapeDeactivates", "delayInitialFocus", "state", "containers", "containerGroups", "tabbableGroups", "nodeFocusedBeforeActivation", "mostRecentlyFocusedNode", "active", "paused", "manuallyPaused", "delayInitialFocusTimer", "undefined", "recentNavEvent", "getOption", "configOverrideOptions", "optionName", "configOptionName", "findContainerIndex", "element", "findIndex", "_ref", "container", "tabbableNodes", "contains", "includes", "find", "node", "getNodeForOption", "_ref2", "_ref2$hasFallback", "<PERSON><PERSON><PERSON><PERSON>", "_ref2$params", "optionValue", "_toConsumableArray", "Error", "concat", "querySelector", "err", "message", "getInitialFocusNode", "isFocusable", "tabbableOptions", "activeElement", "firstTabbableGroup", "firstTabbableNode", "updateTabbableNodes", "map", "tabbable", "focusableNodes", "focusable", "lastTabbableNode", "firstDomTabbableNode", "isTabbable", "lastDomTabbableNode", "slice", "reverse", "posTabIndexesFound", "getTabIndex", "nextTabbableNode", "forward", "nodeIdx", "el", "filter", "group", "g", "getActiveElement", "tryFocus", "focus", "preventScroll", "tagName", "toLowerCase", "select", "isSelectableInput", "getReturnFocusNode", "previousActiveElement", "findNextNavNode", "_ref3", "_ref3$isBackward", "isBackward", "destinationNode", "containerIndex", "containerGroup", "startOfGroupIndex", "_ref4", "destinationGroupIndex", "destinationGroup", "lastOfGroupIndex", "_ref5", "checkPointerDown", "clickOutsideDeactivates", "deactivate", "returnFocus", "allowOutsideClick", "preventDefault", "checkFocusIn", "targetContained", "Document", "nextNode", "stopImmediatePropagation", "navAcrossContainers", "mruContainerIdx", "mruTabIdx", "some", "n", "checkTabKey", "checkKeyNav", "checkEscapeKey", "checkClick", "addListeners", "addEventListener", "capture", "passive", "removeListeners", "removeEventListener", "mutationObserver", "window", "MutationObserver", "mutations", "mutation", "from", "removedNodes", "updateObservedNodes", "disconnect", "observe", "subtree", "childList", "activate", "activateOptions", "this", "onActivate", "onPostActivate", "checkCanFocusTrap", "finishActivation", "then", "deactivateOptions", "options", "onDeactivate", "onPostDeactivate", "checkCanReturnFocus", "clearTimeout", "finishDeactivation", "pause", "pauseOptions", "unpause", "unpauseOptions", "updateContainerElements", "containerElements", "elementsAsArray", "Boolean", "Object", "defineProperties", "onPause", "onPostPause", "onUnpause", "onPostUnpause"], "mappings": ";;;;ozDAQA,IAAMA,WACSC,EAAWC,GACtB,GAAID,EAAUE,OAAS,EAAG,CACxB,IAAMC,EAAaH,EAAUA,EAAUE,OAAS,GAC5CC,IAAeF,GACjBE,EAAWC,iBAAgB,EAE/B,CAEA,IAAMC,EAAYL,EAAUM,QAAQL,IAClB,IAAdI,GAIFL,EAAUO,OAAOF,EAAW,GAH5BL,EAAUQ,KAAKP,EAMlB,EAjBGF,WAmBWC,EAAWC,GACxB,IAAMI,EAAYL,EAAUM,QAAQL,IAClB,IAAdI,GACFL,EAAUO,OAAOF,EAAW,GAI5BL,EAAUE,OAAS,IAClBF,EAAUA,EAAUE,OAAS,GAAGO,qBAEjCT,EAAUA,EAAUE,OAAS,GAAGE,iBAAgB,EAEpD,EAeIM,EAAa,SAAUC,GAC3B,MAAkB,SAAXA,eAAAA,EAAGC,MAAgC,KAAfD,aAAC,EAADA,EAAGE,QAChC,EAGMC,EAAe,SAAUH,GAC7B,OAAOD,EAAWC,KAAOA,EAAEI,QAC7B,EAGMC,EAAgB,SAAUL,GAC9B,OAAOD,EAAWC,IAAMA,EAAEI,QAC5B,EAEME,EAAQ,SAAUC,GACtB,OAAOC,WAAWD,EAAI,EACxB,EASME,EAAiB,SAAUC,GAAkB,IAAAC,IAAAA,EAAAC,UAAArB,OAARsB,MAAMC,MAAAH,EAAAA,EAAAA,OAAAI,EAAA,EAAAA,EAAAJ,EAAAI,IAANF,EAAME,EAAAH,GAAAA,UAAAG,GAC/C,MAAwB,mBAAVL,EAAuBA,EAAKM,WAAIH,EAAAA,GAAUH,CAC1D,EAEMO,EAAkB,SAAUC,GAQhC,OAAOA,EAAMC,OAAOC,YAA4C,mBAAvBF,EAAMG,aAC3CH,EAAMG,eAAe,GACrBH,EAAMC,MACZ,EAIMG,EAAoB,GAEpBC,EAAkB,SAAUC,EAAUC,GAG1C,IAwDInC,EAxDEoC,GAAMD,aAAW,EAAXA,EAAaE,WAAYA,SAE/BtC,GAAYoC,aAAW,EAAXA,EAAapC,YAAaiC,EAEtCM,EAAMC,EAAA,CACVC,yBAAyB,EACzBC,mBAAmB,EACnBC,mBAAmB,EACnB7B,aAAAA,EACAE,cAAAA,GACGoB,GAGCQ,EAAQ,CAGZC,WAAY,GAkBZC,gBAAiB,GAMjBC,eAAgB,GAEhBC,4BAA6B,KAC7BC,wBAAyB,KACzBC,QAAQ,EACRC,QAAQ,EACRC,gBAAgB,EAIhBC,4BAAwBC,EAGxBC,oBAAgBD,GAaZE,EAAY,SAACC,EAAuBC,EAAYC,GACpD,OAAOF,QACiCH,IAAtCG,EAAsBC,GACpBD,EAAsBC,GACtBnB,EAAOoB,GAAoBD,EAChC,EAYKE,EAAqB,SAAUC,EAAShC,GAC5C,IAAMG,EAC2B,mBAAxBH,eAAAA,EAAOG,cACVH,EAAMG,oBACNsB,EAIN,OAAOV,EAAME,gBAAgBgB,WAC3B,SAAAC,GAAA,IAAGC,EAASD,EAATC,UAAWC,EAAaF,EAAbE,cAAa,OACzBD,EAAUE,SAASL,KAKnB7B,aAAAA,EAAAA,EAAcmC,SAASH,KACvBC,EAAcG,MAAK,SAACC,GAAI,OAAKA,IAASR,IAAQ,GAEnD,EAoBKS,EAAmB,SACvBZ,GAEA,IAAAa,EAAAhD,UAAArB,OAAA,QAAAoD,IAAA/B,UAAA,GAAAA,UAAA,GADuC,CAAE,EAAAiD,EAAAD,EAAvCE,YAAAA,OAAc,IAAHD,GAAQA,EAAAE,EAAAH,EAAE/C,OAAAA,OAAS,IAAHkD,EAAG,GAAEA,EAE9BC,EAAcpC,EAAOmB,GAUzB,GAR2B,mBAAhBiB,IACTA,EAAcA,EAAWhD,aAAAiD,EAAIpD,MAGX,IAAhBmD,IACFA,OAAcrB,IAGXqB,EAAa,CAChB,QAAoBrB,IAAhBqB,IAA6C,IAAhBA,EAC/B,OAAOA,EAIT,MAAM,IAAIE,MAAK,IAAAC,OACRpB,kEAET,CAEA,IAAIW,EAAOM,EAEX,GAA2B,iBAAhBA,EAA0B,CACnC,IACEN,EAAOhC,EAAI0C,cAAcJ,EAC1B,CAAC,MAAOK,GACP,MAAM,IAAIH,MAAKC,IAAAA,OACRpB,EAAUoB,gDAAAA,OAAgDE,EAAIC,aAEvE,CAEA,IAAKZ,IACEI,EACH,MAAM,IAAII,MAAK,IAAAC,OACRpB,2CAMb,CAEA,OAAOW,CACR,EAEKa,EAAsB,WAC1B,IAAIb,EAAOC,EAAiB,eAAgB,CAAEG,aAAa,IAG3D,IAAa,IAATJ,EACF,OAAO,EAGT,QACWf,IAATe,GACCA,IAASc,EAAYd,EAAM9B,EAAO6C,iBAGnC,GAAIxB,EAAmBvB,EAAIgD,gBAAkB,EAC3ChB,EAAOhC,EAAIgD,kBACN,CACL,IAAMC,EAAqB1C,EAAMG,eAAe,GAKhDsB,EAHEiB,GAAsBA,EAAmBC,mBAGfjB,EAAiB,gBAC/C,MACkB,OAATD,IAGTA,EAAOC,EAAiB,kBAG1B,IAAKD,EACH,MAAM,IAAIQ,MACR,gEAIJ,OAAOR,CACR,EAEKmB,EAAsB,WA4F1B,GA3FA5C,EAAME,gBAAkBF,EAAMC,WAAW4C,KAAI,SAACzB,GAC5C,IAAMC,EAAgByB,EAAS1B,EAAWzB,EAAO6C,iBAK3CO,EAAiBC,EAAU5B,EAAWzB,EAAO6C,iBAE7CG,EACJtB,EAAc/D,OAAS,EAAI+D,EAAc,QAAKX,EAC1CuC,EACJ5B,EAAc/D,OAAS,EACnB+D,EAAcA,EAAc/D,OAAS,QACrCoD,EAEAwC,EAAuBH,EAAevB,MAAK,SAACC,GAAI,OACpD0B,EAAW1B,EAAK,IAEZ2B,EAAsBL,EACzBM,QACAC,UACA9B,MAAK,SAACC,GAAI,OAAK0B,EAAW1B,MAEvB8B,IAAuBlC,EAAcG,MACzC,SAACC,GAAI,OAAK+B,EAAY/B,GAAQ,CAAC,IAGjC,MAAO,CACLL,UAAAA,EACAC,cAAAA,EACA0B,eAAAA,EAGAQ,mBAAAA,EAGAZ,kBAAAA,EAEAM,iBAAAA,EAUAC,qBAAAA,EAEAE,oBAAAA,EAUAK,iBAAAA,SAAiBhC,GAAsB,IAAhBiC,IAAO/E,UAAArB,OAAA,QAAAoD,IAAA/B,UAAA,KAAAA,UAAA,GACtBgF,EAAUtC,EAAc3D,QAAQ+D,GACtC,OAAIkC,EAAU,EAORD,EACKX,EACJM,MAAMN,EAAerF,QAAQ+D,GAAQ,GACrCD,MAAK,SAACoC,GAAE,OAAKT,EAAWS,MAGtBb,EACJM,MAAM,EAAGN,EAAerF,QAAQ+D,IAChC6B,UACA9B,MAAK,SAACoC,GAAE,OAAKT,EAAWS,MAGtBvC,EAAcsC,GAAWD,EAAU,GAAM,GAClD,EAEJ,IAEA1D,EAAMG,eAAiBH,EAAME,gBAAgB2D,QAC3C,SAACC,GAAK,OAAKA,EAAMzC,cAAc/D,OAAS,CAAC,IAKzC0C,EAAMG,eAAe7C,QAAU,IAC9BoE,EAAiB,iBAElB,MAAM,IAAIO,MACR,uGAWJ,GACEjC,EAAME,gBAAgBsB,MAAK,SAACuC,GAAC,OAAKA,EAAER,kBAAmB,KACvDvD,EAAME,gBAAgB5C,OAAS,EAE/B,MAAM,IAAI2E,MACR,gLAGL,EAUK+B,EAAmB,SAAUJ,GACjC,IAAMnB,EAAgBmB,EAAGnB,cAEzB,GAAKA,EAIL,OACEA,EAActD,YAC6B,OAA3CsD,EAActD,WAAWsD,cAElBuB,EAAiBvB,EAActD,YAGjCsD,CACR,EAEKwB,EAAW,SAAUxC,IACZ,IAATA,GAIAA,IAASuC,EAAiBtE,YAIzB+B,GAASA,EAAKyC,OAKnBzC,EAAKyC,MAAM,CAAEC,gBAAiBxE,EAAOwE,gBAErCnE,EAAMK,wBAA0BoB,EA7aV,SAAUA,GAClC,OACEA,EAAK2C,SAC0B,UAA/B3C,EAAK2C,QAAQC,eACU,mBAAhB5C,EAAK6C,MAEhB,CAyaQC,CAAkB9C,IACpBA,EAAK6C,UATLL,EAAS3B,KAWZ,EAEKkC,EAAqB,SAAUC,GACnC,IAAMhD,EAAOC,EAAiB,iBAAkB,CAC9C9C,OAAQ,CAAC6F,KAEX,OAAOhD,IAAuB,IAATA,GAAyBgD,CAC/C,EAaKC,EAAkB,SAAHC,GAAoD,IAArCzF,EAAMyF,EAANzF,OAAQD,EAAK0F,EAAL1F,MAAK2F,EAAAD,EAAEE,WAAAA,OAAa,IAAHD,GAAQA,EACnE1F,EAASA,GAAUF,EAAgBC,GACnC2D,IAEA,IAAIkC,EAAkB,KAEtB,GAAI9E,EAAMG,eAAe7C,OAAS,EAAG,CAInC,IAAMyH,EAAiB/D,EAAmB9B,EAAQD,GAC5C+F,EACJD,GAAkB,EAAI/E,EAAME,gBAAgB6E,QAAkBrE,EAEhE,GAAIqE,EAAiB,EAKjBD,EAFED,EAGA7E,EAAMG,eAAeH,EAAMG,eAAe7C,OAAS,GAChD2F,iBAGajD,EAAMG,eAAe,GAAGwC,uBAEvC,GAAIkC,EAAY,CAIrB,IAAII,EAAoBjF,EAAMG,eAAee,WAC3C,SAAAgE,GAAA,IAAGvC,EAAiBuC,EAAjBvC,kBAAiB,OAAOzD,IAAWyD,CAAiB,IAmBzD,GAfEsC,EAAoB,IACnBD,EAAe5D,YAAclC,GAC3BqD,EAAYrD,EAAQS,EAAO6C,mBACzBW,EAAWjE,EAAQS,EAAO6C,mBAC1BwC,EAAevB,iBAAiBvE,GAAQ,MAQ7C+F,EAAoBF,GAGlBE,GAAqB,EAAG,CAI1B,IAAME,EACkB,IAAtBF,EACIjF,EAAMG,eAAe7C,OAAS,EAC9B2H,EAAoB,EAEpBG,EAAmBpF,EAAMG,eAAegF,GAE9CL,EACEtB,EAAYtE,IAAW,EACnBkG,EAAiBnC,iBACjBmC,EAAiBhC,mBACzB,MAAYtF,EAAWmB,KAGrB6F,EAAkBE,EAAevB,iBAAiBvE,GAAQ,GAE9D,KAAO,CAIL,IAAImG,EAAmBrF,EAAMG,eAAee,WAC1C,SAAAoE,GAAA,IAAGrC,EAAgBqC,EAAhBrC,iBAAgB,OAAO/D,IAAW+D,CAAgB,IAmBvD,GAfEoC,EAAmB,IAClBL,EAAe5D,YAAclC,GAC3BqD,EAAYrD,EAAQS,EAAO6C,mBACzBW,EAAWjE,EAAQS,EAAO6C,mBAC1BwC,EAAevB,iBAAiBvE,MAQrCmG,EAAmBN,GAGjBM,GAAoB,EAAG,CAIzB,IAAMF,EACJE,IAAqBrF,EAAMG,eAAe7C,OAAS,EAC/C,EACA+H,EAAmB,EAEnBD,EAAmBpF,EAAMG,eAAegF,GAE9CL,EACEtB,EAAYtE,IAAW,EACnBkG,EAAiBzC,kBACjByC,EAAiBlC,oBACzB,MAAYpF,EAAWmB,KAGrB6F,EAAkBE,EAAevB,iBAAiBvE,GAEtD,CACF,MAGE4F,EAAkBpD,EAAiB,iBAGrC,OAAOoD,CACR,EAIKS,EAAmB,SAAUxH,GACjC,IAAMmB,EAASF,EAAgBjB,GAE3BiD,EAAmB9B,EAAQnB,IAAM,IAKjCS,EAAemB,EAAO6F,wBAAyBzH,GAEjDV,EAAKoI,WAAW,CAOdC,YAAa/F,EAAOE,0BAQpBrB,EAAemB,EAAOgG,kBAAmB5H,IAM7CA,EAAE6H,iBACH,EAMKC,EAAe,SAAU5G,GAC7B,IAAMC,EAASF,EAAgBC,GACzB6G,EAAkB9E,EAAmB9B,EAAQD,IAAU,EAG7D,GAAI6G,GAAmB5G,aAAkB6G,SACnCD,IACF9F,EAAMK,wBAA0BnB,OAE7B,CAOL,IAAI8G,EALJ/G,EAAMgH,2BAMN,IAAIC,GAAsB,EAC1B,GAAIlG,EAAMK,wBACR,GAAImD,EAAYxD,EAAMK,yBAA2B,EAAG,CAElD,IAAM8F,EAAkBnF,EACtBhB,EAAMK,yBAMAgB,EAAkBrB,EAAME,gBAAgBiG,GAAxC9E,cACR,GAAIA,EAAc/D,OAAS,EAAG,CAE5B,IAAM8I,EAAY/E,EAAcH,WAC9B,SAACO,GAAI,OAAKA,IAASzB,EAAMK,uBAAuB,IAE9C+F,GAAa,IACXzG,EAAOzB,aAAa8B,EAAMW,gBACxByF,EAAY,EAAI/E,EAAc/D,SAChC0I,EAAW3E,EAAc+E,EAAY,GACrCF,GAAsB,GAKpBE,EAAY,GAAK,IACnBJ,EAAW3E,EAAc+E,EAAY,GACrCF,GAAsB,GAO9B,CAKF,MAMKlG,EAAME,gBAAgBmG,MAAK,SAACtC,GAAC,OAC5BA,EAAE1C,cAAcgF,MAAK,SAACC,GAAC,OAAK9C,EAAY8C,GAAK,IAAE,MAMjDJ,GAAsB,QAQ1BA,GAAsB,EAGpBA,IACFF,EAAWtB,EAAgB,CAGzBxF,OAAQc,EAAMK,wBACdwE,WAAYlF,EAAOvB,cAAc4B,EAAMW,mBAKzCsD,EADE+B,IAGOhG,EAAMK,yBAA2BiC,KAE9C,CAEAtC,EAAMW,oBAAiBD,CACxB,EAuBK6F,EAAc,SAAUtH,IACxBU,EAAOzB,aAAae,IAAUU,EAAOvB,cAAca,KAlBrC,SAAUA,GAA2B,IAApB4F,EAAUlG,UAAArB,OAAA,QAAAoD,IAAA/B,UAAA,IAAAA,UAAA,GAC7CqB,EAAMW,eAAiB1B,EAEvB,IAAM6F,EAAkBJ,EAAgB,CAAEzF,MAAAA,EAAO4F,WAAAA,IAC7CC,IACEhH,EAAWmB,IAKbA,EAAM2G,iBAER3B,EAASa,GAGZ,CAIG0B,CAAYvH,EAAOU,EAAOvB,cAAca,GAE3C,EAGKwH,EAAiB,SAAUxH,GAluBb,IAAUlB,EACZ,YAAXA,OADuBA,EAouBZkB,QAnuBXlB,EAAAA,EAAGC,MAA+B,SAAXD,aAAAA,EAAAA,EAAGC,MAAgC,MAAfD,aAAAA,EAAAA,EAAGE,WAouBG,IAApDO,EAAemB,EAAOG,kBAAmBb,KAEzCA,EAAM2G,iBACNvI,EAAKoI,aAER,EAEKiB,EAAa,SAAU3I,GAC3B,IAAMmB,EAASF,EAAgBjB,GAE3BiD,EAAmB9B,EAAQnB,IAAM,GAIjCS,EAAemB,EAAO6F,wBAAyBzH,IAI/CS,EAAemB,EAAOgG,kBAAmB5H,KAI7CA,EAAE6H,iBACF7H,EAAEkI,2BACH,EAMKU,EAAe,WACnB,GAAK3G,EAAMM,OAkCX,OA7BAnD,EAA8BC,EAAWC,GAIzC2C,EAAMS,uBAAyBd,EAAOI,kBAClC1B,GAAM,WACJ4F,EAAS3B,IACX,IACA2B,EAAS3B,KAEb7C,EAAImH,iBAAiB,UAAWf,GAAc,GAC9CpG,EAAImH,iBAAiB,YAAarB,EAAkB,CAClDsB,SAAS,EACTC,SAAS,IAEXrH,EAAImH,iBAAiB,aAAcrB,EAAkB,CACnDsB,SAAS,EACTC,SAAS,IAEXrH,EAAImH,iBAAiB,QAASF,EAAY,CACxCG,SAAS,EACTC,SAAS,IAEXrH,EAAImH,iBAAiB,UAAWL,EAAa,CAC3CM,SAAS,EACTC,SAAS,IAEXrH,EAAImH,iBAAiB,UAAWH,GAEzBpJ,CACR,EAEK0J,EAAkB,WACtB,GAAK/G,EAAMM,OAWX,OAPAb,EAAIuH,oBAAoB,UAAWnB,GAAc,GACjDpG,EAAIuH,oBAAoB,YAAazB,GAAkB,GACvD9F,EAAIuH,oBAAoB,aAAczB,GAAkB,GACxD9F,EAAIuH,oBAAoB,QAASN,GAAY,GAC7CjH,EAAIuH,oBAAoB,UAAWT,GAAa,GAChD9G,EAAIuH,oBAAoB,UAAWP,GAE5BpJ,CACR,EAuBK4J,EACc,oBAAXC,QAA0B,qBAAsBA,OACnD,IAAIC,kBAnBc,SAAUC,GACHA,EAAUf,MAAK,SAAUgB,GAEpD,OADqBxI,MAAMyI,KAAKD,EAASE,cACrBlB,MAAK,SAAU5E,GACjC,OAAOA,IAASzB,EAAMK,uBACxB,GACF,KAKE4D,EAAS3B,IAEZ,SAOK5B,EAEA8G,EAAsB,WACrBP,IAILA,EAAiBQ,aACbzH,EAAMM,SAAWN,EAAMO,QACzBP,EAAMC,WAAW4C,KAAI,SAAUzB,GAC7B6F,EAAiBS,QAAQtG,EAAW,CAClCuG,SAAS,EACTC,WAAW,GAEf,IAEH,EAgMD,OA1LAvK,EAAO,CACL,UAAIiD,GACF,OAAON,EAAMM,MACd,EAED,UAAIC,GACF,OAAOP,EAAMO,MACd,EAEDsH,SAAAA,SAASC,GACP,GAAI9H,EAAMM,OACR,OAAOyH,KAGT,IAAMC,EAAapH,EAAUkH,EAAiB,cACxCG,EAAiBrH,EAAUkH,EAAiB,kBAC5CI,EAAoBtH,EAAUkH,EAAiB,qBAEhDI,GACHtF,IAGF5C,EAAMM,QAAS,EACfN,EAAMO,QAAS,EACfP,EAAMI,4BAA8B4D,EAAiBvE,GAErDuI,SAAAA,IAEA,IAAMG,EAAmB,WACnBD,GACFtF,IAEF+D,IACAa,IACAS,SAAAA,GACD,EAED,OAAIC,GACFA,EAAkBlI,EAAMC,WAAWiC,UAAUkG,KAC3CD,EACAA,GAEKJ,OAGTI,IACOJ,KACR,EAEDtC,WAAAA,SAAW4C,GACT,IAAKrI,EAAMM,OACT,OAAOyH,KAGT,IAAMO,EAAO1I,EAAA,CACX2I,aAAc5I,EAAO4I,aACrBC,iBAAkB7I,EAAO6I,iBACzBC,oBAAqB9I,EAAO8I,qBACzBJ,GAGLK,aAAa1I,EAAMS,wBACnBT,EAAMS,4BAAyBC,EAE/BqG,IACA/G,EAAMM,QAAS,EACfN,EAAMO,QAAS,EACfiH,IAEArK,EAAgCC,EAAWC,GAE3C,IAAMkL,EAAe3H,EAAU0H,EAAS,gBAClCE,EAAmB5H,EAAU0H,EAAS,oBACtCG,EAAsB7H,EAAU0H,EAAS,uBACzC5C,EAAc9E,EAClB0H,EACA,cACA,2BAGFC,SAAAA,IAEA,IAAMI,EAAqB,WACzBtK,GAAM,WACAqH,GACFzB,EAASO,EAAmBxE,EAAMI,8BAEpCoI,SAAAA,GACF,GACD,EAED,OAAI9C,GAAe+C,GACjBA,EACEjE,EAAmBxE,EAAMI,8BACzBgI,KAAKO,EAAoBA,GACpBZ,OAGTY,IACOZ,KACR,EAEDa,MAAAA,SAAMC,GACJ,OAAK7I,EAAMM,QAIXN,EAAMQ,gBAAiB,EAEhBuH,KAAKvK,iBAAgB,EAAMqL,IALzBd,IAMV,EAEDe,QAAAA,SAAQC,GACN,OAAK/I,EAAMM,QAIXN,EAAMQ,gBAAiB,EAEnBpD,EAAUA,EAAUE,OAAS,KAAOyK,KAC/BA,KAGFA,KAAKvK,iBAAgB,EAAOuL,IAT1BhB,IAUV,EAEDiB,wBAAAA,SAAwBC,GACtB,IAAMC,EAAkB,GAAGhH,OAAO+G,GAAmBpF,OAAOsF,SAY5D,OAVAnJ,EAAMC,WAAaiJ,EAAgBrG,KAAI,SAAC5B,GAAO,MAC1B,iBAAZA,EAAuBxB,EAAI0C,cAAclB,GAAWA,CAAO,IAGhEjB,EAAMM,QACRsC,IAGF4E,IAEOO,IACT,GAGFqB,OAAOC,iBAAiBhM,EAAM,CAC5BQ,kBAAmB,CACjBY,MAAK,WACH,OAAOuB,EAAMQ,cACf,GAEFhD,gBAAiB,CACfiB,eAAM8B,EAAQ+H,GACZ,GAAItI,EAAMO,SAAWA,EACnB,OAAOwH,KAIT,GADA/H,EAAMO,OAASA,EACXA,EAAQ,CACV,IAAM+I,EAAU1I,EAAU0H,EAAS,WAC7BiB,EAAc3I,EAAU0H,EAAS,eACvCgB,SAAAA,IAEAvC,IACAS,IAEA+B,SAAAA,GACF,KAAO,CACL,IAAMC,EAAY5I,EAAU0H,EAAS,aAC/BmB,EAAgB7I,EAAU0H,EAAS,iBAEzCkB,SAAAA,IAEA5G,IACA+D,IACAa,IAEAiC,SAAAA,GACF,CAEA,OAAO1B,IACT,KAKJ1K,EAAK2L,wBAAwBzJ,GAEtBlC,CACT"}