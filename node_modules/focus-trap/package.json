{"name": "focus-trap", "version": "7.6.5", "description": "Trap focus within a DOM node.", "main": "dist/focus-trap.js", "module": "dist/focus-trap.esm.js", "types": "index.d.ts", "sideEffects": false, "files": ["package.json", "README.md", "CHANGELOG.md", "SECURITY.md", "LICENSE", "index.js", "index.d.ts", "dist"], "scripts": {"demo-bundle": "npm run compile:demo", "format": "prettier --write \"{*,src/**/*,test/**/*,docs/js/**/*,.github/workflows/**/*,cypress/**/*}.+(js|cjs|mjs|ts|cts|mts|yml)\"", "format:check": "prettier --check \"{*,src/**/*,test/**/*,docs/js/**/*,.github/workflows/**/*,cypress/**/*}.+(js|cjs|mjs|ts|cts|mts|yml)\"", "format:watch": "onchange \"{*,src/**/*,test/**/*,docs/js/**/*,.github/workflows/**/*,cypress/**/*}.+(js|cjs|mjs|ts|cts|mts|yml)\" -- prettier --write {{changed}}", "lint": "eslint \"{*,docs/js/**/*,cypress/e2e/**/*}.+(js|cjs|mjs|ts|cts|mts)\"", "clean": "rm -rf ./dist", "compile:esm": "cross-env BUILD_ENV=esm BABEL_ENV=esm rollup -c", "compile:cjs": "cross-env BUILD_ENV=cjs BABEL_ENV=es5 rollup -c", "compile:umd": "cross-env BUILD_ENV=umd BABEL_ENV=es5 rollup -c", "compile:demo": "cross-env BUILD_ENV=demo BABEL_ENV=es5 rollup -c", "compile": "npm run compile:esm && npm run compile:cjs && npm run compile:umd", "build": "npm run clean && npm run compile", "start": "npm run compile:demo -- --watch --environment SERVE,RELOAD", "start:cypress": "npm run compile:demo -- --environment SERVE,IS_CYPRESS_ENV:\"$CYPRESS_BROWSER\"", "test:types": "tsc index.d.ts", "test:unit": "echo \"No unit tests to run!\"", "test:e2e": "ELECTRON_ENABLE_LOGGING=1 start-server-and-test start:cypress 9966 'cypress run --browser $CYPRESS_BROWSER --headless'", "test:e2e:chrome": "CYPRESS_BROWSER=chrome npm run test:e2e", "test:e2e:dev": "ELECTRON_ENABLE_LOGGING=1 start-server-and-test start:cypress 9966 'cypress open'", "test": "npm run format:check && npm run lint && npm run test:unit && npm run test:types && npm run test:e2e:chrome", "prepare": "npm run build", "prepublishOnly": "npm run test && npm run build", "release": "npm run build && changeset publish", "all-contributors": "all-contributors"}, "repository": {"type": "git", "url": "git+https://github.com/focus-trap/focus-trap.git"}, "keywords": ["focus", "accessibility", "trap", "capture", "keyboard", "modal"], "author": {"name": "<PERSON>", "url": "http://davidtheclark.com/"}, "license": "MIT", "bugs": {"url": "https://github.com/focus-trap/focus-trap/issues"}, "homepage": "https://github.com/focus-trap/focus-trap#readme", "dependencies": {"tabbable": "^6.2.0"}, "devDependencies": {"@babel/cli": "^7.27.2", "@babel/core": "^7.27.1", "@babel/eslint-parser": "^7.27.1", "@babel/eslint-plugin": "^7.27.1", "@babel/preset-env": "^7.27.2", "@changesets/cli": "^2.29.4", "@eslint/js": "^9.27.0", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^28.0.3", "@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-replace": "^6.0.2", "@rollup/plugin-terser": "^0.4.4", "@testing-library/cypress": "^10.0.3", "@types/jquery": "^3.5.32", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "all-contributors-cli": "^6.26.1", "babel-loader": "^10.0.0", "cross-env": "^7.0.3", "cypress": "^14.4.0", "cypress-plugin-tab": "^1.0.5", "eslint": "^9.27.0", "eslint-config-prettier": "^10.1.5", "eslint-import-resolver-node": "^0.3.9", "eslint-import-resolver-typescript": "^4.3.5", "eslint-plugin-cypress": "^4.3.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jest": "^28.11.0", "eslint-plugin-jest-dom": "^5.5.0", "eslint-plugin-testing-library": "^7.2.1", "globals": "^16.1.0", "onchange": "^7.1.0", "prettier": "^3.5.3", "rollup": "^4.41.0", "rollup-plugin-livereload": "^2.0.5", "rollup-plugin-serve": "^3.0.0", "start-server-and-test": "^2.0.12", "typescript": "^5.8.3"}}