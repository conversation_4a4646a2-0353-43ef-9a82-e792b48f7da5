{"name": "@algolia/requester-fetch", "version": "5.27.0", "description": "Promise-based request library using Fetch.", "repository": {"type": "git", "url": "git+https://github.com/algolia/algoliasearch-client-javascript.git"}, "homepage": "https://github.com/algolia/algoliasearch-client-javascript#readme", "license": "MIT", "author": "Algolia", "type": "module", "exports": {".": {"node": {"types": {"import": "./dist/requester.fetch.node.d.ts", "module": "./dist/requester.fetch.node.d.ts", "require": "./dist/requester.fetch.node.d.cts"}, "import": "./dist/requester.fetch.node.js", "module": "./dist/requester.fetch.node.js", "require": "./dist/requester.fetch.node.cjs"}, "worker": {"types": "./dist/requester.fetch.node.d.ts", "default": "./dist/requester.fetch.node.js"}, "default": {"types": "./dist/requester.fetch.browser.d.ts", "module": "./dist/requester.fetch.browser.js", "import": "./dist/requester.fetch.browser.js", "default": "./dist/requester.fetch.browser.js"}}, "./src/*": "./src/*.ts"}, "react-native": "./dist/requester.fetch.browser.js", "files": ["dist", "index.d.ts", "index.js"], "scripts": {"build": "yarn clean && yarn tsup", "clean": "rm -rf ./dist || true", "test": "tsc --noEmit && vitest --run", "test:bundle": "publint . && attw --pack ."}, "dependencies": {"@algolia/client-common": "5.27.0"}, "devDependencies": {"@arethetypeswrong/cli": "0.18.1", "@types/node": "22.15.29", "cross-fetch": "4.1.0", "nock": "14.0.5", "publint": "0.3.12", "tsup": "8.5.0", "typescript": "5.8.3", "vitest": "3.2.0"}, "engines": {"node": ">= 14.0.0"}}