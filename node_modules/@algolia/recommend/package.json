{"version": "5.27.0", "repository": {"type": "git", "url": "git+https://github.com/algolia/algoliasearch-client-javascript.git"}, "homepage": "https://github.com/algolia/algoliasearch-client-javascript/packages/recommend#readme", "type": "module", "license": "MIT", "author": "Algolia", "scripts": {"build": "yarn clean && yarn tsup && yarn rollup -c rollup.config.js", "clean": "rm -rf ./dist || true", "test:bundle": "publint . && attw --pack ."}, "name": "@algolia/recommend", "description": "JavaScript client for recommend", "exports": {".": {"node": {"types": {"import": "./dist/node.d.ts", "module": "./dist/node.d.ts", "require": "./dist/node.d.cts"}, "import": "./dist/builds/node.js", "module": "./dist/builds/node.js", "require": "./dist/builds/node.cjs"}, "worker": {"types": "./dist/worker.d.ts", "default": "./dist/builds/worker.js"}, "default": {"types": "./dist/browser.d.ts", "module": "./dist/builds/browser.js", "import": "./dist/builds/browser.js", "default": "./dist/builds/browser.umd.js"}}, "./dist/builds/*": "./dist/builds/*.js"}, "jsdelivr": "./dist/builds/browser.umd.js", "unpkg": "./dist/builds/browser.umd.js", "react-native": "./dist/builds/browser.js", "files": ["dist", "index.js", "index.d.ts"], "dependencies": {"@algolia/client-common": "5.27.0", "@algolia/requester-browser-xhr": "5.27.0", "@algolia/requester-fetch": "5.27.0", "@algolia/requester-node-http": "5.27.0"}, "devDependencies": {"@arethetypeswrong/cli": "0.18.1", "@types/node": "22.15.29", "publint": "0.3.12", "rollup": "4.41.0", "tsup": "8.5.0", "typescript": "5.8.3"}, "engines": {"node": ">= 14.0.0"}}