import { Guti<PERSON>uilderEvent, EventHandler } from '../../types/events';

/**
 * Event emitter implementation for Guti Builder
 */
class GutiEventEmitter {
  private listeners = new Map<string, Set<EventHandler>>();

  /**
   * Subscribe to events of a specific type
   * @param eventType - Event type to listen for
   * @param handler - Event handler function
   * @returns Unsubscribe function
   */
  on<T extends GutiBuilderEvent>(
    eventType: T['type'],
    handler: EventHandler<T>
  ): () => void {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, new Set());
    }

    const handlers = this.listeners.get(eventType)!;
    handlers.add(handler as EventHandler);

    // Return unsubscribe function
    return () => {
      handlers.delete(handler as EventHandler);
      if (handlers.size === 0) {
        this.listeners.delete(eventType);
      }
    };
  }

  /**
   * Emit an event to all subscribers
   * @param event - Event to emit
   */
  emit<T extends GutiBuilderEvent>(event: T): void {
    const handlers = this.listeners.get(event.type);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(event);
        } catch (error) {
          console.error(`Error in event handler for ${event.type}:`, error);
        }
      });
    }
  }

  /**
   * Remove all listeners for a specific event type
   * @param eventType - Event type to clear
   */
  off(eventType: string): void {
    this.listeners.delete(eventType);
  }

  /**
   * Remove all listeners
   */
  removeAllListeners(): void {
    this.listeners.clear();
  }

  /**
   * Get the number of listeners for an event type
   * @param eventType - Event type
   * @returns Number of listeners
   */
  listenerCount(eventType: string): number {
    return this.listeners.get(eventType)?.size || 0;
  }

  /**
   * Get all event types that have listeners
   * @returns Array of event types
   */
  eventNames(): string[] {
    return Array.from(this.listeners.keys());
  }

  /**
   * Check if there are any listeners for an event type
   * @param eventType - Event type
   * @returns True if there are listeners
   */
  hasListeners(eventType: string): boolean {
    return this.listenerCount(eventType) > 0;
  }
}

/**
 * Global event emitter instance
 */
export const eventEmitter = new GutiEventEmitter();

/**
 * Create a new event emitter instance
 * @returns New event emitter
 */
export function createEventEmitter(): GutiEventEmitter {
  return new GutiEventEmitter();
}

/**
 * Event factory functions for type safety
 */
export const createEvent = {
  pageCreated: (page: any): GutiBuilderEvent => ({
    type: 'page:created',
    timestamp: Date.now(),
    payload: { page },
  }),

  pageUpdated: (page: any, changes: any): GutiBuilderEvent => ({
    type: 'page:updated',
    timestamp: Date.now(),
    payload: { page, changes },
  }),

  pageDeleted: (pageId: string): GutiBuilderEvent => ({
    type: 'page:deleted',
    timestamp: Date.now(),
    payload: { pageId },
  }),

  blockAdded: (block: any, parentId?: string, index?: number): GutiBuilderEvent => ({
    type: 'block:added',
    timestamp: Date.now(),
    payload: { block, parentId, index: index || 0 },
  }),

  blockUpdated: (block: any, changes: any): GutiBuilderEvent => ({
    type: 'block:updated',
    timestamp: Date.now(),
    payload: { block, changes },
  }),

  blockDeleted: (blockId: string, parentId?: string): GutiBuilderEvent => ({
    type: 'block:deleted',
    timestamp: Date.now(),
    payload: { blockId, parentId },
  }),

  blockMoved: (
    blockId: string,
    fromParentId?: string,
    toParentId?: string,
    fromIndex?: number,
    toIndex?: number
  ): GutiBuilderEvent => ({
    type: 'block:moved',
    timestamp: Date.now(),
    payload: {
      blockId,
      fromParentId,
      toParentId,
      fromIndex: fromIndex || 0,
      toIndex: toIndex || 0,
    },
  }),

  blockSelected: (blockId: string): GutiBuilderEvent => ({
    type: 'block:selected',
    timestamp: Date.now(),
    payload: { blockId },
  }),

  blockDeselected: (blockId: string): GutiBuilderEvent => ({
    type: 'block:deselected',
    timestamp: Date.now(),
    payload: { blockId },
  }),

  editorModeChanged: (mode: 'edit' | 'preview'): GutiBuilderEvent => ({
    type: 'editor:mode-changed',
    timestamp: Date.now(),
    payload: { mode },
  }),

  editorUndo: (action: string): GutiBuilderEvent => ({
    type: 'editor:undo',
    timestamp: Date.now(),
    payload: { action },
  }),

  editorRedo: (action: string): GutiBuilderEvent => ({
    type: 'editor:redo',
    timestamp: Date.now(),
    payload: { action },
  }),
};
