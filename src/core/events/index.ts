// Event system exports
export * from './event-emitter';

// Re-export commonly used items
export {
  eventEmitter,
  createEventEmitter,
  createEvent,
} from './event-emitter';

// Re-export types from main types
export type {
  GutiBuilderEvent,
  EventHandler,
  EventEmitter,
  PageCreatedEvent,
  PageUpdatedEvent,
  PageDeletedEvent,
  BlockAddedEvent,
  BlockUpdatedEvent,
  BlockDeletedEvent,
  BlockMovedEvent,
  BlockSelectedEvent,
  BlockDeselectedEvent,
  EditorModeChangedEvent,
  EditorUndoEvent,
  EditorRedoEvent,
} from '../../types/events';
