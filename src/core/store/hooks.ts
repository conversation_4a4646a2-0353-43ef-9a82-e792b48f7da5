import { useCallback, useEffect } from 'react';
import { useBuilderStore } from './store';
import { eventEmitter, createEvent } from '../events/event-emitter';
import { GutiId, BlockData, PageData } from '../../types/core';
import { EditorMode } from './types';

/**
 * Hook for page-related operations
 */
export function usePage() {
  const pageData = useBuilderStore(state => state.page.pageData);
  const hasUnsavedChanges = useBuilderStore(state => state.page.hasUnsavedChanges);
  const lastSaved = useBuilderStore(state => state.page.lastSaved);
  
  const setPageData = useBuilderStore(state => state.setPageData);
  const createNewPage = useBuilderStore(state => state.createNewPage);
  const updatePageMeta = useBuilderStore(state => state.updatePageMeta);
  const markSaved = useBuilderStore(state => state.markSaved);

  const createPage = useCallback((title?: string) => {
    createNewPage(title);
    if (pageData) {
      eventEmitter.emit(createEvent.pageCreated(pageData));
    }
  }, [createNewPage, pageData]);

  const updatePage = useCallback((updates: Partial<PageData>) => {
    updatePageMeta(updates);
    if (pageData) {
      eventEmitter.emit(createEvent.pageUpdated(pageData, updates));
    }
  }, [updatePageMeta, pageData]);

  return {
    pageData,
    hasUnsavedChanges,
    lastSaved,
    setPageData,
    createPage,
    updatePage,
    markSaved,
  };
}

/**
 * Hook for block-related operations
 */
export function useBlocks() {
  const blocks = useBuilderStore(state => state.page.pageData?.blocks || []);
  const getBlockById = useBuilderStore(state => state.getBlockById);
  const getBlockPath = useBuilderStore(state => state.getBlockPath);
  const findBlockParent = useBuilderStore(state => state.findBlockParent);
  
  const addBlock = useBuilderStore(state => state.addBlock);
  const updateBlock = useBuilderStore(state => state.updateBlock);
  const deleteBlock = useBuilderStore(state => state.deleteBlock);
  const moveBlock = useBuilderStore(state => state.moveBlock);
  const duplicateBlock = useBuilderStore(state => state.duplicateBlock);

  const addBlockWithEvent = useCallback((block: BlockData, parentId?: GutiId, index?: number) => {
    addBlock(block, parentId, index);
    eventEmitter.emit(createEvent.blockAdded(block, parentId, index));
  }, [addBlock]);

  const updateBlockWithEvent = useCallback((blockId: GutiId, updates: Partial<BlockData>) => {
    const block = getBlockById(blockId);
    updateBlock(blockId, updates);
    if (block) {
      eventEmitter.emit(createEvent.blockUpdated({ ...block, ...updates }, updates));
    }
  }, [updateBlock, getBlockById]);

  const deleteBlockWithEvent = useCallback((blockId: GutiId) => {
    const { parent } = findBlockParent(blockId);
    deleteBlock(blockId);
    eventEmitter.emit(createEvent.blockDeleted(blockId, parent?.id));
  }, [deleteBlock, findBlockParent]);

  const moveBlockWithEvent = useCallback((
    blockId: GutiId, 
    newParentId?: GutiId, 
    newIndex?: number
  ) => {
    const { parent: oldParent, index: oldIndex } = findBlockParent(blockId);
    moveBlock(blockId, newParentId, newIndex);
    eventEmitter.emit(createEvent.blockMoved(
      blockId,
      oldParent?.id,
      newParentId,
      oldIndex,
      newIndex
    ));
  }, [moveBlock, findBlockParent]);

  return {
    blocks,
    getBlockById,
    getBlockPath,
    findBlockParent,
    addBlock: addBlockWithEvent,
    updateBlock: updateBlockWithEvent,
    deleteBlock: deleteBlockWithEvent,
    moveBlock: moveBlockWithEvent,
    duplicateBlock,
  };
}

/**
 * Hook for selection operations
 */
export function useSelection() {
  const selectedBlockId = useBuilderStore(state => state.selection.selectedBlockId);
  const hoveredBlockId = useBuilderStore(state => state.selection.hoveredBlockId);
  const selectedBlockIds = useBuilderStore(state => state.selection.selectedBlockIds);
  const multiSelectMode = useBuilderStore(state => state.selection.multiSelectMode);

  const selectBlock = useBuilderStore(state => state.selectBlock);
  const hoverBlock = useBuilderStore(state => state.hoverBlock);
  const toggleMultiSelect = useBuilderStore(state => state.toggleMultiSelect);
  const addToSelection = useBuilderStore(state => state.addToSelection);
  const removeFromSelection = useBuilderStore(state => state.removeFromSelection);
  const clearSelection = useBuilderStore(state => state.clearSelection);

  const selectBlockWithEvent = useCallback((blockId: GutiId | null) => {
    const previousSelection = selectedBlockId;
    selectBlock(blockId);
    
    if (previousSelection && previousSelection !== blockId) {
      eventEmitter.emit(createEvent.blockDeselected(previousSelection));
    }
    if (blockId) {
      eventEmitter.emit(createEvent.blockSelected(blockId));
    }
  }, [selectBlock, selectedBlockId]);

  return {
    selectedBlockId,
    hoveredBlockId,
    selectedBlockIds,
    multiSelectMode,
    selectBlock: selectBlockWithEvent,
    hoverBlock,
    toggleMultiSelect,
    addToSelection,
    removeFromSelection,
    clearSelection,
  };
}

/**
 * Hook for UI state operations
 */
export function useUI() {
  const mode = useBuilderStore(state => state.ui.mode);
  const viewportSize = useBuilderStore(state => state.ui.viewportSize);
  const sidebarOpen = useBuilderStore(state => state.ui.sidebarOpen);
  const propertiesPanelOpen = useBuilderStore(state => state.ui.propertiesPanelOpen);
  const showGrid = useBuilderStore(state => state.ui.showGrid);
  const showOutlines = useBuilderStore(state => state.ui.showOutlines);
  const showResponsiveHelpers = useBuilderStore(state => state.ui.showResponsiveHelpers);
  const zoomLevel = useBuilderStore(state => state.ui.zoomLevel);
  const loading = useBuilderStore(state => state.ui.loading);
  const errors = useBuilderStore(state => state.ui.errors);

  const setEditorMode = useBuilderStore(state => state.setEditorMode);
  const setViewportSize = useBuilderStore(state => state.setViewportSize);
  const toggleSidebar = useBuilderStore(state => state.toggleSidebar);
  const togglePropertiesPanel = useBuilderStore(state => state.togglePropertiesPanel);
  const toggleGrid = useBuilderStore(state => state.toggleGrid);
  const toggleOutlines = useBuilderStore(state => state.toggleOutlines);
  const toggleResponsiveHelpers = useBuilderStore(state => state.toggleResponsiveHelpers);
  const setZoomLevel = useBuilderStore(state => state.setZoomLevel);
  const setLoading = useBuilderStore(state => state.setLoading);
  const setError = useBuilderStore(state => state.setError);
  const clearErrors = useBuilderStore(state => state.clearErrors);

  const setModeWithEvent = useCallback((newMode: EditorMode) => {
    setEditorMode(newMode);
    eventEmitter.emit(createEvent.editorModeChanged(newMode));
  }, [setEditorMode]);

  return {
    mode,
    viewportSize,
    sidebarOpen,
    propertiesPanelOpen,
    showGrid,
    showOutlines,
    showResponsiveHelpers,
    zoomLevel,
    loading,
    errors,
    setEditorMode: setModeWithEvent,
    setViewportSize,
    toggleSidebar,
    togglePropertiesPanel,
    toggleGrid,
    toggleOutlines,
    toggleResponsiveHelpers,
    setZoomLevel,
    setLoading,
    setError,
    clearErrors,
  };
}

/**
 * Hook for history operations (undo/redo)
 */
export function useHistory() {
  const canUndo = useBuilderStore(state => state.history.canUndo);
  const canRedo = useBuilderStore(state => state.history.canRedo);
  const entries = useBuilderStore(state => state.history.entries);
  const currentIndex = useBuilderStore(state => state.history.currentIndex);

  const undo = useBuilderStore(state => state.undo);
  const redo = useBuilderStore(state => state.redo);
  const clearHistory = useBuilderStore(state => state.clearHistory);

  const undoWithEvent = useCallback(() => {
    if (canUndo && currentIndex >= 0) {
      const entry = entries[currentIndex - 1];
      undo();
      if (entry) {
        eventEmitter.emit(createEvent.editorUndo(entry.action));
      }
    }
  }, [undo, canUndo, currentIndex, entries]);

  const redoWithEvent = useCallback(() => {
    if (canRedo && currentIndex < entries.length - 1) {
      const entry = entries[currentIndex + 1];
      redo();
      if (entry) {
        eventEmitter.emit(createEvent.editorRedo(entry.action));
      }
    }
  }, [redo, canRedo, currentIndex, entries]);

  return {
    canUndo,
    canRedo,
    entries,
    currentIndex,
    undo: undoWithEvent,
    redo: redoWithEvent,
    clearHistory,
  };
}

/**
 * Hook for theme operations
 */
export function useTheme() {
  const theme = useBuilderStore(state => state.theme.theme);
  const mode = useBuilderStore(state => state.theme.mode);
  const customizations = useBuilderStore(state => state.theme.customizations);

  const setThemeMode = useBuilderStore(state => state.setThemeMode);
  const updateTheme = useBuilderStore(state => state.updateTheme);
  const resetTheme = useBuilderStore(state => state.resetTheme);

  return {
    theme,
    mode,
    customizations,
    setThemeMode,
    updateTheme,
    resetTheme,
  };
}

/**
 * Hook for keyboard shortcuts
 */
export function useKeyboardShortcuts() {
  const { undo, redo, canUndo, canRedo } = useHistory();
  const { selectBlock } = useSelection();
  const { deleteBlock } = useBlocks();

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Prevent shortcuts when typing in inputs
      if (
        event.target instanceof HTMLInputElement ||
        event.target instanceof HTMLTextAreaElement ||
        (event.target as HTMLElement)?.contentEditable === 'true'
      ) {
        return;
      }

      const { ctrlKey, metaKey, key, shiftKey } = event;
      const isModifier = ctrlKey || metaKey;

      if (isModifier) {
        switch (key.toLowerCase()) {
          case 'z':
            event.preventDefault();
            if (shiftKey && canRedo) {
              redo();
            } else if (canUndo) {
              undo();
            }
            break;
          case 'y':
            event.preventDefault();
            if (canRedo) {
              redo();
            }
            break;
        }
      } else {
        switch (key) {
          case 'Escape':
            event.preventDefault();
            selectBlock(null);
            break;
          case 'Delete':
          case 'Backspace':
            event.preventDefault();
            const selectedBlockId = useBuilderStore.getState().selection.selectedBlockId;
            if (selectedBlockId) {
              deleteBlock(selectedBlockId);
            }
            break;
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [undo, redo, canUndo, canRedo, selectBlock, deleteBlock]);
}

/**
 * Hook for auto-save functionality
 */
export function useAutoSave(onSave: () => Promise<void>, interval = 30000) {
  const hasUnsavedChanges = useBuilderStore(state => state.page.hasUnsavedChanges);
  const markSaved = useBuilderStore(state => state.markSaved);

  useEffect(() => {
    if (!hasUnsavedChanges) return;

    const autoSaveInterval = setInterval(async () => {
      if (hasUnsavedChanges) {
        try {
          await onSave();
          markSaved();
        } catch (error) {
          console.error('Auto-save failed:', error);
        }
      }
    }, interval);

    return () => clearInterval(autoSaveInterval);
  }, [hasUnsavedChanges, onSave, interval, markSaved]);
}
