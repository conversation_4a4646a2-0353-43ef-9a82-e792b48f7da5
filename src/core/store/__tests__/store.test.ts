import { describe, it, expect, beforeEach } from 'vitest';
import { useBuilderStore } from '../store';
import { generateBlockId, generatePageId } from '../../../utils/id';
import { BlockData, PageData } from '../../../types/core';

// Helper to get a fresh store instance for each test
const getStore = () => useBuilderStore.getState();

describe('Builder Store', () => {
  beforeEach(() => {
    // Reset store before each test
    useBuilderStore.getState().reset();
  });

  describe('Page operations', () => {
    it('should create a new page', () => {
      const store = getStore();
      store.createNewPage('Test Page');

      const pageData = store.page.pageData;
      expect(pageData).toBeTruthy();
      expect(pageData?.title).toBe('Test Page');
      expect(pageData?.blocks).toEqual([]);
      expect(store.page.hasUnsavedChanges).toBe(true);
    });

    it('should set page data', () => {
      const store = getStore();
      const mockPage: PageData = {
        id: generatePageId(),
        title: 'Mock Page',
        description: 'Test description',
        blocks: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      store.setPageData(mockPage);

      expect(store.page.pageData).toEqual(mockPage);
      expect(store.page.hasUnsavedChanges).toBe(false);
    });

    it('should update page metadata', () => {
      const store = getStore();
      store.createNewPage('Original Title');

      store.updatePageMeta({ title: 'Updated Title', description: 'New description' });

      expect(store.page.pageData?.title).toBe('Updated Title');
      expect(store.page.pageData?.description).toBe('New description');
      expect(store.page.hasUnsavedChanges).toBe(true);
    });
  });

  describe('Block operations', () => {
    beforeEach(() => {
      const store = getStore();
      store.createNewPage('Test Page');
    });

    it('should add a block to the page', () => {
      const store = getStore();
      const block: BlockData = {
        id: generateBlockId(),
        type: 'text',
        props: { content: 'Hello World' },
      };

      store.addBlock(block);

      expect(store.page.pageData?.blocks).toHaveLength(1);
      expect(store.page.pageData?.blocks[0]).toEqual(block);
      expect(store.selection.selectedBlockId).toBe(block.id);
      expect(store.page.hasUnsavedChanges).toBe(true);
    });

    it('should update a block', () => {
      const store = getStore();
      const block: BlockData = {
        id: generateBlockId(),
        type: 'text',
        props: { content: 'Original' },
      };

      store.addBlock(block);
      store.updateBlock(block.id, { props: { content: 'Updated' } });

      const updatedBlock = store.getBlockById(block.id);
      expect(updatedBlock?.props.content).toBe('Updated');
      expect(store.page.hasUnsavedChanges).toBe(true);
    });

    it('should delete a block', () => {
      const store = getStore();
      const block: BlockData = {
        id: generateBlockId(),
        type: 'text',
        props: { content: 'To be deleted' },
      };

      store.addBlock(block);
      expect(store.page.pageData?.blocks).toHaveLength(1);

      store.deleteBlock(block.id);
      expect(store.page.pageData?.blocks).toHaveLength(0);
      expect(store.selection.selectedBlockId).toBeNull();
    });

    it('should find block by ID', () => {
      const store = getStore();
      const block: BlockData = {
        id: generateBlockId(),
        type: 'text',
        props: { content: 'Find me' },
      };

      store.addBlock(block);
      const foundBlock = store.getBlockById(block.id);

      expect(foundBlock).toEqual(block);
    });

    it('should find block parent and index', () => {
      const store = getStore();
      const parentBlock: BlockData = {
        id: generateBlockId(),
        type: 'container',
        props: {},
        children: [],
      };
      const childBlock: BlockData = {
        id: generateBlockId(),
        type: 'text',
        props: { content: 'Child' },
      };

      store.addBlock(parentBlock);
      store.addBlock(childBlock, parentBlock.id);

      const { parent, index } = store.findBlockParent(childBlock.id);
      expect(parent?.id).toBe(parentBlock.id);
      expect(index).toBe(0);
    });
  });

  describe('Selection operations', () => {
    beforeEach(() => {
      const store = getStore();
      store.createNewPage('Test Page');
    });

    it('should select a block', () => {
      const store = getStore();
      const blockId = generateBlockId();

      store.selectBlock(blockId);

      expect(store.selection.selectedBlockId).toBe(blockId);
      expect(store.selection.selectedBlockIds).toEqual([blockId]);
    });

    it('should clear selection', () => {
      const store = getStore();
      const blockId = generateBlockId();

      store.selectBlock(blockId);
      store.clearSelection();

      expect(store.selection.selectedBlockId).toBeNull();
      expect(store.selection.selectedBlockIds).toEqual([]);
      expect(store.selection.hoveredBlockId).toBeNull();
    });

    it('should handle multi-selection', () => {
      const store = getStore();
      const blockId1 = generateBlockId();
      const blockId2 = generateBlockId();

      store.toggleMultiSelect();
      store.addToSelection(blockId1);
      store.addToSelection(blockId2);

      expect(store.selection.multiSelectMode).toBe(true);
      expect(store.selection.selectedBlockIds).toEqual([blockId1, blockId2]);
    });
  });

  describe('UI operations', () => {
    it('should toggle editor mode', () => {
      const store = getStore();
      expect(store.ui.mode).toBe('edit');

      store.setEditorMode('preview');
      expect(store.ui.mode).toBe('preview');
    });

    it('should toggle sidebar', () => {
      const store = getStore();
      const initialState = store.ui.sidebarOpen;

      store.toggleSidebar();
      expect(store.ui.sidebarOpen).toBe(!initialState);
    });

    it('should set zoom level within bounds', () => {
      const store = getStore();

      store.setZoomLevel(2.0);
      expect(store.ui.zoomLevel).toBe(2.0);

      store.setZoomLevel(5.0); // Should be clamped
      expect(store.ui.zoomLevel).toBe(3.0);

      store.setZoomLevel(0.1); // Should be clamped
      expect(store.ui.zoomLevel).toBe(0.25);
    });

    it('should manage loading states', () => {
      const store = getStore();

      store.setLoading('page', true);
      expect(store.ui.loading.page).toBe(true);

      store.setLoading('page', false);
      expect(store.ui.loading.page).toBe(false);
    });

    it('should manage error states', () => {
      const store = getStore();

      store.setError('page', 'Failed to load page');
      expect(store.ui.errors.page).toBe('Failed to load page');

      store.setError('page');
      expect(store.ui.errors.page).toBeUndefined();

      store.setError('validation', 'Invalid data');
      store.clearErrors();
      expect(store.ui.errors).toEqual({});
    });
  });

  describe('History operations', () => {
    beforeEach(() => {
      const store = getStore();
      store.createNewPage('Test Page');
    });

    it('should add history entries', () => {
      const store = getStore();
      const block: BlockData = {
        id: generateBlockId(),
        type: 'text',
        props: { content: 'Test' },
      };

      store.addBlock(block); // This should add a history entry

      expect(store.history.entries.length).toBeGreaterThan(0);
      expect(store.history.canUndo).toBe(true);
      expect(store.history.canRedo).toBe(false);
    });

    it('should perform undo operation', () => {
      const store = getStore();
      const block: BlockData = {
        id: generateBlockId(),
        type: 'text',
        props: { content: 'Test' },
      };

      // Add block (creates history entry)
      store.addBlock(block);
      expect(store.page.pageData?.blocks).toHaveLength(1);

      // Undo should restore previous state
      if (store.history.canUndo) {
        store.undo();
        expect(store.page.pageData?.blocks).toHaveLength(0);
        expect(store.history.canRedo).toBe(true);
      }
    });
  });

  describe('Theme operations', () => {
    it('should set theme mode', () => {
      const store = getStore();

      store.setThemeMode('dark');
      expect(store.theme.mode).toBe('dark');

      store.setThemeMode('light');
      expect(store.theme.mode).toBe('light');
    });

    it('should update theme configuration', () => {
      const store = getStore();

      store.updateTheme({
        colors: {
          ...store.theme.theme.colors,
          primary: '#ff0000',
        },
      });

      expect(store.theme.theme.colors.primary).toBe('#ff0000');
      expect(store.page.hasUnsavedChanges).toBe(true);
    });

    it('should reset theme to defaults', () => {
      const store = getStore();

      // Modify theme
      store.updateTheme({
        colors: {
          ...store.theme.theme.colors,
          primary: '#ff0000',
        },
      });

      // Reset theme
      store.resetTheme();
      expect(store.theme.theme.colors.primary).toBe('#3b82f6'); // Default blue
    });
  });
});
