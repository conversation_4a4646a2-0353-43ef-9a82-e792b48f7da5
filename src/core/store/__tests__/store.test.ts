import { describe, it, expect, beforeEach } from 'vitest';
import { useBuilderStore } from '../store';
import { generateBlockId, generatePageId } from '../../../utils/id';
import { BlockData, PageData } from '../../../types/core';

describe('Builder Store', () => {
  beforeEach(() => {
    // Reset store before each test
    useBuilderStore.getState().reset();
  });

  describe('Page operations', () => {
    it('should create a new page', () => {
      // Call the action on the store
      useBuilderStore.getState().createNewPage('Test Page');

      // Get the updated state
      const state = useBuilderStore.getState();
      const pageData = state.page.pageData;
      expect(pageData).toBeTruthy();
      expect(pageData?.title).toBe('Test Page');
      expect(pageData?.blocks).toEqual([]);
      expect(state.page.hasUnsavedChanges).toBe(true);
    });

    it('should set page data', () => {
      const mockPage: PageData = {
        id: generatePageId(),
        title: 'Mock Page',
        description: 'Test description',
        blocks: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      useBuilderStore.getState().setPageData(mockPage);
      const state = useBuilderStore.getState();

      expect(state.page.pageData).toEqual(mockPage);
      expect(state.page.hasUnsavedChanges).toBe(false);
    });

    it('should update page metadata', () => {
      useBuilderStore.getState().createNewPage('Original Title');
      useBuilderStore.getState().updatePageMeta({ title: 'Updated Title', description: 'New description' });

      const state = useBuilderStore.getState();
      expect(state.page.pageData?.title).toBe('Updated Title');
      expect(state.page.pageData?.description).toBe('New description');
      expect(state.page.hasUnsavedChanges).toBe(true);
    });
  });

  describe('Block operations', () => {
    beforeEach(() => {
      useBuilderStore.getState().createNewPage('Test Page');
    });

    it('should add a block to the page', () => {
      const block: BlockData = {
        id: generateBlockId(),
        type: 'text',
        props: { content: 'Hello World' },
      };

      useBuilderStore.getState().addBlock(block);
      const state = useBuilderStore.getState();

      expect(state.page.pageData?.blocks).toHaveLength(1);
      expect(state.page.pageData?.blocks[0]).toEqual(block);
      expect(state.selection.selectedBlockId).toBe(block.id);
      expect(state.page.hasUnsavedChanges).toBe(true);
    });

    it('should update a block', () => {
      const block: BlockData = {
        id: generateBlockId(),
        type: 'text',
        props: { content: 'Original' },
      };

      useBuilderStore.getState().addBlock(block);
      useBuilderStore.getState().updateBlock(block.id, { props: { content: 'Updated' } });

      const state = useBuilderStore.getState();
      const updatedBlock = state.getBlockById(block.id);
      expect(updatedBlock?.props.content).toBe('Updated');
      expect(state.page.hasUnsavedChanges).toBe(true);
    });

    it('should delete a block', () => {
      const block: BlockData = {
        id: generateBlockId(),
        type: 'text',
        props: { content: 'To be deleted' },
      };

      useBuilderStore.getState().addBlock(block);
      let state = useBuilderStore.getState();
      expect(state.page.pageData?.blocks).toHaveLength(1);

      useBuilderStore.getState().deleteBlock(block.id);
      state = useBuilderStore.getState();
      expect(state.page.pageData?.blocks).toHaveLength(0);
      expect(state.selection.selectedBlockId).toBeNull();
    });

    it('should find block by ID', () => {
      const block: BlockData = {
        id: generateBlockId(),
        type: 'text',
        props: { content: 'Find me' },
      };

      useBuilderStore.getState().addBlock(block);
      const state = useBuilderStore.getState();
      const foundBlock = state.getBlockById(block.id);

      expect(foundBlock).toEqual(block);
    });

    it('should find block parent and index', () => {
      const parentBlock: BlockData = {
        id: generateBlockId(),
        type: 'container',
        props: {},
        children: [],
      };
      const childBlock: BlockData = {
        id: generateBlockId(),
        type: 'text',
        props: { content: 'Child' },
      };

      useBuilderStore.getState().addBlock(parentBlock);
      useBuilderStore.getState().addBlock(childBlock, parentBlock.id);

      const state = useBuilderStore.getState();
      const { parent, index } = state.findBlockParent(childBlock.id);
      expect(parent?.id).toBe(parentBlock.id);
      expect(index).toBe(0);
    });
  });

  describe('Selection operations', () => {
    beforeEach(() => {
      useBuilderStore.getState().createNewPage('Test Page');
    });

    it('should select a block', () => {
      const blockId = generateBlockId();

      useBuilderStore.getState().selectBlock(blockId);
      const state = useBuilderStore.getState();

      expect(state.selection.selectedBlockId).toBe(blockId);
      expect(state.selection.selectedBlockIds).toEqual([blockId]);
    });

    it('should clear selection', () => {
      const blockId = generateBlockId();

      useBuilderStore.getState().selectBlock(blockId);
      useBuilderStore.getState().clearSelection();
      const state = useBuilderStore.getState();

      expect(state.selection.selectedBlockId).toBeNull();
      expect(state.selection.selectedBlockIds).toEqual([]);
      expect(state.selection.hoveredBlockId).toBeNull();
    });

    it('should handle multi-selection', () => {
      const blockId1 = generateBlockId();
      const blockId2 = generateBlockId();

      useBuilderStore.getState().toggleMultiSelect();
      useBuilderStore.getState().addToSelection(blockId1);
      useBuilderStore.getState().addToSelection(blockId2);
      const state = useBuilderStore.getState();

      expect(state.selection.multiSelectMode).toBe(true);
      expect(state.selection.selectedBlockIds).toEqual([blockId1, blockId2]);
    });
  });

  describe('UI operations', () => {
    it('should toggle editor mode', () => {
      let state = useBuilderStore.getState();
      expect(state.ui.mode).toBe('edit');

      useBuilderStore.getState().setEditorMode('preview');
      state = useBuilderStore.getState();
      expect(state.ui.mode).toBe('preview');
    });

    it('should toggle sidebar', () => {
      const initialState = useBuilderStore.getState().ui.sidebarOpen;

      useBuilderStore.getState().toggleSidebar();
      const state = useBuilderStore.getState();
      expect(state.ui.sidebarOpen).toBe(!initialState);
    });

    it('should set zoom level within bounds', () => {
      useBuilderStore.getState().setZoomLevel(2.0);
      let state = useBuilderStore.getState();
      expect(state.ui.zoomLevel).toBe(2.0);

      useBuilderStore.getState().setZoomLevel(5.0); // Should be clamped
      state = useBuilderStore.getState();
      expect(state.ui.zoomLevel).toBe(3.0);

      useBuilderStore.getState().setZoomLevel(0.1); // Should be clamped
      state = useBuilderStore.getState();
      expect(state.ui.zoomLevel).toBe(0.25);
    });

    it('should manage loading states', () => {
      useBuilderStore.getState().setLoading('page', true);
      let state = useBuilderStore.getState();
      expect(state.ui.loading.page).toBe(true);

      useBuilderStore.getState().setLoading('page', false);
      state = useBuilderStore.getState();
      expect(state.ui.loading.page).toBe(false);
    });

    it('should manage error states', () => {
      useBuilderStore.getState().setError('page', 'Failed to load page');
      let state = useBuilderStore.getState();
      expect(state.ui.errors.page).toBe('Failed to load page');

      useBuilderStore.getState().setError('page');
      state = useBuilderStore.getState();
      expect(state.ui.errors.page).toBeUndefined();

      useBuilderStore.getState().setError('validation', 'Invalid data');
      useBuilderStore.getState().clearErrors();
      state = useBuilderStore.getState();
      expect(state.ui.errors).toEqual({});
    });
  });

  describe('History operations', () => {
    beforeEach(() => {
      useBuilderStore.getState().createNewPage('Test Page');
    });

    it('should add history entries', async () => {
      const block: BlockData = {
        id: generateBlockId(),
        type: 'text',
        props: { content: 'Test' },
      };

      useBuilderStore.getState().addBlock(block); // This should add a history entry

      // Wait for async history entry
      await new Promise(resolve => setTimeout(resolve, 10));

      const state = useBuilderStore.getState();
      expect(state.history.entries.length).toBeGreaterThan(0);
      expect(state.history.canUndo).toBe(true);
      expect(state.history.canRedo).toBe(false);
    });

    it('should perform undo operation', async () => {
      const block: BlockData = {
        id: generateBlockId(),
        type: 'text',
        props: { content: 'Test' },
      };

      // Add block (creates history entry)
      useBuilderStore.getState().addBlock(block);
      let state = useBuilderStore.getState();
      expect(state.page.pageData?.blocks).toHaveLength(1);

      // Wait for async history entry
      await new Promise(resolve => setTimeout(resolve, 10));

      // Undo should restore previous state
      state = useBuilderStore.getState();
      if (state.history.canUndo) {
        useBuilderStore.getState().undo();
        state = useBuilderStore.getState();
        expect(state.page.pageData?.blocks).toHaveLength(0);
        expect(state.history.canRedo).toBe(true);
      }
    });
  });

  describe('Theme operations', () => {
    it('should set theme mode', () => {
      useBuilderStore.getState().setThemeMode('dark');
      let state = useBuilderStore.getState();
      expect(state.theme.mode).toBe('dark');

      useBuilderStore.getState().setThemeMode('light');
      state = useBuilderStore.getState();
      expect(state.theme.mode).toBe('light');
    });

    it('should update theme configuration', () => {
      const initialState = useBuilderStore.getState();

      useBuilderStore.getState().updateTheme({
        colors: {
          ...initialState.theme.theme.colors,
          primary: '#ff0000',
        },
      });

      const state = useBuilderStore.getState();
      expect(state.theme.theme.colors.primary).toBe('#ff0000');
      expect(state.page.hasUnsavedChanges).toBe(true);
    });

    it('should reset theme to defaults', () => {
      const initialState = useBuilderStore.getState();

      // Modify theme
      useBuilderStore.getState().updateTheme({
        colors: {
          ...initialState.theme.theme.colors,
          primary: '#ff0000',
        },
      });

      // Reset theme
      useBuilderStore.getState().resetTheme();
      const state = useBuilderStore.getState();
      expect(state.theme.theme.colors.primary).toBe('#3b82f6'); // Default blue
    });
  });
});
