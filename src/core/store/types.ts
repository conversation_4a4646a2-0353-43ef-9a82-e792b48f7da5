import { <PERSON>D<PERSON>, ThemeConfig, GutiId } from '../../types/core';
import { ThemeMode } from '../../types/theme';

/**
 * Editor modes
 */
export type EditorMode = 'edit' | 'preview';

/**
 * Viewport sizes for responsive preview
 */
export type ViewportSize = 'mobile' | 'tablet' | 'desktop' | 'auto';

/**
 * History entry for undo/redo functionality
 */
export interface HistoryEntry {
  id: string;
  timestamp: number;
  action: string;
  pageData: PageData;
}

/**
 * UI state for the builder interface
 */
export interface UIState {
  /** Current editor mode */
  mode: EditorMode;
  /** Current viewport size for preview */
  viewportSize: ViewportSize;
  /** Whether the sidebar is open */
  sidebarOpen: boolean;
  /** Whether the properties panel is open */
  propertiesPanelOpen: boolean;
  /** Whether to show grid lines */
  showGrid: boolean;
  /** Whether to show block outlines */
  showOutlines: boolean;
  /** Whether to show responsive helpers */
  showResponsiveHelpers: boolean;
  /** Current zoom level (0.5 to 2.0) */
  zoomLevel: number;
  /** Loading states */
  loading: {
    page: boolean;
    saving: boolean;
    blocks: boolean;
  };
  /** Error states */
  errors: {
    page?: string;
    validation?: string[];
    network?: string;
  };
}

/**
 * Selection state
 */
export interface SelectionState {
  /** Currently selected block ID */
  selectedBlockId: GutiId | null;
  /** Currently hovered block ID */
  hoveredBlockId: GutiId | null;
  /** Multi-selection (for future use) */
  selectedBlockIds: GutiId[];
  /** Whether we're in multi-select mode */
  multiSelectMode: boolean;
}

/**
 * History state for undo/redo
 */
export interface HistoryState {
  /** History entries */
  entries: HistoryEntry[];
  /** Current position in history */
  currentIndex: number;
  /** Maximum history entries to keep */
  maxEntries: number;
  /** Whether undo is available */
  canUndo: boolean;
  /** Whether redo is available */
  canRedo: boolean;
}

/**
 * Theme state
 */
export interface ThemeState {
  /** Current theme configuration */
  theme: ThemeConfig;
  /** Current theme mode */
  mode: ThemeMode;
  /** Available theme presets */
  presets: Record<string, ThemeConfig>;
  /** Custom theme modifications */
  customizations: Partial<ThemeConfig>;
}

/**
 * Page state
 */
export interface PageState {
  /** Current page data */
  pageData: PageData | null;
  /** Whether the page has unsaved changes */
  hasUnsavedChanges: boolean;
  /** Last saved timestamp */
  lastSaved: number | null;
  /** Auto-save interval ID */
  autoSaveInterval: number | null;
}

/**
 * Complete builder state
 */
export interface BuilderState {
  /** Page state */
  page: PageState;
  /** UI state */
  ui: UIState;
  /** Selection state */
  selection: SelectionState;
  /** History state */
  history: HistoryState;
  /** Theme state */
  theme: ThemeState;
}

/**
 * Action types for history tracking
 */
export type ActionType = 
  | 'block:add'
  | 'block:update'
  | 'block:delete'
  | 'block:move'
  | 'block:duplicate'
  | 'page:update'
  | 'page:create'
  | 'theme:update'
  | 'bulk:update';

/**
 * Action payload for history entries
 */
export interface ActionPayload {
  type: ActionType;
  description: string;
  blockId?: GutiId;
  parentId?: GutiId;
  data?: any;
}
