 import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { BuilderState, ActionType } from './types';
import { PageData, BlockData, GutiId } from '../../types/core';
import { ThemeMode } from '../../types/theme';
import { defaultThemeTokens } from '../../theme/tokens';
import { tokensToConfig } from '../../theme/converter';
import { generateId, generatePageId } from '../../utils/id';

/**
 * Initial state for the builder
 */
const initialState: BuilderState = {
  page: {
    pageData: null,
    hasUnsavedChanges: false,
    lastSaved: null,
    autoSaveInterval: null,
  },
  ui: {
    mode: 'edit',
    viewportSize: 'desktop',
    sidebarOpen: true,
    propertiesPanelOpen: true,
    showGrid: false,
    showOutlines: true,
    showResponsiveHelpers: false,
    zoomLevel: 1.0,
    loading: {
      page: false,
      saving: false,
      blocks: false,
    },
    errors: {},
  },
  selection: {
    selectedBlockId: null,
    hoveredBlockId: null,
    selectedBlockIds: [],
    multiSelectMode: false,
  },
  history: {
    entries: [],
    currentIndex: -1,
    maxEntries: 50,
    canUndo: false,
    canRedo: false,
  },
  theme: {
    theme: tokensToConfig(defaultThemeTokens),
    mode: 'light',
    presets: {},
    customizations: {},
  },
};

/**
 * Builder store actions interface
 */
export interface BuilderActions {
  // Page actions
  setPageData: (pageData: PageData) => void;
  createNewPage: (title?: string) => void;
  updatePageMeta: (meta: Partial<PageData>) => void;
  markUnsavedChanges: () => void;
  markSaved: () => void;

  // Block actions
  addBlock: (block: BlockData, parentId?: GutiId, index?: number) => void;
  updateBlock: (blockId: GutiId, updates: Partial<BlockData>) => void;
  deleteBlock: (blockId: GutiId) => void;
  moveBlock: (blockId: GutiId, newParentId?: GutiId, newIndex?: number) => void;
  duplicateBlock: (blockId: GutiId) => void;

  // Selection actions
  selectBlock: (blockId: GutiId | null) => void;
  hoverBlock: (blockId: GutiId | null) => void;
  toggleMultiSelect: () => void;
  addToSelection: (blockId: GutiId) => void;
  removeFromSelection: (blockId: GutiId) => void;
  clearSelection: () => void;

  // UI actions
  setEditorMode: (mode: 'edit' | 'preview') => void;
  setViewportSize: (size: 'mobile' | 'tablet' | 'desktop' | 'auto') => void;
  toggleSidebar: () => void;
  togglePropertiesPanel: () => void;
  toggleGrid: () => void;
  toggleOutlines: () => void;
  toggleResponsiveHelpers: () => void;
  setZoomLevel: (level: number) => void;
  setLoading: (key: keyof BuilderState['ui']['loading'], loading: boolean) => void;
  setError: (key: keyof BuilderState['ui']['errors'], error?: string) => void;
  clearErrors: () => void;

  // History actions
  undo: () => void;
  redo: () => void;
  addHistoryEntry: (action: ActionType, description: string, payload?: any) => void;
  clearHistory: () => void;

  // Theme actions
  setThemeMode: (mode: ThemeMode) => void;
  updateTheme: (updates: Partial<BuilderState['theme']['theme']>) => void;
  resetTheme: () => void;

  // Utility actions
  reset: () => void;
  getBlockById: (blockId: GutiId) => BlockData | null;
  getBlockPath: (blockId: GutiId) => GutiId[];
  findBlockParent: (blockId: GutiId) => { parent: BlockData | null; index: number };
}

/**
 * Complete store type
 */
export type BuilderStore = BuilderState & BuilderActions;

/**
 * Create the builder store
 */
export const useBuilderStore = create<BuilderStore>()(
  subscribeWithSelector((set, get) => ({
        ...initialState,

        // Page actions
        setPageData: (pageData: PageData) => {
          set((state) => ({
            ...state,
            page: {
              ...state.page,
              pageData,
              hasUnsavedChanges: false,
              lastSaved: Date.now(),
            },
            selection: {
              ...state.selection,
              selectedBlockId: null,
              selectedBlockIds: [],
            },
          }));
        },

        createNewPage: (title = 'New Page') => {
          const newPage: PageData = {
            id: generatePageId(),
            title,
            description: '',
            blocks: [],
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          };

          set((state) => ({
            ...state,
            page: {
              ...state.page,
              pageData: newPage,
              hasUnsavedChanges: true,
              lastSaved: null,
            },
            selection: {
              ...state.selection,
              selectedBlockId: null,
              selectedBlockIds: [],
            },
            history: {
              ...state.history,
              entries: [],
              currentIndex: -1,
              canUndo: false,
              canRedo: false,
            },
          }));
        },

        updatePageMeta: (meta: Partial<PageData>) => {
          set((state) => {
            if (!state.page.pageData) return state;

            return {
              ...state,
              page: {
                ...state.page,
                pageData: {
                  ...state.page.pageData,
                  ...meta,
                  updatedAt: new Date().toISOString(),
                },
                hasUnsavedChanges: true,
              },
            };
          });
        },

        markUnsavedChanges: () => {
          set((state) => ({
            ...state,
            page: {
              ...state.page,
              hasUnsavedChanges: true,
            },
          }));
        },

        markSaved: () => {
          set((state) => ({
            ...state,
            page: {
              ...state.page,
              hasUnsavedChanges: false,
              lastSaved: Date.now(),
            },
          }));
        },

        // Block actions
        addBlock: (block: BlockData, parentId?: GutiId, index?: number) => {
          set((state) => {
            if (!state.page.pageData) return state;

            // Helper function to add block to blocks array immutably
            const addBlockToArray = (blocks: BlockData[], targetParentId?: GutiId): BlockData[] => {
              if (!targetParentId) {
                // Add to root level
                const insertIndex = index !== undefined ? index : blocks.length;
                const newBlocks = [...blocks];
                newBlocks.splice(insertIndex, 0, block);
                return newBlocks;
              }

              // Add to nested parent
              return blocks.map(b => {
                if (b.id === targetParentId) {
                  const children = b.children || [];
                  const insertIndex = index !== undefined ? index : children.length;
                  const newChildren = [...children];
                  newChildren.splice(insertIndex, 0, block);
                  return { ...b, children: newChildren };
                }
                if (b.children) {
                  return { ...b, children: addBlockToArray(b.children, targetParentId) };
                }
                return b;
              });
            };

            const newBlocks = addBlockToArray(state.page.pageData.blocks, parentId);

            return {
              ...state,
              page: {
                ...state.page,
                pageData: {
                  ...state.page.pageData,
                  blocks: newBlocks,
                  updatedAt: new Date().toISOString(),
                },
                hasUnsavedChanges: true,
              },
              selection: {
                ...state.selection,
                selectedBlockId: block.id,
              },
            };
          });

          // Add history entry after state update
          setTimeout(() => {
            get().addHistoryEntry('block:add', `Added ${block.type} block`, { blockId: block.id, parentId });
          }, 0);
        },

        updateBlock: (blockId: GutiId, updates: Partial<BlockData>) => {
          set((state) => {
            if (!state.page.pageData) return state;

            // Helper function to update block immutably
            const updateBlockInArray = (blocks: BlockData[]): BlockData[] => {
              return blocks.map(block => {
                if (block.id === blockId) {
                  return { ...block, ...updates };
                }
                if (block.children) {
                  return { ...block, children: updateBlockInArray(block.children) };
                }
                return block;
              });
            };

            const newBlocks = updateBlockInArray(state.page.pageData.blocks);

            return {
              ...state,
              page: {
                ...state.page,
                pageData: {
                  ...state.page.pageData,
                  blocks: newBlocks,
                  updatedAt: new Date().toISOString(),
                },
                hasUnsavedChanges: true,
              },
            };
          });

          setTimeout(() => {
            get().addHistoryEntry('block:update', `Updated block`, { blockId, updates });
          }, 0);
        },

        deleteBlock: (blockId: GutiId) => {
          set((state) => {
            if (!state.page.pageData) return state;

            // Helper function to delete block immutably
            const deleteBlockFromArray = (blocks: BlockData[]): BlockData[] => {
              return blocks.filter(block => {
                if (block.id === blockId) {
                  return false; // Remove this block
                }
                if (block.children) {
                  return { ...block, children: deleteBlockFromArray(block.children) };
                }
                return block;
              }).map(block => {
                if (block.children) {
                  return { ...block, children: deleteBlockFromArray(block.children) };
                }
                return block;
              });
            };

            const newBlocks = deleteBlockFromArray(state.page.pageData.blocks);
            const newSelectedBlockIds = state.selection.selectedBlockIds.filter(id => id !== blockId);

            return {
              ...state,
              page: {
                ...state.page,
                pageData: {
                  ...state.page.pageData,
                  blocks: newBlocks,
                  updatedAt: new Date().toISOString(),
                },
                hasUnsavedChanges: true,
              },
              selection: {
                ...state.selection,
                selectedBlockId: state.selection.selectedBlockId === blockId ? null : state.selection.selectedBlockId,
                selectedBlockIds: newSelectedBlockIds,
              },
            };
          });

          setTimeout(() => {
            get().addHistoryEntry('block:delete', `Deleted block`, { blockId });
          }, 0);
        },

        moveBlock: (blockId: GutiId, newParentId?: GutiId, newIndex?: number) => {
          // Implementation for moving blocks between containers
          // This is complex and will be implemented in the next iteration
          setTimeout(() => {
            get().addHistoryEntry('block:move', `Moved block`, { blockId, newParentId, newIndex });
          }, 0);
        },

        duplicateBlock: (blockId: GutiId) => {
          const state = get();
          const block = state.getBlockById(blockId);
          if (block) {
            const { parent } = state.findBlockParent(blockId);

            // Deep clone the block and assign new IDs
            const cloneBlockWithNewIds = (originalBlock: BlockData): BlockData => {
              const cloned: BlockData = {
                ...originalBlock,
                id: generateId('block'),
              };

              if (originalBlock.children) {
                cloned.children = originalBlock.children.map(child => cloneBlockWithNewIds(child));
              }

              return cloned;
            };

            const duplicatedBlock = cloneBlockWithNewIds(block);
            state.addBlock(duplicatedBlock, parent?.id);
          }
        },

        // Selection actions
        selectBlock: (blockId: GutiId | null) => {
          set((state) => ({
            ...state,
            selection: {
              ...state.selection,
              selectedBlockId: blockId,
              selectedBlockIds: !state.selection.multiSelectMode
                ? (blockId ? [blockId] : [])
                : state.selection.selectedBlockIds,
            },
          }));
        },

        hoverBlock: (blockId: GutiId | null) => {
          set((state) => ({
            ...state,
            selection: {
              ...state.selection,
              hoveredBlockId: blockId,
            },
          }));
        },

        toggleMultiSelect: () => {
          set((state) => ({
            ...state,
            selection: {
              ...state.selection,
              multiSelectMode: !state.selection.multiSelectMode,
              selectedBlockIds: !state.selection.multiSelectMode
                ? state.selection.selectedBlockIds
                : (state.selection.selectedBlockId ? [state.selection.selectedBlockId] : []),
            },
          }));
        },

        addToSelection: (blockId: GutiId) => {
          set((state) => ({
            ...state,
            selection: {
              ...state.selection,
              selectedBlockIds: state.selection.selectedBlockIds.includes(blockId)
                ? state.selection.selectedBlockIds
                : [...state.selection.selectedBlockIds, blockId],
            },
          }));
        },

        removeFromSelection: (blockId: GutiId) => {
          set((state) => ({
            ...state,
            selection: {
              ...state.selection,
              selectedBlockIds: state.selection.selectedBlockIds.filter(id => id !== blockId),
            },
          }));
        },

        clearSelection: () => {
          set((state) => ({
            ...state,
            selection: {
              ...state.selection,
              selectedBlockId: null,
              selectedBlockIds: [],
              hoveredBlockId: null,
            },
          }));
        },

        // UI actions
        setEditorMode: (mode) => {
          set((state) => ({
            ...state,
            ui: {
              ...state.ui,
              mode,
            },
          }));
        },

        setViewportSize: (size) => {
          set((state) => ({
            ...state,
            ui: {
              ...state.ui,
              viewportSize: size,
            },
          }));
        },

        toggleSidebar: () => {
          set((state) => ({
            ...state,
            ui: {
              ...state.ui,
              sidebarOpen: !state.ui.sidebarOpen,
            },
          }));
        },

        togglePropertiesPanel: () => {
          set((state) => ({
            ...state,
            ui: {
              ...state.ui,
              propertiesPanelOpen: !state.ui.propertiesPanelOpen,
            },
          }));
        },

        toggleGrid: () => {
          set((state) => ({
            ...state,
            ui: {
              ...state.ui,
              showGrid: !state.ui.showGrid,
            },
          }));
        },

        toggleOutlines: () => {
          set((state) => ({
            ...state,
            ui: {
              ...state.ui,
              showOutlines: !state.ui.showOutlines,
            },
          }));
        },

        toggleResponsiveHelpers: () => {
          set((state) => ({
            ...state,
            ui: {
              ...state.ui,
              showResponsiveHelpers: !state.ui.showResponsiveHelpers,
            },
          }));
        },

        setZoomLevel: (level: number) => {
          set((state) => ({
            ...state,
            ui: {
              ...state.ui,
              zoomLevel: Math.max(0.25, Math.min(3.0, level)),
            },
          }));
        },

        setLoading: (key, loading) => {
          set((state) => ({
            ...state,
            ui: {
              ...state.ui,
              loading: {
                ...state.ui.loading,
                [key]: loading,
              },
            },
          }));
        },

        setError: (key, error) => {
          set((state) => {
            const newErrors = { ...state.ui.errors };
            if (error) {
              if (key === 'validation') {
                newErrors[key] = [error];
              } else {
                newErrors[key] = error;
              }
            } else {
              delete newErrors[key];
            }

            return {
              ...state,
              ui: {
                ...state.ui,
                errors: newErrors,
              },
            };
          });
        },

        clearErrors: () => {
          set((state) => ({
            ...state,
            ui: {
              ...state.ui,
              errors: {},
            },
          }));
        },

        // History actions (simplified for now)
        undo: () => {
          const state = get();
          if (state.history.canUndo && state.history.currentIndex > 0) {
            const previousEntry = state.history.entries[state.history.currentIndex - 1];
            if (previousEntry) {
              set((currentState) => ({
                ...currentState,
                page: {
                  ...currentState.page,
                  pageData: previousEntry.pageData,
                  hasUnsavedChanges: true,
                },
                history: {
                  ...currentState.history,
                  currentIndex: currentState.history.currentIndex - 1,
                  canUndo: currentState.history.currentIndex - 1 > 0,
                  canRedo: true,
                },
              }));
            }
          }
        },

        redo: () => {
          const state = get();
          if (state.history.canRedo && state.history.currentIndex < state.history.entries.length - 1) {
            const nextEntry = state.history.entries[state.history.currentIndex + 1];
            if (nextEntry) {
              set((currentState) => ({
                ...currentState,
                page: {
                  ...currentState.page,
                  pageData: nextEntry.pageData,
                  hasUnsavedChanges: true,
                },
                history: {
                  ...currentState.history,
                  currentIndex: currentState.history.currentIndex + 1,
                  canRedo: currentState.history.currentIndex + 1 < currentState.history.entries.length - 1,
                  canUndo: true,
                },
              }));
            }
          }
        },

        addHistoryEntry: (_action: ActionType, description: string, _payload?: any) => {
          const state = get();
          if (!state.page.pageData) return;

          // Create a deep clone of the current page data
          const pageDataClone = JSON.parse(JSON.stringify(state.page.pageData));

          set((currentState) => {
            const entry = {
              id: generateId('history'),
              timestamp: Date.now(),
              action: description,
              pageData: pageDataClone,
            };

            // Remove any entries after current index (when undoing then making new changes)
            const newEntries = currentState.history.entries.slice(0, currentState.history.currentIndex + 1);

            // Add new entry
            newEntries.push(entry);
            let newCurrentIndex = newEntries.length - 1;

            // Limit history size
            if (newEntries.length > currentState.history.maxEntries) {
              newEntries.shift();
              newCurrentIndex--;
            }

            return {
              ...currentState,
              history: {
                ...currentState.history,
                entries: newEntries,
                currentIndex: newCurrentIndex,
                canUndo: newEntries.length > 1,
                canRedo: false,
              },
            };
          });
        },

        clearHistory: () => {
          set((state) => ({
            ...state,
            history: {
              ...state.history,
              entries: [],
              currentIndex: -1,
              canUndo: false,
              canRedo: false,
            },
          }));
        },

        // Theme actions
        setThemeMode: (mode: ThemeMode) => {
          set((state) => ({
            ...state,
            theme: {
              ...state.theme,
              mode,
            },
          }));
        },

        updateTheme: (updates) => {
          set((state) => ({
            ...state,
            theme: {
              ...state.theme,
              theme: {
                ...state.theme.theme,
                ...updates,
              },
            },
            page: {
              ...state.page,
              hasUnsavedChanges: true,
            },
          }));
        },

        resetTheme: () => {
          set((state) => ({
            ...state,
            theme: {
              ...state.theme,
              theme: tokensToConfig(defaultThemeTokens),
              customizations: {},
            },
          }));
        },

        // Utility actions
        reset: () => {
          set(() => ({ ...initialState }));
        },

        getBlockById: (blockId: GutiId): BlockData | null => {
          const state = get();
          if (!state.page.pageData) return null;

          const findBlock = (blocks: BlockData[]): BlockData | null => {
            for (const block of blocks) {
              if (block.id === blockId) return block;
              if (block.children) {
                const found = findBlock(block.children);
                if (found) return found;
              }
            }
            return null;
          };

          return findBlock(state.page.pageData.blocks);
        },

        getBlockPath: (blockId: GutiId): GutiId[] => {
          const state = get();
          if (!state.page.pageData) return [];

          const findPath = (blocks: BlockData[], path: GutiId[] = []): GutiId[] => {
            for (const block of blocks) {
              const currentPath = [...path, block.id];
              if (block.id === blockId) return currentPath;
              if (block.children) {
                const found = findPath(block.children, currentPath);
                if (found.length > 0) return found;
              }
            }
            return [];
          };

          return findPath(state.page.pageData.blocks);
        },

        findBlockParent: (blockId: GutiId): { parent: BlockData | null; index: number } => {
          const state = get();
          if (!state.page.pageData) return { parent: null, index: -1 };

          // Check root level
          const rootIndex = state.page.pageData.blocks.findIndex(block => block.id === blockId);
          if (rootIndex >= 0) {
            return { parent: null, index: rootIndex };
          }

          // Check nested blocks
          const findParent = (blocks: BlockData[]): { parent: BlockData | null; index: number } => {
            for (const block of blocks) {
              if (block.children) {
                const childIndex = block.children.findIndex(child => child.id === blockId);
                if (childIndex >= 0) {
                  return { parent: block, index: childIndex };
                }
                const result = findParent(block.children);
                if (result.parent) return result;
              }
            }
            return { parent: null, index: -1 };
          };

          return findParent(state.page.pageData.blocks);
        },
      }))
    );
