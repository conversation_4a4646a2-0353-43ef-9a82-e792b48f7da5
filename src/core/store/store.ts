import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { persist } from 'zustand/middleware';
import { BuilderState, ActionType } from './types';
import { PageData, BlockData, GutiId } from '../../types/core';
import { ThemeMode } from '../../types/theme';
import { defaultThemeTokens } from '../../theme/tokens';
import { tokensToConfig } from '../../theme/converter';
import { generateId, generatePageId } from '../../utils/id';

/**
 * Initial state for the builder
 */
const initialState: BuilderState = {
  page: {
    pageData: null,
    hasUnsavedChanges: false,
    lastSaved: null,
    autoSaveInterval: null,
  },
  ui: {
    mode: 'edit',
    viewportSize: 'desktop',
    sidebarOpen: true,
    propertiesPanelOpen: true,
    showGrid: false,
    showOutlines: true,
    showResponsiveHelpers: false,
    zoomLevel: 1.0,
    loading: {
      page: false,
      saving: false,
      blocks: false,
    },
    errors: {},
  },
  selection: {
    selectedBlockId: null,
    hoveredBlockId: null,
    selectedBlockIds: [],
    multiSelectMode: false,
  },
  history: {
    entries: [],
    currentIndex: -1,
    maxEntries: 50,
    canUndo: false,
    canRedo: false,
  },
  theme: {
    theme: tokensToConfig(defaultThemeTokens),
    mode: 'light',
    presets: {},
    customizations: {},
  },
};

/**
 * Builder store actions interface
 */
export interface BuilderActions {
  // Page actions
  setPageData: (pageData: PageData) => void;
  createNewPage: (title?: string) => void;
  updatePageMeta: (meta: Partial<PageData>) => void;
  markUnsavedChanges: () => void;
  markSaved: () => void;

  // Block actions
  addBlock: (block: BlockData, parentId?: GutiId, index?: number) => void;
  updateBlock: (blockId: GutiId, updates: Partial<BlockData>) => void;
  deleteBlock: (blockId: GutiId) => void;
  moveBlock: (blockId: GutiId, newParentId?: GutiId, newIndex?: number) => void;
  duplicateBlock: (blockId: GutiId) => void;

  // Selection actions
  selectBlock: (blockId: GutiId | null) => void;
  hoverBlock: (blockId: GutiId | null) => void;
  toggleMultiSelect: () => void;
  addToSelection: (blockId: GutiId) => void;
  removeFromSelection: (blockId: GutiId) => void;
  clearSelection: () => void;

  // UI actions
  setEditorMode: (mode: 'edit' | 'preview') => void;
  setViewportSize: (size: 'mobile' | 'tablet' | 'desktop' | 'auto') => void;
  toggleSidebar: () => void;
  togglePropertiesPanel: () => void;
  toggleGrid: () => void;
  toggleOutlines: () => void;
  toggleResponsiveHelpers: () => void;
  setZoomLevel: (level: number) => void;
  setLoading: (key: keyof BuilderState['ui']['loading'], loading: boolean) => void;
  setError: (key: keyof BuilderState['ui']['errors'], error?: string) => void;
  clearErrors: () => void;

  // History actions
  undo: () => void;
  redo: () => void;
  addHistoryEntry: (action: ActionType, description: string, payload?: any) => void;
  clearHistory: () => void;

  // Theme actions
  setThemeMode: (mode: ThemeMode) => void;
  updateTheme: (updates: Partial<BuilderState['theme']['theme']>) => void;
  resetTheme: () => void;

  // Utility actions
  reset: () => void;
  getBlockById: (blockId: GutiId) => BlockData | null;
  getBlockPath: (blockId: GutiId) => GutiId[];
  findBlockParent: (blockId: GutiId) => { parent: BlockData | null; index: number };
}

/**
 * Complete store type
 */
export type BuilderStore = BuilderState & BuilderActions;

/**
 * Create the builder store
 */
export const useBuilderStore = create<BuilderStore>()(
  subscribeWithSelector(
    persist(
      immer((set, get) => ({
        ...initialState,

        // Page actions
        setPageData: (pageData: PageData) => {
          set((state) => {
            state.page.pageData = pageData;
            state.page.hasUnsavedChanges = false;
            state.page.lastSaved = Date.now();
            state.selection.selectedBlockId = null;
            state.selection.selectedBlockIds = [];
          });
        },

        createNewPage: (title = 'New Page') => {
          const newPage: PageData = {
            id: generatePageId(),
            title,
            description: '',
            blocks: [],
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          };
          
          set((state) => {
            state.page.pageData = newPage;
            state.page.hasUnsavedChanges = true;
            state.page.lastSaved = null;
            state.selection.selectedBlockId = null;
            state.selection.selectedBlockIds = [];
            state.history.entries = [];
            state.history.currentIndex = -1;
            state.history.canUndo = false;
            state.history.canRedo = false;
          });
        },

        updatePageMeta: (meta: Partial<PageData>) => {
          set((state) => {
            if (state.page.pageData) {
              Object.assign(state.page.pageData, meta);
              state.page.pageData.updatedAt = new Date().toISOString();
              state.page.hasUnsavedChanges = true;
            }
          });
        },

        markUnsavedChanges: () => {
          set((state) => {
            state.page.hasUnsavedChanges = true;
          });
        },

        markSaved: () => {
          set((state) => {
            state.page.hasUnsavedChanges = false;
            state.page.lastSaved = Date.now();
          });
        },

        // Block actions
        addBlock: (block: BlockData, parentId?: GutiId, index?: number) => {
          set((state) => {
            if (!state.page.pageData) return;

            const targetBlocks = parentId 
              ? get().getBlockById(parentId)?.children || []
              : state.page.pageData.blocks;

            const insertIndex = index !== undefined ? index : targetBlocks.length;
            
            if (parentId) {
              const parent = get().getBlockById(parentId);
              if (parent?.children) {
                parent.children.splice(insertIndex, 0, block);
              }
            } else {
              state.page.pageData.blocks.splice(insertIndex, 0, block);
            }

            state.page.pageData.updatedAt = new Date().toISOString();
            state.page.hasUnsavedChanges = true;
            state.selection.selectedBlockId = block.id;
          });

          get().addHistoryEntry('block:add', `Added ${block.type} block`, { blockId: block.id, parentId });
        },

        updateBlock: (blockId: GutiId, updates: Partial<BlockData>) => {
          set((state) => {
            const block = get().getBlockById(blockId);
            if (block && state.page.pageData) {
              Object.assign(block, updates);
              state.page.pageData.updatedAt = new Date().toISOString();
              state.page.hasUnsavedChanges = true;
            }
          });

          get().addHistoryEntry('block:update', `Updated block`, { blockId, updates });
        },

        deleteBlock: (blockId: GutiId) => {
          set((state) => {
            if (!state.page.pageData) return;

            const { parent, index } = get().findBlockParent(blockId);
            
            if (parent?.children) {
              parent.children.splice(index, 1);
            } else if (index >= 0) {
              state.page.pageData.blocks.splice(index, 1);
            }

            state.page.pageData.updatedAt = new Date().toISOString();
            state.page.hasUnsavedChanges = true;
            
            // Clear selection if deleted block was selected
            if (state.selection.selectedBlockId === blockId) {
              state.selection.selectedBlockId = null;
            }
            
            // Remove from multi-selection
            const multiIndex = state.selection.selectedBlockIds.indexOf(blockId);
            if (multiIndex >= 0) {
              state.selection.selectedBlockIds.splice(multiIndex, 1);
            }
          });

          get().addHistoryEntry('block:delete', `Deleted block`, { blockId });
        },

        moveBlock: (blockId: GutiId, newParentId?: GutiId, newIndex?: number) => {
          // Implementation for moving blocks between containers
          // This is complex and will be implemented in the next iteration
          get().addHistoryEntry('block:move', `Moved block`, { blockId, newParentId, newIndex });
        },

        duplicateBlock: (blockId: GutiId) => {
          const block = get().getBlockById(blockId);
          if (block) {
            const { parent } = get().findBlockParent(blockId);
            const duplicatedBlock = {
              ...block,
              id: generateId('block'),
            };
            
            get().addBlock(duplicatedBlock, parent?.id);
          }
        },

        // Selection actions
        selectBlock: (blockId: GutiId | null) => {
          set((state) => {
            state.selection.selectedBlockId = blockId;
            if (!state.selection.multiSelectMode) {
              state.selection.selectedBlockIds = blockId ? [blockId] : [];
            }
          });
        },

        hoverBlock: (blockId: GutiId | null) => {
          set((state) => {
            state.selection.hoveredBlockId = blockId;
          });
        },

        toggleMultiSelect: () => {
          set((state) => {
            state.selection.multiSelectMode = !state.selection.multiSelectMode;
            if (!state.selection.multiSelectMode) {
              state.selection.selectedBlockIds = state.selection.selectedBlockId 
                ? [state.selection.selectedBlockId] 
                : [];
            }
          });
        },

        addToSelection: (blockId: GutiId) => {
          set((state) => {
            if (!state.selection.selectedBlockIds.includes(blockId)) {
              state.selection.selectedBlockIds.push(blockId);
            }
          });
        },

        removeFromSelection: (blockId: GutiId) => {
          set((state) => {
            const index = state.selection.selectedBlockIds.indexOf(blockId);
            if (index >= 0) {
              state.selection.selectedBlockIds.splice(index, 1);
            }
          });
        },

        clearSelection: () => {
          set((state) => {
            state.selection.selectedBlockId = null;
            state.selection.selectedBlockIds = [];
            state.selection.hoveredBlockId = null;
          });
        },

        // UI actions
        setEditorMode: (mode) => {
          set((state) => {
            state.ui.mode = mode;
          });
        },

        setViewportSize: (size) => {
          set((state) => {
            state.ui.viewportSize = size;
          });
        },

        toggleSidebar: () => {
          set((state) => {
            state.ui.sidebarOpen = !state.ui.sidebarOpen;
          });
        },

        togglePropertiesPanel: () => {
          set((state) => {
            state.ui.propertiesPanelOpen = !state.ui.propertiesPanelOpen;
          });
        },

        toggleGrid: () => {
          set((state) => {
            state.ui.showGrid = !state.ui.showGrid;
          });
        },

        toggleOutlines: () => {
          set((state) => {
            state.ui.showOutlines = !state.ui.showOutlines;
          });
        },

        toggleResponsiveHelpers: () => {
          set((state) => {
            state.ui.showResponsiveHelpers = !state.ui.showResponsiveHelpers;
          });
        },

        setZoomLevel: (level: number) => {
          set((state) => {
            state.ui.zoomLevel = Math.max(0.25, Math.min(3.0, level));
          });
        },

        setLoading: (key, loading) => {
          set((state) => {
            state.ui.loading[key] = loading;
          });
        },

        setError: (key, error) => {
          set((state) => {
            if (error) {
              if (key === 'validation') {
                state.ui.errors[key] = [error];
              } else {
                state.ui.errors[key] = error;
              }
            } else {
              delete state.ui.errors[key];
            }
          });
        },

        clearErrors: () => {
          set((state) => {
            state.ui.errors = {};
          });
        },

        // History actions (simplified for now)
        undo: () => {
          const state = get();
          if (state.history.canUndo && state.history.currentIndex > 0) {
            const previousEntry = state.history.entries[state.history.currentIndex - 1];
            if (previousEntry) {
              set((draft) => {
                draft.page.pageData = previousEntry.pageData;
                draft.history.currentIndex--;
                draft.history.canUndo = draft.history.currentIndex > 0;
                draft.history.canRedo = true;
                draft.page.hasUnsavedChanges = true;
              });
            }
          }
        },

        redo: () => {
          const state = get();
          if (state.history.canRedo && state.history.currentIndex < state.history.entries.length - 1) {
            const nextEntry = state.history.entries[state.history.currentIndex + 1];
            if (nextEntry) {
              set((draft) => {
                draft.page.pageData = nextEntry.pageData;
                draft.history.currentIndex++;
                draft.history.canRedo = draft.history.currentIndex < draft.history.entries.length - 1;
                draft.history.canUndo = true;
                draft.page.hasUnsavedChanges = true;
              });
            }
          }
        },

        addHistoryEntry: (_action: ActionType, description: string, _payload?: any) => {
          const state = get();
          if (!state.page.pageData) return;

          set((draft) => {
            const entry = {
              id: generateId('history'),
              timestamp: Date.now(),
              action: description,
              pageData: JSON.parse(JSON.stringify(state.page.pageData)), // Deep clone
            };

            // Remove any entries after current index (when undoing then making new changes)
            draft.history.entries = draft.history.entries.slice(0, draft.history.currentIndex + 1);
            
            // Add new entry
            draft.history.entries.push(entry);
            draft.history.currentIndex = draft.history.entries.length - 1;
            
            // Limit history size
            if (draft.history.entries.length > draft.history.maxEntries) {
              draft.history.entries.shift();
              draft.history.currentIndex--;
            }
            
            // Update undo/redo availability
            draft.history.canUndo = draft.history.entries.length > 1;
            draft.history.canRedo = false;
          });
        },

        clearHistory: () => {
          set((state) => {
            state.history.entries = [];
            state.history.currentIndex = -1;
            state.history.canUndo = false;
            state.history.canRedo = false;
          });
        },

        // Theme actions
        setThemeMode: (mode: ThemeMode) => {
          set((state) => {
            state.theme.mode = mode;
          });
        },

        updateTheme: (updates) => {
          set((state) => {
            Object.assign(state.theme.theme, updates);
            state.page.hasUnsavedChanges = true;
          });
        },

        resetTheme: () => {
          set((state) => {
            state.theme.theme = tokensToConfig(defaultThemeTokens);
            state.theme.customizations = {};
          });
        },

        // Utility actions
        reset: () => {
          set(() => ({ ...initialState }));
        },

        getBlockById: (blockId: GutiId): BlockData | null => {
          const state = get();
          if (!state.page.pageData) return null;

          const findBlock = (blocks: BlockData[]): BlockData | null => {
            for (const block of blocks) {
              if (block.id === blockId) return block;
              if (block.children) {
                const found = findBlock(block.children);
                if (found) return found;
              }
            }
            return null;
          };

          return findBlock(state.page.pageData.blocks);
        },

        getBlockPath: (blockId: GutiId): GutiId[] => {
          const state = get();
          if (!state.page.pageData) return [];

          const findPath = (blocks: BlockData[], path: GutiId[] = []): GutiId[] => {
            for (const block of blocks) {
              const currentPath = [...path, block.id];
              if (block.id === blockId) return currentPath;
              if (block.children) {
                const found = findPath(block.children, currentPath);
                if (found.length > 0) return found;
              }
            }
            return [];
          };

          return findPath(state.page.pageData.blocks);
        },

        findBlockParent: (blockId: GutiId): { parent: BlockData | null; index: number } => {
          const state = get();
          if (!state.page.pageData) return { parent: null, index: -1 };

          // Check root level
          const rootIndex = state.page.pageData.blocks.findIndex(block => block.id === blockId);
          if (rootIndex >= 0) {
            return { parent: null, index: rootIndex };
          }

          // Check nested blocks
          const findParent = (blocks: BlockData[]): { parent: BlockData | null; index: number } => {
            for (const block of blocks) {
              if (block.children) {
                const childIndex = block.children.findIndex(child => child.id === blockId);
                if (childIndex >= 0) {
                  return { parent: block, index: childIndex };
                }
                const result = findParent(block.children);
                if (result.parent) return result;
              }
            }
            return { parent: null, index: -1 };
          };

          return findParent(state.page.pageData.blocks);
        },
      })),
      {
        name: 'guti-builder-store',
        partialize: (state) => ({
          // Only persist certain parts of the state
          page: {
            pageData: state.page.pageData,
          },
          theme: {
            mode: state.theme.mode,
            customizations: state.theme.customizations,
          },
          ui: {
            sidebarOpen: state.ui.sidebarOpen,
            propertiesPanelOpen: state.ui.propertiesPanelOpen,
            showGrid: state.ui.showGrid,
            showOutlines: state.ui.showOutlines,
            zoomLevel: state.ui.zoomLevel,
          },
        }),
      }
    )
  )
);
