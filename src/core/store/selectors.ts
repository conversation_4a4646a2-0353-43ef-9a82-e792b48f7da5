import { BuilderState } from './types';
import { GutiId, BlockData } from '../../types/core';

/**
 * Memoized selectors for performance optimization
 */

/**
 * Select page data
 */
export const selectPageData = (state: BuilderState) => state.page.pageData;

/**
 * Select blocks array
 */
export const selectBlocks = (state: BuilderState) => state.page.pageData?.blocks || [];

/**
 * Select a specific block by ID
 */
export const selectBlockById = (blockId: GutiId) => (state: BuilderState): BlockData | null => {
  if (!state.page.pageData) return null;

  const findBlock = (blocks: BlockData[]): BlockData | null => {
    for (const block of blocks) {
      if (block.id === blockId) return block;
      if (block.children) {
        const found = findBlock(block.children);
        if (found) return found;
      }
    }
    return null;
  };

  return findBlock(state.page.pageData.blocks);
};

/**
 * Select blocks by type
 */
export const selectBlocksByType = (blockType: string) => (state: BuilderState): BlockData[] => {
  if (!state.page.pageData) return [];

  const findBlocks = (blocks: BlockData[]): BlockData[] => {
    const result: BlockData[] = [];
    for (const block of blocks) {
      if (block.type === blockType) {
        result.push(block);
      }
      if (block.children) {
        result.push(...findBlocks(block.children));
      }
    }
    return result;
  };

  return findBlocks(state.page.pageData.blocks);
};

/**
 * Select children of a specific block
 */
export const selectBlockChildren = (parentId: GutiId) => (state: BuilderState): BlockData[] => {
  const parent = selectBlockById(parentId)(state);
  return parent?.children || [];
};

/**
 * Select block path (array of parent IDs)
 */
export const selectBlockPath = (blockId: GutiId) => (state: BuilderState): GutiId[] => {
  if (!state.page.pageData) return [];

  const findPath = (blocks: BlockData[], path: GutiId[] = []): GutiId[] => {
    for (const block of blocks) {
      const currentPath = [...path, block.id];
      if (block.id === blockId) return currentPath;
      if (block.children) {
        const found = findPath(block.children, currentPath);
        if (found.length > 0) return found;
      }
    }
    return [];
  };

  return findPath(state.page.pageData.blocks);
};

/**
 * Select block depth in the tree
 */
export const selectBlockDepth = (blockId: GutiId) => (state: BuilderState): number => {
  const path = selectBlockPath(blockId)(state);
  return path.length - 1; // Subtract 1 because path includes the block itself
};

/**
 * Select all descendant blocks of a parent
 */
export const selectDescendantBlocks = (parentId: GutiId) => (state: BuilderState): BlockData[] => {
  const parent = selectBlockById(parentId)(state);
  if (!parent?.children) return [];

  const getAllDescendants = (blocks: BlockData[]): BlockData[] => {
    const result: BlockData[] = [];
    for (const block of blocks) {
      result.push(block);
      if (block.children) {
        result.push(...getAllDescendants(block.children));
      }
    }
    return result;
  };

  return getAllDescendants(parent.children);
};

/**
 * Select currently selected block
 */
export const selectSelectedBlock = (state: BuilderState): BlockData | null => {
  const selectedId = state.selection.selectedBlockId;
  return selectedId ? selectBlockById(selectedId)(state) : null;
};

/**
 * Select currently hovered block
 */
export const selectHoveredBlock = (state: BuilderState): BlockData | null => {
  const hoveredId = state.selection.hoveredBlockId;
  return hoveredId ? selectBlockById(hoveredId)(state) : null;
};

/**
 * Select all selected blocks (for multi-selection)
 */
export const selectSelectedBlocks = (state: BuilderState): BlockData[] => {
  return state.selection.selectedBlockIds
    .map(id => selectBlockById(id)(state))
    .filter((block): block is BlockData => block !== null);
};

/**
 * Select UI state
 */
export const selectUIState = (state: BuilderState) => state.ui;

/**
 * Select editor mode
 */
export const selectEditorMode = (state: BuilderState) => state.ui.mode;

/**
 * Select viewport size
 */
export const selectViewportSize = (state: BuilderState) => state.ui.viewportSize;

/**
 * Select loading states
 */
export const selectLoadingStates = (state: BuilderState) => state.ui.loading;

/**
 * Select error states
 */
export const selectErrorStates = (state: BuilderState) => state.ui.errors;

/**
 * Select theme configuration
 */
export const selectTheme = (state: BuilderState) => state.theme.theme;

/**
 * Select theme mode
 */
export const selectThemeMode = (state: BuilderState) => state.theme.mode;

/**
 * Select history state
 */
export const selectHistoryState = (state: BuilderState) => state.history;

/**
 * Select undo/redo availability
 */
export const selectUndoRedoState = (state: BuilderState) => ({
  canUndo: state.history.canUndo,
  canRedo: state.history.canRedo,
  currentIndex: state.history.currentIndex,
  totalEntries: state.history.entries.length,
});

/**
 * Select unsaved changes state
 */
export const selectUnsavedChanges = (state: BuilderState) => ({
  hasUnsavedChanges: state.page.hasUnsavedChanges,
  lastSaved: state.page.lastSaved,
});

/**
 * Select page metadata
 */
export const selectPageMeta = (state: BuilderState) => {
  const page = state.page.pageData;
  return page ? {
    id: page.id,
    title: page.title,
    description: page.description,
    meta: page.meta,
    createdAt: page.createdAt,
    updatedAt: page.updatedAt,
  } : null;
};

/**
 * Select block count statistics
 */
export const selectBlockStats = (state: BuilderState) => {
  if (!state.page.pageData) {
    return { total: 0, byType: {} };
  }

  const countBlocks = (blocks: BlockData[]): { total: number; byType: Record<string, number> } => {
    let total = 0;
    const byType: Record<string, number> = {};

    for (const block of blocks) {
      total++;
      byType[block.type] = (byType[block.type] || 0) + 1;
      
      if (block.children) {
        const childStats = countBlocks(block.children);
        total += childStats.total;
        
        for (const [type, count] of Object.entries(childStats.byType)) {
          byType[type] = (byType[type] || 0) + count;
        }
      }
    }

    return { total, byType };
  };

  return countBlocks(state.page.pageData.blocks);
};

/**
 * Select whether a block can be deleted (not if it's the only block)
 */
export const selectCanDeleteBlock = (_blockId: GutiId) => (state: BuilderState): boolean => {
  if (!state.page.pageData) return false;

  const stats = selectBlockStats(state);
  return stats.total > 1;
};

/**
 * Select whether a block can have children
 */
export const selectCanHaveChildren = (blockId: GutiId) => (state: BuilderState): boolean => {
  const block = selectBlockById(blockId)(state);
  if (!block) return false;
  
  // Container and grid blocks can have children
  return ['container', 'grid'].includes(block.type);
};

/**
 * Select validation errors for the current page
 */
export const selectValidationErrors = (state: BuilderState): string[] => {
  return state.ui.errors.validation || [];
};

/**
 * Select whether the builder is in a loading state
 */
export const selectIsLoading = (state: BuilderState): boolean => {
  const loading = state.ui.loading;
  return loading.page || loading.saving || loading.blocks;
};
