import { ThemeTokens, ThemeMode } from '../types/theme';
import { defaultThemeTokens, darkThemeOverrides } from './tokens';

/**
 * Deep merge two objects
 * @param target - Target object
 * @param source - Source object to merge
 * @returns Merged object
 */
function deepMerge<T extends Record<string, any>>(target: T, source: Partial<T>): T {
  const result = { ...target };

  for (const key in source) {
    if (Object.prototype.hasOwnProperty.call(source, key)) {
      const sourceValue = source[key];
      const targetValue = result[key];

      if (
        sourceValue &&
        typeof sourceValue === 'object' &&
        !Array.isArray(sourceValue) &&
        targetValue &&
        typeof targetValue === 'object' &&
        !Array.isArray(targetValue)
      ) {
        result[key] = deepMerge(targetValue, sourceValue);
      } else if (sourceValue !== undefined) {
        result[key] = sourceValue;
      }
    }
  }

  return result;
}

/**
 * Create a complete theme configuration from partial tokens
 * @param tokens - Partial theme tokens to override defaults
 * @returns Complete theme tokens
 */
export function createThemeTokens(tokens: Partial<ThemeTokens> = {}): ThemeTokens {
  return deepMerge(defaultThemeTokens, tokens);
}

/**
 * Apply theme mode overrides to theme tokens
 * @param tokens - Base theme tokens
 * @param mode - Theme mode
 * @returns Theme tokens with mode applied
 */
export function applyThemeMode(tokens: ThemeTokens, mode: ThemeMode): ThemeTokens {
  if (mode === 'dark') {
    return deepMerge(tokens, darkThemeOverrides);
  }
  return tokens;
}

/**
 * Generate CSS custom properties from theme tokens
 * @param tokens - Theme tokens
 * @param prefix - CSS custom property prefix
 * @returns CSS custom properties object
 */
export function generateCSSCustomProperties(
  tokens: ThemeTokens,
  prefix = 'guti'
): Record<string, string> {
  const properties: Record<string, string> = {};

  // Colors
  Object.entries(tokens.colors).forEach(([key, value]) => {
    if (typeof value === 'object') {
      // Color scale
      Object.entries(value).forEach(([scale, color]) => {
        properties[`--${prefix}-color-${key}-${scale}`] = color;
      });
    } else {
      // Single color
      properties[`--${prefix}-color-${key}`] = value;
    }
  });

  // Typography
  Object.entries(tokens.typography.fontFamily).forEach(([key, value]) => {
    properties[`--${prefix}-font-family-${key}`] = value;
  });

  Object.entries(tokens.typography.fontSize).forEach(([key, value]) => {
    properties[`--${prefix}-font-size-${key}`] = value;
  });

  Object.entries(tokens.typography.fontWeight).forEach(([key, value]) => {
    properties[`--${prefix}-font-weight-${key}`] = value.toString();
  });

  Object.entries(tokens.typography.lineHeight).forEach(([key, value]) => {
    properties[`--${prefix}-line-height-${key}`] = value.toString();
  });

  Object.entries(tokens.typography.letterSpacing).forEach(([key, value]) => {
    properties[`--${prefix}-letter-spacing-${key}`] = value;
  });

  // Spacing
  Object.entries(tokens.spacing).forEach(([key, value]) => {
    properties[`--${prefix}-spacing-${key}`] = value;
  });

  // Border radius
  Object.entries(tokens.borderRadius).forEach(([key, value]) => {
    properties[`--${prefix}-radius-${key}`] = value;
  });

  // Shadows
  Object.entries(tokens.shadows).forEach(([key, value]) => {
    properties[`--${prefix}-shadow-${key}`] = value;
  });

  // Breakpoints
  Object.entries(tokens.breakpoints).forEach(([key, value]) => {
    properties[`--${prefix}-breakpoint-${key}`] = value;
  });

  // Animation durations
  Object.entries(tokens.animations.duration).forEach(([key, value]) => {
    properties[`--${prefix}-duration-${key}`] = value;
  });

  // Animation easing
  Object.entries(tokens.animations.easing).forEach(([key, value]) => {
    properties[`--${prefix}-easing-${key}`] = value;
  });

  return properties;
}

/**
 * Apply CSS custom properties to a DOM element
 * @param element - DOM element to apply properties to
 * @param properties - CSS custom properties
 */
export function applyCSSCustomProperties(
  element: HTMLElement,
  properties: Record<string, string>
): void {
  Object.entries(properties).forEach(([property, value]) => {
    element.style.setProperty(property, value);
  });
}

/**
 * Get a color value from a color scale
 * @param scale - Color scale object
 * @param shade - Shade number (50-950)
 * @returns Color value or undefined
 */
export function getColorFromScale(
  scale: Record<string, string>,
  shade: number | string
): string | undefined {
  return scale[shade.toString()];
}

/**
 * Validate if a color is a valid hex color
 * @param color - Color string to validate
 * @returns True if valid hex color
 */
export function isValidHexColor(color: string): boolean {
  return /^#[0-9a-fA-F]{6}$/.test(color);
}

/**
 * Convert hex color to RGB values
 * @param hex - Hex color string
 * @returns RGB values object or null if invalid
 */
export function hexToRgb(hex: string): { r: number; g: number; b: number } | null {
  if (!isValidHexColor(hex)) {
    return null;
  }

  const result = /^#([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result
    ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16),
      }
    : null;
}

/**
 * Convert RGB values to hex color
 * @param r - Red value (0-255)
 * @param g - Green value (0-255)
 * @param b - Blue value (0-255)
 * @returns Hex color string
 */
export function rgbToHex(r: number, g: number, b: number): string {
  const toHex = (n: number) => {
    const hex = Math.round(Math.max(0, Math.min(255, n))).toString(16);
    return hex.length === 1 ? '0' + hex : hex;
  };

  return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
}
