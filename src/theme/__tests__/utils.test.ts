import { describe, it, expect } from 'vitest';
import {
  createThemeTokens,
  applyThemeMode,
  generateCSSCustomProperties,
  isValidHexColor,
  hexToRgb,
  rgbToHex,
} from '../utils';
import { defaultThemeTokens } from '../tokens';

describe('Theme utilities', () => {
  describe('createThemeTokens', () => {
    it('should return default tokens when no overrides provided', () => {
      const tokens = createThemeTokens();
      expect(tokens).toEqual(defaultThemeTokens);
    });

    it('should merge custom tokens with defaults', () => {
      const customTokens = createThemeTokens({
        colors: {
          ...defaultThemeTokens.colors,
          primary: {
            ...defaultThemeTokens.colors.primary,
            500: '#ff0000',
          },
        },
      });

      expect(customTokens.colors.primary[500]).toBe('#ff0000');
      expect(customTokens.colors.secondary).toEqual(defaultThemeTokens.colors.secondary);
    });
  });

  describe('applyThemeMode', () => {
    it('should return original tokens for light mode', () => {
      const tokens = createThemeTokens();
      const lightTokens = applyThemeMode(tokens, 'light');
      expect(lightTokens).toEqual(tokens);
    });

    it('should apply dark overrides for dark mode', () => {
      const tokens = createThemeTokens();
      const darkTokens = applyThemeMode(tokens, 'dark');
      
      expect(darkTokens.colors.background).toBe('#0f172a');
      expect(darkTokens.colors.text).toBe('#f8fafc');
    });
  });

  describe('generateCSSCustomProperties', () => {
    it('should generate CSS custom properties from tokens', () => {
      const tokens = createThemeTokens();
      const properties = generateCSSCustomProperties(tokens);

      expect(properties['--guti-color-primary-500']).toBe('#3b82f6');
      expect(properties['--guti-color-background']).toBe('#ffffff');
      expect(properties['--guti-font-size-base']).toBe('1rem');
      expect(properties['--guti-spacing-4']).toBe('1rem');
    });

    it('should use custom prefix', () => {
      const tokens = createThemeTokens();
      const properties = generateCSSCustomProperties(tokens, 'custom');

      expect(properties['--custom-color-primary-500']).toBe('#3b82f6');
      expect(properties['--guti-color-primary-500']).toBeUndefined();
    });
  });

  describe('isValidHexColor', () => {
    it('should validate correct hex colors', () => {
      expect(isValidHexColor('#ffffff')).toBe(true);
      expect(isValidHexColor('#000000')).toBe(true);
      expect(isValidHexColor('#3b82f6')).toBe(true);
      expect(isValidHexColor('#ABC123')).toBe(true);
    });

    it('should reject invalid hex colors', () => {
      expect(isValidHexColor('#fff')).toBe(false); // too short
      expect(isValidHexColor('#1234567')).toBe(false); // too long
      expect(isValidHexColor('ffffff')).toBe(false); // missing #
      expect(isValidHexColor('#gggggg')).toBe(false); // invalid characters
      expect(isValidHexColor('')).toBe(false); // empty
    });
  });

  describe('hexToRgb', () => {
    it('should convert valid hex to RGB', () => {
      expect(hexToRgb('#ffffff')).toEqual({ r: 255, g: 255, b: 255 });
      expect(hexToRgb('#000000')).toEqual({ r: 0, g: 0, b: 0 });
      expect(hexToRgb('#3b82f6')).toEqual({ r: 59, g: 130, b: 246 });
    });

    it('should return null for invalid hex', () => {
      expect(hexToRgb('#fff')).toBeNull();
      expect(hexToRgb('invalid')).toBeNull();
      expect(hexToRgb('')).toBeNull();
    });
  });

  describe('rgbToHex', () => {
    it('should convert RGB to hex', () => {
      expect(rgbToHex(255, 255, 255)).toBe('#ffffff');
      expect(rgbToHex(0, 0, 0)).toBe('#000000');
      expect(rgbToHex(59, 130, 246)).toBe('#3b82f6');
    });

    it('should clamp values to valid range', () => {
      expect(rgbToHex(-10, 300, 150)).toBe('#00ff96');
      expect(rgbToHex(128, 128, 128)).toBe('#808080');
    });
  });
});
