import React, { createContext, useContext, useEffect, useMemo } from 'react';
import { ThemeTokens, ThemeMode, ThemeContextValue } from '../types/theme';
import { applyThemeMode, generateCSSCustomProperties, applyCSSCustomProperties } from './utils';
import { defaultThemeTokens } from './tokens';

/**
 * Theme context
 */
const ThemeContext = createContext<ThemeContextValue | null>(null);

/**
 * Theme provider props
 */
export interface ThemeProviderProps {
  children: React.ReactNode;
  theme?: ThemeTokens;
  mode?: ThemeMode;
  onModeChange?: (mode: ThemeMode) => void;
  /**
   * Whether to apply CSS custom properties to the document root
   * @default true
   */
  applyToRoot?: boolean;
  /**
   * CSS custom property prefix
   * @default 'guti'
   */
  prefix?: string;
}

/**
 * Theme provider component
 * Provides theme tokens and mode management to child components
 */
export function ThemeProvider({
  children,
  theme = defaultThemeTokens,
  mode = 'light',
  onModeChange,
  applyToRoot = true,
  prefix = 'guti',
}: ThemeProviderProps) {
  // Apply theme mode to tokens
  const themedTokens = useMemo(() => {
    return applyThemeMode(theme, mode);
  }, [theme, mode]);

  // Generate CSS custom properties
  const cssProperties = useMemo(() => {
    return generateCSSCustomProperties(themedTokens, prefix);
  }, [themedTokens, prefix]);

  // Apply CSS custom properties to document root
  useEffect(() => {
    if (applyToRoot && typeof document !== 'undefined') {
      const root = document.documentElement;
      applyCSSCustomProperties(root, cssProperties);
      
      // Add theme mode class
      root.classList.remove('guti-light', 'guti-dark');
      root.classList.add(`guti-${mode}`);
      
      // Cleanup function
      return () => {
        Object.keys(cssProperties).forEach(property => {
          root.style.removeProperty(property);
        });
        root.classList.remove('guti-light', 'guti-dark');
      };
    }
  }, [cssProperties, mode, applyToRoot]);

  // Theme context value
  const contextValue: ThemeContextValue = useMemo(() => ({
    theme: themedTokens,
    mode,
    setMode: (newMode: ThemeMode) => {
      onModeChange?.(newMode);
    },
    toggleMode: () => {
      const newMode = mode === 'light' ? 'dark' : 'light';
      onModeChange?.(newMode);
    },
  }), [themedTokens, mode, onModeChange]);

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
}

/**
 * Hook to use theme context
 */
export function useTheme(): ThemeContextValue {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

/**
 * Hook to get CSS custom properties for the current theme
 */
export function useThemeCSS(prefix = 'guti'): Record<string, string> {
  const { theme } = useTheme();
  return useMemo(() => {
    return generateCSSCustomProperties(theme, prefix);
  }, [theme, prefix]);
}
