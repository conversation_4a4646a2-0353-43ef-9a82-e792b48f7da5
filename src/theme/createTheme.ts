import { ThemeTokens } from '../types/theme';
import { createThemeTokens } from './utils';

/**
 * Create a theme configuration from partial theme tokens
 * This is the main theme creation function used by consumers
 * 
 * @param tokens - Partial theme tokens to override defaults
 * @returns Complete theme tokens
 */
export function createTheme(tokens: Partial<ThemeTokens> = {}): ThemeTokens {
  return createThemeTokens(tokens);
}

/**
 * Re-export for convenience
 */
export { createThemeTokens } from './utils';
