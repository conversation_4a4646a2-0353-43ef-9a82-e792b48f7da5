import { ThemeTokens } from '../types/theme';
import { ThemeConfig } from '../types/core';

/**
 * Convert ThemeTokens to ThemeConfig for store compatibility
 * @param tokens - Theme tokens
 * @returns Theme config
 */
export function tokensToConfig(tokens: ThemeTokens): ThemeConfig {
  return {
    colors: {
      primary: tokens.colors.primary[500],
      secondary: tokens.colors.secondary[500],
      accent: tokens.colors.accent[500],
      background: tokens.colors.background,
      surface: tokens.colors.surface,
      text: tokens.colors.text,
      textMuted: tokens.colors.textMuted,
      border: tokens.colors.border,
      success: tokens.colors.success[500],
      warning: tokens.colors.warning[500],
      error: tokens.colors.error[500],
    },
    typography: {
      fontFamily: tokens.typography.fontFamily.sans,
      fontFamilyMono: tokens.typography.fontFamily.mono,
      fontSize: {
        xs: tokens.typography.fontSize.xs,
        sm: tokens.typography.fontSize.sm,
        base: tokens.typography.fontSize.base,
        lg: tokens.typography.fontSize.lg,
        xl: tokens.typography.fontSize.xl,
        '2xl': tokens.typography.fontSize['2xl'],
        '3xl': tokens.typography.fontSize['3xl'],
        '4xl': tokens.typography.fontSize['4xl'],
      },
      fontWeight: {
        light: tokens.typography.fontWeight.light,
        normal: tokens.typography.fontWeight.normal,
        medium: tokens.typography.fontWeight.medium,
        semibold: tokens.typography.fontWeight.semibold,
        bold: tokens.typography.fontWeight.bold,
      },
      lineHeight: {
        tight: tokens.typography.lineHeight.tight,
        normal: tokens.typography.lineHeight.normal,
        relaxed: tokens.typography.lineHeight.relaxed,
      },
    },
    spacing: {
      xs: tokens.spacing[1],
      sm: tokens.spacing[2],
      md: tokens.spacing[4],
      lg: tokens.spacing[6],
      xl: tokens.spacing[8],
      '2xl': tokens.spacing[12],
      '3xl': tokens.spacing[16],
      '4xl': tokens.spacing[24],
    },
    borderRadius: {
      none: tokens.borderRadius.none,
      sm: tokens.borderRadius.sm,
      base: tokens.borderRadius.base,
      lg: tokens.borderRadius.lg,
      full: tokens.borderRadius.full,
    },
    shadows: {
      sm: tokens.shadows.sm,
      base: tokens.shadows.base,
      lg: tokens.shadows.lg,
      xl: tokens.shadows.xl,
    },
    breakpoints: {
      mobile: tokens.breakpoints.sm,
      tablet: tokens.breakpoints.md,
      desktop: tokens.breakpoints.lg,
    },
  };
}
