/* Guti Builder Core Styles */
@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Component Styles */
@import './components.css';

/* CSS Custom Properties for Theme System */
:root {
  /* Colors */
  --guti-color-primary-50: #eff6ff;
  --guti-color-primary-100: #dbeafe;
  --guti-color-primary-200: #bfdbfe;
  --guti-color-primary-300: #93c5fd;
  --guti-color-primary-400: #60a5fa;
  --guti-color-primary-500: #3b82f6;
  --guti-color-primary-600: #2563eb;
  --guti-color-primary-700: #1d4ed8;
  --guti-color-primary-800: #1e40af;
  --guti-color-primary-900: #1e3a8a;
  --guti-color-primary-950: #172554;

  --guti-color-secondary-50: #f8fafc;
  --guti-color-secondary-100: #f1f5f9;
  --guti-color-secondary-200: #e2e8f0;
  --guti-color-secondary-300: #cbd5e1;
  --guti-color-secondary-400: #94a3b8;
  --guti-color-secondary-500: #64748b;
  --guti-color-secondary-600: #475569;
  --guti-color-secondary-700: #334155;
  --guti-color-secondary-800: #1e293b;
  --guti-color-secondary-900: #0f172a;
  --guti-color-secondary-950: #020617;

  /* Semantic Colors */
  --guti-color-background: #ffffff;
  --guti-color-surface: #f8fafc;
  --guti-color-text: #0f172a;
  --guti-color-text-muted: #64748b;
  --guti-color-border: #e2e8f0;
  --guti-color-success: #10b981;
  --guti-color-warning: #f59e0b;
  --guti-color-error: #ef4444;

  /* Typography */
  --guti-font-family: 'Inter', system-ui, sans-serif;
  --guti-font-family-mono: 'Monaco', 'Cascadia Code', monospace;
  --guti-font-size-xs: 0.75rem;
  --guti-font-size-sm: 0.875rem;
  --guti-font-size-base: 1rem;
  --guti-font-size-lg: 1.125rem;
  --guti-font-size-xl: 1.25rem;

  /* Spacing */
  --guti-spacing-xs: 0.25rem;
  --guti-spacing-sm: 0.5rem;
  --guti-spacing-md: 1rem;
  --guti-spacing-lg: 1.5rem;
  --guti-spacing-xl: 2rem;

  /* Border Radius */
  --guti-radius-sm: 0.125rem;
  --guti-radius-base: 0.25rem;
  --guti-radius-lg: 0.5rem;

  /* Shadows */
  --guti-shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --guti-shadow-base: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --guti-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

  /* Z-index */
  --guti-z-dropdown: 1000;
  --guti-z-sticky: 1020;
  --guti-z-fixed: 1030;
  --guti-z-modal-backdrop: 1040;
  --guti-z-modal: 1050;
  --guti-z-popover: 1060;
  --guti-z-tooltip: 1070;
}

/* Dark theme overrides */
[data-theme="dark"] {
  --guti-color-background: #0f172a;
  --guti-color-surface: #1e293b;
  --guti-color-text: #f8fafc;
  --guti-color-text-muted: #94a3b8;
  --guti-color-border: #334155;
}

/* Base component styles */
.guti-builder {
  font-family: var(--guti-font-family);
  color: var(--guti-color-text);
  background-color: var(--guti-color-background);
}

.guti-builder * {
  box-sizing: border-box;
}

/* Block wrapper styles */
.guti-block {
  position: relative;
}

.guti-block--editing {
  outline: 2px solid transparent;
  outline-offset: 2px;
  transition: outline-color 0.15s ease-in-out;
}

.guti-block--selected {
  outline-color: var(--guti-color-primary-500);
}

.guti-block--hover {
  outline-color: var(--guti-color-primary-300);
}

/* Drag and drop styles */
.guti-drag-preview {
  opacity: 0.8;
  transform: rotate(5deg);
  box-shadow: var(--guti-shadow-lg);
}

.guti-drop-zone {
  min-height: 2rem;
  border: 2px dashed var(--guti-color-border);
  border-radius: var(--guti-radius-base);
  background-color: var(--guti-color-surface);
  transition: all 0.15s ease-in-out;
}

.guti-drop-zone--active {
  border-color: var(--guti-color-primary-500);
  background-color: var(--guti-color-primary-50);
}

/* Editor panel styles */
.guti-panel {
  background-color: var(--guti-color-surface);
  border: 1px solid var(--guti-color-border);
  border-radius: var(--guti-radius-base);
  box-shadow: var(--guti-shadow-base);
}

.guti-panel__header {
  padding: var(--guti-spacing-md);
  border-bottom: 1px solid var(--guti-color-border);
  font-weight: 600;
  font-size: var(--guti-font-size-sm);
}

.guti-panel__content {
  padding: var(--guti-spacing-md);
}

/* Form styles */
.guti-form-group {
  margin-bottom: var(--guti-spacing-md);
}

.guti-form-label {
  display: block;
  margin-bottom: var(--guti-spacing-xs);
  font-size: var(--guti-font-size-sm);
  font-weight: 500;
  color: var(--guti-color-text);
}

.guti-form-input {
  width: 100%;
  padding: var(--guti-spacing-sm);
  border: 1px solid var(--guti-color-border);
  border-radius: var(--guti-radius-base);
  font-size: var(--guti-font-size-sm);
  background-color: var(--guti-color-background);
  color: var(--guti-color-text);
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.guti-form-input:focus {
  outline: none;
  border-color: var(--guti-color-primary-500);
  box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}

/* Button styles */
.guti-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--guti-spacing-sm) var(--guti-spacing-md);
  border: 1px solid transparent;
  border-radius: var(--guti-radius-base);
  font-size: var(--guti-font-size-sm);
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
}

.guti-button--primary {
  background-color: var(--guti-color-primary-500);
  color: white;
}

.guti-button--primary:hover {
  background-color: var(--guti-color-primary-600);
}

.guti-button--secondary {
  background-color: var(--guti-color-surface);
  border-color: var(--guti-color-border);
  color: var(--guti-color-text);
}

.guti-button--secondary:hover {
  background-color: var(--guti-color-secondary-50);
}

/* Utility classes */
.guti-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.guti-focus-visible:focus-visible {
  outline: 2px solid var(--guti-color-primary-500);
  outline-offset: 2px;
}

/* Animation classes */
.guti-fade-in {
  animation: guti-fade-in 0.15s ease-in-out;
}

.guti-slide-in {
  animation: guti-slide-in 0.2s ease-out;
}

@keyframes guti-fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes guti-slide-in {
  from {
    transform: translateY(-10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Responsive utilities */
@media (max-width: 768px) {
  .guti-panel {
    border-radius: 0;
    border-left: none;
    border-right: none;
  }
  
  .guti-button {
    padding: var(--guti-spacing-md);
    font-size: var(--guti-font-size-base);
  }
}
