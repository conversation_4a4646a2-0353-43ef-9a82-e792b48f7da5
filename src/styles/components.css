/* Guti Builder Component Styles */

/* Builder Layout */
.guti-builder {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: var(--guti-color-background, #ffffff);
  color: var(--guti-color-text, #1f2937);
  font-family: var(--guti-font-sans, system-ui, sans-serif);
}

.guti-builder-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.guti-builder-canvas {
  flex: 1;
  overflow: auto;
  padding: var(--guti-spacing-4, 1rem);
  background: var(--guti-color-surface, #f9fafb);
}

/* Toolbar */
.guti-builder-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--guti-spacing-3, 0.75rem) var(--guti-spacing-4, 1rem);
  background: var(--guti-color-surface, #ffffff);
  border-bottom: 1px solid var(--guti-color-border, #e5e7eb);
  min-height: 60px;
}

.guti-toolbar-section {
  display: flex;
  align-items: center;
  gap: var(--guti-spacing-2, 0.5rem);
}

.guti-toolbar-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--guti-spacing-2, 0.5rem);
  background: transparent;
  border: 1px solid var(--guti-color-border, #e5e7eb);
  border-radius: var(--guti-radius-md, 0.375rem);
  color: var(--guti-color-text, #374151);
  cursor: pointer;
  transition: all 0.2s ease;
}

.guti-toolbar-button:hover {
  background: var(--guti-color-background, #f3f4f6);
  border-color: var(--guti-color-border-strong, #d1d5db);
}

.guti-toolbar-button.active {
  background: var(--guti-color-primary-500, #3b82f6);
  border-color: var(--guti-color-primary-500, #3b82f6);
  color: white;
}

.guti-toolbar-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.guti-toolbar-title {
  font-size: var(--guti-font-size-lg, 1.125rem);
  font-weight: var(--guti-font-weight-semibold, 600);
  margin: 0;
}

.guti-save-button {
  display: flex;
  align-items: center;
  gap: var(--guti-spacing-1, 0.25rem);
  background: var(--guti-color-primary-500, #3b82f6);
  border-color: var(--guti-color-primary-500, #3b82f6);
  color: white;
  padding: var(--guti-spacing-2, 0.5rem) var(--guti-spacing-4, 1rem);
  font-weight: var(--guti-font-weight-medium, 500);
}

.guti-save-button:hover {
  background: var(--guti-color-primary-600, #2563eb);
  border-color: var(--guti-color-primary-600, #2563eb);
}

/* Mode Switcher */
.guti-mode-switcher {
  display: flex;
  background: var(--guti-color-background, #f3f4f6);
  border-radius: var(--guti-radius-md, 0.375rem);
  padding: 2px;
}

.guti-mode-button {
  display: flex;
  align-items: center;
  gap: var(--guti-spacing-1, 0.25rem);
  padding: var(--guti-spacing-2, 0.5rem) var(--guti-spacing-3, 0.75rem);
  background: transparent;
  border: none;
  border-radius: var(--guti-radius-sm, 0.25rem);
  color: var(--guti-color-text-muted, #6b7280);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: var(--guti-font-size-sm, 0.875rem);
  font-weight: var(--guti-font-weight-medium, 500);
}

.guti-mode-button.active {
  background: white;
  color: var(--guti-color-text, #374151);
  box-shadow: var(--guti-shadow-sm, 0 1px 2px 0 rgba(0, 0, 0, 0.05));
}

/* Sidebar */
.guti-builder-sidebar {
  width: 280px;
  background: var(--guti-color-surface, #ffffff);
  border-right: 1px solid var(--guti-color-border, #e5e7eb);
  overflow-y: auto;
  padding: var(--guti-spacing-4, 1rem);
}

.guti-sidebar-section {
  margin-bottom: var(--guti-spacing-6, 1.5rem);
}

.guti-sidebar-title {
  font-size: var(--guti-font-size-sm, 0.875rem);
  font-weight: var(--guti-font-weight-semibold, 600);
  color: var(--guti-color-text, #374151);
  margin: 0 0 var(--guti-spacing-3, 0.75rem) 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Block Palette */
.guti-block-palette {
  display: flex;
  flex-direction: column;
  gap: var(--guti-spacing-2, 0.5rem);
}

.guti-block-item {
  position: relative;
  display: flex;
  align-items: center;
  padding: var(--guti-spacing-3, 0.75rem);
  background: var(--guti-color-white, #ffffff);
  border: 1px solid var(--guti-color-border, #e5e7eb);
  border-radius: var(--guti-radius-lg, 0.5rem);
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 60px;
}

.guti-block-item:hover {
  background: var(--guti-color-primary-50, #eff6ff);
  border-color: var(--guti-color-primary-300, #93c5fd);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
}

.guti-block-item.dragging {
  opacity: 0.8;
  transform: rotate(2deg);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  z-index: 1000;
}

.guti-block-item-content {
  display: flex;
  align-items: center;
  flex: 1;
  cursor: pointer;
}

.guti-block-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: var(--guti-color-primary-100, #dbeafe);
  color: var(--guti-color-primary-600, #2563eb);
  border-radius: var(--guti-radius-md, 0.375rem);
  margin-right: var(--guti-spacing-3, 0.75rem);
}

.guti-block-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.guti-block-label {
  font-size: var(--guti-font-size-sm, 0.875rem);
  color: var(--guti-color-text, #374151);
  font-weight: var(--guti-font-weight-medium, 500);
  margin-bottom: 2px;
}

.guti-block-description {
  font-size: var(--guti-font-size-xs, 0.75rem);
  color: var(--guti-color-text-muted, #6b7280);
  line-height: 1.2;
}

.guti-block-drag-handle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  color: var(--guti-color-gray-400, #9ca3af);
  cursor: grab;
  opacity: 0;
  transition: opacity 0.2s ease;
  margin-right: var(--guti-spacing-2, 0.5rem);
}

.guti-block-drag-handle:hover {
  color: var(--guti-color-gray-600, #4b5563);
}

.guti-block-drag-handle:active {
  cursor: grabbing;
}

.guti-block-item:hover .guti-block-drag-handle {
  opacity: 1;
}

.guti-block-add-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: var(--guti-color-primary-500, #3b82f6);
  color: white;
  border: none;
  border-radius: var(--guti-radius-sm, 0.25rem);
  cursor: pointer;
  transition: all 0.2s ease;
  opacity: 0;
}

.guti-block-add-button:hover {
  background: var(--guti-color-primary-600, #2563eb);
  transform: scale(1.05);
}

.guti-block-item:hover .guti-block-add-button {
  opacity: 1;
}

/* Properties Panel */
.guti-builder-properties {
  width: 320px;
  background: var(--guti-color-surface, #ffffff);
  border-left: 1px solid var(--guti-color-border, #e5e7eb);
  overflow-y: auto;
}

/* Page Renderer */
.guti-page-renderer {
  max-width: 100%;
  margin: 0 auto;
}

.guti-page-renderer.guti-mode-edit {
  padding: var(--guti-spacing-4, 1rem);
}

.guti-page-meta {
  margin-bottom: var(--guti-spacing-6, 1.5rem);
  padding-bottom: var(--guti-spacing-4, 1rem);
  border-bottom: 1px solid var(--guti-color-border, #e5e7eb);
}

.guti-page-title {
  font-size: var(--guti-font-size-2xl, 1.5rem);
  font-weight: var(--guti-font-weight-bold, 700);
  margin: 0 0 var(--guti-spacing-2, 0.5rem) 0;
  color: var(--guti-color-text, #111827);
}

.guti-page-description {
  color: var(--guti-color-text-muted, #6b7280);
  margin: 0;
}

.guti-page-blocks {
  display: flex;
  flex-direction: column;
  gap: var(--guti-spacing-4, 1rem);
}

.guti-block-wrapper {
  position: relative;
}

/* Block Components */
.guti-text-block,
.guti-paragraph-block {
  margin: 0;
  line-height: var(--guti-line-height-relaxed, 1.625);
}

.guti-heading-block {
  margin: 0;
  line-height: var(--guti-line-height-tight, 1.25);
  font-weight: var(--guti-font-weight-bold, 700);
}

.guti-heading-1 { font-size: var(--guti-font-size-4xl, 2.25rem); }
.guti-heading-2 { font-size: var(--guti-font-size-3xl, 1.875rem); }
.guti-heading-3 { font-size: var(--guti-font-size-2xl, 1.5rem); }
.guti-heading-4 { font-size: var(--guti-font-size-xl, 1.25rem); }
.guti-heading-5 { font-size: var(--guti-font-size-lg, 1.125rem); }
.guti-heading-6 { font-size: var(--guti-font-size-base, 1rem); }

.guti-container-block {
  width: 100%;
}

.guti-unknown-block {
  padding: var(--guti-spacing-4, 1rem);
  background: var(--guti-color-error-50, #fef2f2);
  border: 1px dashed var(--guti-color-error-300, #fca5a5);
  border-radius: var(--guti-radius-md, 0.375rem);
  color: var(--guti-color-error-700, #b91c1c);
  text-align: center;
}

/* Theme Mode Classes */
.guti-light {
  color-scheme: light;
}

.guti-dark {
  color-scheme: dark;
}

/* Drag and Drop Styles */
.guti-draggable-block {
  position: relative;
  margin-bottom: var(--guti-spacing-4, 1rem);
  border-radius: var(--guti-radius-md, 0.375rem);
  transition: all 0.2s ease;
  cursor: pointer;
}

.guti-block-editable {
  border: 2px solid transparent;
  padding: var(--guti-spacing-2, 0.5rem);
}

.guti-block-hovered {
  border-color: var(--guti-color-green-400, #4ade80);
}

.guti-block-selected {
  border-color: var(--guti-color-blue-500, #3b82f6);
  box-shadow: 0 0 0 1px var(--guti-color-blue-500, #3b82f6);
}

.guti-block-dragging {
  opacity: 0.8;
  transform: rotate(2deg) scale(1.02);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  z-index: 1000;
}

.guti-drag-handle {
  position: absolute;
  top: var(--guti-spacing-1, 0.25rem);
  left: var(--guti-spacing-1, 0.25rem);
  width: 20px;
  height: 20px;
  background: var(--guti-color-gray-100, #f3f4f6);
  border: 1px solid var(--guti-color-gray-300, #d1d5db);
  border-radius: var(--guti-radius-sm, 0.25rem);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: grab;
  color: var(--guti-color-gray-500, #6b7280);
  opacity: 0;
  transition: opacity 0.2s ease;
  z-index: 10;
}

.guti-drag-handle:hover {
  background: var(--guti-color-gray-200, #e5e7eb);
  color: var(--guti-color-gray-700, #374151);
}

.guti-drag-handle:active {
  cursor: grabbing;
}

.guti-draggable-block:hover .guti-drag-handle,
.guti-block-selected .guti-drag-handle {
  opacity: 1;
}

.guti-block-content {
  position: relative;
  z-index: 1;
}

.guti-block-actions {
  position: absolute;
  top: var(--guti-spacing-1, 0.25rem);
  right: var(--guti-spacing-1, 0.25rem);
  display: flex;
  gap: var(--guti-spacing-1, 0.25rem);
  opacity: 0;
  transition: opacity 0.2s ease;
  z-index: 10;
}

.guti-block-selected .guti-block-actions {
  opacity: 1;
}

.guti-block-action {
  width: 28px;
  height: 28px;
  background: var(--guti-color-white, #ffffff);
  border: 1px solid var(--guti-color-gray-300, #d1d5db);
  border-radius: var(--guti-radius-md, 0.375rem);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.guti-block-action:hover {
  background: var(--guti-color-gray-50, #f9fafb);
  border-color: var(--guti-color-gray-400, #9ca3af);
  transform: scale(1.05);
}

.guti-action-edit {
  color: var(--guti-color-blue-600, #2563eb);
}

.guti-action-edit:hover {
  background: var(--guti-color-blue-50, #eff6ff);
  border-color: var(--guti-color-blue-300, #93c5fd);
}

.guti-action-copy {
  color: var(--guti-color-green-600, #059669);
}

.guti-action-copy:hover {
  background: var(--guti-color-green-50, #ecfdf5);
  border-color: var(--guti-color-green-300, #86efac);
}

.guti-action-delete {
  color: var(--guti-color-red-600, #dc2626);
}

.guti-action-delete:hover {
  background: var(--guti-color-red-50, #fef2f2);
  border-color: var(--guti-color-red-300, #fca5a5);
}

/* Canvas Styles */
.guti-droppable-canvas {
  min-height: 500px;
  padding: var(--guti-spacing-6, 1.5rem);
  background: var(--guti-color-white, #ffffff);
  border-radius: var(--guti-radius-lg, 0.5rem);
  position: relative;
}

.guti-canvas-editable {
  border: 2px dashed var(--guti-color-gray-200, #e5e7eb);
}

.guti-canvas-drag-over {
  border-color: var(--guti-color-primary-400, #60a5fa);
  background-color: var(--guti-color-primary-25, #f8faff);
}

.guti-page-header {
  margin-bottom: var(--guti-spacing-8, 2rem);
  padding-bottom: var(--guti-spacing-4, 1rem);
  border-bottom: 1px solid var(--guti-color-gray-200, #e5e7eb);
}

.guti-blocks-container {
  min-height: 300px;
}

.guti-empty-canvas {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  text-align: center;
}

.guti-empty-message h3 {
  font-size: var(--guti-font-size-lg, 1.125rem);
  font-weight: var(--guti-font-weight-semibold, 600);
  color: var(--guti-color-gray-700, #374151);
  margin: 0 0 var(--guti-spacing-2, 0.5rem) 0;
}

.guti-empty-message p {
  color: var(--guti-color-gray-500, #6b7280);
  margin: 0;
}

.guti-drop-indicator {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(59, 130, 246, 0.1);
  border-radius: var(--guti-radius-lg, 0.5rem);
  pointer-events: none;
}

.guti-drop-line {
  position: absolute;
  top: 50%;
  left: var(--guti-spacing-4, 1rem);
  right: var(--guti-spacing-4, 1rem);
  height: 2px;
  background: var(--guti-color-primary-500, #3b82f6);
  border-radius: 1px;
}

.guti-drop-text {
  background: var(--guti-color-primary-500, #3b82f6);
  color: white;
  padding: var(--guti-spacing-2, 0.5rem) var(--guti-spacing-4, 1rem);
  border-radius: var(--guti-radius-md, 0.375rem);
  font-size: var(--guti-font-size-sm, 0.875rem);
  font-weight: var(--guti-font-weight-medium, 500);
}

/* Inline Editor */
.guti-inline-editor {
  width: 100%;
  border: 2px solid var(--guti-color-blue-400, #60a5fa);
  border-radius: var(--guti-radius-md, 0.375rem);
  padding: var(--guti-spacing-2, 0.5rem);
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  color: inherit;
  background: var(--guti-color-white, #ffffff);
  min-height: 1.5em;
  outline: none;
  box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
}

.guti-inline-editor:focus {
  border-color: var(--guti-color-blue-500, #3b82f6);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

.guti-inline-editor:empty:before {
  content: 'Click to edit...';
  color: var(--guti-color-gray-400, #9ca3af);
  font-style: italic;
}

/* Inline editor variants */
.guti-inline-heading {
  font-weight: var(--guti-font-weight-bold, 700);
  font-size: var(--guti-font-size-2xl, 1.5rem);
}

.guti-inline-paragraph {
  line-height: 1.6;
}

.guti-inline-text {
  display: inline-block;
  min-width: 50px;
}

/* Draggable Block Items */
.guti-block-item.dragging {
  opacity: 0.5;
  transform: rotate(5deg);
}

/* Responsive */
@media (max-width: 768px) {
  .guti-builder-sidebar {
    width: 240px;
  }

  .guti-builder-properties {
    width: 280px;
  }

  .guti-block-palette {
    grid-template-columns: 1fr;
  }

  .guti-droppable-canvas {
    padding: var(--guti-spacing-4, 1rem);
  }
}
