// Main entry point for Guti Builder
export * from './types';
export * from './utils';
export * from './schemas';

// Re-export commonly used types and interfaces
export type {
  GutiBuilderConfig,
  PageData,
  BlockData,
  BlockDefinition,
  ThemeConfig,
  ThemeTokens,
  ValidationResult,
  SchemaRegistry,
} from './types';

// Re-export theme utilities
export {
  createThemeTokens,
  applyThemeMode,
  generateCSSCustomProperties,
} from './theme/utils';

export {
  defaultThemeTokens,
  darkThemeOverrides,
} from './theme/tokens';

// Re-export schema utilities
export {
  schemaRegistry,
  createSchemaRegistry,
  registerBuiltInSchemas,
} from './schemas';

// Version
export const VERSION = '0.1.0';
