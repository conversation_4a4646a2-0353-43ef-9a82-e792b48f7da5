// Main entry point for Guti Builder
export * from './core';
export * from './blocks';
export * from './theme';
export * from './types';
export * from './utils';

// Re-export commonly used types and interfaces
export type {
  GutiBuilderConfig,
  PageData,
  BlockData,
  BlockDefinition,
  ThemeConfig,
} from './types';

// Re-export core components
export {
  GutiBuilder,
  BlockRenderer,
  PageRenderer,
} from './core';

// Re-export built-in blocks
export {
  TextBlock,
  ImageBlock,
  VideoBlock,
  HeadingBlock,
  ParagraphBlock,
  SpacerBlock,
  ContainerBlock,
  GridBlock,
} from './blocks';

// Re-export theme utilities
export {
  ThemeProvider,
  useTheme,
  createTheme,
} from './theme';

// Version
export const VERSION = '0.1.0';
