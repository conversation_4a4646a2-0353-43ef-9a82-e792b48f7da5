import { GutiSchema, ValidationResult, SchemaRegistry } from '../types/schema';
import { validateSchema } from './validation';

/**
 * Implementation of SchemaRegistry for managing block schemas
 */
class SchemaRegistryImpl implements SchemaRegistry {
  private schemas = new Map<string, GutiSchema>();

  /**
   * Register a schema for a block type
   * @param blockType - Block type identifier
   * @param schema - JSON Schema for the block
   */
  register(blockType: string, schema: GutiSchema): void {
    if (!blockType || typeof blockType !== 'string') {
      throw new Error('Block type must be a non-empty string');
    }

    if (!schema || typeof schema !== 'object') {
      throw new Error('Schema must be a valid JSON Schema object');
    }

    this.schemas.set(blockType, schema);
  }

  /**
   * Get schema for a block type
   * @param blockType - Block type identifier
   * @returns Schema or undefined if not found
   */
  get(blockType: string): GutiSchema | undefined {
    return this.schemas.get(blockType);
  }

  /**
   * Validate data against a block type's schema
   * @param blockType - Block type identifier
   * @param data - Data to validate
   * @returns Validation result
   */
  validate(blockType: string, data: any): ValidationResult {
    const schema = this.schemas.get(blockType);
    
    if (!schema) {
      return {
        valid: false,
        errors: [{
          path: '',
          message: `No schema registered for block type: ${blockType}`,
          value: data,
        }],
      };
    }

    return validateSchema(schema, data);
  }

  /**
   * Get all registered schemas
   * @returns Record of all schemas by block type
   */
  getAll(): Record<string, GutiSchema> {
    const result: Record<string, GutiSchema> = {};
    for (const [blockType, schema] of this.schemas.entries()) {
      result[blockType] = schema;
    }
    return result;
  }

  /**
   * Check if a block type is registered
   * @param blockType - Block type identifier
   * @returns True if registered
   */
  has(blockType: string): boolean {
    return this.schemas.has(blockType);
  }

  /**
   * Unregister a block type
   * @param blockType - Block type identifier
   * @returns True if was registered and removed
   */
  unregister(blockType: string): boolean {
    return this.schemas.delete(blockType);
  }

  /**
   * Clear all registered schemas
   */
  clear(): void {
    this.schemas.clear();
  }

  /**
   * Get list of all registered block types
   * @returns Array of block type identifiers
   */
  getBlockTypes(): string[] {
    return Array.from(this.schemas.keys());
  }
}

/**
 * Global schema registry instance
 */
export const schemaRegistry = new SchemaRegistryImpl();

/**
 * Create a new schema registry instance
 * @returns New schema registry
 */
export function createSchemaRegistry(): SchemaRegistry {
  return new SchemaRegistryImpl();
}
