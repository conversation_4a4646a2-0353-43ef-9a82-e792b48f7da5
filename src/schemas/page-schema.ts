import { GutiSchema } from '../types/schema';

/**
 * J<PERSON>N Schema for page data validation
 */
export const pageSchema: GutiSchema = {
  type: 'object',
  required: ['id', 'title', 'blocks', 'createdAt', 'updatedAt'],
  properties: {
    id: {
      type: 'string',
      description: 'Unique page identifier',
    },
    title: {
      type: 'string',
      minLength: 1,
      maxLength: 200,
      description: 'Page title',
      'x-ui': {
        component: 'input',
        label: 'Page Title',
        placeholder: 'Enter page title',
      },
    },
    description: {
      type: 'string',
      maxLength: 500,
      description: 'Page description',
      'x-ui': {
        component: 'textarea',
        label: 'Page Description',
        placeholder: 'Enter page description',
      },
    },
    meta: {
      type: 'object',
      description: 'Page metadata',
      additionalProperties: true,
    },
    blocks: {
      type: 'array',
      description: 'Root blocks in the page',
      items: {
        $ref: '#/definitions/blockData',
      },
    },
    theme: {
      type: 'object',
      description: 'Page-specific theme overrides',
      additionalProperties: true,
    },
    settings: {
      type: 'object',
      description: 'Page settings',
      properties: {
        layout: {
          type: 'string',
          enum: ['full-width', 'contained', 'centered'],
          default: 'contained',
          'x-ui': {
            component: 'select',
            label: 'Page Layout',
          },
        },
        background: {
          type: 'string',
          description: 'Page background color or image',
          'x-ui': {
            component: 'color',
            label: 'Background',
          },
        },
        className: {
          type: 'string',
          description: 'Custom CSS classes',
          'x-ui': {
            component: 'input',
            label: 'CSS Classes',
            placeholder: 'custom-class another-class',
          },
        },
      },
    },
    createdAt: {
      type: 'string',
      format: 'date-time',
      description: 'Page creation timestamp',
    },
    updatedAt: {
      type: 'string',
      format: 'date-time',
      description: 'Page last update timestamp',
    },
  },
  definitions: {
    blockData: {
      type: 'object',
      required: ['id', 'type', 'props'],
      properties: {
        id: {
          type: 'string',
          description: 'Unique block identifier',
        },
        type: {
          type: 'string',
          description: 'Block type identifier',
        },
        props: {
          type: 'object',
          description: 'Block properties/content',
          additionalProperties: true,
        },
        children: {
          type: 'array',
          description: 'Child blocks',
          items: {
            $ref: '#/definitions/blockData',
          },
        },
        style: {
          type: 'object',
          description: 'Block styling',
          additionalProperties: true,
        },
        settings: {
          type: 'object',
          description: 'Block settings',
          properties: {
            className: {
              type: 'string',
            },
            responsive: {
              type: 'object',
              properties: {
                mobile: { type: 'boolean' },
                tablet: { type: 'boolean' },
                desktop: { type: 'boolean' },
              },
            },
            animation: {
              type: 'object',
              properties: {
                type: { type: 'string' },
                duration: { type: 'number' },
                delay: { type: 'number' },
              },
            },
          },
        },
      },
    },
  },
};
