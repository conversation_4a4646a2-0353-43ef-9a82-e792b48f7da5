import { GutiSchema } from '../types/schema';

/**
 * JSON Schema for color scale validation
 */
const colorScaleSchema: GutiSchema = {
  type: 'object',
  properties: {
    50: { type: 'string', pattern: '^#[0-9a-fA-F]{6}$' },
    100: { type: 'string', pattern: '^#[0-9a-fA-F]{6}$' },
    200: { type: 'string', pattern: '^#[0-9a-fA-F]{6}$' },
    300: { type: 'string', pattern: '^#[0-9a-fA-F]{6}$' },
    400: { type: 'string', pattern: '^#[0-9a-fA-F]{6}$' },
    500: { type: 'string', pattern: '^#[0-9a-fA-F]{6}$' },
    600: { type: 'string', pattern: '^#[0-9a-fA-F]{6}$' },
    700: { type: 'string', pattern: '^#[0-9a-fA-F]{6}$' },
    800: { type: 'string', pattern: '^#[0-9a-fA-F]{6}$' },
    900: { type: 'string', pattern: '^#[0-9a-fA-F]{6}$' },
    950: { type: 'string', pattern: '^#[0-9a-fA-F]{6}$' },
  },
  required: ['500'], // Base color is required
  additionalProperties: false,
};

/**
 * JSON Schema for color tokens validation
 */
const colorTokensSchema: GutiSchema = {
  type: 'object',
  properties: {
    primary: colorScaleSchema,
    secondary: colorScaleSchema,
    accent: colorScaleSchema,
    gray: colorScaleSchema,
    success: colorScaleSchema,
    warning: colorScaleSchema,
    error: colorScaleSchema,
    info: colorScaleSchema,
    background: { type: 'string', pattern: '^#[0-9a-fA-F]{6}$' },
    surface: { type: 'string', pattern: '^#[0-9a-fA-F]{6}$' },
    overlay: { type: 'string', pattern: '^#[0-9a-fA-F]{6}$' },
    text: { type: 'string', pattern: '^#[0-9a-fA-F]{6}$' },
    textMuted: { type: 'string', pattern: '^#[0-9a-fA-F]{6}$' },
    textInverse: { type: 'string', pattern: '^#[0-9a-fA-F]{6}$' },
    border: { type: 'string', pattern: '^#[0-9a-fA-F]{6}$' },
    borderMuted: { type: 'string', pattern: '^#[0-9a-fA-F]{6}$' },
    borderStrong: { type: 'string', pattern: '^#[0-9a-fA-F]{6}$' },
  },
  required: ['primary', 'background', 'text'],
  additionalProperties: false,
};

/**
 * JSON Schema for typography tokens validation
 */
const typographyTokensSchema: GutiSchema = {
  type: 'object',
  properties: {
    fontFamily: {
      type: 'object',
      properties: {
        sans: { type: 'string' },
        serif: { type: 'string' },
        mono: { type: 'string' },
      },
      required: ['sans'],
      additionalProperties: true,
    },
    fontSize: {
      type: 'object',
      properties: {
        xs: { type: 'string', pattern: '^\\d+(\\.\\d+)?(px|rem|em)$' },
        sm: { type: 'string', pattern: '^\\d+(\\.\\d+)?(px|rem|em)$' },
        base: { type: 'string', pattern: '^\\d+(\\.\\d+)?(px|rem|em)$' },
        lg: { type: 'string', pattern: '^\\d+(\\.\\d+)?(px|rem|em)$' },
        xl: { type: 'string', pattern: '^\\d+(\\.\\d+)?(px|rem|em)$' },
        '2xl': { type: 'string', pattern: '^\\d+(\\.\\d+)?(px|rem|em)$' },
        '3xl': { type: 'string', pattern: '^\\d+(\\.\\d+)?(px|rem|em)$' },
        '4xl': { type: 'string', pattern: '^\\d+(\\.\\d+)?(px|rem|em)$' },
        '5xl': { type: 'string', pattern: '^\\d+(\\.\\d+)?(px|rem|em)$' },
        '6xl': { type: 'string', pattern: '^\\d+(\\.\\d+)?(px|rem|em)$' },
      },
      required: ['base'],
      additionalProperties: true,
    },
    fontWeight: {
      type: 'object',
      properties: {
        thin: { type: 'number', minimum: 100, maximum: 900 },
        light: { type: 'number', minimum: 100, maximum: 900 },
        normal: { type: 'number', minimum: 100, maximum: 900 },
        medium: { type: 'number', minimum: 100, maximum: 900 },
        semibold: { type: 'number', minimum: 100, maximum: 900 },
        bold: { type: 'number', minimum: 100, maximum: 900 },
        extrabold: { type: 'number', minimum: 100, maximum: 900 },
        black: { type: 'number', minimum: 100, maximum: 900 },
      },
      required: ['normal'],
      additionalProperties: true,
    },
    lineHeight: {
      type: 'object',
      properties: {
        none: { type: 'number', minimum: 0.5, maximum: 3 },
        tight: { type: 'number', minimum: 0.5, maximum: 3 },
        snug: { type: 'number', minimum: 0.5, maximum: 3 },
        normal: { type: 'number', minimum: 0.5, maximum: 3 },
        relaxed: { type: 'number', minimum: 0.5, maximum: 3 },
        loose: { type: 'number', minimum: 0.5, maximum: 3 },
      },
      required: ['normal'],
      additionalProperties: true,
    },
    letterSpacing: {
      type: 'object',
      properties: {
        tighter: { type: 'string', pattern: '^-?\\d+(\\.\\d+)?(px|em)$' },
        tight: { type: 'string', pattern: '^-?\\d+(\\.\\d+)?(px|em)$' },
        normal: { type: 'string', pattern: '^-?\\d+(\\.\\d+)?(px|em)$' },
        wide: { type: 'string', pattern: '^-?\\d+(\\.\\d+)?(px|em)$' },
        wider: { type: 'string', pattern: '^-?\\d+(\\.\\d+)?(px|em)$' },
        widest: { type: 'string', pattern: '^-?\\d+(\\.\\d+)?(px|em)$' },
      },
      required: ['normal'],
      additionalProperties: true,
    },
  },
  required: ['fontFamily', 'fontSize', 'fontWeight', 'lineHeight'],
  additionalProperties: false,
};

/**
 * JSON Schema for spacing tokens validation
 */
const spacingTokensSchema: GutiSchema = {
  type: 'object',
  patternProperties: {
    '^(0|px|0\\.5|[1-9]\\d*(\\.5)?)$': {
      type: 'string',
      pattern: '^\\d+(\\.\\d+)?(px|rem|em)$',
    },
  },
  additionalProperties: false,
};

/**
 * JSON Schema for complete theme tokens validation
 */
export const themeSchema: GutiSchema = {
  type: 'object',
  properties: {
    colors: colorTokensSchema,
    typography: typographyTokensSchema,
    spacing: spacingTokensSchema,
    borderRadius: {
      type: 'object',
      properties: {
        none: { type: 'string', pattern: '^\\d+(\\.\\d+)?(px|rem|em)$' },
        sm: { type: 'string', pattern: '^\\d+(\\.\\d+)?(px|rem|em)$' },
        base: { type: 'string', pattern: '^\\d+(\\.\\d+)?(px|rem|em)$' },
        md: { type: 'string', pattern: '^\\d+(\\.\\d+)?(px|rem|em)$' },
        lg: { type: 'string', pattern: '^\\d+(\\.\\d+)?(px|rem|em)$' },
        xl: { type: 'string', pattern: '^\\d+(\\.\\d+)?(px|rem|em)$' },
        '2xl': { type: 'string', pattern: '^\\d+(\\.\\d+)?(px|rem|em)$' },
        '3xl': { type: 'string', pattern: '^\\d+(\\.\\d+)?(px|rem|em)$' },
        full: { type: 'string', enum: ['9999px', '50%', '100%'] },
      },
      required: ['base'],
      additionalProperties: true,
    },
    shadows: {
      type: 'object',
      properties: {
        sm: { type: 'string' },
        base: { type: 'string' },
        md: { type: 'string' },
        lg: { type: 'string' },
        xl: { type: 'string' },
        '2xl': { type: 'string' },
        inner: { type: 'string' },
        none: { type: 'string', enum: ['none'] },
      },
      required: ['base'],
      additionalProperties: true,
    },
    breakpoints: {
      type: 'object',
      properties: {
        sm: { type: 'string', pattern: '^\\d+px$' },
        md: { type: 'string', pattern: '^\\d+px$' },
        lg: { type: 'string', pattern: '^\\d+px$' },
        xl: { type: 'string', pattern: '^\\d+px$' },
        '2xl': { type: 'string', pattern: '^\\d+px$' },
      },
      required: ['sm', 'md', 'lg'],
      additionalProperties: true,
    },
    animations: {
      type: 'object',
      properties: {
        duration: {
          type: 'object',
          patternProperties: {
            '^\\d+$': { type: 'string', pattern: '^\\d+ms$' },
          },
          additionalProperties: false,
        },
        easing: {
          type: 'object',
          properties: {
            linear: { type: 'string' },
            in: { type: 'string' },
            out: { type: 'string' },
            'in-out': { type: 'string' },
          },
          required: ['linear'],
          additionalProperties: true,
        },
      },
      additionalProperties: false,
    },
  },
  required: ['colors', 'typography', 'spacing'],
  additionalProperties: false,
};
