import { schemaRegistry } from './registry';
import {
  textBlockSchema,
  imageBlockSchema,
  videoBlockSchema,
  headingBlockSchema,
  paragraphBlockSchema,
  spacerBlockSchema,
  containerBlockSchema,
  gridBlockSchema,
} from './block-schemas';

/**
 * Register all built-in block schemas with the global registry
 */
export function registerBuiltInSchemas(): void {
  // Register content blocks
  schemaRegistry.register('text', textBlockSchema);
  schemaRegistry.register('image', imageBlockSchema);
  schemaRegistry.register('video', videoBlockSchema);
  schemaRegistry.register('heading', headingBlockSchema);
  schemaRegistry.register('paragraph', paragraphBlockSchema);

  // Register layout blocks
  schemaRegistry.register('spacer', spacerBlockSchema);
  schemaRegistry.register('container', containerBlockSchema);
  schemaRegistry.register('grid', gridBlockSchema);
}

/**
 * Get list of all built-in block types
 * @returns Array of built-in block type identifiers
 */
export function getBuiltInBlockTypes(): string[] {
  return [
    'text',
    'image',
    'video',
    'heading',
    'paragraph',
    'spacer',
    'container',
    'grid',
  ];
}

/**
 * Check if a block type is a built-in type
 * @param blockType - Block type to check
 * @returns True if built-in block type
 */
export function isBuiltInBlockType(blockType: string): boolean {
  return getBuiltInBlockTypes().includes(blockType);
}
