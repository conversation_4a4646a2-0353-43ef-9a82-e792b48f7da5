import { GutiSchema } from '../types/schema';

/**
 * Text Block Schema
 */
export const textBlockSchema: GutiSchema = {
  type: 'object',
  required: ['content'],
  properties: {
    content: {
      type: 'string',
      description: 'Text content',
      'x-ui': {
        component: 'rich-text',
        label: 'Content',
        placeholder: 'Enter text content',
      },
    },
    tag: {
      type: 'string',
      enum: ['span', 'p', 'div'],
      default: 'span',
      'x-ui': {
        component: 'select',
        label: 'HTML Tag',
      },
    },
    className: {
      type: 'string',
      'x-ui': {
        component: 'input',
        label: 'CSS Classes',
        placeholder: 'custom-class',
      },
    },
  },
};

/**
 * Image Block Schema
 */
export const imageBlockSchema: GutiSchema = {
  type: 'object',
  required: ['src', 'alt'],
  properties: {
    src: {
      type: 'string',
      format: 'uri',
      description: 'Image source URL',
      'x-ui': {
        component: 'image',
        label: 'Image',
      },
    },
    alt: {
      type: 'string',
      description: 'Alternative text',
      'x-ui': {
        component: 'input',
        label: 'Alt Text',
        placeholder: 'Describe the image',
      },
    },
    width: {
      type: 'number',
      minimum: 1,
      'x-ui': {
        component: 'input',
        label: 'Width',
        placeholder: '400',
      },
    },
    height: {
      type: 'number',
      minimum: 1,
      'x-ui': {
        component: 'input',
        label: 'Height',
        placeholder: '300',
      },
    },
    objectFit: {
      type: 'string',
      enum: ['cover', 'contain', 'fill', 'none', 'scale-down'],
      default: 'cover',
      'x-ui': {
        component: 'select',
        label: 'Object Fit',
      },
    },
    loading: {
      type: 'string',
      enum: ['lazy', 'eager'],
      default: 'lazy',
      'x-ui': {
        component: 'select',
        label: 'Loading',
      },
    },
    className: {
      type: 'string',
      'x-ui': {
        component: 'input',
        label: 'CSS Classes',
      },
    },
  },
};

/**
 * Video Block Schema
 */
export const videoBlockSchema: GutiSchema = {
  type: 'object',
  properties: {
    src: {
      type: 'string',
      format: 'uri',
      'x-ui': {
        component: 'video',
        label: 'Video File',
      },
    },
    poster: {
      type: 'string',
      format: 'uri',
      'x-ui': {
        component: 'image',
        label: 'Poster Image',
      },
    },
    width: {
      type: 'number',
      minimum: 1,
      'x-ui': {
        component: 'input',
        label: 'Width',
      },
    },
    height: {
      type: 'number',
      minimum: 1,
      'x-ui': {
        component: 'input',
        label: 'Height',
      },
    },
    autoplay: {
      type: 'boolean',
      default: false,
      'x-ui': {
        component: 'toggle',
        label: 'Autoplay',
      },
    },
    loop: {
      type: 'boolean',
      default: false,
      'x-ui': {
        component: 'toggle',
        label: 'Loop',
      },
    },
    muted: {
      type: 'boolean',
      default: false,
      'x-ui': {
        component: 'toggle',
        label: 'Muted',
      },
    },
    controls: {
      type: 'boolean',
      default: true,
      'x-ui': {
        component: 'toggle',
        label: 'Show Controls',
      },
    },
    embedUrl: {
      type: 'string',
      format: 'uri',
      'x-ui': {
        component: 'input',
        label: 'Embed URL',
        placeholder: 'YouTube or Vimeo URL',
      },
    },
    embedType: {
      type: 'string',
      enum: ['youtube', 'vimeo', 'custom'],
      'x-ui': {
        component: 'select',
        label: 'Embed Type',
      },
    },
    className: {
      type: 'string',
      'x-ui': {
        component: 'input',
        label: 'CSS Classes',
      },
    },
  },
  oneOf: [
    { required: ['src'] },
    { required: ['embedUrl'] },
  ],
};

/**
 * Heading Block Schema
 */
export const headingBlockSchema: GutiSchema = {
  type: 'object',
  required: ['content', 'level'],
  properties: {
    content: {
      type: 'string',
      minLength: 1,
      'x-ui': {
        component: 'input',
        label: 'Heading Text',
        placeholder: 'Enter heading',
      },
    },
    level: {
      type: 'integer',
      minimum: 1,
      maximum: 6,
      default: 1,
      'x-ui': {
        component: 'select',
        label: 'Heading Level',
      },
    },
    className: {
      type: 'string',
      'x-ui': {
        component: 'input',
        label: 'CSS Classes',
      },
    },
  },
};

/**
 * Paragraph Block Schema
 */
export const paragraphBlockSchema: GutiSchema = {
  type: 'object',
  required: ['content'],
  properties: {
    content: {
      type: 'string',
      minLength: 1,
      'x-ui': {
        component: 'rich-text',
        label: 'Paragraph Content',
        placeholder: 'Enter paragraph text',
      },
    },
    className: {
      type: 'string',
      'x-ui': {
        component: 'input',
        label: 'CSS Classes',
      },
    },
  },
};

/**
 * Spacer Block Schema
 */
export const spacerBlockSchema: GutiSchema = {
  type: 'object',
  required: ['height'],
  properties: {
    height: {
      type: 'string',
      pattern: '^\\d+(px|rem|em|vh|%)$',
      default: '20px',
      'x-ui': {
        component: 'input',
        label: 'Height',
        placeholder: '20px, 2rem, 5vh',
      },
    },
    className: {
      type: 'string',
      'x-ui': {
        component: 'input',
        label: 'CSS Classes',
      },
    },
  },
};

/**
 * Container Block Schema
 */
export const containerBlockSchema: GutiSchema = {
  type: 'object',
  properties: {
    direction: {
      type: 'string',
      enum: ['row', 'column'],
      default: 'row',
      'x-ui': {
        component: 'select',
        label: 'Direction',
      },
    },
    gap: {
      type: 'string',
      pattern: '^\\d+(px|rem|em)$',
      default: '16px',
      'x-ui': {
        component: 'input',
        label: 'Gap',
        placeholder: '16px, 1rem',
      },
    },
    alignItems: {
      type: 'string',
      enum: ['flex-start', 'center', 'flex-end', 'stretch'],
      default: 'stretch',
      'x-ui': {
        component: 'select',
        label: 'Align Items',
      },
    },
    justifyContent: {
      type: 'string',
      enum: ['flex-start', 'center', 'flex-end', 'space-between', 'space-around', 'space-evenly'],
      default: 'flex-start',
      'x-ui': {
        component: 'select',
        label: 'Justify Content',
      },
    },
    wrap: {
      type: 'boolean',
      default: false,
      'x-ui': {
        component: 'toggle',
        label: 'Wrap Items',
      },
    },
    className: {
      type: 'string',
      'x-ui': {
        component: 'input',
        label: 'CSS Classes',
      },
    },
  },
};

/**
 * Grid Block Schema
 */
export const gridBlockSchema: GutiSchema = {
  type: 'object',
  required: ['columns'],
  properties: {
    columns: {
      type: 'integer',
      minimum: 1,
      maximum: 12,
      default: 2,
      'x-ui': {
        component: 'slider',
        label: 'Columns',
      },
    },
    gap: {
      type: 'string',
      pattern: '^\\d+(px|rem|em)$',
      default: '16px',
      'x-ui': {
        component: 'input',
        label: 'Gap',
      },
    },
    columnGap: {
      type: 'string',
      pattern: '^\\d+(px|rem|em)$',
      'x-ui': {
        component: 'input',
        label: 'Column Gap',
      },
    },
    rowGap: {
      type: 'string',
      pattern: '^\\d+(px|rem|em)$',
      'x-ui': {
        component: 'input',
        label: 'Row Gap',
      },
    },
    mobileColumns: {
      type: 'integer',
      minimum: 1,
      maximum: 12,
      default: 1,
      'x-ui': {
        component: 'slider',
        label: 'Mobile Columns',
      },
    },
    tabletColumns: {
      type: 'integer',
      minimum: 1,
      maximum: 12,
      'x-ui': {
        component: 'slider',
        label: 'Tablet Columns',
      },
    },
    className: {
      type: 'string',
      'x-ui': {
        component: 'input',
        label: 'CSS Classes',
      },
    },
  },
};
