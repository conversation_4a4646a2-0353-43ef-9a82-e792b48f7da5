import { describe, it, expect, beforeEach } from 'vitest';
import { createSchemaRegistry } from '../registry';
import { textBlockSchema } from '../block-schemas';
import { GutiSchema } from '../../types/schema';

describe('SchemaRegistry', () => {
  let registry: ReturnType<typeof createSchemaRegistry>;

  beforeEach(() => {
    registry = createSchemaRegistry();
  });

  describe('register', () => {
    it('should register a schema for a block type', () => {
      registry.register('text', textBlockSchema);
      expect(registry.has('text')).toBe(true);
    });

    it('should throw error for invalid block type', () => {
      expect(() => registry.register('', textBlockSchema)).toThrow();
      expect(() => registry.register(null as any, textBlockSchema)).toThrow();
    });

    it('should throw error for invalid schema', () => {
      expect(() => registry.register('text', null as any)).toThrow();
      expect(() => registry.register('text', undefined as any)).toThrow();
    });
  });

  describe('get', () => {
    it('should return registered schema', () => {
      registry.register('text', textBlockSchema);
      const schema = registry.get('text');
      expect(schema).toBe(textBlockSchema);
    });

    it('should return undefined for unregistered block type', () => {
      const schema = registry.get('nonexistent');
      expect(schema).toBeUndefined();
    });
  });

  describe('validate', () => {
    beforeEach(() => {
      registry.register('text', textBlockSchema);
    });

    it('should validate valid data', () => {
      const validData = {
        content: 'Hello world',
        tag: 'p',
      };

      const result = registry.validate('text', validData);
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should return errors for invalid data', () => {
      const invalidData = {
        // missing required 'content' field
        tag: 'p',
      };

      const result = registry.validate('text', invalidData);
      expect(result.valid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    it('should return error for unregistered block type', () => {
      const result = registry.validate('nonexistent', {});
      expect(result.valid).toBe(false);
      expect(result.errors[0].message).toContain('No schema registered');
    });
  });

  describe('has', () => {
    it('should return true for registered block type', () => {
      registry.register('text', textBlockSchema);
      expect(registry.has('text')).toBe(true);
    });

    it('should return false for unregistered block type', () => {
      expect(registry.has('text')).toBe(false);
    });
  });

  describe('unregister', () => {
    it('should unregister a block type', () => {
      registry.register('text', textBlockSchema);
      expect(registry.has('text')).toBe(true);

      const removed = registry.unregister('text');
      expect(removed).toBe(true);
      expect(registry.has('text')).toBe(false);
    });

    it('should return false for unregistered block type', () => {
      const removed = registry.unregister('nonexistent');
      expect(removed).toBe(false);
    });
  });

  describe('clear', () => {
    it('should clear all registered schemas', () => {
      registry.register('text', textBlockSchema);
      registry.register('heading', textBlockSchema);
      
      expect(registry.getBlockTypes()).toHaveLength(2);
      
      registry.clear();
      expect(registry.getBlockTypes()).toHaveLength(0);
    });
  });

  describe('getAll', () => {
    it('should return all registered schemas', () => {
      registry.register('text', textBlockSchema);
      registry.register('heading', textBlockSchema);

      const all = registry.getAll();
      expect(Object.keys(all)).toHaveLength(2);
      expect(all.text).toBe(textBlockSchema);
      expect(all.heading).toBe(textBlockSchema);
    });
  });

  describe('getBlockTypes', () => {
    it('should return array of registered block types', () => {
      registry.register('text', textBlockSchema);
      registry.register('heading', textBlockSchema);

      const types = registry.getBlockTypes();
      expect(types).toEqual(['text', 'heading']);
    });

    it('should return empty array when no schemas registered', () => {
      const types = registry.getBlockTypes();
      expect(types).toEqual([]);
    });
  });
});
