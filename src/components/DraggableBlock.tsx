import { useState, useRef, useEffect, useCallback, memo } from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import {
  GripVertical,
  Edit3,
  Trash2,
  Copy
} from 'lucide-react';
import { BlockData } from '../types/core';
import { BlockRenderer } from '../blocks/BlockRegistry';
import { useBuilderStore } from '../core/store';
import { ContentEditable } from './ContentEditable';

export interface DraggableBlockProps {
  block: BlockData;
  index: number;
  onEdit?: (blockId: string) => void;
}

export const DraggableBlock = memo(function DraggableBlock({ block, index, onEdit }: DraggableBlockProps) {
  const [isEditing, setIsEditing] = useState(false);
  const { 
    selection, 
    selectBlock, 
    hoverBlock, 
    updateBlock,
    deleteBlock,
    ui 
  } = useBuilderStore();

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ 
    id: block.id,
    data: {
      type: 'block',
      block,
      index,
    }
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  const isSelected = selection.selectedBlockId === block.id;
  const isHovered = selection.hoveredBlockId === block.id;
  const isEditMode = ui.mode === 'edit';

  const handleClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    if (isEditMode) {
      selectBlock(block.id);
    }
  }, [isEditMode, selectBlock, block.id]);

  const handleDoubleClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    if (isEditMode) {
      setIsEditing(true);
      onEdit?.(block.id);
    }
  }, [isEditMode, onEdit, block.id]);

  const handleMouseEnter = useCallback(() => {
    if (isEditMode) {
      hoverBlock(block.id);
    }
  }, [isEditMode, hoverBlock, block.id]);

  const handleMouseLeave = useCallback(() => {
    if (isEditMode) {
      hoverBlock(null);
    }
  }, [isEditMode, hoverBlock]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Delete' || e.key === 'Backspace') {
      if (isSelected) {
        deleteBlock(block.id);
      }
    }
  }, [isSelected, deleteBlock, block.id]);

  const handleContentChange = useCallback((newContent: string) => {
    updateBlock(block.id, {
      props: {
        ...block.props,
        content: newContent,
      }
    });
    setIsEditing(false);
  }, [updateBlock, block.id, block.props]);

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`
        guti-draggable-block
        ${isSelected ? 'guti-block-selected' : ''}
        ${isHovered ? 'guti-block-hovered' : ''}
        ${isDragging ? 'guti-block-dragging' : ''}
        ${isEditMode ? 'guti-block-editable' : ''}
      `}
      onClick={handleClick}
      onDoubleClick={handleDoubleClick}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onKeyDown={handleKeyDown}
      {...attributes}
      tabIndex={isEditMode ? 0 : -1}
    >
      {/* Drag Handle (only in edit mode) */}
      {isEditMode && (
        <div
          className="guti-drag-handle"
          {...listeners}
          title="Drag to reorder"
        >
          <GripVertical size={16} />
        </div>
      )}

      {/* Block Content */}
      <div className="guti-block-content">
        {isEditing && (block.type === 'text' || block.type === 'paragraph' || block.type === 'heading') ? (
          <ContentEditable
            content={block.props.content || ''}
            onSave={handleContentChange}
            onCancel={() => setIsEditing(false)}
            placeholder={`Enter ${block.type} content...`}
            multiline={block.type === 'paragraph'}
          />
        ) : (
          <BlockRenderer
            block={block}
            mode={ui.mode}
          />
        )}
      </div>

      {/* Block Actions (only when selected in edit mode) */}
      {isSelected && isEditMode && (
        <div className="guti-block-actions">
          <button
            className="guti-block-action guti-action-edit"
            onClick={(e) => {
              e.stopPropagation();
              setIsEditing(true);
            }}
            title="Edit content"
          >
            <Edit3 size={14} />
          </button>
          <button
            className="guti-block-action guti-action-copy"
            onClick={(e) => {
              e.stopPropagation();
              // TODO: Implement duplicate functionality
            }}
            title="Duplicate block"
          >
            <Copy size={14} />
          </button>
          <button
            className="guti-block-action guti-action-delete"
            onClick={(e) => {
              e.stopPropagation();
              deleteBlock(block.id);
            }}
            title="Delete block"
          >
            <Trash2 size={14} />
          </button>
        </div>
      )}
    </div>
  );
});

// Proper HTML5 contentEditable implementation
interface InlineEditorProps {
  content: string;
  onSave: (content: string) => void;
  onCancel: () => void;
  className?: string;
  placeholder?: string;
}

function InlineEditor({ content, onSave, onCancel, className = '', placeholder = 'Type here...' }: InlineEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null);
  const [isEditing, setIsEditing] = useState(true);

  useEffect(() => {
    if (editorRef.current && isEditing) {
      // Set initial content
      editorRef.current.textContent = content;

      // Focus and select all text
      editorRef.current.focus();

      // Select all content for easy replacement
      const range = document.createRange();
      const selection = window.getSelection();
      if (selection && editorRef.current.firstChild) {
        range.selectNodeContents(editorRef.current);
        selection.removeAllRanges();
        selection.addRange(range);
      }
    }
  }, [content, isEditing]);

  const handleKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSave();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancel();
    }
  };

  const handleSave = () => {
    if (editorRef.current) {
      const newContent = editorRef.current.textContent || '';
      setIsEditing(false);
      onSave(newContent);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
    onCancel();
  };

  const handleBlur = (_e: React.FocusEvent<HTMLDivElement>) => {
    // Small delay to allow clicking on buttons
    setTimeout(() => {
      if (isEditing) {
        handleSave();
      }
    }, 100);
  };

  return (
    <div className="guti-inline-editor-wrapper">
      <div
        ref={editorRef}
        contentEditable={isEditing}
        suppressContentEditableWarning
        className={`guti-inline-editor ${className}`}
        onKeyDown={handleKeyDown}
        onBlur={handleBlur}
        data-placeholder={placeholder}
        style={{
          minHeight: '1.2em',
          outline: 'none',
          border: '2px solid #3b82f6',
          borderRadius: '4px',
          padding: '4px 8px',
          background: 'white',
        }}
      />
      <div className="guti-inline-editor-hint">
        <small>Press Enter to save, Esc to cancel</small>
      </div>
    </div>
  );
}
