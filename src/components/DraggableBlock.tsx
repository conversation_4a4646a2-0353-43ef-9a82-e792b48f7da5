import { useState, useCallback, memo } from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import {
  GripVertical,
  Edit3,
  Trash2,
  Copy
} from 'lucide-react';
import { BlockData } from '../types/core';
import { BlockRenderer } from '../blocks/BlockRegistry';
import { useBuilderStore } from '../core/store';
import { ContentEditable } from './ContentEditable';

export interface DraggableBlockProps {
  block: BlockData;
  index: number;
  onEdit?: (blockId: string) => void;
}

export const DraggableBlock = memo(function DraggableBlock({ block, index, onEdit }: DraggableBlockProps) {
  const [isEditing, setIsEditing] = useState(false);
  const { 
    selection, 
    selectBlock, 
    hoverBlock, 
    updateBlock,
    deleteBlock,
    ui 
  } = useBuilderStore();

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ 
    id: block.id,
    data: {
      type: 'block',
      block,
      index,
    }
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  const isSelected = selection.selectedBlockId === block.id;
  const isHovered = selection.hoveredBlockId === block.id;
  const isEditMode = ui.mode === 'edit';

  const handleClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    if (isEditMode) {
      selectBlock(block.id);
      // Start editing text blocks on single click
      if (block.type === 'text' || block.type === 'paragraph' || block.type === 'heading') {
        setIsEditing(true);
        onEdit?.(block.id);
      }
    }
  }, [isEditMode, selectBlock, block.id, block.type, onEdit]);

  const handleMouseEnter = useCallback(() => {
    if (isEditMode) {
      hoverBlock(block.id);
    }
  }, [isEditMode, hoverBlock, block.id]);

  const handleMouseLeave = useCallback(() => {
    if (isEditMode) {
      hoverBlock(null);
    }
  }, [isEditMode, hoverBlock]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Delete' || e.key === 'Backspace') {
      if (isSelected) {
        deleteBlock(block.id);
      }
    }
  }, [isSelected, deleteBlock, block.id]);

  const handleContentChange = useCallback((newContent: string) => {
    console.log('Content change:', { blockId: block.id, oldContent: block.props.content, newContent });
    updateBlock(block.id, {
      props: {
        ...block.props,
        content: newContent,
      }
    });
    setIsEditing(false);
  }, [updateBlock, block.id, block.props]);

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`
        guti-draggable-block
        ${isSelected ? 'guti-block-selected' : ''}
        ${isHovered ? 'guti-block-hovered' : ''}
        ${isDragging ? 'guti-block-dragging' : ''}
        ${isEditMode ? 'guti-block-editable' : ''}
      `}
      onClick={handleClick}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onKeyDown={handleKeyDown}
      {...attributes}
      tabIndex={isEditMode ? 0 : -1}
    >
      {/* Drag Handle (only in edit mode) */}
      {isEditMode && (
        <div
          className="guti-drag-handle"
          {...listeners}
          title="Drag to reorder"
        >
          <GripVertical size={16} />
        </div>
      )}

      {/* Block Content */}
      <div className={`guti-block-content ${isEditing ? 'guti-block-editing' : ''}`}>
        {isEditing && (block.type === 'text' || block.type === 'paragraph' || block.type === 'heading') ? (
          <ContentEditable
            content={block.props.content || ''}
            onSave={handleContentChange}
            onCancel={() => setIsEditing(false)}
            placeholder={`Click to edit ${block.type}...`}
            multiline={block.type === 'paragraph'}
          />
        ) : (
          <BlockRenderer
            block={block}
            mode={ui.mode}
          />
        )}
      </div>

      {/* Block Actions (only when selected in edit mode) */}
      {isSelected && isEditMode && (
        <div className="guti-block-actions">
          <button
            className="guti-block-action guti-action-edit"
            onClick={(e) => {
              e.stopPropagation();
              setIsEditing(true);
            }}
            title="Edit content"
          >
            <Edit3 size={14} />
          </button>
          <button
            className="guti-block-action guti-action-copy"
            onClick={(e) => {
              e.stopPropagation();
              // TODO: Implement duplicate functionality
            }}
            title="Duplicate block"
          >
            <Copy size={14} />
          </button>
          <button
            className="guti-block-action guti-action-delete"
            onClick={(e) => {
              e.stopPropagation();
              deleteBlock(block.id);
            }}
            title="Delete block"
          >
            <Trash2 size={14} />
          </button>
        </div>
      )}
    </div>
  );
});


