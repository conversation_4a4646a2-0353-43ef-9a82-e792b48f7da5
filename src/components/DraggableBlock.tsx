import { useState } from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import {
  GripVertical,
  Edit3,
  Trash2,
  Copy,
  Move
} from 'lucide-react';
import { BlockData } from '../types/core';
import { BlockRenderer } from '../blocks/BlockRegistry';
import { useBuilderStore } from '../core/store';

export interface DraggableBlockProps {
  block: BlockData;
  index: number;
  onEdit?: (blockId: string) => void;
}

export function DraggableBlock({ block, index, onEdit }: DraggableBlockProps) {
  const [isEditing, setIsEditing] = useState(false);
  const { 
    selection, 
    selectBlock, 
    hoverBlock, 
    updateBlock,
    deleteBlock,
    ui 
  } = useBuilderStore();

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ 
    id: block.id,
    data: {
      type: 'block',
      block,
      index,
    }
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  const isSelected = selection.selectedBlockId === block.id;
  const isHovered = selection.hoveredBlockId === block.id;
  const isEditMode = ui.mode === 'edit';

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (isEditMode) {
      selectBlock(block.id);
    }
  };

  const handleDoubleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (isEditMode) {
      setIsEditing(true);
      onEdit?.(block.id);
    }
  };

  const handleMouseEnter = () => {
    if (isEditMode) {
      hoverBlock(block.id);
    }
  };

  const handleMouseLeave = () => {
    if (isEditMode) {
      hoverBlock(null);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Delete' || e.key === 'Backspace') {
      if (isSelected) {
        deleteBlock(block.id);
      }
    }
  };

  const handleContentChange = (newContent: string) => {
    updateBlock(block.id, {
      props: {
        ...block.props,
        content: newContent,
      }
    });
    setIsEditing(false);
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`
        guti-draggable-block
        ${isSelected ? 'guti-block-selected' : ''}
        ${isHovered ? 'guti-block-hovered' : ''}
        ${isDragging ? 'guti-block-dragging' : ''}
        ${isEditMode ? 'guti-block-editable' : ''}
      `}
      onClick={handleClick}
      onDoubleClick={handleDoubleClick}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onKeyDown={handleKeyDown}
      {...attributes}
      tabIndex={isEditMode ? 0 : -1}
    >
      {/* Drag Handle (only in edit mode) */}
      {isEditMode && (
        <div 
          className="guti-drag-handle"
          {...listeners}
        >
          <svg width="12" height="12" viewBox="0 0 12 12" fill="currentColor">
            <circle cx="2" cy="2" r="1"/>
            <circle cx="6" cy="2" r="1"/>
            <circle cx="10" cy="2" r="1"/>
            <circle cx="2" cy="6" r="1"/>
            <circle cx="6" cy="6" r="1"/>
            <circle cx="10" cy="6" r="1"/>
            <circle cx="2" cy="10" r="1"/>
            <circle cx="6" cy="10" r="1"/>
            <circle cx="10" cy="10" r="1"/>
          </svg>
        </div>
      )}

      {/* Block Content */}
      <div className="guti-block-content">
        {isEditing && (block.type === 'text' || block.type === 'paragraph' || block.type === 'heading') ? (
          <InlineEditor
            content={block.props.content || ''}
            onSave={handleContentChange}
            onCancel={() => setIsEditing(false)}
          />
        ) : (
          <BlockRenderer
            block={block}
            mode={ui.mode}
          />
        )}
      </div>

      {/* Block Actions (only when selected in edit mode) */}
      {isSelected && isEditMode && (
        <div className="guti-block-actions">
          <button
            className="guti-block-action"
            onClick={(e) => {
              e.stopPropagation();
              setIsEditing(true);
            }}
            title="Edit"
          >
            ✏️
          </button>
          <button
            className="guti-block-action"
            onClick={(e) => {
              e.stopPropagation();
              deleteBlock(block.id);
            }}
            title="Delete"
          >
            🗑️
          </button>
        </div>
      )}
    </div>
  );
}

// Simple inline editor component
interface InlineEditorProps {
  content: string;
  onSave: (content: string) => void;
  onCancel: () => void;
}

function InlineEditor({ content, onSave, onCancel }: InlineEditorProps) {
  const [value, setValue] = useState(content);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      onSave(value);
    } else if (e.key === 'Escape') {
      e.preventDefault();
      onCancel();
    }
  };

  const handleBlur = () => {
    onSave(value);
  };

  return (
    <textarea
      value={value}
      onChange={(e) => setValue(e.target.value)}
      onKeyDown={handleKeyDown}
      onBlur={handleBlur}
      className="guti-inline-editor"
      autoFocus
      rows={Math.max(1, value.split('\n').length)}
    />
  );
}
