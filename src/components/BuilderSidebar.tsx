import { useDraggable } from '@dnd-kit/core';
import { BlockData } from '../types/core';
import { generateBlockId } from '../utils/id';

/**
 * Builder sidebar props
 */
export interface BuilderSidebarProps {
  onBlockAdd: (block: BlockData) => void;
  settings?: any;
}

/**
 * Available block types for the sidebar
 */
const BLOCK_TYPES = [
  {
    type: 'heading',
    label: 'Heading',
    icon: '📝',
    defaultProps: {
      content: 'New Heading',
      level: 1,
    },
  },
  {
    type: 'paragraph',
    label: 'Paragraph',
    icon: '📄',
    defaultProps: {
      content: 'This is a new paragraph. Click to edit.',
    },
  },
  {
    type: 'text',
    label: 'Text',
    icon: '📝',
    defaultProps: {
      content: 'New text block',
      tag: 'p',
    },
  },
  {
    type: 'container',
    label: 'Container',
    icon: '📦',
    defaultProps: {
      direction: 'column',
      gap: '16px',
      alignItems: 'stretch',
    },
  },
];

/**
 * Builder sidebar component
 * Provides block palette and tools
 */
export function BuilderSidebar({
  onBlockAdd,
  settings,
}: BuilderSidebarProps) {
  const handleBlockAdd = (blockType: typeof BLOCK_TYPES[0]) => {
    const newBlock: BlockData = {
      id: generateBlockId(),
      type: blockType.type,
      props: { ...blockType.defaultProps },
    };

    // Add children array for container blocks
    if (blockType.type === 'container') {
      newBlock.children = [];
    }

    onBlockAdd(newBlock);
  };

  return (
    <div className="guti-builder-sidebar">
      {/* Blocks Section */}
      <div className="guti-sidebar-section">
        <h3 className="guti-sidebar-title">Blocks</h3>
        <div className="guti-block-palette">
          {BLOCK_TYPES.map(blockType => (
            <DraggableBlockItem
              key={blockType.type}
              blockType={blockType}
              onAdd={() => handleBlockAdd(blockType)}
            />
          ))}
        </div>
      </div>

      {/* Settings Section */}
      {settings && (
        <div className="guti-sidebar-section">
          <h3 className="guti-sidebar-title">Settings</h3>
          <div className="guti-settings-panel">
            {/* TODO: Implement settings panel */}
            <p className="text-sm text-gray-500">Settings panel coming soon...</p>
          </div>
        </div>
      )}

      {/* Help Section */}
      <div className="guti-sidebar-section">
        <h3 className="guti-sidebar-title">Help</h3>
        <div className="guti-help-panel">
          <p className="text-sm text-gray-600">
            Drag blocks from the palette to add them to your page.
          </p>
        </div>
      </div>
    </div>
  );
}

BuilderSidebar.displayName = 'BuilderSidebar';

// Draggable block item component
interface DraggableBlockItemProps {
  blockType: typeof BLOCK_TYPES[0];
  onAdd: () => void;
}

function DraggableBlockItem({ blockType, onAdd }: DraggableBlockItemProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    isDragging,
  } = useDraggable({
    id: `new-${blockType.type}`,
    data: {
      type: 'new-block',
      blockType,
    }
  });

  const style = transform ? {
    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
    opacity: isDragging ? 0.5 : 1,
  } : undefined;

  return (
    <button
      ref={setNodeRef}
      style={style}
      className={`guti-block-item ${isDragging ? 'dragging' : ''}`}
      onClick={onAdd}
      title={`Add ${blockType.label}`}
      {...attributes}
      {...listeners}
    >
      <span className="guti-block-icon">{blockType.icon}</span>
      <span className="guti-block-label">{blockType.label}</span>
    </button>
  );
}
