import { useDraggable } from '@dnd-kit/core';
import {
  Type,
  Heading1,
  AlignLeft,
  Square,
  GripVertical,
  Plus
} from 'lucide-react';
import { BlockData } from '../types/core';
import { generateBlockId } from '../utils/id';

/**
 * Builder sidebar props
 */
export interface BuilderSidebarProps {
  onBlockAdd: (block: BlockData) => void;
  settings?: any;
}

/**
 * Available block types for the sidebar
 */
const BLOCK_TYPES = [
  {
    type: 'heading',
    label: 'Heading',
    icon: Heading1,
    description: 'Add a heading (H1-H6)',
    defaultProps: {
      content: 'New Heading',
      level: 1,
    },
  },
  {
    type: 'paragraph',
    label: 'Paragraph',
    icon: AlignLeft,
    description: 'Add a paragraph of text',
    defaultProps: {
      content: 'This is a new paragraph. Click to edit.',
    },
  },
  {
    type: 'text',
    label: 'Text',
    icon: Type,
    description: 'Add simple text content',
    defaultProps: {
      content: 'New text block',
      tag: 'p',
    },
  },
  {
    type: 'container',
    label: 'Container',
    icon: Square,
    description: 'Add a layout container',
    defaultProps: {
      direction: 'column',
      gap: '16px',
      alignItems: 'stretch',
    },
  },
];

/**
 * Builder sidebar component
 * Provides block palette and tools
 */
export function BuilderSidebar({
  onBlockAdd,
  settings,
}: BuilderSidebarProps) {
  const handleBlockAdd = (blockType: typeof BLOCK_TYPES[0]) => {
    const newBlock: BlockData = {
      id: generateBlockId(),
      type: blockType.type,
      props: { ...blockType.defaultProps },
    };

    // Add children array for container blocks
    if (blockType.type === 'container') {
      newBlock.children = [];
    }

    onBlockAdd(newBlock);
  };

  return (
    <div className="guti-builder-sidebar">
      {/* Blocks Section */}
      <div className="guti-sidebar-section">
        <h3 className="guti-sidebar-title">Blocks</h3>
        <div className="guti-block-palette">
          {BLOCK_TYPES.map(blockType => (
            <DraggableBlockItem
              key={blockType.type}
              blockType={blockType}
              onAdd={() => handleBlockAdd(blockType)}
            />
          ))}
        </div>
      </div>

      {/* Settings Section */}
      {settings && (
        <div className="guti-sidebar-section">
          <h3 className="guti-sidebar-title">Settings</h3>
          <div className="guti-settings-panel">
            {/* TODO: Implement settings panel */}
            <p className="text-sm text-gray-500">Settings panel coming soon...</p>
          </div>
        </div>
      )}

      {/* Help Section */}
      <div className="guti-sidebar-section">
        <h3 className="guti-sidebar-title">Help</h3>
        <div className="guti-help-panel">
          <p className="text-sm text-gray-600">
            Drag blocks from the palette to add them to your page.
          </p>
        </div>
      </div>
    </div>
  );
}

BuilderSidebar.displayName = 'BuilderSidebar';

// Draggable block item component
interface DraggableBlockItemProps {
  blockType: typeof BLOCK_TYPES[0];
  onAdd: () => void;
}

function DraggableBlockItem({ blockType, onAdd }: DraggableBlockItemProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    isDragging,
  } = useDraggable({
    id: `new-${blockType.type}`,
    data: {
      type: 'new-block',
      blockType,
    }
  });

  const style = transform ? {
    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
    opacity: isDragging ? 0.8 : 1,
    zIndex: isDragging ? 1000 : 1,
  } : undefined;

  const IconComponent = blockType.icon;

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`guti-block-item ${isDragging ? 'dragging' : ''}`}
      title={blockType.description}
      {...attributes}
    >
      <div className="guti-block-item-content" onClick={onAdd}>
        <div className="guti-block-icon">
          <IconComponent size={20} />
        </div>
        <div className="guti-block-info">
          <span className="guti-block-label">{blockType.label}</span>
          <span className="guti-block-description">{blockType.description}</span>
        </div>
      </div>
      <div className="guti-block-drag-handle" {...listeners}>
        <GripVertical size={14} />
      </div>
      <button
        className="guti-block-add-button"
        onClick={onAdd}
        title={`Add ${blockType.label}`}
      >
        <Plus size={14} />
      </button>
    </div>
  );
}
