import { useState, useRef, useEffect } from 'react';
import {
  Bold,
  Italic,
  Underline,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  Link,
  Palette,
  Type,
  X
} from 'lucide-react';

export interface RichTextToolbarProps {
  onFormat: (command: string, value?: string) => void;
  onClose: () => void;
  position?: { x: number; y: number };
  visible?: boolean;
}

export function RichTextToolbar({ 
  onFormat, 
  onClose, 
  position = { x: 0, y: 0 },
  visible = false 
}: RichTextToolbarProps) {
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [showFontSize, setShowFontSize] = useState(false);
  const [showLinkDialog, setShowLinkDialog] = useState(false);
  const [linkUrl, setLinkUrl] = useState('');
  const toolbarRef = useRef<HTMLDivElement>(null);

  // Close toolbar when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (toolbarRef.current && !toolbarRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (visible) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [visible, onClose]);

  if (!visible) return null;

  const handleFormatClick = (command: string, value?: string) => {
    onFormat(command, value);
  };

  const handleLinkInsert = () => {
    if (linkUrl.trim()) {
      onFormat('createLink', linkUrl);
      setLinkUrl('');
      setShowLinkDialog(false);
    }
  };

  const colors = [
    '#000000', '#333333', '#666666', '#999999', '#cccccc',
    '#ff0000', '#ff6600', '#ffcc00', '#33cc33', '#0066cc',
    '#6600cc', '#cc0066', '#ffffff'
  ];

  const fontSizes = [
    { label: 'Small', value: '12px' },
    { label: 'Normal', value: '14px' },
    { label: 'Medium', value: '16px' },
    { label: 'Large', value: '18px' },
    { label: 'X-Large', value: '24px' },
  ];

  return (
    <div
      ref={toolbarRef}
      className="guti-rich-text-toolbar"
      style={{
        position: 'absolute',
        left: position.x,
        top: position.y - 50,
        zIndex: 1000,
      }}
    >
      <div className="guti-toolbar-content">
        {/* Basic Formatting */}
        <div className="guti-toolbar-group">
          <button
            className="guti-toolbar-button"
            onClick={() => handleFormatClick('bold')}
            title="Bold (Ctrl+B)"
          >
            <Bold size={16} />
          </button>
          <button
            className="guti-toolbar-button"
            onClick={() => handleFormatClick('italic')}
            title="Italic (Ctrl+I)"
          >
            <Italic size={16} />
          </button>
          <button
            className="guti-toolbar-button"
            onClick={() => handleFormatClick('underline')}
            title="Underline (Ctrl+U)"
          >
            <Underline size={16} />
          </button>
        </div>

        {/* Text Alignment */}
        <div className="guti-toolbar-group">
          <button
            className="guti-toolbar-button"
            onClick={() => handleFormatClick('justifyLeft')}
            title="Align Left"
          >
            <AlignLeft size={16} />
          </button>
          <button
            className="guti-toolbar-button"
            onClick={() => handleFormatClick('justifyCenter')}
            title="Align Center"
          >
            <AlignCenter size={16} />
          </button>
          <button
            className="guti-toolbar-button"
            onClick={() => handleFormatClick('justifyRight')}
            title="Align Right"
          >
            <AlignRight size={16} />
          </button>
          <button
            className="guti-toolbar-button"
            onClick={() => handleFormatClick('justifyFull')}
            title="Justify"
          >
            <AlignJustify size={16} />
          </button>
        </div>

        {/* Font Size */}
        <div className="guti-toolbar-group">
          <div className="guti-toolbar-dropdown">
            <button
              className="guti-toolbar-button"
              onClick={() => setShowFontSize(!showFontSize)}
              title="Font Size"
            >
              <Type size={16} />
            </button>
            {showFontSize && (
              <div className="guti-dropdown-menu">
                {fontSizes.map((size) => (
                  <button
                    key={size.value}
                    className="guti-dropdown-item"
                    onClick={() => {
                      handleFormatClick('fontSize', size.value);
                      setShowFontSize(false);
                    }}
                  >
                    {size.label}
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Text Color */}
        <div className="guti-toolbar-group">
          <div className="guti-toolbar-dropdown">
            <button
              className="guti-toolbar-button"
              onClick={() => setShowColorPicker(!showColorPicker)}
              title="Text Color"
            >
              <Palette size={16} />
            </button>
            {showColorPicker && (
              <div className="guti-color-picker">
                {colors.map((color) => (
                  <button
                    key={color}
                    className="guti-color-swatch"
                    style={{ backgroundColor: color }}
                    onClick={() => {
                      handleFormatClick('foreColor', color);
                      setShowColorPicker(false);
                    }}
                    title={color}
                  />
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Link */}
        <div className="guti-toolbar-group">
          <button
            className="guti-toolbar-button"
            onClick={() => setShowLinkDialog(!showLinkDialog)}
            title="Insert Link"
          >
            <Link size={16} />
          </button>
          {showLinkDialog && (
            <div className="guti-link-dialog">
              <input
                type="url"
                placeholder="Enter URL..."
                value={linkUrl}
                onChange={(e) => setLinkUrl(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleLinkInsert();
                  } else if (e.key === 'Escape') {
                    setShowLinkDialog(false);
                  }
                }}
                autoFocus
              />
              <button onClick={handleLinkInsert}>Add</button>
            </div>
          )}
        </div>

        {/* Close Button */}
        <div className="guti-toolbar-group">
          <button
            className="guti-toolbar-button guti-toolbar-close"
            onClick={onClose}
            title="Close"
          >
            <X size={16} />
          </button>
        </div>
      </div>
    </div>
  );
}
