import { useEffect } from 'react';
import { PageData, GutiBuilderConfig } from '../types/core';
import { ThemeProvider } from '../theme/ThemeProvider';
import { useBuilderStore } from '../core/store';
import { PageRenderer } from './PageRenderer';
import { BuilderSidebar } from './BuilderSidebar';
import { BuilderToolbar } from './BuilderToolbar';
import { defaultThemeTokens } from '../theme/tokens';

/**
 * Guti Builder props
 */
export interface GutiBuilderProps extends Partial<GutiBuilderConfig> {
  pageData: PageData;
  className?: string;
}

/**
 * Main Guti Builder component
 * Provides the complete page builder interface
 */
export function GutiBuilder({
  pageData,
  theme,
  settings = {},
  className = '',
  onBlockAdd,
  onBlockUpdate,
}: GutiBuilderProps) {
  // Use provided theme or default
  const builderTheme = theme || defaultThemeTokens;
  // Get store actions
  const {
    setPageData,
    addBlock,
    updateBlock,
    selectBlock,
    ui,
    toggleSidebar,
    setEditorMode,
  } = useBuilderStore();

  // Initialize store with page data
  useEffect(() => {
    setPageData(pageData);
  }, [pageData, setPageData]);

  // Handle block operations
  const handleBlockUpdate = (blockId: string, updates: any) => {
    updateBlock(blockId, updates);
    onBlockUpdate?.(updates);
  };

  const handleBlockSelect = (blockId: string) => {
    selectBlock(blockId);
  };

  const handleBlockAdd = (block: any) => {
    addBlock(block);
    onBlockAdd?.(block);
  };

  return (
    <ThemeProvider theme={builderTheme}>
      <div className={`guti-builder ${className}`}>
        {/* Builder Toolbar */}
        <BuilderToolbar
          onModeChange={setEditorMode}
          onToggleSidebar={toggleSidebar}
          currentMode={ui.mode}
          sidebarOpen={ui.sidebarOpen}
        />

        <div className="guti-builder-content">
          {/* Sidebar */}
          {ui.sidebarOpen && (
            <BuilderSidebar
              onBlockAdd={handleBlockAdd}
              settings={settings}
            />
          )}

          {/* Main Canvas */}
          <div className="guti-builder-canvas">
            <PageRenderer
              pageData={pageData}
              mode={ui.mode}
              onBlockUpdate={handleBlockUpdate}
              onBlockSelect={handleBlockSelect}
            />
          </div>

          {/* Properties Panel */}
          {ui.propertiesPanelOpen && (
            <div className="guti-builder-properties">
              {/* TODO: Implement properties panel */}
              <div className="p-4">
                <h3>Properties</h3>
                <p>Properties panel coming soon...</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </ThemeProvider>
  );
}

GutiBuilder.displayName = 'GutiBuilder';
