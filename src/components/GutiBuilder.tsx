import { useEffect } from 'react';
import {
  DndContext,
  DragEndEvent,
  DragOverEvent,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
  closestCenter,
} from '@dnd-kit/core';
import { arrayMove } from '@dnd-kit/sortable';
import { PageData, GutiBuilderConfig, BlockData } from '../types/core';
import { ThemeProvider } from '../theme/ThemeProvider';
import { useBuilderStore } from '../core/store';
import { DroppableCanvas } from './DroppableCanvas';
import { BuilderSidebar } from './BuilderSidebar';
import { BuilderToolbar } from './BuilderToolbar';
import { defaultThemeTokens } from '../theme/tokens';
import { generateBlockId } from '../utils/id';

/**
 * Guti Builder props
 */
export interface GutiBuilderProps extends Partial<GutiBuilderConfig> {
  pageData: PageData;
  className?: string;
}

/**
 * Main Guti Builder component
 * Provides the complete page builder interface
 */
export function GutiBuilder({
  pageData,
  theme,
  settings = {},
  className = '',
  onBlockAdd,
}: GutiBuilderProps) {
  // Use provided theme or default
  const builderTheme = theme || defaultThemeTokens;
  // Get store actions
  const {
    setPageData,
    addBlock,
    selectBlock,
    ui,
    page,
    toggleSidebar,
    setEditorMode,
  } = useBuilderStore();

  // Drag and drop sensors
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  // Initialize store with page data
  useEffect(() => {
    setPageData(pageData);
  }, [pageData, setPageData]);

  // Handle block operations
  const handleBlockSelect = (blockId: string) => {
    selectBlock(blockId);
  };

  const handleBlockAdd = (block: any) => {
    addBlock(block);
    onBlockAdd?.(block);
  };

  // Drag and drop handlers
  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;

    if (active.data.current?.type === 'block') {
      selectBlock(active.id as string);
    }
  };

  const handleDragOver = (_event: DragOverEvent) => {
    // Handle drag over logic if needed
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (!over) return;

    // Handle dropping new blocks from sidebar
    if (active.data.current?.type === 'new-block') {
      const blockType = active.data.current.blockType;
      const newBlock: BlockData = {
        id: generateBlockId(),
        type: blockType.type,
        props: { ...blockType.defaultProps },
      };

      // Add children array for container blocks
      if (blockType.type === 'container') {
        newBlock.children = [];
      }

      addBlock(newBlock);
      onBlockAdd?.(newBlock);
      return;
    }

    // Handle reordering existing blocks
    if (active.data.current?.type === 'block' && page.pageData) {
      const activeIndex = active.data.current.index;
      let overIndex: number;

      // Determine the target index
      if (over.data.current?.type === 'block') {
        overIndex = over.data.current.index;
      } else {
        // Dropping on canvas - add to end
        overIndex = page.pageData.blocks.length;
      }

      if (activeIndex !== overIndex && activeIndex !== undefined) {
        const newBlocks = arrayMove(page.pageData.blocks, activeIndex, overIndex);
        setPageData({
          ...page.pageData,
          blocks: newBlocks,
          updatedAt: new Date().toISOString(),
        });
      }
    }
  };

  return (
    <ThemeProvider theme={builderTheme}>
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragStart={handleDragStart}
        onDragOver={handleDragOver}
        onDragEnd={handleDragEnd}
      >
        <div className={`guti-builder ${className}`}>
          {/* Builder Toolbar */}
          <BuilderToolbar
            onModeChange={setEditorMode}
            onToggleSidebar={toggleSidebar}
            currentMode={ui.mode}
            sidebarOpen={ui.sidebarOpen}
          />

          <div className="guti-builder-content">
            {/* Sidebar */}
            {ui.sidebarOpen && (
              <BuilderSidebar
                onBlockAdd={handleBlockAdd}
                settings={settings}
              />
            )}

            {/* Main Canvas */}
            <div className="guti-builder-canvas">
              <DroppableCanvas
                pageData={pageData}
                onBlockEdit={handleBlockSelect}
              />
            </div>

            {/* Properties Panel */}
            {ui.propertiesPanelOpen && (
              <div className="guti-builder-properties">
                {/* TODO: Implement properties panel */}
                <div className="p-4">
                  <h3>Properties</h3>
                  <p>Properties panel coming soon...</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </DndContext>
    </ThemeProvider>
  );
}

GutiBuilder.displayName = 'GutiBuilder';
