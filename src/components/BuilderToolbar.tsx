import {
  <PERSON>u,
  Eye,
  Edit3,
  <PERSON>do,
  <PERSON>o,
  Save
} from 'lucide-react';
import { EditorMode } from '../core/store/types';

/**
 * Builder toolbar props
 */
export interface BuilderToolbarProps {
  currentMode: EditorMode;
  sidebarOpen: boolean;
  onModeChange: (mode: EditorMode) => void;
  onToggleSidebar: () => void;
  onSave?: () => void;
  onUndo?: () => void;
  onRedo?: () => void;
  canUndo?: boolean;
  canRedo?: boolean;
}

/**
 * Builder toolbar component
 * Provides main toolbar with mode switching and actions
 */
export function BuilderToolbar({
  currentMode,
  sidebarOpen,
  onModeChange,
  onToggleSidebar,
  onSave,
  onUndo,
  onRedo,
  canUndo = false,
  canRedo = false,
}: BuilderToolbarProps) {
  return (
    <div className="guti-builder-toolbar">
      <div className="guti-toolbar-section guti-toolbar-left">
        {/* Sidebar Toggle */}
        <button
          className={`guti-toolbar-button ${sidebarOpen ? 'active' : ''}`}
          onClick={onToggleSidebar}
          title="Toggle Sidebar"
        >
          <Menu size={16} />
        </button>

        {/* Mode Switcher */}
        <div className="guti-mode-switcher">
          <button
            className={`guti-mode-button ${currentMode === 'edit' ? 'active' : ''}`}
            onClick={() => onModeChange('edit')}
            title="Edit Mode"
          >
            <Edit3 size={14} />
            <span>Edit</span>
          </button>
          <button
            className={`guti-mode-button ${currentMode === 'preview' ? 'active' : ''}`}
            onClick={() => onModeChange('preview')}
            title="Preview Mode"
          >
            <Eye size={14} />
            <span>Preview</span>
          </button>
        </div>
      </div>

      <div className="guti-toolbar-section guti-toolbar-center">
        <h2 className="guti-toolbar-title">Guti Builder</h2>
      </div>

      <div className="guti-toolbar-section guti-toolbar-right">
        {/* Undo/Redo */}
        <button
          className={`guti-toolbar-button ${!canUndo ? 'disabled' : ''}`}
          onClick={onUndo}
          disabled={!canUndo}
          title="Undo"
        >
          <Undo size={16} />
        </button>

        <button
          className={`guti-toolbar-button ${!canRedo ? 'disabled' : ''}`}
          onClick={onRedo}
          disabled={!canRedo}
          title="Redo"
        >
          <Redo size={16} />
        </button>

        {/* Save Button */}
        <button
          className="guti-toolbar-button guti-save-button"
          onClick={onSave}
          title="Save"
        >
          <Save size={16} />
          <span>Save</span>
        </button>
      </div>
    </div>
  );
}

BuilderToolbar.displayName = 'BuilderToolbar';
