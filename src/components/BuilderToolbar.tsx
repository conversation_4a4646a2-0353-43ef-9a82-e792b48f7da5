import { EditorMode } from '../core/store/types';

/**
 * Builder toolbar props
 */
export interface BuilderToolbarProps {
  currentMode: EditorMode;
  sidebarOpen: boolean;
  onModeChange: (mode: EditorMode) => void;
  onToggleSidebar: () => void;
  onSave?: () => void;
  onUndo?: () => void;
  onRedo?: () => void;
  canUndo?: boolean;
  canRedo?: boolean;
}

/**
 * Builder toolbar component
 * Provides main toolbar with mode switching and actions
 */
export function BuilderToolbar({
  currentMode,
  sidebarOpen,
  onModeChange,
  onToggleSidebar,
  onSave,
  onUndo,
  onRedo,
  canUndo = false,
  canRedo = false,
}: BuilderToolbarProps) {
  return (
    <div className="guti-builder-toolbar">
      <div className="guti-toolbar-section guti-toolbar-left">
        {/* Sidebar Toggle */}
        <button
          className={`guti-toolbar-button ${sidebarOpen ? 'active' : ''}`}
          onClick={onToggleSidebar}
          title="Toggle Sidebar"
        >
          <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
            <path d="M2 3h12a1 1 0 0 1 0 2H2a1 1 0 0 1 0-2zm0 4h12a1 1 0 0 1 0 2H2a1 1 0 0 1 0-2zm0 4h12a1 1 0 0 1 0 2H2a1 1 0 0 1 0-2z"/>
          </svg>
        </button>

        {/* Mode Switcher */}
        <div className="guti-mode-switcher">
          <button
            className={`guti-mode-button ${currentMode === 'edit' ? 'active' : ''}`}
            onClick={() => onModeChange('edit')}
          >
            Edit
          </button>
          <button
            className={`guti-mode-button ${currentMode === 'preview' ? 'active' : ''}`}
            onClick={() => onModeChange('preview')}
          >
            Preview
          </button>
        </div>
      </div>

      <div className="guti-toolbar-section guti-toolbar-center">
        <h2 className="guti-toolbar-title">Guti Builder</h2>
      </div>

      <div className="guti-toolbar-section guti-toolbar-right">
        {/* Undo/Redo */}
        <button
          className={`guti-toolbar-button ${!canUndo ? 'disabled' : ''}`}
          onClick={onUndo}
          disabled={!canUndo}
          title="Undo"
        >
          <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
            <path d="M3.5 8a4.5 4.5 0 0 1 9 0 .5.5 0 0 0 1 0 5.5 5.5 0 1 0-5.5 5.5h1a.5.5 0 0 0 0-1h-1A4.5 4.5 0 0 1 3.5 8z"/>
            <path d="M6.854 4.646a.5.5 0 0 0-.708.708L7.293 6.5H2.5a.5.5 0 0 0 0 1h4.793l-1.147 1.146a.5.5 0 0 0 .708.708l2-2a.5.5 0 0 0 0-.708l-2-2z"/>
          </svg>
        </button>
        
        <button
          className={`guti-toolbar-button ${!canRedo ? 'disabled' : ''}`}
          onClick={onRedo}
          disabled={!canRedo}
          title="Redo"
        >
          <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
            <path d="M12.5 8a4.5 4.5 0 0 0-9 0 .5.5 0 0 1-1 0 5.5 5.5 0 1 1 5.5 5.5h-1a.5.5 0 0 1 0-1h1A4.5 4.5 0 0 0 12.5 8z"/>
            <path d="M9.146 4.646a.5.5 0 0 1 .708.708L8.707 6.5H13.5a.5.5 0 0 1 0 1H8.707l1.147 1.146a.5.5 0 0 1-.708.708l-2-2a.5.5 0 0 1 0-.708l2-2z"/>
          </svg>
        </button>

        {/* Save Button */}
        <button
          className="guti-toolbar-button guti-save-button"
          onClick={onSave}
          title="Save"
        >
          Save
        </button>
      </div>
    </div>
  );
}

BuilderToolbar.displayName = 'BuilderToolbar';
