import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import TextStyle from '@tiptap/extension-text-style';
import Color from '@tiptap/extension-color';
import TextAlign from '@tiptap/extension-text-align';
import Link from '@tiptap/extension-link';
import { useEffect, useCallback } from 'react';
import {
  Bold,
  Italic,
  Underline,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  Link as LinkIcon,
  Palette
} from 'lucide-react';

export interface TiptapEditorProps {
  content: string;
  onUpdate: (content: string) => void;
  onBlur?: () => void;
  placeholder?: string;
  className?: string;
  autoFocus?: boolean;
}

export function TiptapEditor({
  content,
  onUpdate,
  onBlur,
  placeholder = 'Start typing...',
  className = '',
  autoFocus = false
}: TiptapEditorProps) {
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        // Disable default heading, paragraph, etc. for inline editing
        heading: false,
        paragraph: {
          HTMLAttributes: {
            class: 'tiptap-paragraph',
          },
        },
      }),
      TextStyle,
      Color,
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'tiptap-link',
        },
      }),
    ],
    content,
    onUpdate: ({ editor }) => {
      const html = editor.getHTML();
      onUpdate(html);
    },
    onBlur: () => {
      if (onBlur) {
        onBlur();
      }
    },
    editorProps: {
      attributes: {
        class: `tiptap-editor ${className}`,
        'data-placeholder': placeholder,
      },
    },
  });

  // Update content when prop changes
  useEffect(() => {
    if (editor && content !== editor.getHTML()) {
      editor.commands.setContent(content);
    }
  }, [content, editor]);

  // Auto focus
  useEffect(() => {
    if (editor && autoFocus) {
      editor.commands.focus('end');
    }
  }, [editor, autoFocus]);

  const setLink = useCallback(() => {
    if (!editor) return;
    
    const previousUrl = editor.getAttributes('link').href;
    const url = window.prompt('URL', previousUrl);

    if (url === null) {
      return;
    }

    if (url === '') {
      editor.chain().focus().extendMarkRange('link').unsetLink().run();
      return;
    }

    editor.chain().focus().extendMarkRange('link').setLink({ href: url }).run();
  }, [editor]);

  const setColor = useCallback((color: string) => {
    if (!editor) return;
    editor.chain().focus().setColor(color).run();
  }, [editor]);

  if (!editor) {
    return null;
  }

  const colors = [
    '#000000', '#333333', '#666666', '#999999', '#cccccc',
    '#ff0000', '#ff6600', '#ffcc00', '#33cc33', '#0066cc',
    '#6600cc', '#cc0066', '#ffffff'
  ];

  return (
    <div className="tiptap-wrapper">
      {/* Toolbar */}
      <div className="tiptap-toolbar">
        {/* Basic Formatting */}
        <div className="tiptap-toolbar-group">
          <button
            onClick={() => editor.chain().focus().toggleBold().run()}
            className={`tiptap-toolbar-button ${editor.isActive('bold') ? 'active' : ''}`}
            title="Bold (Ctrl+B)"
          >
            <Bold size={16} />
          </button>
          <button
            onClick={() => editor.chain().focus().toggleItalic().run()}
            className={`tiptap-toolbar-button ${editor.isActive('italic') ? 'active' : ''}`}
            title="Italic (Ctrl+I)"
          >
            <Italic size={16} />
          </button>
          <button
            onClick={() => editor.chain().focus().toggleStrike().run()}
            className={`tiptap-toolbar-button ${editor.isActive('strike') ? 'active' : ''}`}
            title="Strikethrough"
          >
            <Underline size={16} />
          </button>
        </div>

        {/* Text Alignment */}
        <div className="tiptap-toolbar-group">
          <button
            onClick={() => editor.chain().focus().setTextAlign('left').run()}
            className={`tiptap-toolbar-button ${editor.isActive({ textAlign: 'left' }) ? 'active' : ''}`}
            title="Align Left"
          >
            <AlignLeft size={16} />
          </button>
          <button
            onClick={() => editor.chain().focus().setTextAlign('center').run()}
            className={`tiptap-toolbar-button ${editor.isActive({ textAlign: 'center' }) ? 'active' : ''}`}
            title="Align Center"
          >
            <AlignCenter size={16} />
          </button>
          <button
            onClick={() => editor.chain().focus().setTextAlign('right').run()}
            className={`tiptap-toolbar-button ${editor.isActive({ textAlign: 'right' }) ? 'active' : ''}`}
            title="Align Right"
          >
            <AlignRight size={16} />
          </button>
          <button
            onClick={() => editor.chain().focus().setTextAlign('justify').run()}
            className={`tiptap-toolbar-button ${editor.isActive({ textAlign: 'justify' }) ? 'active' : ''}`}
            title="Justify"
          >
            <AlignJustify size={16} />
          </button>
        </div>

        {/* Text Color */}
        <div className="tiptap-toolbar-group">
          <div className="tiptap-color-picker-wrapper">
            <button
              className="tiptap-toolbar-button"
              title="Text Color"
            >
              <Palette size={16} />
            </button>
            <div className="tiptap-color-picker">
              {colors.map((color) => (
                <button
                  key={color}
                  className="tiptap-color-swatch"
                  style={{ backgroundColor: color }}
                  onClick={() => setColor(color)}
                  title={color}
                />
              ))}
            </div>
          </div>
        </div>

        {/* Link */}
        <div className="tiptap-toolbar-group">
          <button
            onClick={setLink}
            className={`tiptap-toolbar-button ${editor.isActive('link') ? 'active' : ''}`}
            title="Insert Link"
          >
            <LinkIcon size={16} />
          </button>
        </div>
      </div>

      {/* Editor Content */}
      <EditorContent editor={editor} />
    </div>
  );
}
