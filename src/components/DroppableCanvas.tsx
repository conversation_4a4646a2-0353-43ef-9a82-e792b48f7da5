import { useDroppable } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { PageData } from '../types/core';
import { DraggableBlock } from './DraggableBlock';
import { useBuilderStore } from '../core/store';

export interface DroppableCanvasProps {
  pageData: PageData;
  onBlockEdit?: (blockId: string) => void;
}

export function DroppableCanvas({ pageData, onBlockEdit }: DroppableCanvasProps) {
  const { ui, clearSelection } = useBuilderStore();
  
  const { setNodeRef, isOver } = useDroppable({
    id: 'canvas',
    data: {
      type: 'canvas',
      accepts: ['block', 'new-block'],
    }
  });

  const handleCanvasClick = (e: React.MouseEvent) => {
    // Only clear selection if clicking on the canvas itself, not on blocks
    if (e.target === e.currentTarget) {
      clearSelection();
    }
  };

  return (
    <div
      ref={setNodeRef}
      className={`
        guti-droppable-canvas
        ${isOver ? 'guti-canvas-drag-over' : ''}
        ${ui.mode === 'edit' ? 'guti-canvas-editable' : ''}
      `}
      onClick={handleCanvasClick}
    >
      {/* Page Header (only in edit mode) */}
      {ui.mode === 'edit' && (
        <div className="guti-page-header">
          <h1 className="guti-page-title">{pageData.title}</h1>
          {pageData.description && (
            <p className="guti-page-description">{pageData.description}</p>
          )}
        </div>
      )}

      {/* Blocks Container */}
      <div className="guti-blocks-container">
        {pageData.blocks.length === 0 ? (
          <div className="guti-empty-canvas">
            <div className="guti-empty-message">
              <h3>Start building your page</h3>
              <p>Drag blocks from the sidebar to add content</p>
            </div>
          </div>
        ) : (
          <SortableContext 
            items={pageData.blocks.map(block => block.id)}
            strategy={verticalListSortingStrategy}
          >
            {pageData.blocks.map((block, index) => (
              <DraggableBlock
                key={block.id}
                block={block}
                index={index}
                onEdit={onBlockEdit}
              />
            ))}
          </SortableContext>
        )}
      </div>

      {/* Drop Zone Indicator */}
      {isOver && (
        <div className="guti-drop-indicator">
          <div className="guti-drop-line" />
          <span className="guti-drop-text">Drop here to add block</span>
        </div>
      )}
    </div>
  );
}
