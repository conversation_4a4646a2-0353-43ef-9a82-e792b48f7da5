import { PageData } from '../types/core';
import { ThemeTokens } from '../types/theme';
import { ThemeProvider } from '../theme/ThemeProvider';
import { BlockRenderer, BlockRegistry, defaultBlockRegistry } from '../blocks/BlockRegistry';

/**
 * Page renderer props
 */
export interface PageRendererProps {
  pageData: PageData;
  theme?: ThemeTokens;
  mode?: 'edit' | 'preview';
  blockRegistry?: BlockRegistry;
  className?: string;
  onBlockUpdate?: (blockId: string, updates: any) => void;
  onBlockSelect?: (blockId: string) => void;
}

/**
 * Page renderer component
 * Renders a complete page with all its blocks
 */
export function PageRenderer({
  pageData,
  theme,
  mode = 'preview',
  blockRegistry = defaultBlockRegistry,
  className = '',
  onBlockUpdate,
  onBlockSelect,
}: PageRendererProps) {
  const content = (
    <div className={`guti-page-renderer guti-mode-${mode} ${className}`}>
      {/* Page metadata (hidden in preview) */}
      {mode === 'edit' && (
        <div className="guti-page-meta">
          <h1 className="guti-page-title">{pageData.title}</h1>
          {pageData.description && (
            <p className="guti-page-description">{pageData.description}</p>
          )}
        </div>
      )}
      
      {/* Page blocks */}
      <div className="guti-page-blocks">
        {pageData.blocks.map(block => (
          <div key={block.id} className="guti-block-wrapper">
            <BlockRenderer
              block={block}
              registry={blockRegistry}
              mode={mode}
              onBlockUpdate={onBlockUpdate}
              onBlockSelect={onBlockSelect}
            />
          </div>
        ))}
      </div>
    </div>
  );

  // Wrap with theme provider if theme is provided
  if (theme) {
    return (
      <ThemeProvider theme={theme}>
        {content}
      </ThemeProvider>
    );
  }

  return content;
}

PageRenderer.displayName = 'PageRenderer';
