import { useRef, useEffect, useCallback } from 'react';

export interface ContentEditableProps {
  content: string;
  onSave: (content: string) => void;
  onCancel: () => void;
  className?: string;
  placeholder?: string;
  multiline?: boolean;
}

export function ContentEditable({
  content,
  onSave,
  onCancel,
  className = '',
  placeholder = 'Type here...',
  multiline = false
}: ContentEditableProps) {
  const elementRef = useRef<HTMLDivElement>(null);
  const initialContent = useRef(content);

  // Set up the element when it mounts
  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    // Set initial content
    element.textContent = content;
    
    // Focus and select all
    element.focus();
    
    // Select all text for easy replacement
    const selection = window.getSelection();
    if (selection) {
      selection.selectAllChildren(element);
    }
  }, []);

  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    if (e.key === 'Enter' && !multiline) {
      e.preventDefault();
      handleSave();
    } else if (e.key === 'Enter' && e.shiftKey && multiline) {
      // Allow shift+enter for new lines in multiline mode
      return;
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancel();
    }
  }, [multiline]);

  const handleSave = useCallback(() => {
    const element = elementRef.current;
    if (element) {
      const newContent = element.textContent || '';
      onSave(newContent);
    }
  }, [onSave]);

  const handleCancel = useCallback(() => {
    const element = elementRef.current;
    if (element) {
      element.textContent = initialContent.current;
    }
    onCancel();
  }, [onCancel]);

  const handleBlur = useCallback(() => {
    // Auto-save on blur
    handleSave();
  }, [handleSave]);

  // Set up event listeners
  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    element.addEventListener('keydown', handleKeyDown);
    element.addEventListener('blur', handleBlur);

    return () => {
      element.removeEventListener('keydown', handleKeyDown);
      element.removeEventListener('blur', handleBlur);
    };
  }, [handleKeyDown, handleBlur]);

  return (
    <div
      ref={elementRef}
      contentEditable
      suppressContentEditableWarning
      className={`guti-content-editable ${className}`}
      data-placeholder={placeholder}
      style={{
        minHeight: multiline ? '1.5em' : '1.2em',
        outline: 'none',
        border: '2px dashed rgba(59, 130, 246, 0.5)',
        borderRadius: '4px',
        padding: '2px 4px',
        background: 'rgba(59, 130, 246, 0.05)',
        fontSize: 'inherit',
        fontFamily: 'inherit',
        lineHeight: 'inherit',
        color: 'inherit',
        width: '100%',
        boxSizing: 'border-box',
        transition: 'all 0.2s ease',
      }}
    />
  );
}
