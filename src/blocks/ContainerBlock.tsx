import React from 'react';
import { ContainerBlockProps } from '../types/blocks';
import { BlockData } from '../types/core';

/**
 * Container block component props
 */
export interface ContainerBlockComponentProps extends ContainerBlockProps {
  children?: React.ReactNode;
  blocks?: BlockData[];
  renderBlock?: (block: BlockData) => React.ReactNode;
}

/**
 * Container block component
 * Renders a flexible container with configurable layout
 */
export function ContainerBlock({ 
  direction = 'column',
  gap = '16px',
  alignItems = 'stretch',
  justifyContent = 'flex-start',
  padding = '0',
  className = '',
  children,
  blocks,
  renderBlock,
}: ContainerBlockComponentProps) {
  const containerStyle: React.CSSProperties = {
    display: 'flex',
    flexDirection: direction,
    gap,
    alignItems,
    justifyContent,
    padding,
  };

  return (
    <div 
      className={`guti-container-block guti-container-${direction} ${className}`}
      style={containerStyle}
    >
      {children}
      {blocks && renderBlock && blocks.map(block => (
        <div key={block.id}>
          {renderBlock(block)}
        </div>
      ))}
    </div>
  );
}

ContainerBlock.displayName = 'ContainerBlock';
