import React from 'react';
import { ContainerBlockProps } from '../types/blocks';
import { BlockData } from '../types/core';

/**
 * Container block component props
 */
export interface ContainerBlockComponentProps extends ContainerBlockProps {
  children?: React.ReactNode;
  blocks?: BlockData[];
  renderBlock?: (block: BlockData) => React.ReactNode;
}

/**
 * Container block component
 * Renders a flexible container with configurable layout
 */
export function ContainerBlock({
  padding = '0',
  className = '',
  children,
  blocks,
  renderBlock,
}: ContainerBlockComponentProps) {
  const containerStyle: React.CSSProperties = {
    padding,
  };

  return (
    <div 
      className={`guti-container-block ${className}`}
      style={containerStyle}
    >
      {children}
      {blocks && renderBlock && blocks.map(block => (
        <div key={block.id}>
          {renderBlock(block)}
        </div>
      ))}
    </div>
  );
}

ContainerBlock.displayName = 'ContainerBlock';
