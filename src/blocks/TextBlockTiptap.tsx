import { useState, useCallback } from 'react';
import { TextBlockProps } from '../types/blocks';
import { TiptapEditor } from '../components/TiptapEditor';

/**
 * Text block component with Tiptap rich text editing
 */
export function TextBlockTiptap({ 
  content, 
  tag = 'p', 
  className = '',
  isEditing = false,
  onContentChange,
  onEditingChange,
  mode = 'preview'
}: TextBlockProps & {
  isEditing?: boolean;
  onContentChange?: (content: string) => void;
  onEditingChange?: (editing: boolean) => void;
  mode?: 'edit' | 'preview';
}) {
  const Tag = tag;
  const [localContent, setLocalContent] = useState(content);

  const handleUpdate = useCallback((newContent: string) => {
    setLocalContent(newContent);
    if (onContentChange) {
      onContentChange(newContent);
    }
  }, [onContentChange]);

  const handleBlur = useCallback(() => {
    if (onEditingChange) {
      onEditingChange(false);
    }
  }, [onEditingChange]);

  const handleClick = useCallback(() => {
    if (mode === 'edit' && !isEditing && onEditingChange) {
      onEditingChange(true);
    }
  }, [mode, isEditing, onEditingChange]);

  if (isEditing) {
    return (
      <div className={`guti-text-block-editing ${className}`}>
        <TiptapEditor
          content={localContent}
          onUpdate={handleUpdate}
          onBlur={handleBlur}
          placeholder="Start typing..."
          className="tiptap-inline"
          autoFocus
        />
      </div>
    );
  }

  return (
    <Tag 
      className={`guti-text-block ${className}`}
      style={{
        cursor: mode === 'edit' ? 'text' : 'inherit',
        outline: 'none',
        border: mode === 'edit' ? '1px solid transparent' : 'none',
        borderRadius: '2px',
        padding: mode === 'edit' ? '2px' : '0',
        transition: 'all 0.2s ease',
      }}
      onClick={handleClick}
      onMouseEnter={(e) => {
        if (mode === 'edit') {
          e.currentTarget.style.borderColor = 'rgba(34, 197, 94, 0.5)';
        }
      }}
      onMouseLeave={(e) => {
        if (mode === 'edit') {
          e.currentTarget.style.borderColor = 'transparent';
        }
      }}
      dangerouslySetInnerHTML={{ __html: localContent }}
    />
  );
}

TextBlockTiptap.displayName = 'TextBlockTiptap';
