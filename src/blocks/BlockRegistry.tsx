import React from 'react';
import { BlockData } from '../types/core';
import { TextBlock } from './TextBlock';
import { HeadingBlock } from './HeadingBlock';
import { ParagraphBlock } from './ParagraphBlock';
import { ContainerBlock } from './ContainerBlock';

/**
 * Block component type
 */
export type BlockComponent = React.ComponentType<any>;

/**
 * Block registry for mapping block types to components
 */
export class BlockRegistry {
  private blocks = new Map<string, BlockComponent>();

  /**
   * Register a block component
   */
  register(type: string, component: BlockComponent): void {
    this.blocks.set(type, component);
  }

  /**
   * Get a block component by type
   */
  get(type: string): BlockComponent | undefined {
    return this.blocks.get(type);
  }

  /**
   * Check if a block type is registered
   */
  has(type: string): boolean {
    return this.blocks.has(type);
  }

  /**
   * Get all registered block types
   */
  getTypes(): string[] {
    return Array.from(this.blocks.keys());
  }

  /**
   * Unregister a block type
   */
  unregister(type: string): boolean {
    return this.blocks.delete(type);
  }

  /**
   * Clear all registered blocks
   */
  clear(): void {
    this.blocks.clear();
  }
}

/**
 * Default block registry instance
 */
export const defaultBlockRegistry = new BlockRegistry();

// Register built-in blocks
defaultBlockRegistry.register('text', TextBlock);
defaultBlockRegistry.register('heading', HeadingBlock);
defaultBlockRegistry.register('paragraph', ParagraphBlock);
defaultBlockRegistry.register('container', ContainerBlock);

/**
 * Block renderer component
 * Renders a block using the registered component
 */
export interface BlockRendererProps {
  block: BlockData;
  registry?: BlockRegistry;
  mode?: 'edit' | 'preview';
  onBlockUpdate?: (blockId: string, updates: Partial<BlockData>) => void;
  onBlockSelect?: (blockId: string) => void;
  renderChildren?: boolean;
}

export function BlockRenderer({
  block,
  registry = defaultBlockRegistry,
  mode = 'preview',
  onBlockUpdate,
  onBlockSelect,
  renderChildren = true,
}: BlockRendererProps) {
  const BlockComponent = registry.get(block.type);

  if (!BlockComponent) {
    console.warn(`Block type "${block.type}" not found in registry`);
    return (
      <div className="guti-unknown-block">
        <p>Unknown block type: {block.type}</p>
      </div>
    );
  }

  // Handle container blocks with children
  if (block.children && renderChildren) {
    return (
      <BlockComponent
        {...block.props}
        blocks={block.children}
        renderBlock={(childBlock: BlockData) => (
          <BlockRenderer
            key={childBlock.id}
            block={childBlock}
            registry={registry}
            mode={mode}
            onBlockUpdate={onBlockUpdate}
            onBlockSelect={onBlockSelect}
            renderChildren={renderChildren}
          />
        )}
      />
    );
  }

  return <BlockComponent {...block.props} />;
}

BlockRenderer.displayName = 'BlockRenderer';
