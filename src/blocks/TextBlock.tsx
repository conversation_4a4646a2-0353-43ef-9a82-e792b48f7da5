import { TextBlockProps } from '../types/blocks';

/**
 * Text block component
 * Renders text content with configurable HTML tag
 */
export function TextBlock({ 
  content, 
  tag = 'p', 
  className = '' 
}: TextBlockProps) {
  const Tag = tag;
  
  return (
    <Tag 
      className={`guti-text-block ${className}`}
      dangerouslySetInnerHTML={{ __html: content }}
    />
  );
}

TextBlock.displayName = 'TextBlock';
