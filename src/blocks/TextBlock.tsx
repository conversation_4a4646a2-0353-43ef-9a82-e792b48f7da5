import { useRef, useEffect, useCallback } from 'react';
import { TextBlockProps } from '../types/blocks';

/**
 * Text block component with direct inline editing
 * Renders text content with configurable HTML tag
 */
export function TextBlock({
  content,
  tag = 'p',
  className = '',
  isEditing = false,
  onContentChange,
  onEditingChange,
  mode = 'preview'
}: TextBlockProps & {
  isEditing?: boolean;
  onContentChange?: (content: string) => void;
  onEditingChange?: (editing: boolean) => void;
  mode?: 'edit' | 'preview';
}) {
  const Tag = tag;
  const elementRef = useRef<any>(null);
  const initialContent = useRef(content);

  // Handle editing state changes
  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    if (isEditing) {
      // Make editable
      element.contentEditable = 'true';
      element.focus();

      // Select all text
      const selection = window.getSelection();
      if (selection) {
        selection.selectAllChildren(element);
      }
    } else {
      // Make non-editable
      element.contentEditable = 'false';
    }
  }, [isEditing]);

  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    if (!isEditing) return;

    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSave();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancel();
    }
  }, [isEditing]);

  const handleSave = useCallback(() => {
    const element = elementRef.current;
    if (element && onContentChange) {
      const newContent = element.textContent || '';
      onContentChange(newContent);
    }
    if (onEditingChange) {
      onEditingChange(false);
    }
  }, [onContentChange, onEditingChange]);

  const handleCancel = useCallback(() => {
    const element = elementRef.current;
    if (element) {
      element.textContent = initialContent.current;
    }
    if (onEditingChange) {
      onEditingChange(false);
    }
  }, [onEditingChange]);

  const handleBlur = useCallback(() => {
    if (isEditing) {
      handleSave();
    }
  }, [isEditing, handleSave]);

  // Set up event listeners
  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    element.addEventListener('keydown', handleKeyDown);
    element.addEventListener('blur', handleBlur);

    return () => {
      element.removeEventListener('keydown', handleKeyDown);
      element.removeEventListener('blur', handleBlur);
    };
  }, [handleKeyDown, handleBlur]);

  return (
    <Tag
      ref={elementRef}
      className={`guti-text-block ${className} ${isEditing ? 'guti-editing' : ''}`}
      style={{
        outline: isEditing ? '1px dashed rgba(59, 130, 246, 0.6)' : 'none',
        borderRadius: isEditing ? '2px' : '0',
        padding: isEditing ? '1px 2px' : '0',
        cursor: mode === 'edit' && !isEditing ? 'text' : 'inherit',
      }}
      suppressContentEditableWarning
    >
      {content}
    </Tag>
  );
}

TextBlock.displayName = 'TextBlock';
