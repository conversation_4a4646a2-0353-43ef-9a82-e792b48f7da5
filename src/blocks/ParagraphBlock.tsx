import { useRef, useEffect, useCallback } from 'react';
import { ParagraphBlockProps } from '../types/blocks';

/**
 * Paragraph block component with direct inline editing
 * Renders paragraph content with rich text support
 */
export function ParagraphBlock({
  content,
  className = '',
  isEditing = false,
  onContentChange,
  onEditingChange,
  mode = 'preview'
}: ParagraphBlockProps & {
  isEditing?: boolean;
  onContentChange?: (content: string) => void;
  onEditingChange?: (editing: boolean) => void;
  mode?: 'edit' | 'preview';
}) {
  const elementRef = useRef<HTMLParagraphElement>(null);
  const initialContent = useRef(content);

  // Handle editing state changes
  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    if (isEditing) {
      element.contentEditable = 'true';
      element.focus();

      const selection = window.getSelection();
      if (selection) {
        selection.selectAllChildren(element);
      }
    } else {
      element.contentEditable = 'false';
    }
  }, [isEditing]);

  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    if (!isEditing) return;

    if (e.key === 'Enter' && e.shiftKey) {
      // Allow shift+enter for new lines
      return;
    } else if (e.key === 'Enter') {
      e.preventDefault();
      handleSave();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancel();
    }
  }, [isEditing]);

  const handleSave = useCallback(() => {
    const element = elementRef.current;
    if (element && onContentChange) {
      const newContent = element.textContent || '';
      onContentChange(newContent);
    }
    if (onEditingChange) {
      onEditingChange(false);
    }
  }, [onContentChange, onEditingChange]);

  const handleCancel = useCallback(() => {
    const element = elementRef.current;
    if (element) {
      element.textContent = initialContent.current;
    }
    if (onEditingChange) {
      onEditingChange(false);
    }
  }, [onEditingChange]);

  const handleBlur = useCallback(() => {
    if (isEditing) {
      handleSave();
    }
  }, [isEditing, handleSave]);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    element.addEventListener('keydown', handleKeyDown);
    element.addEventListener('blur', handleBlur);

    return () => {
      element.removeEventListener('keydown', handleKeyDown);
      element.removeEventListener('blur', handleBlur);
    };
  }, [handleKeyDown, handleBlur]);

  return (
    <p
      ref={elementRef}
      className={`guti-paragraph-block ${className} ${isEditing ? 'guti-editing' : ''}`}
      style={{
        outline: isEditing ? '2px dashed rgba(59, 130, 246, 0.5)' : 'none',
        background: isEditing ? 'rgba(59, 130, 246, 0.05)' : 'transparent',
        borderRadius: isEditing ? '3px' : '0',
        padding: isEditing ? '4px 6px' : '0',
        cursor: mode === 'edit' && !isEditing ? 'text' : 'inherit',
        minHeight: isEditing ? '1.5em' : 'auto',
      }}
      suppressContentEditableWarning
    >
      {content}
    </p>
  );
}

ParagraphBlock.displayName = 'ParagraphBlock';
