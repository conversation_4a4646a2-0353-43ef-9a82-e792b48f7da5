import { useRef, useEffect, useCallback, useState } from 'react';
import { ParagraphBlockProps } from '../types/blocks';
import { RichTextToolbar } from '../components/RichTextToolbar';

/**
 * Paragraph block component with direct inline editing
 * Renders paragraph content with rich text support
 */
export function ParagraphBlock({
  content,
  className = '',
  isEditing = false,
  onContentChange,
  onEditingChange,
  mode = 'preview'
}: ParagraphBlockProps & {
  isEditing?: boolean;
  onContentChange?: (content: string) => void;
  onEditingChange?: (editing: boolean) => void;
  mode?: 'edit' | 'preview';
}) {
  const elementRef = useRef<HTMLParagraphElement>(null);
  const initialContent = useRef(content);
  const [showToolbar, setShowToolbar] = useState(false);
  const [toolbarPosition, setToolbarPosition] = useState({ x: 0, y: 0 });

  // Handle editing state changes
  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    if (isEditing) {
      element.contentEditable = 'true';
      element.focus();

      // Place cursor at the end instead of selecting all
      const range = document.createRange();
      const selection = window.getSelection();
      if (selection && element.childNodes.length > 0) {
        range.selectNodeContents(element);
        range.collapse(false); // Collapse to end
        selection.removeAllRanges();
        selection.addRange(range);
      }

      // Add selection change listener
      const handleSelectionChange = () => {
        const selection = window.getSelection();
        if (selection && selection.rangeCount > 0) {
          const range = selection.getRangeAt(0);
          const selectedText = range.toString();

          if (selectedText.length > 0 && element.contains(range.commonAncestorContainer)) {
            const rect = range.getBoundingClientRect();
            setToolbarPosition({
              x: rect.left + rect.width / 2 - 150,
              y: rect.top + window.scrollY - 10
            });
            setShowToolbar(true);
          } else {
            setShowToolbar(false);
          }
        } else {
          setShowToolbar(false);
        }
      };

      document.addEventListener('selectionchange', handleSelectionChange);

      return () => {
        document.removeEventListener('selectionchange', handleSelectionChange);
      };
    } else {
      element.contentEditable = 'false';
      setShowToolbar(false);
    }
  }, [isEditing]);

  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    if (!isEditing) return;

    if (e.key === 'Enter' && e.shiftKey) {
      // Allow shift+enter for new lines
      return;
    } else if (e.key === 'Enter') {
      e.preventDefault();
      handleSave();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancel();
    }
  }, [isEditing]);

  const handleSave = useCallback(() => {
    const element = elementRef.current;
    if (element && onContentChange) {
      // Save HTML content to preserve formatting
      const newContent = element.innerHTML || '';
      onContentChange(newContent);
    }
    if (onEditingChange) {
      onEditingChange(false);
    }
  }, [onContentChange, onEditingChange]);

  // Handle rich text formatting
  const handleFormat = useCallback((command: string, value?: string) => {
    const element = elementRef.current;
    if (!element) return;

    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    const range = selection.getRangeAt(0);

    if (!element.contains(range.commonAncestorContainer)) return;

    try {
      const success = document.execCommand(command, false, value);

      if (success) {
        setTimeout(() => {
          const newContent = element.innerHTML || '';
          if (onContentChange) {
            onContentChange(newContent);
          }
        }, 0);
      }
    } catch (error) {
      console.warn('Formatting command failed:', command, error);
    }

    element.focus();
  }, [onContentChange]);

  const handleCancel = useCallback(() => {
    const element = elementRef.current;
    if (element) {
      element.textContent = initialContent.current;
    }
    if (onEditingChange) {
      onEditingChange(false);
    }
  }, [onEditingChange]);

  const handleBlur = useCallback(() => {
    if (isEditing) {
      handleSave();
    }
  }, [isEditing, handleSave]);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    element.addEventListener('keydown', handleKeyDown);
    element.addEventListener('blur', handleBlur);

    return () => {
      element.removeEventListener('keydown', handleKeyDown);
      element.removeEventListener('blur', handleBlur);
    };
  }, [handleKeyDown, handleBlur]);

  return (
    <>
      <p
        ref={elementRef}
        className={`guti-paragraph-block ${className} ${isEditing ? 'guti-editing' : ''}`}
        style={{
          outline: isEditing ? '1px dashed rgba(59, 130, 246, 0.6)' : 'none',
          borderRadius: isEditing ? '2px' : '0',
          padding: isEditing ? '2px 3px' : '0',
          cursor: mode === 'edit' && !isEditing ? 'text' : 'inherit',
          minHeight: isEditing ? '1.5em' : 'auto',
        }}
        suppressContentEditableWarning
        dangerouslySetInnerHTML={{ __html: content }}
      />

      {/* Rich Text Toolbar */}
      {isEditing && (
        <RichTextToolbar
          visible={showToolbar}
          position={toolbarPosition}
          onFormat={handleFormat}
          onClose={() => {
            setShowToolbar(false);
            if (onEditingChange) {
              onEditingChange(false);
            }
          }}
        />
      )}
    </>
  );
}

ParagraphBlock.displayName = 'ParagraphBlock';
