import { HeadingBlockProps } from '../types/blocks';

/**
 * Heading block component
 * Renders heading content with configurable level (H1-H6)
 */
export function HeadingBlock({ 
  content, 
  level = 1, 
  className = '' 
}: HeadingBlockProps) {
  const Tag = `h${level}` as keyof JSX.IntrinsicElements;
  
  return (
    <Tag 
      className={`guti-heading-block guti-heading-${level} ${className}`}
      dangerouslySetInnerHTML={{ __html: content }}
    />
  );
}

HeadingBlock.displayName = 'HeadingBlock';
