import { useRef, useEffect, useCallback } from 'react';
import { HeadingBlockProps } from '../types/blocks';

/**
 * Heading block component with direct inline editing
 * Renders heading content with configurable level (H1-H6)
 */
export function HeadingBlock({
  content,
  level = 1,
  className = '',
  isEditing = false,
  onContentChange,
  onEditingChange,
  mode = 'preview'
}: HeadingBlockProps & {
  isEditing?: boolean;
  onContentChange?: (content: string) => void;
  onEditingChange?: (editing: boolean) => void;
  mode?: 'edit' | 'preview';
}) {
  const elementRef = useRef<HTMLDivElement>(null);
  const initialContent = useRef(content);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    if (isEditing) {
      element.contentEditable = 'true';
      element.focus();

      const selection = window.getSelection();
      if (selection) {
        selection.selectAllChildren(element);
      }
    } else {
      element.contentEditable = 'false';
    }
  }, [isEditing]);

  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    if (!isEditing) return;

    if (e.key === 'Enter') {
      e.preventDefault();
      handleSave();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancel();
    }
  }, [isEditing]);

  const handleSave = useCallback(() => {
    const element = elementRef.current;
    if (element && onContentChange) {
      const newContent = element.textContent || '';
      onContentChange(newContent);
    }
    if (onEditingChange) {
      onEditingChange(false);
    }
  }, [onContentChange, onEditingChange]);

  const handleCancel = useCallback(() => {
    const element = elementRef.current;
    if (element) {
      element.textContent = initialContent.current;
    }
    if (onEditingChange) {
      onEditingChange(false);
    }
  }, [onEditingChange]);

  const handleBlur = useCallback(() => {
    if (isEditing) {
      handleSave();
    }
  }, [isEditing, handleSave]);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    element.addEventListener('keydown', handleKeyDown);
    element.addEventListener('blur', handleBlur);

    return () => {
      element.removeEventListener('keydown', handleKeyDown);
      element.removeEventListener('blur', handleBlur);
    };
  }, [handleKeyDown, handleBlur]);

  // Get heading styles based on level
  const getHeadingStyles = () => {
    const baseStyles = {
      fontWeight: 'bold',
      margin: '0',
      lineHeight: '1.2',
    };

    switch (level) {
      case 1: return { ...baseStyles, fontSize: '2rem' };
      case 2: return { ...baseStyles, fontSize: '1.5rem' };
      case 3: return { ...baseStyles, fontSize: '1.25rem' };
      case 4: return { ...baseStyles, fontSize: '1.125rem' };
      case 5: return { ...baseStyles, fontSize: '1rem' };
      case 6: return { ...baseStyles, fontSize: '0.875rem' };
      default: return { ...baseStyles, fontSize: '1rem' };
    }
  };

  return (
    <div
      ref={elementRef}
      className={`guti-heading-block guti-heading-${level} ${className} ${isEditing ? 'guti-editing' : ''}`}
      style={{
        ...getHeadingStyles(),
        outline: isEditing ? '1px dashed rgba(59, 130, 246, 0.6)' : 'none',
        borderRadius: isEditing ? '2px' : '0',
        padding: isEditing ? '1px 2px' : '0',
        cursor: mode === 'edit' && !isEditing ? 'text' : 'inherit',
      }}
      suppressContentEditableWarning
    >
      {content}
    </div>
  );
}

HeadingBlock.displayName = 'HeadingBlock';
