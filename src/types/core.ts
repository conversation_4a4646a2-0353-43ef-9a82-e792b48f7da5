import { ReactNode, ComponentType } from 'react';
import { JSONSchema7 } from 'json-schema';
import { ThemeTokens } from './theme';

/**
 * Unique identifier type for blocks and pages
 */
export type GutiId = string;

/**
 * Base configuration for Guti Builder
 */
export interface GutiBuilderConfig {
  /** Theme configuration */
  theme?: ThemeTokens;
  /** Custom block definitions */
  customBlocks?: Record<string, BlockDefinition>;
  /** Global settings */
  settings?: {
    /** Enable/disable drag and drop */
    dragAndDrop?: boolean;
    /** Enable/disable inline editing */
    inlineEditing?: boolean;
    /** Enable/disable responsive preview */
    responsivePreview?: boolean;
    /** Maximum nesting depth for containers */
    maxNestingDepth?: number;
  };
  /** Event handlers */
  onPageChange?: (pageData: PageData) => void;
  onBlockAdd?: (blockData: BlockData) => void;
  onBlockUpdate?: (blockData: BlockData) => void;
  onBlockDelete?: (blockId: GutiId) => void;
}

/**
 * Page data structure
 */
export interface PageData {
  /** Unique page identifier */
  id: GutiId;
  /** Page title */
  title: string;
  /** Page description */
  description?: string;
  /** Page metadata */
  meta?: Record<string, any>;
  /** Root blocks in the page */
  blocks: BlockData[];
  /** Page-specific theme overrides */
  theme?: Partial<ThemeConfig>;
  /** Page settings */
  settings?: {
    /** Page layout type */
    layout?: 'full-width' | 'contained' | 'centered';
    /** Page background */
    background?: string;
    /** Custom CSS classes */
    className?: string;
  };
  /** Timestamps */
  createdAt: string;
  updatedAt: string;
}

/**
 * Block data structure
 */
export interface BlockData {
  /** Unique block identifier */
  id: GutiId;
  /** Block type identifier */
  type: string;
  /** Block properties/content */
  props: Record<string, any>;
  /** Child blocks (for container blocks) */
  children?: BlockData[];
  /** Block-specific styling */
  style?: BlockStyle;
  /** Block settings */
  settings?: {
    /** Custom CSS classes */
    className?: string;
    /** Responsive visibility */
    responsive?: {
      mobile?: boolean;
      tablet?: boolean;
      desktop?: boolean;
    };
    /** Animation settings */
    animation?: {
      type?: string;
      duration?: number;
      delay?: number;
    };
  };
}

/**
 * Block styling configuration
 */
export interface BlockStyle {
  /** Spacing */
  margin?: SpacingConfig;
  padding?: SpacingConfig;
  /** Layout */
  width?: string;
  height?: string;
  minWidth?: string;
  minHeight?: string;
  maxWidth?: string;
  maxHeight?: string;
  /** Background */
  backgroundColor?: string;
  backgroundImage?: string;
  backgroundSize?: string;
  backgroundPosition?: string;
  backgroundRepeat?: string;
  /** Border */
  border?: string;
  borderRadius?: string;
  /** Typography */
  fontSize?: string;
  fontWeight?: string;
  fontFamily?: string;
  color?: string;
  textAlign?: 'left' | 'center' | 'right' | 'justify';
  lineHeight?: string;
  /** Effects */
  boxShadow?: string;
  opacity?: number;
  transform?: string;
  /** Custom CSS properties */
  custom?: Record<string, string>;
}

/**
 * Spacing configuration
 */
export interface SpacingConfig {
  top?: string;
  right?: string;
  bottom?: string;
  left?: string;
}

/**
 * Theme configuration (simplified for store compatibility)
 */
export interface ThemeConfig {
  /** Color palette */
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    surface: string;
    text: string;
    textMuted: string;
    border: string;
    success: string;
    warning: string;
    error: string;
    [key: string]: string;
  };
  /** Typography */
  typography: {
    fontFamily: string;
    fontFamilyMono: string;
    fontSize: {
      xs: string;
      sm: string;
      base: string;
      lg: string;
      xl: string;
      '2xl': string;
      '3xl': string;
      '4xl': string;
      [key: string]: string;
    };
    fontWeight: {
      light: number;
      normal: number;
      medium: number;
      semibold: number;
      bold: number;
      [key: string]: number;
    };
    lineHeight: {
      tight: number;
      normal: number;
      relaxed: number;
      [key: string]: number;
    };
  };
  /** Spacing scale */
  spacing: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
    '2xl': string;
    '3xl': string;
    '4xl': string;
    [key: string]: string;
  };
  /** Border radius */
  borderRadius: {
    none: string;
    sm: string;
    base: string;
    lg: string;
    full: string;
    [key: string]: string;
  };
  /** Shadows */
  shadows: {
    sm: string;
    base: string;
    lg: string;
    xl: string;
    [key: string]: string;
  };
  /** Breakpoints */
  breakpoints: {
    mobile: string;
    tablet: string;
    desktop: string;
    [key: string]: string;
  };
}

/**
 * Block definition for registration
 */
export interface BlockDefinition {
  /** Block type identifier */
  type: string;
  /** Human-readable name */
  name: string;
  /** Block description */
  description?: string;
  /** Block category */
  category: 'content' | 'layout' | 'media' | 'form' | 'custom';
  /** Block icon (can be React component or string) */
  icon?: ReactNode | string;
  /** JSON Schema for block props validation */
  schema: JSONSchema7;
  /** Default props */
  defaultProps: Record<string, any>;
  /** React component for rendering */
  component: ComponentType<BlockComponentProps>;
  /** React component for editing (optional, falls back to generic editor) */
  editor?: ComponentType<BlockEditorProps>;
  /** Whether block can have children */
  canHaveChildren?: boolean;
  /** Allowed child block types (if canHaveChildren is true) */
  allowedChildren?: string[];
  /** Whether block can be nested inside other blocks */
  canBeNested?: boolean;
  /** Block-specific settings */
  settings?: {
    /** Whether block supports styling */
    supportsStyling?: boolean;
    /** Whether block supports responsive settings */
    supportsResponsive?: boolean;
    /** Whether block supports animations */
    supportsAnimations?: boolean;
  };
}

/**
 * Props passed to block components
 */
export interface BlockComponentProps {
  /** Block data */
  block: BlockData;
  /** Whether in edit mode */
  isEditing?: boolean;
  /** Whether block is selected */
  isSelected?: boolean;
  /** Child blocks rendered as React nodes */
  children?: ReactNode;
  /** Theme configuration */
  theme: ThemeConfig;
  /** Event handlers */
  onUpdate?: (props: Record<string, any>) => void;
  onSelect?: () => void;
  onDelete?: () => void;
}

/**
 * Props passed to block editor components
 */
export interface BlockEditorProps {
  /** Block data */
  block: BlockData;
  /** Block definition */
  definition: BlockDefinition;
  /** Theme configuration */
  theme: ThemeConfig;
  /** Update handler */
  onUpdate: (props: Record<string, any>) => void;
  /** Close editor handler */
  onClose: () => void;
}
