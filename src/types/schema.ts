/**
 * JSON Schema related types and utilities
 */

import { JSONSchema7 } from 'json-schema';

/**
 * Extended JSON Schema with Guti Builder specific properties
 */
export interface GutiSchema extends Omit<JSONSchema7, 'properties'> {
  /** UI hints for form generation */
  'x-ui'?: {
    /** Input component type */
    component?: 'input' | 'textarea' | 'select' | 'checkbox' | 'radio' | 'color' | 'image' | 'video' | 'rich-text' | 'slider' | 'toggle';
    /** Input placeholder */
    placeholder?: string;
    /** Input label */
    label?: string;
    /** Help text */
    help?: string;
    /** Field group */
    group?: string;
    /** Field order */
    order?: number;
    /** Conditional display */
    condition?: {
      field: string;
      value: any;
      operator?: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'nin';
    };
    /** Custom validation */
    validation?: {
      message?: string;
      pattern?: string;
    };
  };
  /** Properties can be GutiSchema or JSONSchema7 */
  properties?: {
    [key: string]: GutiSchema | JSONSchema7;
  };
}

/**
 * Schema validation result
 */
export interface ValidationResult {
  valid: boolean;
  errors: ValidationError[];
}

/**
 * Validation error
 */
export interface ValidationError {
  path: string;
  message: string;
  value?: any;
  schema?: GutiSchema;
}

/**
 * Schema registry for block types
 */
export interface SchemaRegistry {
  /** Register a schema for a block type */
  register(blockType: string, schema: GutiSchema): void;
  /** Get schema for a block type */
  get(blockType: string): GutiSchema | undefined;
  /** Validate data against a schema */
  validate(blockType: string, data: any): ValidationResult;
  /** Get all registered schemas */
  getAll(): Record<string, GutiSchema>;
  /** Check if a block type is registered */
  has(blockType: string): boolean;
  /** Unregister a block type */
  unregister(blockType: string): boolean;
  /** Clear all registered schemas */
  clear(): void;
  /** Get list of all registered block types */
  getBlockTypes(): string[];
}

/**
 * Form field configuration derived from schema
 */
export interface FormField {
  name: string;
  type: string;
  label: string;
  placeholder?: string;
  help?: string;
  required: boolean;
  options?: Array<{ label: string; value: any }>;
  validation?: {
    pattern?: string;
    min?: number;
    max?: number;
    minLength?: number;
    maxLength?: number;
    message?: string;
  };
  group?: string;
  order: number;
  condition?: {
    field: string;
    value: any;
    operator: string;
  };
}
