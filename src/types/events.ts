/**
 * Event system types for Guti Builder
 */

import { GutiId, BlockData, PageData } from './core';

/**
 * Base event interface
 */
export interface GutiEvent {
  type: string;
  timestamp: number;
  source?: string;
}

/**
 * Page events
 */
export interface PageCreatedEvent extends GutiEvent {
  type: 'page:created';
  payload: {
    page: PageData;
  };
}

export interface PageUpdatedEvent extends GutiEvent {
  type: 'page:updated';
  payload: {
    page: PageData;
    changes: Partial<PageData>;
  };
}

export interface PageDeletedEvent extends GutiEvent {
  type: 'page:deleted';
  payload: {
    pageId: GutiId;
  };
}

/**
 * Block events
 */
export interface BlockAddedEvent extends GutiEvent {
  type: 'block:added';
  payload: {
    block: BlockData;
    parentId?: GutiId;
    index: number;
  };
}

export interface BlockUpdatedEvent extends GutiEvent {
  type: 'block:updated';
  payload: {
    block: BlockData;
    changes: Partial<BlockData>;
  };
}

export interface BlockDeletedEvent extends GutiEvent {
  type: 'block:deleted';
  payload: {
    blockId: GutiId;
    parentId?: GutiId;
  };
}

export interface BlockMovedEvent extends GutiEvent {
  type: 'block:moved';
  payload: {
    blockId: GutiId;
    fromParentId?: GutiId;
    toParentId?: GutiId;
    fromIndex: number;
    toIndex: number;
  };
}

export interface BlockSelectedEvent extends GutiEvent {
  type: 'block:selected';
  payload: {
    blockId: GutiId;
  };
}

export interface BlockDeselectedEvent extends GutiEvent {
  type: 'block:deselected';
  payload: {
    blockId: GutiId;
  };
}

/**
 * Editor events
 */
export interface EditorModeChangedEvent extends GutiEvent {
  type: 'editor:mode-changed';
  payload: {
    mode: 'edit' | 'preview';
  };
}

export interface EditorUndoEvent extends GutiEvent {
  type: 'editor:undo';
  payload: {
    action: string;
  };
}

export interface EditorRedoEvent extends GutiEvent {
  type: 'editor:redo';
  payload: {
    action: string;
  };
}

/**
 * Union of all event types
 */
export type GutiBuilderEvent = 
  | PageCreatedEvent
  | PageUpdatedEvent
  | PageDeletedEvent
  | BlockAddedEvent
  | BlockUpdatedEvent
  | BlockDeletedEvent
  | BlockMovedEvent
  | BlockSelectedEvent
  | BlockDeselectedEvent
  | EditorModeChangedEvent
  | EditorUndoEvent
  | EditorRedoEvent;

/**
 * Event handler type
 */
export type EventHandler<T extends GutiEvent = GutiEvent> = (event: T) => void;

/**
 * Event emitter interface
 */
export interface EventEmitter {
  /** Subscribe to events */
  on<T extends GutiEvent>(eventType: T['type'], handler: EventHandler<T>): () => void;
  /** Emit an event */
  emit<T extends GutiEvent>(event: T): void;
  /** Remove all listeners */
  removeAllListeners(): void;
}
