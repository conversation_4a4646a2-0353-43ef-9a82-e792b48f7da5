/**
 * Built-in block type definitions
 */

// Text Block
export interface TextBlockProps {
  content: string;
  tag?: 'span' | 'p' | 'div';
  className?: string;
}

// Image Block
export interface ImageBlockProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  objectFit?: 'cover' | 'contain' | 'fill' | 'none' | 'scale-down';
  loading?: 'lazy' | 'eager';
  className?: string;
}

// Video Block
export interface VideoBlockProps {
  src?: string;
  poster?: string;
  width?: number;
  height?: number;
  autoplay?: boolean;
  loop?: boolean;
  muted?: boolean;
  controls?: boolean;
  // YouTube/Vimeo embed support
  embedUrl?: string;
  embedType?: 'youtube' | 'vimeo' | 'custom';
  className?: string;
}

// Heading Block
export interface HeadingBlockProps {
  content: string;
  level: 1 | 2 | 3 | 4 | 5 | 6;
  className?: string;
}

// Paragraph Block
export interface ParagraphBlockProps {
  content: string;
  className?: string;
}

// Spacer Block
export interface SpacerBlockProps {
  height: string;
  className?: string;
}

// Container Block
export interface ContainerBlockProps {
  direction?: 'row' | 'column';
  gap?: string;
  alignItems?: 'flex-start' | 'center' | 'flex-end' | 'stretch';
  justifyContent?: 'flex-start' | 'center' | 'flex-end' | 'space-between' | 'space-around' | 'space-evenly';
  wrap?: boolean;
  className?: string;
}

// Grid Block
export interface GridBlockProps {
  columns: number;
  gap?: string;
  columnGap?: string;
  rowGap?: string;
  // Responsive columns
  mobileColumns?: number;
  tabletColumns?: number;
  className?: string;
}

/**
 * Union type of all built-in block props
 */
export type BuiltInBlockProps = 
  | TextBlockProps
  | ImageBlockProps
  | VideoBlockProps
  | HeadingBlockProps
  | ParagraphBlockProps
  | SpacerBlockProps
  | ContainerBlockProps
  | GridBlockProps;
