/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
    "./example/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // Guti Builder brand colors
        'guti': {
          50: '#f0f9ff',
          100: '#e0f2fe',
          200: '#bae6fd',
          300: '#7dd3fc',
          400: '#38bdf8',
          500: '#0ea5e9',
          600: '#0284c7',
          700: '#0369a1',
          800: '#075985',
          900: '#0c4a6e',
          950: '#082f49',
        },
        // Semantic colors for the builder
        'builder': {
          'bg': 'var(--builder-bg)',
          'surface': 'var(--builder-surface)',
          'border': 'var(--builder-border)',
          'text': 'var(--builder-text)',
          'text-muted': 'var(--builder-text-muted)',
          'primary': 'var(--builder-primary)',
          'primary-hover': 'var(--builder-primary-hover)',
          'secondary': 'var(--builder-secondary)',
          'accent': 'var(--builder-accent)',
          'success': 'var(--builder-success)',
          'warning': 'var(--builder-warning)',
          'error': 'var(--builder-error)',
        }
      },
      spacing: {
        // Builder-specific spacing
        'builder-xs': 'var(--builder-spacing-xs)',
        'builder-sm': 'var(--builder-spacing-sm)',
        'builder-md': 'var(--builder-spacing-md)',
        'builder-lg': 'var(--builder-spacing-lg)',
        'builder-xl': 'var(--builder-spacing-xl)',
      },
      borderRadius: {
        'builder': 'var(--builder-radius)',
        'builder-sm': 'var(--builder-radius-sm)',
        'builder-lg': 'var(--builder-radius-lg)',
      },
      fontFamily: {
        'builder': 'var(--builder-font-family)',
        'builder-mono': 'var(--builder-font-family-mono)',
      },
      fontSize: {
        'builder-xs': 'var(--builder-font-size-xs)',
        'builder-sm': 'var(--builder-font-size-sm)',
        'builder-base': 'var(--builder-font-size-base)',
        'builder-lg': 'var(--builder-font-size-lg)',
        'builder-xl': 'var(--builder-font-size-xl)',
      },
      boxShadow: {
        'builder': 'var(--builder-shadow)',
        'builder-sm': 'var(--builder-shadow-sm)',
        'builder-lg': 'var(--builder-shadow-lg)',
      }
    },
  },
  plugins: [],
}
